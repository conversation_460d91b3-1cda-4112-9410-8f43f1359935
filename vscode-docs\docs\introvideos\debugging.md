---
ContentId: cf275b3d-c1d8-4a55-b2eb-a8a744882b6a
DateApproved: 9/9/2024
MetaDescription: Debugging is a core feature of Visual Studio Code. Learn how to configure and use the Node.js debugger in this introductory video.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Debugging in Visual Studio Code

Debugging is a core feature of Visual Studio Code. In this tutorial, we will show you how to run and debug a program in VS Code. We'll take a tour of the **Run and Debug** view, explore some debugging features, and end by setting a breakpoint.

> **Tip:** To use the debugging features demonstrated in this video for Node.js, you will need to first install [Node.js](https://nodejs.org). To follow along with the Python portion of the video, you'll need to install [Python](https://www.python.org/downloads/).

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/3HiLLByBWkg" title="Getting started with debugging in VS Code" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Here's the next video we recommend: [Version Control](/docs/introvideos/versioncontrol.md)

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Debugging that a sample Node.js app.
* Exploring the debugging user interface.
* Debugging a sample C# app.
* Set a breakpoint.
* View variables and watch variables
* Inspect the call stack and loaded scripts.
* Use the debug console.
* Debug a React app.
* Launch configurations.
* Conditional breakpoints.
* Edit mode.
* Debug in Microsoft Edge.

## Next video

* [Version Control](/docs/introvideos/versioncontrol.md) - Learn the basics of Git version control.
* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [Debugging](/docs/debugtest/debugging.md) - Official documentation for VS Code debugging.
* [Integrated Terminal](/docs/terminal/basics.md) - Use the integrated terminal inside VS Code.
