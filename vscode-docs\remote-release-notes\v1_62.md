# October 2021 (version 1.62)

## Containers (version 0.205.x)

Dev Containers releases for VS Code 1.62.x with versions 0.205.x. (Issue grooming milestone.)

### New configuration videos

New advanced container configuration videos to help you:

* [Persist bash history](https://code.visualstudio.com/remote/advancedcontainers/persist-bash-history) in your container.
* [Work with monorepos](https://code.visualstudio.com/remote/advancedcontainers/change-default-source-mount) that contain multiple independent projects.
* [Run npm install](https://code.visualstudio.com/remote/advancedcontainers/start-processes) when a container is created.
* [Add a local folder](https://code.visualstudio.com/remote/advancedcontainers/add-local-file-mount) to your dev container.

## SSH

### Sort Folders in Remote Explorer Alphabetically

A new setting was added to Remote - SSH, `remote.SSH.foldersSortOrder`, that allows you to sort your folders under the targets in the SSH Remote Explorer either alphabetically or in order of most recently opened.

![SSH Folder Sorting Setting in Settings Editor](images/1_62/ssh-folder-sorting-setting.png)

## WSL

The hover over the Remote indicator now shows you whether the current Linux distribution is a WSL 1 or WSL 2 distro:

![WSL Remote indicator hover](images/1_62/wsl-remote-indicator-hover.png)

Additionally, the distro pickers and the Remote Explorer put a label ("wsl1") after the WSL 1 distros:

![Distro picker](images/1_62/distro-picker.png)

We recommend using WSL 2 for better performance.
