# February 2023 (version 1.76)

## Dev Containers (version 0.282.x)

### Note on bind mount performance

On Windows and macOS, we have added a note to the details of bind mounts because these cross operating system boundaries (Linux <> Windows/macOS) and that can be slow on large folders.

To avoid this performance penalty on Windows, you could use a folder in WSL. On both platforms, macOS and Windows, you can use the **Remote-Containers: Clone Repository in Container Volume...** command to clone a repository into a Docker volume, which also uses a Linux file system and avoids bridging operating systems.

![Note on bind mount performance](images/1_76/bind-mount-performance.png)

### Deprecated legacy Dev Container Features

Dev Container Features listed under `"features"` in the `devcontainer.json` were initially built-in and we have since moved on to a model where these can be contributed by the community in OSI registries in a decentralized way and updated on-demand. See the [Dev Container Features](https://containers.dev/implementors/features) documentation for more details on the current contribution model.

For most of the legacy Features, we have introduced a corresponding replacement in the current set of Features provided by us and these will continue to work into the future. Five of the legacy Features have not been replaced because they are either beyond the scope of what we can provide or we have folded them into other Features. These five legacy Features are now deprecated and will be removed in a future release.

The deprecated legacy features are:

* `fish`: Check the list of [available features](https://containers.dev/features) for replacements.
* `maven`: `ghcr.io/devcontainers/features/java` has an option to install Maven.
* `gradle`: `ghcr.io/devcontainers/features/java` has an option to install Gradle.
* `homebrew`: Check the list of [available features](https://containers.dev/features) for replacements.
* `jupyterlab`: `ghcr.io/devcontainers/features/python` has an option to install JupyterLab.

## WSL

### Download WSL server build in the background

When VS Code Desktop detects a new update and downloads it in the background, the WSL extension now also downloads the matching server build. When the VS Code update is applied, VS Code is able to open WSL windows without a extra download. This will come handy when the update is applied while the machine has no connection to the internet.
