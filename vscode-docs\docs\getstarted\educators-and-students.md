---
ContentId: 33b73b13-3d85-49d9-95b2-92b75137818c
DateApproved: 05/08/2025
MetaDescription: Resources for educators and students to get started with Visual Studio Code.
---
# VS Code for Educators and Students

Welcome to the world of using Visual Studio Code as an educator or student! In this article, we introduce you to various tools, extension packs, and learning paths that can help you get started with coding in Visual Studio Code. Whether you're an educator looking for resources to enhance your classroom experience, or a student eager to learn and explore coding, this guide is here to help you get started.

## Educator resources

The following resources can help you get started with Visual Studio Code in your classroom:

### Coding and extension packs

It can be frustrating to install all the different software components to get ready to code. There's editors/IDEs, runtimes and compilers, packages or libraries, and different versions of each. We've got you covered with coding and extension packs that can help accelerate your setup.

- Coding Pack for Java: [Windows](https://aka.ms/vscode-java-installer-win) / [macOS](https://aka.ms/vscode-java-installer-mac)
- [Node.js and JavaScript Education Extension Pack](https://marketplace.visualstudio.com/items?itemName=tanhakabir.node-js-education-extension-pack)
- [Python Education Extension Pack](https://marketplace.visualstudio.com/items?itemName=tanhakabir.python-education-extension-pack)

### CodeTour

Helping students understand a new codebase can be challenging and time-consuming. Creating descriptive enough instructions can be cumbersome and still not easy enough for students to follow. CodeTour can help you introduce your students to your assignments and projects in a streamlined and interactive way.

[CodeTour](https://marketplace.visualstudio.com/items?itemName=vsls-contrib.codetour) is a Visual Studio Code extension that enables you to create a step-by-step guide through your codebases. This functionality can be helpful in your classroom to break up your assignments into steps for students to follow or to get them familiar with all parts of the assignment by pointing them to relevant pieces within the assignment.

The following short clip shows a student using CodeTour to start working on their app:

![CodeTour in a Space Jam lesson](images/educators-and-students/codetour-space-jam.gif)

Essentially, a "code tour" is a series of interactive steps, each of which are associated with a specific directory, or file/line, and include a description of the respective code. Tours can either be checked into a repo to share with source control, or exported to a "tour file", which enables anyone to replay the same tour, without having to clone any code!

In the following video, an instructor is removing existing instructions embedded in the source code and replacing them with CodeTour steps. Students can then jump from step to step in the large game development project.

![CodeTour in a sample assignment](images/educators-and-students/codetour-example-lesson.gif)

#### Getting started with CodeTour

To start using CodeTour, you need to download the CodeTour extension from the VS Code [Marketplace](https://marketplace.visualstudio.com/vscode).

![CodeTour extension in Extension Marketplace](images/educators-and-students/codetour-extension-marketplace.png)

To use CodeTour effectively in your classroom, we recommend following the guidelines in the [CodeTour repository](https://github.com/microsoft/codetour#getting-started).

## Student resources

### Lessons for getting started with VS Code

| Title | Description |
|-------|-------------|
| [Set up your Python beginner development environment](https://learn.microsoft.com/training/modules/python-install-vscode/) | Get started with learning Python by installing and configuring the tools you need to build real applications. |
| [Use Git version-control tools](https://learn.microsoft.com/training/modules/use-git-from-vs-code/) | Utilize the tight integration of Visual Studio Code with the Git source-control management system. |
| [Write basic Python in notebooks](https://learn.microsoft.com/training/modules/basic-python-nasa/) | Learn the basics of Python by using notebooks in Visual Studio Code. |
| [Build a Node.js app for Azure Cosmos DB](https://learn.microsoft.com/training/modules/build-node-cosmos-app-vscode/) | Build a database app to store and query data in Azure Cosmos DB by using Visual Studio Code and Node.js. |

### Learn Python with NASA inspired lessons

This learning path introduces you to the world of Python with modules inspired by National Aeronautics and Space Administration (NASA) scientists to prepare you for a career in space exploration. Visit [Microsoft Learn training](https://learn.microsoft.com/training/topics/nasa) for the full list of modules.

Through these modules, you will:

- Understand and install the tools needed to learn programming
- Learn core programming concepts and apply them to real-life NASA problems
- Gain knowledge about cutting-edge technologies like machine learning and artificial intelligence
- See actual NASA employees talk about their job and give advice

| Title | Description |
|-------|-------------|
| [Discover the role of Python in space exploration](https://learn.microsoft.com/training/paths/introduction-python-space-exploration-nasa/?WT.mc_id=python-0000-cxa) | Learn how Python and data science play a role in the innovative solutions that NASA creates. |
| [Use Artificial Intelligence to classify space rocks](https://learn.microsoft.com/training/paths/classify-space-rocks-artificial-intelligence-nasa/?WT.mc_id=python-0000-cxa) | Create an AI model that can classify types of space rock present in a photo. |
| [Use Machine Learning to predict rocket launches](https://learn.microsoft.com/training/paths/machine-learning-predict-launch-delay-nasa/?WT.mc_id=python-0000-cxa) | This module introduces you to the world of machine learning and help you build a basic ML model. |

### Learn Python with Over the Moon

Try out these space-travel themed challenges that introduce you to data science, machine learning, and artificial intelligence with tools like Python, Visual Studio Code, and Azure.

| Title | Description |
|-------|-------------|
| [Explore space with Python and Visual Studio Code](https://learn.microsoft.com/training/paths/explore-space-using-python/?WT.mc_id=python-0000-cxa) | Plan a moon mission, predict meteor showers, and use artificial intelligence to recognize objects in this learning path inspired by the Netflix film "Over the Moon". |

### Learn Python with Wonder Woman

Check out these WONDER WOMAN 1984 inspired lessons built to help you write your first lines of Python code. In the first module, learn how to crack a Caesar Cipher code that reveals a secret message with a location. In the second module, create a personality challenge to determine which character you're most like!

Follow along with the [video walkthrough of the first lesson](https://www.youtube-nocookie.com/embed/VH_mU42lQkQ).

| Title | Description |
|-------|-------------|
| [Learn Python with Wonder Woman](https://learn.microsoft.com/training/paths/python-partnership/?WT.mc_id=python-0000-cxa) | Build a message decoder program and a personality quiz in these lessons inspired by the WONDER WOMAN 1984 film. |

### Learn Python with Space Jam

Inspired by the film "Space Jam: A New Legacy," this learning path shows basketball fans how an understanding of data science and coding can support their passions, create opportunities, and even open doors to possible careers. Develop skills in Visual Studio Code, Azure, GitHub, JavaScript, and Python, to gain insights into how individual moments throughout a player's history can lead to a critical game decision in the finals. Watch the [introduction video](https://www.youtube-nocookie.com/embed/pXkIIzihEYM).

| Title | Description |
|-------|-------------|
| [Use basketball stats to optimize game play with Visual Studio Code](https://learn.microsoft.com/training/paths/optimize-basketball-games-with-machine-learning/?WT.mc_id=LearnDrG-c9-niner&WT.mc_id=SpaceJam_Learn_-all-cxa) | Analyze basketball stats and create an app in this learning path inspired by the film "Space Jam: A New Legacy." |

## Tools for both educators and students

### Collaborate with Live Share

[Live Share](https://learn.microsoft.com/visualstudio/liveshare) enables you to quickly collaborate in real time with a friend, classmate, or professor on the same code without the need to sync code or to configure the same development tools, settings, or environment.

<iframe src="https://www.youtube-nocookie.com/embed/A2ceblXTBBc?rel=0&amp;disablekb=0&amp;modestbranding=1&amp;showinfo=0" frameborder="0" allowfullscreen title="Collaborate with Live Share"></iframe>

>**Note**: The video mentions the deprecated Live Share Extension Pack and Live Share Audio extension. To follow along with the video, you only need to install the [Live Share](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare) extension.

When you share a collaborative session, the person you're working with sees the workspace files in their editor and also receives editor context, such as language services or debugging configuration. This means your classmate can read the code you shared without having to clone a repo or install any dependencies your code relies on. They can help you with your code in the Visual Studio Code environment that's familiar to them.

Each of you can open files, navigate, edit code, or highlight, and changes are instantly reflected. As you edit, you can see your classmate's cursor, jump to the same location, and follow their actions.

You can also debug together using VS Code's debugging features, like hovers, locals and watches, the stack trace or the debug console. You are both able to set breakpoints and advance the debug cursor to step through the session.

For more details about what you can do with Live Share, visit the [how-to-guide](https://learn.microsoft.com/visualstudio/liveshare/use/install-live-share-visual-studio-code).

#### Get started with Live Share

To get started with using Live Share in VS Code, download the [Live Share](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare) extension from the VS Code Marketplace.

> <a class="install-extension-btn" href="vscode:extension/MS-vsliveshare.vsliveshare">Install the Live Share extension</a>

Once you log into your GitHub account, you can see the Live Share icon in the Activity Bar.

![Live Share icon in the Activity Bar](images/educators-and-students/liveshare-icon.png)

#### Starting a Live Share session

If you select **Start Collaboration session** from the Session Details menu, an invitation link to your session is automatically copied to your clipboard. You can share this link with anyone you'd like to collaborate with, as long as they also have VS Code and the Live Share extension installed.

![Live Share invitation](images/educators-and-students/liveshare-invitation.png)

When your classmate joins the session, you get a notification, and see their name come up under Participants.

![Live Share joined](images/educators-and-students/liveshare-joined.png)

#### Joining a Live Share session

If you select **Join Collaboration session** from the Session Details menu, you're able to enter the URL you received.

![Joining a Live Share session](images/educators-and-students/liveshare-join-session.png)

You should be able to see and interact with your classmate's code on your screen!

#### Joining a Live Share session from the browser

If you want to join a Live Share session but you don't have Visual Studio Code installed on your machine, you can use [Visual Studio Code for the Web](/docs/setup/vscode-web.md) to join a session. VS Code for the Web provides a zero-install Visual Studio Code experience running entirely in your browser. To get started, go to [https://vscode.dev](https://vscode.dev) in your browser.

After you navigate to VS Code for the Web, you can then install the [Live Share](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare) extension, and [join the Live Share session](#joining-a-live-share-session).

## Next steps

- Watch the [VS Code intro videos](/docs/getstarted/introvideos.md)
- Check out the [VS Code tips and tricks](/docs/getstarted/tips-and-tricks.md)
