---
ContentId: 9b79fbb2-f7d1-4b54-a4ad-e5ccb0ebd891
DateApproved: 11/18/2021
MetaDescription: Become a Visual Studio Code power user with these productivity tips.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Productivity Tips

In this Visual Studio Code tutorial, we describe Visual Studio Code features that help increase your coding productivity. Learning just a few tips can speed up your workflow and help you discover the full power of the VS Code editor.

<iframe src="https://www.youtube-nocookie.com/embed/HIqONcVBEm0" width="640" height="320" allowFullScreen="true" frameBorder="0" title="Productivity tips for Visual Studio Code"></iframe>

Here's the next video we recommend: [Personalize Visual Studio Code](/docs/introvideos/configure.md)

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Double Shift

  * Assign the double `kbstyle(Shift)` keyboard shortcut for often-used commands in the Keyboard Shortcuts editor (**Preferences: Open Keyboard Shortcuts** command)

* Side Bar focus with keyboard shortcuts

  * Press `kb(workbench.action.focusSideBar)` to put the focus in the Primary Side Bar
  * Press `kb(workbench.action.focusFirstEditorGroup)` to put the focus back in the editor

* Copy Paste

  * Press `kb(editor.action.clipboardCopyAction)` to copy the entire line
  * Press `kb(editor.action.clipboardPasteAction)` to paste the entire line

* Multiple cursors

  * Press `kb(editor.action.insertCursorAtEndOfEachLineSelected)` to add a cursor to the end of each selected line

* Multiple terminal windows

  * Drag terminal windows in the editor
  * Switch between open terminals in the editor with `kb(workbench.action.quickOpen)`

## Next video

* [Personalize Visual Studio Code](/docs/introvideos/configure.md) - Learn how to personalize VS Code to your work style with themes.
* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [Tips and Tricks](/docs/getstarted/tips-and-tricks.md) - Learn more productivity tips and tricks.
* [Code Editing](/docs/editing/codebasics.md) - Discover the full power of the VS Code editor.
* [IntelliSense](/docs/editing/intellisense.md) - Smart code completions and methods signatures.
