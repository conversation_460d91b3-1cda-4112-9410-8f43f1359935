---
ContentId: c720387b-e6db-4b40-865f-0c0a814b0b12
DateApproved: 10/5/2021
MetaDescription: Use extensions in Visual Studio Code to add new features, themes, and more.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Using extensions in Visual Studio Code

Use Visual Studio Code extensions to add new features, themes and more. In this tutorial, we will show you how to find extensions, install the ones you like, and disable extensions you don't want to use all the time.

<iframe src="https://www.youtube-nocookie.com/embed/SKcZ3cwX8lA?rel=0&amp;disablekb=0&amp;modestbranding=1&amp;showinfo=0" frameborder="0" allowfullscreen title="Using extensions in Visual Studio Code" ></iframe>

Here's the next video we recommend: [Debugging](/docs/introvideos/debugging.md)

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Find extensions to install using the Extensions view.
* Install an extension from the VS Code Extension Marketplace.
* See what features are added via the **Features Contributions** tab or Command Palette (`kb(workbench.action.showCommands)`).
* See recommendations for other extensions.

## Next video

* [Debugging](/docs/introvideos/debugging.md) - Learn how to debug your application, including setting breakpoints and inspecting variables.
* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [Extension Marketplace Documentation](/docs/configure/extensions/extension-marketplace.md) - Official documentation for the VS Code Extension Marketplace.
* [Marketplace](https://marketplace.visualstudio.com/) - Browse extensions online.
