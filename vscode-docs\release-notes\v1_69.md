---
Order:
TOCTitle: June 2022
PageTitle: Visual Studio Code June 2022
MetaDescription: Learn what is new in the Visual Studio Code June 2022 Release (1.69)
MetaSocialImage: 1_69/release-highlights.png
Date: 2022-7-7
DownloadVersion: 1.69.2
---
# June 2022 (version 1.69)

**Update 1.69.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2022+Recovery+1%22+is%3Aclosed).

**Update 1.69.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2022+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the June 2022 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[3-way merge editor](#3-way-merge-editor)** - Resolve merge conflicts within VS Code.
* **[Command Center](#command-center)** - New UI to search files, run commands, and navigate cursor history.
* **[Do Not Disturb mode](#do-not-disturb-mode)** - Silence non-critical notification popups.
* **[Toggle Light/Dark themes](#toggle-between-light-and-dark-themes)** - Quickly switch between preferred light and dark themes.
* **[Terminal shell integration](#shell-integration)** - Display command status, run recent commands, and more.
* **[Task output decorations](#decorations)** - Highlights task success or failure exit codes.
* **[Git Commit action button](#commit-action-button-for-git-repositories)** - Configure your default Git Commit action.
* **[Debug Step Into Target support](#improved-step-into-target-ui)** - Allows you to step directly into functions when paused.
* **[JavaScript sourcemap toggling](#javascript-debugging)** - Switch to debugging compiled rather than source code.
* **[Color theme tester](#theme-tester-supports-built-in-themes)** - Use vscode.dev to preview color themes.
* **[VS Code Server preview](#vs-code-server-private-preview)** - Run the same server used for Remote Development.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Watch the release party:** Listen in as the VS Code team discusses some of the new features. You can find the [recording of the event](https://www.youtube.com/watch?v=eiRrggf1EF4) on our YouTube channel.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Workbench

### 3 way merge editor

In this release, we continued working on the 3-way merge editor. This feature can be enabled by setting `git.mergeEditor` to `true` and will be enabled by default in future releases.

The merge editor allows you to quickly resolve Git merge conflicts. When enabled, the merge editor can be opened by clicking on a conflicting file in the Source Control view. Checkboxes are available to accept and combine changes in **Theirs** or **Yours**:

<video src="images/1_69/merge-editor.mp4" autoplay loop controls muted title="Merge Editor Conflict Resolution Demo"></video>

All language features are available in the merge editor (including diagnostics, breakpoints, and tests), so you get immediate feedback about any issues in the merged result.

The result can also be edited directly. Note how the checkbox updates as expected:

<video src="images/1_69/merge-editor2.mp4" autoplay loop controls muted title="Editing the result directly updates the checkbox states"></video>

When closing the merge editor or accepting the merge, a warning is shown if not all conflicts have been addressed.

The merge editor supports word-level merging - as long as the changes don't intersect, both sides can be applied. If the insertion order matters, it can be swapped. At any time, the conflict can also be resolved manually.

<video src="images/1_69/merge-editor3.mp4" autoplay loop controls muted title="Accepting both sides of a merge conflict"></video>

We would love to hear your feedback! To share a merge conflict with us, use the command **Merge Editor (Dev): Copy Contents of Inputs, Base and Result from JSON** and paste the content in your GitHub issue.

### Command Center

The Command Center has been polished and is now ready to be tried. Enable it via `window.commandCenter` setting.

The Command Center replaces the normal title bar and lets you quickly search for files in your project. Click the main section to display the Quick Open dropdown with your recent files and a search box.

![The Command Center and its hover message atop the window](images/1_69/cc-polish.png)

_Theme: [GitHub Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)_

The Command Center also has a button on the right to display the Quick Access options via '?'. On the left are **Go Back** and **Go Forward** buttons to navigate through your editor history.

### Settings editor modified indicator

The Settings editor now shows when a setting has a language override. For context, language overrides always take precedence over non-language-specific overrides, so if the value of a setting doesn't seem to affect the behavior of the setting for a certain type of file, it could be that the setting has a language override.

The indicator text was changed to be more clear and concise. The short video below shows the new **_Modified elsewhere_** indicator. Hovering over the indicator reveals a hover that shows language overrides, when applicable. The video later shows the new default override indicator with the text **_Default value changed_**.

<video src="images/1_69/settings-editor-new-indicators.mp4" autoplay loop controls muted title="Showing the new Modified elsewhere and default override indicators, along with the custom hover"></video>

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme)_

### Do Not Disturb mode

The new Do Not Disturb mode hides all non-error notification popups when enabled. Progress notifications will be automatically shown in the Status bar. Hidden notifications are still available to view in the Notification center.

![Do Not Disturb mode shown in the Status bar and Notification center](images/1_69/do-not-disturb.jpg)

You toggle Do Not Disturb mode by opening the Notification center (select the bell icon in the right of the Status bar) and clicking the slash bell icon. Do Not Disturb mode is indicated in the Status bar by the slash bell icon.

### Toggle between Light and Dark themes

A new command allows you to toggle between your preferred light and dark themes, **Preferences: Toggle between Light/Dark Themes**. When in a light theme, the command switches to a dark theme and vice versa.

When in a high contrast theme, the command remains in high contrast and switches to the preferred high contrast theme of the opposite color theme.

The preferred themes are defined by the settings:

* `workbench.preferredDarkColorTheme`
* `workbench.preferredLightColorTheme`
* `workbench.preferredHighContrastColorTheme`
* `workbench.preferredHighContrastLightColorTheme`

### Minimap context menu

The VS Code minimap displays a high level outline of your source code file on the side of the editor. You have been able to toggle the visibility of the minimap via the **View: Toggle Minimap** command or disable the minimap completely with the **Editor > Minimap: Enabled** (`editor.minimap.enabled`) setting. With this release, there is now a context menu to easily show or hide the minimap.

![Minimap context menu](images/1_69/minimap-context-menu.png)

You can also tune the appearance with the **Size** (`editor.minimap.size`), **Scale** (`editor.minimap.scale`), and **Slider** (`editor.minimap.showSlider`) settings. If the minimap is hidden, you can still display the context menu by right-clicking the side gutter.

### Share menu

The **Copy vscode.dev Link** command can be used from the **File** > **Share** submenu to quickly get a [vscode.dev](https://vscode.dev) link for your open GitHub repository. The **Share** submenu is also available from the editor context menu.

<video src="images/1_69/share-vscode-dev-link.mp4" autoplay loop controls muted title="Share menu with Copy vscode.dev Link command"></video>

## Terminal

### Shell integration

Shell integration for PowerShell, bash, and zsh that has been in preview since the January release is now out of preview! We're planning on enabling it by default in the 1.70 release.

To enable the shell integration feature, check **Terminal** > **Integrated** > **Shell Integration : Enabled** in the Settings editor or set the value in your `settings.json`:

```json
"terminal.integrated.shellIntegration.enabled": true
```

Shell integration allows VS Code's terminal to understand more of what's going on inside the shell in order to enable more features. One of the goals with shell integration was for it to work with **zero configuration required**. This was achieved by automatically "injecting" the shell integration script into the shell session via shell arguments and/or environment variables when the setting is enabled. There are some cases where this does not work such as in sub-shells or some complex shell setups, but we also have a manual install route for those more advanced cases.

Below is a summary of the features shell integration currently provides:

#### Command decorations and the overview ruler

Shell integration has the ability to get the exit codes of the commands run within the terminal. Using this information, decorations are added to the left of the line to indicate whether the command succeeded or failed. These decorations also show up in the overview ruler in the scroll bar, just like in the editor.

![Blue circles appear next to successful commands, red circles with crosses appear next to failed commands. The color of the circles appears in the scroll bar](images/1_69/terminal-si-decorations.png)

The decorations can support contextual actions like rerunning the command:

![Clicking a successful command decoration shows a context menu containing items: Copy Output, Copy Output as HTML, Rerun Command and How does this work?](images/1_69/terminal-si-decoration-menu.png)

The command decorations can be configured with the following settings:

* `terminal.integrated.shellIntegration.decorationIcon`
* `terminal.integrated.shellIntegration.decorationIconSuccess`
* `terminal.integrated.shellIntegration.decorationIconError`

#### Command navigation

The commands detected by shell integration feed into the command navigation feature (`Ctrl/Cmd+Up`, `Ctrl/Cmd+Down`) to give it more reliable command positions. This feature allows quick navigation between commands and selection of their output.

<video src="images/1_69/terminal-si-command-nav.mp4" autoplay loop controls muted title="Ctrl+Up and Ctrl+Down will navigate up and down between commands, Ctrl+Shift+Up and Ctrl+Shift+Down will do the same but also select between the 2 locations. To use this feature in screen readers it currently requires custom keybindings"></video>

#### Run recent command

The **Terminal: Run Recent Command** command surfaces history from various sources in a Quick Pick, providing similar functionality to a shell's reverse search (`Ctrl+R`). Recent commands come from the current session's history, previous session history for this shell type, and the common shell history file.

![The "run recent command" command shows a Quick Pick with commands ran previously that can be filtered similar to the Go to File command](images/1_69/terminal-si-recent-command.png)

Some other functionality of the command:

* In the current session section, there is a clipboard icon in the right of the Quick Pick that will open the command output in an editor.
* `Alt` can be held to write the text to the terminal without running it.
* The amount of history stored in the previous session section is determined by the `terminal.integrated.shellIntegration.history` setting.

There is currently no keybinding assigned by default to **Run Recent Command** but, as an example, it could be hooked up to `Ctrl+Space` with the following keybinding:

```json
{
    "key": "ctrl+space",
    "command": "workbench.action.terminal.runRecentCommand",
    "when": "terminalFocus"
},
```

#### Go to recent directory

Similar to the run recent command feature, the **Terminal: Go to Recent Directory** command keeps track of directories that have been visited and allows quick filtering and navigating (`cd`) to them:

<video src="images/1_69/terminal-si-go-to-dir.mp4" autoplay loop controls muted title="The Go to Recent Directory command shows a Quick Pick with directories previously navigated to, selecting one will run "cd path""></video>

`Alt` can be held to write the text to the terminal without running it.

#### Current working directory detection

Shell integration tells us what the current working directory is. This information was previously impossible to determine on Windows without a bunch of hacks, and required polling on macOS and Linux, which wasn't good for performance.

The current working directory is used to resolve relative links, show the directory a recent command ran within, as well as support the split current working directory `"terminal.integrated.splitCwd": "inherited"` feature.

#### Growing support for shell setups

This release we improved integration with [bash-preexec](https://github.com/rcaloras/bash-preexec), added basic support for [powerlevel10k](https://github.com/romkatv/powerlevel10k), and detected more cases where shell integration will not work and gracefully disable the feature. We expect a long tail of improvements in this area over time.

### SetMark sequence support

The terminal now supports the sequence `'OSC 1337 ; SetMark ST'` originally created by [iTerm2](https://iterm2.com/), making it possible to add a mark on the left of the line as well as in the scroll bar:

![When the sequence is written to the terminal, a small grey circle will appear to the left of the command, with a matching annotation in the scroll bar](images/1_69/terminal-setmark.png)

_Theme: [Sapphire Theme](https://marketplace.visualstudio.com/items?itemName=Tyriar.theme-sapphire)_

Here are some examples of how to trigger this in scripts:

* bash: `echo -e 'Mark this line\x1b]1337;SetMark\x07'`
* pwsh: ``Write-Host "Mark this line`e]1337;SetMark`a"``

### Custom rendering of simple Powerline glyphs

The four common angled bracket [Powerline](https://powerline.readthedocs.io) symbols are now custom rendered similar to [box drawing and block characters](https://code.visualstudio.com/updates/v1_60#_custom-rendering-of-box-drawing-and-block-element-characters) when GPU acceleration is enabled in the terminal. This comes with several benefits:

* Sub-pixel anti-aliasing will never be used that could cause unsightly red or blue outlines on either side, breaking up the "powerline".
* These characters will work without installing a patched font.
* Line height affects these characters.

Before:

![Line height would not be respected, leading to a Powerline symbol with a different height to adjacent cells. Additionally, a blue outline could appear when sub-pixel anti-aliasing was used](images/1_69/terminal-powerline-before.png)

After:

![Line height is now respected and greyscale anti-aliasing is used for improved rendering](images/1_69/terminal-powerline-after.png)

### Consistent format for messages from VS Code

Previously messages in the terminal that were directly written by VS Code, not the process, used several different formats. This is now consistent across the board with loud format and a more subtle format:

![Loud formatting starts the line with an asterisk with the inverted style, followed by a message using a blue background](images/1_69/terminal-message-loud.png)

![Subtle formatting starts the line with an asterisk with the inverted style, followed by a message using the default background](images/1_69/terminal-message-subtle.png)

### Accessibility improvements

The terminal saw several improvements to accessibility this iteration:

* More links are now available by default in the **Terminal: Open Detected Link...** command, with the ability to search the rest of the terminal buffer.
* The **Terminal: Run Recent Command** command now allows inputting the resulting command without running it by holding `Alt`.
* Find in the terminal has improved screen reader announcements.
* Up and down can be used within navigation mode (`Ctrl+Up`) without using a modifier.
* Page up and page down can now be used in navigation mode (`Ctrl+Up`) to scroll by page.

### Process reconnection and revive improvements

Terminal persistence received some UX and reliability improvements:

* Previously reloading a window `n` times would show `n` session restored messages. Now the session restored message should only show up when that particular session has had input.
* The session restored message that previously contained the date has been reduced to simply "History restored" to reduce noise.
* The "environment variable collection" feature that notifies the user about extensions wanting to change the environment (for example, to support automatic Git authentication) now works across reloads.

## Tasks

### Decorations

A few iterations ago, we added decorations to the terminal buffer and overview ruler, thanks to the shell integration feature, to improve navigation of the terminal. Decorations now also mark points of interest for tasks, which can be jumped to with the command navigation feature (`Ctrl/Cmd+Up`, `Ctrl/Cmd+Down`).

For start/stop tasks, a decoration appears next to the start of the task and is styled according to the run's exit code, if any.

![A task fails and an error decoration is added. A task succeeds and a success decoration is added](images/1_69/task-success-fail.png)

For watch tasks, a decoration appears by the first reported problem for each compilation.

![A watch task's first error is marked with a decoration](images/1_69/task-error.png)

### Icon and color support

An `icon` can now be set for a task with a [Codicon](https://code.visualstudio.com/api/references/icons-in-labels) `id` and a terminal ANSI `color` property. For example:

```json
{
    "label": "test",
    "type": "shell",
    "command": "echo test",
    "icon": { "id": "light-bulb", "color": "terminal.ansiBlue"}
}
```

![The task custom icon appears in the terminal tabs list](images/1_69/task-icon.png)

Tasks that have the `kind` property set to `test`, use the `beaker` icon by default.

## Source Control

### Commit "action button" for Git repositories

In the [1.61 release](https://code.visualstudio.com/updates/v1_61#_publish-or-sync-action-button-for-git-repositories), **Publish** and **Sync Changes** "action buttons" were added for Git repositories. This milestone, we have added a **Commit** button that has a primary action as well as a set of secondary actions. The secondary action can be controlled using the `git.postCommitCommand` setting, and lets you also do a push or sync after the commit.

With the addition of the **Commit** "action button", there is a new setting, `git.showActionButton`, that you can use to control the Git action buttons shown in the Source Control view. You can still use the general `scm.showActionButton` setting to globally disable the visibility of any action button contributed by an extension that implements a source control provider.

<video src="images/1_69/scm-commit-action-button.mp4" autoplay loop controls muted title="Making commits using the new commit action button"></video>

### Author commit message using an editor

This milestone you can now use a full text editor to author commit messages, whenever no message is provided in the commit input box. To use this new flow, click the **Commit** button without providing a commit message.

You now can author your commit message in the editor. To accept the commit message and complete the commit operation, either close the editor tab or select the **Accept Commit Message** button in the editor toolbar. To cancel the commit operation, you can either clear the contents of the text editor and close the editor tab, or select the **Discard Commit Message** button in the editor toolbar.

You can disable this new flow, and fallback to the previous experience that uses the quick input control, by toggling the `git.useEditorAsCommitInput` setting. After the setting is changed, you will have to restart VS Code for the change to take effect.

To use the same flow for `git commit` commands executed in the Integrated Terminal, enable the `git.terminalGitEditor` setting. After enabling the setting, you will have to restart your terminal session.

<video src="images/1_69/scm-git-editor.mp4" autoplay loop controls muted title="Author commit message in a full text editor"></video>

### Branch protection indicators

[Last milestone](https://code.visualstudio.com/updates/v1_68#_git-branch-protection), we added the `git.branchProtection` setting that you can use to configure specific branches to be protected. This milestone visual indicators (lock icon) were added to the Status bar and the branch picker for the branches that are protected.

Status bar:

![The branch protection icon is shown in the Status bar](images/1_69/scm-branch-protection-statusbar.png)

Branch picker:

![The branch protection icon is shown in the branch picker](images/1_69/scm-branch-protection-picker.png)

## Debugging

### Improved Step Into Target UI

Some debuggers allow stepping directly into specific function calls when paused on a line. This iteration, we improved the UI for this:

* Right-clicking on a target region on a source line and picking **Step Into Target** will automatically step into the target region, if there is one.
* There is a new command **Debug: Step Into Target** available in the Command Palette and with the `kb(workbench.action.debug.stepIntoTarget)` keyboard shortcut.

### Navigate in the Debug Console

#### Console access through the Quick Access menu

There is now a Quick Access menu to view a list of your Debug Consoles. In the menu, you can select a console name to show it in the bottom panel. Here, you can search and filter through your console names.

<video src="images/1_69/debug-filter-and-select.mp4" autoplay loop controls muted title="filtering and selecting a Debug Console from the Quick Access list"></video>

_Theme: [GitHub Dark Dimmed](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)_

There are three different ways to access this Quick Access menu:

* Typing `?` in the Quick Open menu (`kb(workbench.action.quickOpen)`), then selecting the option to view Debug Consoles.

  <video src="images/1_69/goto-debug-quickaccess.mp4" autoplay loop controls muted title="Go to Quick Access"></video>

* Using the **Debug: Select Debug Console** command.
* Manually typing out `debug consoles ` (with a trailing space character) in the Quick Open menu.

#### Console access through the View menu

You can also access a list of Debug Consoles from within the existing View menu. Type `view ` (with a trailing space) in the Quick Open menu and scroll down to the **Debug Console** panel header. You will see a list of available Debug Consoles.

<video src="images/1_69/debug-view-menu.mp4" autoplay loop controls muted title="Accessing the Debug Console via View menu"></video>

#### Toggling between consoles

You can now use `kb(workbench.action.debug.prevConsole)` to navigate to the previous Debug Console and `kb(workbench.action.debug.nextConsole)` to go to the next console.

### Loaded Scripts search and filtering

Previously, you could only view your loaded scripts within a tree view from the **Run and Debug** menu. Now, you can search and navigate to a script using the new **Debug: Open Loaded Script...** command. It works just like the **Go to File...** command, but you can navigate to a loaded script.

<video src="images/1_69/loaded-scripts.mp4" autoplay loop controls muted title="Going to loaded script from Quick Open"></video>

### JavaScript debugging

#### Sourcemap toggling

You can now easily toggle sourcemaps on and off by clicking the 🧭 compass icon in the title of the Call Stack view. When sourcemaps are toggled off, breakpoints set in the source code will still work, but be moved to their equivalent locations in compiled code, and the debugger will step through compiled code instead of source code.

<video src="images/1_69/js-debug-toggle-sourcemaps.mp4" autoplay loop controls muted title="Toggling sourcemaps off and on during a JavaScript debugging session"></video>

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)_

When sourcemaps are turned off, a similar button is shown in the debug toolbar that can turn them back on. Toggle state is persisted in the workspace.

#### toString() variable previews

If a variable or property has a custom `toString()` method, it will be called to render the preview in the **VARIABLES** view and hovers. This is an improvement over generic object previews for complex types that have good string representations.

For example, VS Code's `Range` has a `toString` method that makes it easier to digest at a glance:

![Displaying the toString() method output in the Variables view](images/1_69/js-debug-custom-tostring.png)

#### Step Into Target support

The JavaScript debugger now supports **Step Into Target**, allowing you to easily step into function calls on a line of code.

![Step Into Targets dropdown](images/1_69/js-debug-step-in-target.png)

This can be accessed via the **Debug: Step Into Target** command (`kb(workbench.action.debug.stepIntoTarget)`) or by right-clicking on the line.

#### Unbound breakpoint warning icon in JavaScript debugging

If you are debugging a TypeScript/JavaScript project and having trouble configuring sourcemaps or getting your breakpoints to bind, VS Code will now show a hint message in the Breakpoints view with a link to the JavaScript debugger's breakpoint troubleshooting tool. You will also see the hint when hovering the gray breakpoint icon in the editor.

![hovering the breakpoint warning icon](images/1_69/bp-hover.png)

When the API to show this hint is finalized, other debuggers will be able to show the hint with their own appropriate message.

## Editor

### Refactoring with Preview

Previously, there was no way to review changes that would have been applied by a refactoring. To improve the refactoring experience, there is now a **Refactor with Preview** command that can be accessed from the Command Palette. **Refactor with Preview...** works just like the **Refactor...** command, but will open up a refactor preview panel. Users can select a refactor preview to open up a larger view of the refactoring preview in the editor. This iteration primarily focused on previewing code changes from refactorings, with future features and a custom Code Action menu control to follow.

![Refactor preview panel showing a refactoring that results in two changes](images/1_69/refactor-preview.png)

**Refactor with Preview...** opening a pane in the workbench and opening the refactor preview editor view.

<video src="images/1_69/refactoring-editor.mp4" autoplay loop controls muted title="Selecting a refactor preview that opens an editor diff view"></video>

## Languages

### Support for angle bracket links in Markdown

Markdown lets you use angle brackets to write link destinations that contain spaces or other special characters:

```markdown
[Some link](<path to file with spaces.md>)
```

This iteration, we've improved support for angle bracket links so that they are correctly highlighted and clickable in the editor.

### Emmet inline completions setting

Emmet has a new setting `emmet.useInlineCompletions` that allows inline completions to occur in files. For now, it is best to enable the setting only for editing HTML and CSS files.

The short video below was recorded with the `editor.quickSuggestions` setting set to `off` for all entries.

<video src="images/1_69/emmet-inline-html.mp4" autoplay loop controls muted title="An Emmet abbreviation being expanded in an HTML file with the inline completion setting on"></video>

### JSON notifications in the language indicator

When there are too many folding ranges, document symbols, or color decorators to be shown, VS Code no longer uses notifications, but uses the JSON language indicator to inform the user.

![JSON notifications from the Status bar JSON language indicator](images/1_69/json-notifications.png)

### HTML final new line formatter setting change

The setting `html.format.endWithNewline` has been removed in favor of `files.insertFinalNewline`.

`files.insertFinalNewline` applies to all programming languages. If you want different settings per language, you can use a language scope:

```json
{
    "[html]": {
        "files.insertFinalNewline": true
    }
}
```

## Notebooks

### Support for in-notebook extension recommendations

To help improve the getting started experience for notebooks when a required extension is not installed, we have updated the extension recommendation flow and let you install extensions directly from a notebook window. This greatly reduces the number of steps needed to go from no extensions installed to runnable cells within VS Code. This iteration enabled this experience for Python Jupyter notebooks with more notebook types and languages to follow.

![A Quick Pick depicting two entries. The top entry is selected that has a light bulb to its left and says 'Install suggested extensions Python + Jupyter'](images/1_69/jupyter-extension-install.png)

## Comments

### Toggle editor commenting

The new command **Comments: Toggle Editor Commenting** toggles all editor commenting features, including the commenting range gutter decoration, the "+" on line hover, and all editor comment widgets. Commenting will be automatically disabled in Zen Mode.

### Drag for multiline comments

The Add a Comment "+" button can be dragged to add a multiline comment.

<video src="images/1_69/drag-for-comment.mp4" autoplay loop controls muted title="Drag the + button for a multiline comment"></video>

## Enterprise

### UpdateMode Group Policy on Windows

VS Code now has support for [Windows Registry-based Group Policy](https://learn.microsoft.com/previous-versions/windows/desktop/policy/implementing-registry-based-policy), which allows system administrators to create and deploy policies on machines they manage.

The first VS Code policy supported is `UpdateMode`, which overrides the `update.mode` setting and controls whether VS Code automatically updates when a new version is released.

Refer to Group Policy on Windows in the [Enterprise](https://code.visualstudio.com/docs/setup/enterprise#_group-policy-on-windows) documentation to learn more.

## VS Code for the Web

### Theme tester supports built in themes

The theme tester is a route in [vscode.dev](https://vscode.dev) that can be used to showcase and try a color theme without installing it. This has been available for Theme extensions on the Marketplace by specifying the extension ID and theme name. For example, to see the GitHub Theme extension (extension ID `github.github-vscode-theme`) "GitHub Dark Default" theme, you would launch vscode.dev with the URL:

https://vscode.dev/editor/theme/github.github-vscode-theme/GitHub%20Dark%20Default

![Color theme tester with vscode.dev showing GitHub Theme extension GitHub Dark Default](images/1_69/theme-tester-marketplace.png)

The theme tester can now also be used with the built-in themes in VS Code. Use `vscode.theme-defaults` for the extension ID placeholder and then the theme name you want to check out.

For example, to test the "High Contrast Light" theme, you would use the following URL:

https://vscode.dev/editor/theme/vscode.theme-defaults/Default%20High%20Contrast%20Light

### Partial localization support for extensions

Last iteration in [vscode.dev](https://vscode.dev), we introduced localization for strings provided by the core product. This iteration we are including more strings - specifically static strings provided by extensions in their extension manifest. There are still other segments of strings provided by extensions that need to be localized (strings declared in their source code) so we will continue with that work next iteration.

### Configure Display Language command

For some users, configuring the language used by their browser is not possible. To work around this, VS Code for the Web now has the **Configure Display Language** command that allows you to override the default language that your browser is set to.

Additionally, you can use the **Clear Display Language Preference** command remove this override.

## VS Code Server (private preview)

In VS Code, we want users to seamlessly leverage the environments that make them the most productive. The [VS Code Remote Development extensions](https://code.visualstudio.com/docs/remote/remote-overview) allow you to work in the Windows Subsystem for Linux (WSL), remote machines via SSH, and dev containers directly from VS Code. These extensions install a server on the remote environment, allowing local VS Code to smoothly interact with remote source code and runtimes.

We are now providing a private preview of a standalone **"VS Code Server,"** which is a service built off the same underlying server used by the remote extensions, plus some additional functionality, like an interactive CLI and facilitating secure connections to vscode.dev, without requiring an SSH connection.

![The VS Code Server's help menu and a remote VS Code connection](images/1_69/help-and-start.png)

Our ultimate goal is to enhance the [`code` CLI you use today](https://code.visualstudio.com/docs/configure/command-line#_launching-from-command-line) to open both desktop and web instances of VS Code, no matter where your projects are stored. While we're actively working toward this, the VS Code Server is a great milestone we're eager to release as a private preview to get your feedback.

You can learn more about how to get started with the VS Code Server in its announcement [blog post](https://aka.ms/vscode-server-blog) and [video](https://aka.ms/vscode-server-video), and review further details in its [documentation](https://aka.ms/vscode-server-doc).

## Contributions to extensions

### ESLint

Version 2.2.6 of the [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) extension has shipped. In addition to various [bug fixes](https://github.com/microsoft/vscode-eslint/issues?q=is%3Aclosed+milestone%3A2.2.6), there is now linting support in notebook cells for all supported ESLint languages.

![ESlint validating notebook cells](images/1_69/eslint.png)

### Jupyter

#### IPyWidgets

`IPyWidgets` are now supported for kernels other than `Python`, such as [Common Lisp](https://github.com/yitzchak/common-lisp-jupyter) and [IHaskell](https://github.com/IHaskell/IHaskell).

<video src="images/1_69/jupyter_ihaskell_widgets.mp4" autoplay loop controls muted title="IPyWidget support in IHaskell"></video>

A number of `IPyWidget` issues related to loading of resources (images, scripts, etc.) have been resolved, thereby improving support for widgets such as [VegaFusion](https://github.com/vegafusion/vegafusion), [ipyleaflet](https://ipyleaflet.readthedocs.io/en/latest/index.html), [Chemiscope](https://github.com/lab-cosmo/chemiscope), [mobilechelonian](https://github.com/takluyver/mobilechelonian), and [ipyturtle](https://github.com/gkvoelkl/ipython-turtle-widget).

<video src="images/1_69/jupyter_chemiscope_widget.mp4" autoplay loop controls muted title="IPyWidget support for Chemiscope"></video>

Other notable `IPyWidget` fixes include displaying complex and nested (including interactive) outputs within the [Jupyter Output widget](https://ipywidgets.readthedocs.io/en/latest/examples/Output%20Widget.html).

#### Web extension

We've made progress on supporting more of the core functionality in the web version of the [Jupyter](https://marketplace.visualstudio.com/itemdetails?itemName=ms-toolsai.jupyter) extension.

This month the following features were ported to the web extension:

* Exporting an Interactive Window
* Debugging an Interactive Window
* Collapsible cells in an Interactive Window

If you'd like to experiment with the functionality, launch Jupyter from your local machine with:

```bash
jupyter notebook --no-browser --NotebookApp.allow_origin_pat=https://.*\.vscode-cdn\.net
```

And then connect to it using the command **Jupyter: Specify Jupyter server for connections** from within [vscode.dev](https://vscode.dev).

For more information (and to comment), see this [discussion item](https://github.com/microsoft/vscode-jupyter/discussions/10315).

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Highlights of this release include:

* A new setting `"githubPullRequests.ignoredPullRequestBranches"` to ignore branches for pull requests.
* The **Publish branch?** dialog can be skipped when creating a PR using the setting `"githubPullRequests.pushBranch"`.
* An auto-merge checkbox is now available in the **Overview** editor.

Check out the [changelog for the 0.46.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0460) release of the extension to see the other highlights.

## Preview features

### TypeScript 4.8 support

This update includes support for the upcoming TypeScript 4.8 release. See the [TypeScript 4.8 iteration plan](https://github.com/microsoft/TypeScript/issues/49074) for more details on what the TypeScript team is currently working on.

To start using the TypeScript 4.8 nightly builds, install the [TypeScript Nightly](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next) extension.

### Markdown link validation

We've continued to polish [link validation in Markdown files](https://code.visualstudio.com/updates/v1_68#_markdown-link-validation). This experimental feature can help catch broken links to images or headers in Markdown files.

In addition to fixing a number of bugs and improving performance, we've also renamed some of the settings to make their functionality more clear. Here are the new setting names:

* `markdown.experimental.validate` - Enable/disable validation all link validation functionality.
* `markdown.experimental.validate.fileLinks.enabled`- Enable/disable validation of links to local files: `[link](/path/to/file.md)`
* `markdown.experimental.validate.fileLinks.markdownFragmentLinks`- Enable/disable validation of the fragment part of links to local files: `[link](/path/to/file.md#some-header)`
* `markdown.experimental.validate.fragmentLinks.enabled`- Enable/disable validation of links to headers in the current file: `[link](#some-header)`
* `markdown.experimental.validate.referenceLinks.enabled`- Enable/disable validation of reference links: `[link][ref]`.
* `markdown.experimental.validate.ignoreLinks` - A list of links that skip validation. This is useful if you link to files that don't exist on disk but do exist once the Markdown has been published.

Give it a try and let us know what you think of the new feature!

### Settings Profiles

We have been working the last couple of months to support **Settings Profiles** in VS Code, which is one of the most popular asks from the community. In this milestone, we are happy to announce that this feature is ready for preview in the [Insiders](https://code.visualstudio.com/insiders/) release via `workbench.experimental.settingsProfiles.enabled` setting. Please try out this feature and let us know your feedback by creating issues in our [VS Code Repository](https://github.com/microsoft/vscode/issues) or commenting in [issue #116740](https://github.com/microsoft/vscode/issues/116740).

A Settings Profile is comprised of settings, keyboard shortcuts, extensions, state, tasks, and snippets. You can customize VS Code for different development environments like Web, Machine Learning, or for multiple programming language classrooms like Python, Java or for personal modes like Work or Demo and save them as Settings Profiles. You can open multiple workspaces (folders) with different profiles simultaneously based on the projects you are working on or the classroom you are attending or when you are presenting.

Below demonstrates customizing VS Code for web development environment and creating a settings profile out of it.

<video src="images/1_69/settings-profile-create.mp4" autoplay loop controls muted title="Create Settings Profile"></video>

Here shows how you can easily change from a development profile to a demo profile.

<video src="images/1_69/settings-profile-switch.mp4" autoplay loop controls muted title="Switching Settings Profile"></video>

You can open your React project in web development profile and Python class project in Python class profile simultaneously as shown in the following picture.

![Multiple opened Profiles](images/1_69/settings-profile-multiple.png)

You can customize the foreground and background colors of the settings profile Status bar entry by using following color customization keys in settings.

```json
"workbench.colorCustomizations": {
    "statusBarItem.settingsProfilesBackground": "#ce4918",
    "statusBarItem.settingsProfilesForeground": "#e0dfdb",
}
```

You can manage and remove settings profiles using the **Remove Settings Profile...** action available in the **Settings Profiles** sub menu in Settings gear menu.

![Removing Settings Profiles](images/1_69/settings-profile-remove.png)

Even though you can customize VS Code per profile, there are some settings that can only be customized at the application level. For example, all application-scoped settings like `update.mode`, language pack extensions, settings sync enablement, and Workspace Trust state are customized across all profiles at the application level.

### Access edit sessions across VS Code for the Web and desktop

This milestone we are announcing a preview of **edit sessions** in VS Code. Edit sessions enable you to bring your uncommitted changes along when you continue working on a repository wherever VS Code is available, whether that is VS Code for the Web, desktop VS Code, or across different machines.

For example, you can use edit sessions to transfer and access working changes:

* From a repository in [github.dev](https://github.dev) to a local desktop clone of that repository.
* From a clone of a repository on a Windows device to a clone of that repository on a macOS device.

Edit sessions are currently in preview behind the `workbench.experimental.editSessions.enabled` setting. In this initial release, you can use the following commands to store and resume your working changes:

* **Edit Sessions: Store Current Edit Session**
* **Edit Sessions: Resume Latest Edit Session**

To get started with edit sessions, use the **Edit Sessions: Store Current Edit Session** command and sign in with GitHub or Microsoft authentication when prompted. Then use the **Edit Sessions: Resume Latest Edit Session** command in a repository on another machine or VS Code instance to restore the working contents of all modified files to your workspace. You can sign out of edit sessions at any time using the **Edit Sessions: Sign Out** command.

As we continue to iterate on the edit sessions experience, try it out and share your feedback with us via a comment in [issue #141293](https://github.com/microsoft/vscode/issues/141293).

## Extension authoring

### Iterable vscode.d.ts collection types

To make it easier to work with the collection types in `vscode.d.ts`, this iteration we've made them all iterable. This allows you to use `for...of` loops and spreads with these types:

```ts
const data: vscode.DataTransfer = ...;
for (const [mime, item] of data) {
    const str = await item.asString();
    ...
}
```

The full set of newly iterable types is:

* `DiagnosticCollection`
* `DataTransfer`
* `EnvironmentVariableCollection`
* `TestItemCollection`

### Extensible notebook renderers

[Notebook renderers](https://code.visualstudio.com/api/extension-guides/notebook#notebook-renderer) allow extensions to customize how cells and outputs are rendered in notebooks. This iteration, we've extended the notebook renderer API to allow a renderer to augment another renderer, including some of built-in renderers from VS Code. This allows extensions to add new functionality to notebook rendering without having to reimplement a full renderer. It also provides a better user experience since users don't have to switch between a new renderer to see the correctly rendered content.

The [Notebook Extend Markdown Renderer Sample](https://github.com/microsoft/vscode-extension-samples/tree/main/notebook-extend-markdown-renderer-sample) shows how an extension can extend VS Code's built-in Markdown renderer with emoji support. Let's take a quick look at the key parts!

The API for extensible notebook renderers is intentionally loosely defined. To get started, our extension needs to contribute a new notebook renderer. Because we are extending an existing renderer, we use the `entrypoint` field here to specify both the ID of the renderer to extend (in this case, VS Code's built-in `vscode.markdown-it-renderer`) along with a path to the renderer script to load:

```json
"notebookRenderer": [
    {
        "id": "sample.markdown-it.emoji-extension",
        "displayName": "Markdown it Emoji renderer",
        "entrypoint": {
            "extends": "vscode.markdown-it-renderer",
            "path": "./out/emoji.js"
        }
    }
]
```

This renderer will now be activated whenever the `vscode.markdown-it-renderer` is activated.

Now to extend the `vscode.markdown-it-renderer`, our renderer can call a method on the base renderer. This method is specific to the base renderer. In this case, we first get the base renderer using `getRenderer` and then call `extendMarkdownIt` on it to augmented the instance of Markdown-It it uses for rendering:

```ts
import type * as MarkdownIt from 'markdown-it';
import type { RendererContext } from 'vscode-notebook-renderer';

interface MarkdownItRenderer {
    extendMarkdownIt(fn: (md: MarkdownIt) => void): void;
}

export async function activate(ctx: RendererContext<void>) {
    // Acquire the base renderer
    const markdownItRenderer = await ctx.getRenderer('vscode.markdown-it-renderer') as MarkdownItRenderer | undefined;
    if (!markdownItRenderer) {
        throw new Error(`Could not load 'vscode.markdown-it-renderer'`);
    }

    // Load our Markdown-It extension
    const emoji = require('markdown-it-emoji');

    // Call the base renderer's extension method (extendMarkdownIt) to augment
    // the Markdown-It with emoji support.
    markdownItRenderer.extendMarkdownIt((md: MarkdownIt) => {
        return md.use(emoji, {});
    });
}
```

The key part to note here is that `extendMarkdownIt` is simply a method returned by the base renderer. Other extensible renderers can easily return their own extension points.

Check out the [Notebook Extend Markdown Renderer Sample](https://github.com/microsoft/vscode-extension-samples/tree/main/notebook-extend-markdown-renderer-sample) for a complete example of extending VS Code's built-in Markdown-It renderer.

### Read external files from DataTransfers

Extensions can now read external files using a `DataTransfer`. This can be used in the tree drag and drop API, as well as in the drop into editor API proposal:

```ts
export class TestViewDragAndDrop implements vscode.TreeDataProvider<Node>, vscode.TreeDragAndDropController<Node> {

    public async handleDrop(target: Node | undefined, sources: vscode.DataTransfer, token: vscode.CancellationToken): Promise<void> {
        // Get a list of all files
        const files: vscode.DataTransferFile[] = [];
        sources.forEach((item) => {
            const file = item.asFile();
            if (file) {
                files.push(file);
            }
        });

        const decoder = new TextDecoder();

        // Print out the names and first 100 characters of the file
        for (const file of files) {
            const data = await file.data();
            const text = decoder.decode(data);
            const fileContentsPreview = text.slice(0, 100);
            console.log(file.name + ' — ' + fileContentsPreview + '\n');
        }

        ...
    }
}
```

### High contrast light in webviews

Webviews now can target [high contrast light color themes](https://code.visualstudio.com/updates/v1_66#_high-contrast-light-theme-for-color-contributions) using the `vscode-high-contrast-light` CSS class:

```css
.vscode-high-contrast-light .error {
    color: red;
}
```

The `vscode-high-contrast-light` class is automatically added to the `body` element of the webview.

### Icons in Test Item Labels

[Codicons](https://code.visualstudio.com/api/references/icons-in-labels) in the standard `$(icon)` format can now be used in the `TestItem.label` to show icons in the Test Explorer view as well as test results.

### Source Control input box enablement

Extension authors can now toggle the enablement of the input box in the Source Control view using the `enabled` property.

### JSON word pattern change

Every language comes with a word pattern that defines which characters belong to a word when in that language. JSON was different to all other languages in that it included the quotes of string literals and the full string literal content. It now follows other languages and contains just letters, numbers, and hyphens.

The change will fix issues when expanding the selection (Command: **Expand Selection**), allow word completions inside strings, and fix some code suggestion abnormalities.

Extensions that relied on the word pattern, for example for the default replace range in code completions, need to update their implementation to explicitly state the replace range for all completions.

## Debug Adapter Protocol

* To provide a better user experience for the **Step Into Target** feature, additional information was added to DAP's `StepInTarget` type. With the four properties `line`, `column`, `endLine`, and `endColumn`, a step-into target can now be linked to the corresponding source range.
* Passing program arguments to a debuggee is hampered by two conflicting goals: some users want to be able to pass **all characters** to the debuggee and other users want to use **some characters** to talk to an underlying shell. To better support both use cases, we are working on DAP's `runInTerminal` request to control the escaping of special characters in program arguments. A proposal is already [available](https://github.com/microsoft/debug-adapter-protocol/issues/146). If you are a debug extension author interested in this DAP addition, we'd appreciate your feedback.
* In addition, we have addressed several documentation issues of the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol). These include clarifications around the terms "clients" and "falsey", more precise specification for checksums and date strings, and improved wording for `Source.origin`.

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [vscode-dts](https://www.npmjs.com/package/vscode-dts) and run `vscode-dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Paste Edit Provider

We've updated the `documentPaste` API proposal to make it easier to correctly implement, specifically when working with multiple cursors.

As a reminder, this API lets extensions hook into copy and paste inside text editors. Extensions can use it to modify the text that is inserted on paste. The [document paste extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/document-paste) shows the updated API in action:

```ts
/**
 * Provider that maintains a count of the number of times it has copied text.
 */
class CopyCountPasteEditProvider implements vscode.DocumentPasteEditProvider {

    private readonly countMimeTypes = 'application/vnd.code.copydemo-copy-count';

    private count = 0;

    prepareDocumentPaste?(
        _document: vscode.TextDocument,
        _ranges: readonly vscode.Range[],
        dataTransfer: vscode.DataTransfer,
        _token: vscode.CancellationToken
    ): void | Thenable<void> {
        dataTransfer.set(this.countMimeTypes, new vscode.DataTransferItem(this.count++));
        dataTransfer.set('text/plain', new vscode.DataTransferItem(this.count++));
    }

    async provideDocumentPasteEdits(
        _document: vscode.TextDocument,
        _ranges: readonly vscode.Range[],
        dataTransfer: vscode.DataTransfer,
        _token: vscode.CancellationToken
    ): Promise<vscode.DocumentPasteEdit | undefined> {
        const countDataTransferItem = dataTransfer.get(this.countMimeTypes);
        if (!countDataTransferItem) {
            return undefined;
        }

        const textDataTransferItem = dataTransfer.get('text') ?? dataTransfer.get('text/plain');
        if (!textDataTransferItem) {
            return undefined;
        }

        const count = await countDataTransferItem.asString();
        const text = await textDataTransferItem.asString();

        // Build a snippet to insert
        const snippet = new vscode.SnippetString();
        snippet.appendText(`(copy #${count}) ${text}`);

        return { insertText: snippet };
    }
}

export function activate(context: vscode.ExtensionContext) {
    // Enable our provider in plaintext files
    const selector: vscode.DocumentSelector = { language: 'plaintext' };

    // Register our provider
    context.subscriptions.push(vscode.languages.registerDocumentPasteEditProvider(selector, new CopyCountPasteEditProvider(), {
        pasteMimeTypes: ['text/plain'],
    }));
}
```

### WebUSB, WebSerial, and WebHID access on web

There is now experimental support for using [WebUSB](https://developer.mozilla.org/docs/Web/API/WebUSB_API), [Web Serial](https://developer.mozilla.org/docs/Web/API/Web_Serial_API), and [WebHID](https://developer.mozilla.org/docs/Web/API/WebHID_API) from [web extensions](https://code.visualstudio.com/api/extension-guides/web-extensions).

To access these APIs, your extension first needs to request access to the desired devices by running one of these commands:

* `workbench.experimental.requestUsbDevice`
* `workbench.experimental.requestSerialPort`
* `workbench.experimental.requestHidDevice`

These commands will prompt the user to select a device. Each command takes a [list of device filters](https://developer.mozilla.org/docs/Web/API/USB/requestDevice) as its argument. For example:

```ts
import * as vscode from 'vscode';

await vscode.commands.executeCommand('workbench.experimental.requestUsbDevice', {
    filters: [
        { vendorId: 0x404 } // Your custom filter or empty if you want users to select any device
    ]
});
```

After the command finishes, your extension can use the normal web APIs to access any devices that the user has granted access to:

```ts
const devices = await navigator.usb.getDevices();
// Do something with devices
```

All of the normal web device APIs should work, with the expectation of those such as [requestDevice](https://developer.mozilla.org/docs/Web/API/USB/requestDevice) that need to show UI elements to the user.

At the moment, this functionality only works on web and support is limited to the Chrome and Edge browsers. Also keep in mind that this feature is still experimental, so we may iterate on its design. Give it a try and share your feedback!

## Engineering

### Electron 18 update

In this milestone, we finished the exploration to bundle Electron 18 into VS Code desktop and we want to thank everyone involved with self-hosting on Insiders. This update comes with Chromium `100.0.4896.160` and Node.js `16.13.2`.

## Documentation

### TypeScript editing and refactoring

There are two new topics focusing on TypeScript [Editing](https://code.visualstudio.com/docs/typescript/typescript-editing) and [Refactoring](https://code.visualstudio.com/docs/typescript/typescript-refactoring) in VS Code. There you'll learn about specific features such as [Inlay hints](https://code.visualstudio.com/docs/typescript/typescript-editing#_inlay-hints), [CodeLens](https://code.visualstudio.com/docs/typescript/typescript-editing#_references-codelens), and [formatting](https://code.visualstudio.com/docs/typescript/typescript-editing#_formatting) options specific to TypeScript as well as the available code [refactorings](https://code.visualstudio.com/docs/typescript/typescript-refactoring#_refactoring).

## Notable fixes

* [109565](https://github.com/microsoft/vscode/issues/109565) Terminal always show uppercase letters when using caps lock to switch input source
* [146491](https://github.com/microsoft/vscode/issues/146491) Color Difference between active and inactive menu lost
* [149538](https://github.com/microsoft/vscode/issues/149538) Unable to log in to Microsoft or GitHub Account in China
* [149890](https://github.com/microsoft/vscode/issues/149890) Can't drag an item and drop it to an empty treeview
* [150934](https://github.com/microsoft/vscode/issues/150934) RPM should not list bundled libraries as "provides"
* [151158](https://github.com/microsoft/vscode/issues/151158) Retain selected kernel in untitled notebook when saving notebook
* [151664](https://github.com/microsoft/vscode/issues/152642) Improve the efficiency of announcements when stepping through code with screen readers
* [151739](https://github.com/microsoft/vscode/issues/151739) workbench.action.submitComment not functional when first replying to a comment thread & confusing when editing comments
* [152067](https://github.com/microsoft/vscode/issues/152067) Only show comment thread range when the comment is expanded
* [152141](https://github.com/microsoft/vscode/issues/152141) Drop task status when the process exits
* [152642](https://github.com/microsoft/vscode/issues/152642) Added a button to remove watch expressions with a single click

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Web extensions

Extension authors for enabling extensions that run code as [web extensions](https://code.visualstudio.com/api/extension-guides/web-extensions) (the list below is between June 7 and July 4, 2022):

* [Pipeline Editor](https://marketplace.visualstudio.com/items?itemName=Cloud-pipelines.pipeline-editor-vscode) ([Alexey Volkov](https://marketplace.visualstudio.com/publishers/Cloud-pipelines))
* [Django Support](https://marketplace.visualstudio.com/items?itemName=almahdi.code-django) ([Al Mahdi](https://marketplace.visualstudio.com/publishers/almahdi))
* [ROOT File Viewer](https://marketplace.visualstudio.com/items?itemName=albertopdrf.root-file-viewer) ([Alberto Pérez de Rada Fiol](https://marketplace.visualstudio.com/publishers/albertopdrf))
* [Thirft Formatter](https://marketplace.visualstudio.com/items?itemName=alingse.thirft-formatter) ([alingse](https://marketplace.visualstudio.com/publishers/alingse))
* [Smarty Template Support](https://marketplace.visualstudio.com/items?itemName=aswinkumar863.smarty-template-support) ([Aswin Kumar](https://marketplace.visualstudio.com/publishers/aswinkumar863))
* [Sort Editors](https://marketplace.visualstudio.com/items?itemName=aswinkumar863.sort-editors) ([Aswin Kumar](https://marketplace.visualstudio.com/publishers/aswinkumar863))
* [Compare View](https://marketplace.visualstudio.com/items?itemName=chekweitan.compare-view) ([chekweitan](https://marketplace.visualstudio.com/publishers/chekweitan))
* [CloudStudio.github-authentication](https://marketplace.visualstudio.com/items?itemName=CloudStudio.github-authentication) ([CloudStudio](https://marketplace.visualstudio.com/publishers/CloudStudio))
* [Text Power Tools](https://marketplace.visualstudio.com/items?itemName=qcz.text-power-tools) ([Dániel Tar](https://marketplace.visualstudio.com/publishers/qcz))
* [filtered-interview-tool](https://marketplace.visualstudio.com/items?itemName=filtered-vscode.filtered-interview-tool) ([filtered-vscode](https://marketplace.visualstudio.com/publishers/filtered-vscode))
* [HashiCorp HCL](https://marketplace.visualstudio.com/items?itemName=HashiCorp.HCL) ([HashiCorp](https://marketplace.visualstudio.com/publishers/hashicorp))
* [fcs-vscode-web](https://marketplace.visualstudio.com/items?itemName=kuboja.fcs-vscode-web) ([HiStruct Team](https://marketplace.visualstudio.com/publishers/kuboja))
* [Marked](https://marketplace.visualstudio.com/items?itemName=hyrious.vscode-marked) ([hyrious](https://marketplace.visualstudio.com/publishers/hyrious))
* [Our Code](https://marketplace.visualstudio.com/items?itemName=IvanDwane.ourcode) ([Ivan Dwane](https://marketplace.visualstudio.com/publishers/IvanDwane))
* [前端开发集合](https://marketplace.visualstudio.com/items?itemName=jihe.code-sets) ([jihe](https://marketplace.visualstudio.com/publishers/jihe))
* [surfaces.txt](https://marketplace.visualstudio.com/items?itemName=kanadelab.surfaces) ([kanadelab](https://marketplace.visualstudio.com/publishers/kanadelab))
* [lcode webdav editor](https://marketplace.visualstudio.com/items?itemName=lcode.lwebdav) ([lcode](https://marketplace.visualstudio.com/publishers/lcode))
* [Lightrun](https://marketplace.visualstudio.com/items?itemName=Lightrun.lightrunplugin-saas) ([Lightrun](https://marketplace.visualstudio.com/publishers/lightrun))
* [f-string Converter (Python 3.6+)](https://marketplace.visualstudio.com/items?itemName=mdebi.fstring-converter) ([mdebi](https://marketplace.visualstudio.com/publishers/mdebi))
* [MutableAI (beta)](https://marketplace.visualstudio.com/items?itemName=mutable-ai.mutable-ai) ([mutable-ai](https://marketplace.visualstudio.com/publishers/mutable-ai))
* [NaiveSystems Analyze (Extension)](https://marketplace.visualstudio.com/items?itemName=naivesystems.analyze) ([Naive Systems](https://marketplace.visualstudio.com/publishers/naivesystems))
* [File Utils](https://marketplace.visualstudio.com/items?itemName=pomdtr.fileutils) ([pomdtr](https://marketplace.visualstudio.com/publishers/pomdtr))
* [Secrets](https://marketplace.visualstudio.com/items?itemName=pomdtr.secrets) ([pomdtr](https://marketplace.visualstudio.com/publishers/pomdtr))
* [dotbook](https://marketplace.visualstudio.com/items?itemName=ShivaPrasanth.dotbook) ([Prasanth](https://marketplace.visualstudio.com/publishers/shivaprasanth))
* [Common Lisp](https://marketplace.visualstudio.com/items?itemName=qingpeng.common-lisp) ([Qingpeng Li](https://marketplace.visualstudio.com/publishers/qingpeng))
* [react replace/refactor component](https://marketplace.visualstudio.com/items?itemName=rumemeta-maila.react-replace-refactor-component) ([rumemeta-maila](https://marketplace.visualstudio.com/publishers/rumemeta-maila))
* [fiber-ifttt-tool](https://marketplace.visualstudio.com/items?itemName=t-codespaces.fiber-ifttt-tool) ([t-codespaces](https://marketplace.visualstudio.com/publishers/t-codespaces))
* [ide-data-sync](https://marketplace.visualstudio.com/items?itemName=t-codespaces.ide-data-sync) ([t-codespaces](https://marketplace.visualstudio.com/publishers/t-codespaces))
* [Even Better TOML](https://marketplace.visualstudio.com/items?itemName=tamasfe.even-better-toml) ([tamasfe](https://marketplace.visualstudio.com/publishers/tamasfe))
* [web-ext](https://marketplace.visualstudio.com/items?itemName=yogesh.web-ext) ([yogesh](https://marketplace.visualstudio.com/publishers/yogesh))
* [Vetur Extended](https://marketplace.visualstudio.com/items?itemName=zardoy.vetur-extended) ([zardoy](https://marketplace.visualstudio.com/publishers/zardoy))

### Issue tracking

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart)
  * Add more sanitization around the attributes in MD generated HTML [PR #149100](https://github.com/microsoft/vscode/pull/149100)
  * Prefer the markdown description in the preferences page [PR #152860](https://github.com/microsoft/vscode/pull/152860)
  * Remove duplicate declarations in terminal [PR #153280](https://github.com/microsoft/vscode/pull/153280)
* [@alanrenmsft (Alan Ren)](https://github.com/alanrenmsft): add aria-description support for selectbox [PR #152251](https://github.com/microsoft/vscode/pull/152251)
* [@andrewbranch (Andrew Branch)](https://github.com/andrewbranch)
  * [typescript-language-features] Pass through all `typescript.unstable.*` settings [PR #151472](https://github.com/microsoft/vscode/pull/151472)
  * [typescript-language-features] Add setting for autoImportFileExcludePatterns [PR #153160](https://github.com/microsoft/vscode/pull/153160)
* [@babakks (Babak K. Shandiz)](https://github.com/babakks)
  * Polish PR template picker [PR #151415](https://github.com/microsoft/vscode/pull/151415)
  * [markdown] Ignore brackets inside link text [PR #151610](https://github.com/microsoft/vscode/pull/151610)
  * Add option to always reveal tests after state change [PR #152331](https://github.com/microsoft/vscode/pull/152331)
  * 🎁 Add "Remove expression" as an action bar command to debug watch expressions [PR #152861](https://github.com/microsoft/vscode/pull/152861)
  * 🔨 Indicate "No snippet available" on the surround with snippet picker [PR #152892](https://github.com/microsoft/vscode/pull/152892)
* [@Balastrong (Leonardo Montini)](https://github.com/Balastrong): Add command to toggle between light/dark color themes [PR #151554](https://github.com/microsoft/vscode/pull/151554)
* [@CGNonofr (Loïc Mangeonjean)](https://github.com/CGNonofr): Add removeAllMarkers method in monaco api [PR #151237](https://github.com/microsoft/vscode/pull/151237)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): `Keep Editors Open` -> `Disable Preview Mode` (fixes #152240) [PR #152322](https://github.com/microsoft/vscode/pull/152322)
* [@guiherzog (Guilherme Herzog)](https://github.com/guiherzog): Improve performance of CompressibleAsyncDataTree [PR #152961](https://github.com/microsoft/vscode/pull/152961)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes terminal not restoring properly [PR #149621](https://github.com/microsoft/vscode/pull/149621)
  * Fix terminal quick pick index [PR #152699](https://github.com/microsoft/vscode/pull/152699)
  * Fixes detaching a terminal editor doesn't fire vscode.window.onDidCloseTerminal event [PR #152791](https://github.com/microsoft/vscode/pull/152791)
* [@JohnnyCrazy (Jonas Dellinger)](https://github.com/JohnnyCrazy): A full editor can be used as git commit message editor [PR #95266](https://github.com/microsoft/vscode/pull/95266)
* [@jramsay (jram)](https://github.com/jramsay): [npm extension] exclude top level npm_modules when detecting npm scripts in workspaces [PR #150205](https://github.com/microsoft/vscode/pull/150205)
* [@laurentlb (Laurent Le Brun)](https://github.com/laurentlb): Comments: Wrap non-Markdown content [PR #152987](https://github.com/microsoft/vscode/pull/152987)
* [@leopoldsedev (Christian Leopoldseder)](https://github.com/leopoldsedev)
  * Fix #151515 [PR #151590](https://github.com/microsoft/vscode/pull/151590)
  * Fix #148150 [PR #151594](https://github.com/microsoft/vscode/pull/151594)
  * Implement action to clear keyboard shortcuts search history [PR #151607](https://github.com/microsoft/vscode/pull/151607)
* [@MachineMitch21 (Mitch Schutt)](https://github.com/MachineMitch21): Focus editor for tab after dragged over for 1500 millis [PR #149604](https://github.com/microsoft/vscode/pull/149604)
* [@mbelsky (Max Belsky)](https://github.com/mbelsky): Support `Transform to Kebab Case` [PR #147956](https://github.com/microsoft/vscode/pull/147956)
* [@Mingpan](https://github.com/Mingpan)
  * Arrows to revert changes in side by side diff editor [PR #151826](https://github.com/microsoft/vscode/pull/151826)
  * Add config for disable arrows in diff [PR #153199](https://github.com/microsoft/vscode/pull/153199)
* [@MonadChains (MonadChains)](https://github.com/MonadChains): Fix missing horizontal bracket guides when editor.wrappingIndent == "same" [PR #152529](https://github.com/microsoft/vscode/pull/152529)
* [@prashantvc (Prashant Cholachagudda)](https://github.com/prashantvc): Added the `VSCode-SessionId` for extension queries Issue2961 [PR #153271](https://github.com/microsoft/vscode/pull/153271)
* [@Priyankcoder (Priyank Rastogi)](https://github.com/Priyankcoder): Add latex to markdown embedded languages [PR #151732](https://github.com/microsoft/vscode/pull/151732)
* [@qingpeng9802 (Qingpeng Li)](https://github.com/qingpeng9802): Fix symbolkind icon [PR #152216](https://github.com/microsoft/vscode/pull/152216)
* [@thegecko (Rob Moran)](https://github.com/thegecko): Add web command to request device access [PR #152310](https://github.com/microsoft/vscode/pull/152310)
* [@TheV360 (V360)](https://github.com/TheV360): Fix inlay hints being offset by 1px in some cases [PR #153424](https://github.com/microsoft/vscode/pull/153424)
* [@tjx666 (余腾靖)](https://github.com/tjx666): fix jsx text foreground in tomorrow-night theme [PR #151478](https://github.com/microsoft/vscode/pull/151478)
* [@ulugbekna (Ulugbek Abdullaev)](https://github.com/ulugbekna)
  * add progress reporting for fetching repos [PR #153082](https://github.com/microsoft/vscode/pull/153082)
  * add auto-hide option for minimap (default = false) which [PR #153093](https://github.com/microsoft/vscode/pull/153093)
* [@yiliang114 (jinjing.zzj)](https://github.com/yiliang114): fix: typos [PR #153229](https://github.com/microsoft/vscode/pull/153229)
* [@yutotnh (yutotnh)](https://github.com/yutotnh): fix: typo [PR #153187](https://github.com/microsoft/vscode/pull/153187)

Contributions to `vscode-generator-code`:

* [@hawkticehurst (Hawk Ticehurst)](https://github.com/hawkticehurst): Fix incorrect keyboard shortcut listed in template READMEs [PR #354](https://github.com/microsoft/vscode-generator-code/pull/354)

Contributions to `vscode-js-debug`:

* [@jakebailey (Jake Bailey)](https://github.com/jakebailey): Stop REPL stack traces at the REPL eval frame [PR #1263](https://github.com/microsoft/vscode-js-debug/pull/1263)

Contributions to `vscode-pull-request-github`:

* [@blindpirate (Bo Zhang)](https://github.com/blindpirate): Show pull request's close button for author [PR #3507](https://github.com/microsoft/vscode-pull-request-github/pull/3507)
* [@leopoldsedev (Christian Leopoldseder)](https://github.com/leopoldsedev): Implement quick self assign link as available on .com (#3382) [PR #3601](https://github.com/microsoft/vscode-pull-request-github/pull/3601)

Contributions to `debug-adapter-protocol`:

* [@rillig (Roland Illig)](https://github.com/rillig)
  * Remove extra line breaks [PR #277](https://github.com/microsoft/debug-adapter-protocol/pull/277)
  * Fix spelling [PR #292](https://github.com/microsoft/debug-adapter-protocol/pull/292)
  * Reword ambiguous 'not A or B' [PR #293](https://github.com/microsoft/debug-adapter-protocol/pull/293)

Contributions to `devcontainers/cli`:

* [@juzuluag (Juan Zuluaga)](https://github.com/juzuluag): Add multi build support [PR #24](https://github.com/devcontainers/cli/pull/24)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
