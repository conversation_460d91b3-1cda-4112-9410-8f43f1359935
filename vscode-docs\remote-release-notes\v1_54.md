# February 2021 (version 1.54)

## Visual Studio Code Remote Core

### Ports view table layout

The **Ports** view now displays your forwarded ports in a table instead of list or tree. The new layout provides all the same functionality as the previous layout. As part of the move to a table layout, the **Ports** view has moved to the **Panel**, where there is enough room for a table to fit well.

![Ports view table layout](images/1_54/ports-view-table.gif)

### Preview in editor

Your running remote web applications can be previewed in an simple browser editor.

![Preview port in editor](images/1_54/preview-port-in-editor.gif)

### Friendly name for extension host process

Extension sometimes listen on a port from within the VS Code remote extension host process. This process command line is long, so instead of showing the full extension host process command line, we now show a more friendly name.

![Code Extension Host friendly name](images/1_54/ports-extension-host-process.png)

### Choose a source for automatic port forwarding

By default, VS Code will find ports to automatically forward for you by looking at the currently running processes on the remote system. In some cases, this is not desirable. To help with those cases, there is now the setting `remote.autoForwardPortsSource`. To look for ports to forward based on terminal and debug output (instead of running processes) set `"remote.autoForwardPortsSource": "output"`. Note that ports auto forwarded from an `output` source will not be closed when the process stops running.

## Containers (version 0.163.x)

### Security update

Version 0.163.2 of the Dev Containers extension includes a fix for a security issue in how the Docker executable's path can be configured. Details are in [CVE-2021-27083](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-27083).

### portsAttributes setting in devcontainer.json

The setting `remote.portsAttributes` now has a home as a top level property in the `devcontainer.json` schema. Like other `devcontainer.json` properties, you must rebuild the container for the new properties to take effect when you change them.

![devcontainer.json with portsAttributes](images/1_54/ports-attributes-devcontainer-json.png)

### Set port label in devcontainer.json

When you set a port label, you now have the option to update the `devcontainer.json` file with your label, so that other users of that dev container can benefit from the label.

![Set port label and update devcontainer.json](images/1_54/set-port-label-devcontainer.gif)

### Running "postCreateCommand" in the background

The `"postCreateCommand"` (and `"postStartCommand"` and `"postAttachCommand"`) now run in the background while the workbench window is already fully functional. The output from the command is shown in a terminal that opens automatically and accepts keyboard input including `Ctrl+C` to cancel it.

![Running `"postCreateCommand"` in the background](images/1_54/postcreate-in-background.gif)

### Simplified recommendations for development container configurations

The command **Add Dev Container Configuration Files...** is now only recommending some core configurations on the first page. To see all configurations you can select **Show All Definitions**.

Whether a container configuration is a core configuration is defined by meta data that has been added to the development container configs in the [container configurations repository](https://github.com/microsoft/vscode-dev-containers).

## WSL

### Use polling for Windows file system mount in WSL 2

In Windows Subsystem for Linux (WSL), you can access the Windows file system through a mount: `/mnt/...`. However, in WSL 2 distros, the mount has [known issues with file events](https://github.com/microsoft/WSL/issues/).

To work around these issues, we now use polling for all folders opened on a Windows file system. This feature is controlled by the setting `remote.WSL.fileWatcher.polling`, which is true by default.

### Recommend using WSL 2 Linux file system

Working with a WSL 2 distro on the Windows file system is far less performant than on the Linux file system (for example, `/home/<USER>

There is now a notification when the user opens a folder on a Windows file system mount, which recommends using the Linux file system instead.

### Fix for WSL 2 connection issues when behind a proxy

When connecting to the VS Code server running in WSL 2, we directly use the WSL 2 VM's IP.

Due to a change in 1.53, this no longer worked for users that have proxy settings defined. To fix this, we now try to detect that the IP cannot be reached and use `localhost` instead. If the detection doesn't work or takes too long, you can force the use of `localhost` with the setting `remote.WSL.server.connectThroughLocalhost`.

## SSH

### X11 forwarding

X11 forwarding is now supported with Remote - SSH. If `ForwardX11 yes` is already set in your [SSH config file](https://man7.org/linux/man-pages/man5/ssh_config.5.html), and X11 forwarding is enabled on the SSH server, then it will work for programs launched from terminals, tasks, and debuggers. X11 forwarding is a feature that allows forwarding [X Window System](http://www.opengroup.org/tech/desktop/x-window-system/) application windows from your remote to an X Window Server running locally.

### Port forwarding source is "output"

To prevent forwarding of unwanted ports when connected to an SSH host, the default value of `remote.autoForwardPortsSource` is `"output"`. See [port forwarding source](#choose-a-source-for-automatic-port-forwarding) for more details on this setting.
