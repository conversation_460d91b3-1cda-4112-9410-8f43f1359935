---
Order:
TOCTitle: October 2021
PageTitle: Visual Studio Code October 2021
MetaDescription: Learn what is new in the Visual Studio Code October 2021 Release (1.62)
MetaSocialImage: 1_62/release-highlights.png
Date: 2021-11-4
DownloadVersion: 1.62.3
---
# October 2021 (version 1.62)

**Update 1.62.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2021+Recovery%22+is%3Aclosed).

**Update 1.62.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2021+Recovery+2%22+is%3Aclosed).

**Update 1.62.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2021+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the October 2021 release of Visual Studio Code. In addition to releasing a preview of [vscode.dev](https://vscode.dev/), we announced in the [October iteration plan](https://github.com/microsoft/vscode/issues/134730) that we would focus on housekeeping GitHub issues and pull requests (see our [issue cleanup guide](https://github.com/microsoft/vscode/wiki/Issue-Grooming)). Across all of our VS Code repositories, we closed (either triaged or fixed) **4163** issues. While we closed issues, you created **2222** new issues. The main [vscode repository](https://github.com/microsoft/vscode/issues) now has **2491** open feature requests and **1246** open bugs. In addition, we closed **194** pull requests.

As in previous years, we used the [live tracker](https://vscode-issue-tracker.netlify.com/) from [Benjamin Lannon](https://github.com/lannonbr) to track our progress:

![Burn down chart of VS Code issues](images/1_62/october-issue-cleanup.png)

Given the focus on shipping `vscode.dev`, not everybody on the team had cycles for clean-up, so some issue clean-up will continue in November. After housekeeping, we also addressed feature requests and community [pull requests](#pull-requests).

**Watch a highlight** of the new features in this version in the VS Code team's release party. You can find the [recording of the event](https://www.youtube.com/watch?v=IKvtYHfzE3g) on our YouTube channel.

## Visual Studio Code for the Web - vscode.dev (Preview)

[![open vscode.dev](images/1_62/vscode-dev-badge.svg)](https://vscode.dev) [![open vscode.dev](images/1_62/insiders-vscode-dev-badge.svg)](https://insiders.vscode.dev)

This iteration, we released a preview of Visual Studio Code for the Web. Visual Studio Code for the Web provides a zero-install experience running entirely in your browser, allowing you to quickly and safely browse source code repositories and make lightweight code changes. To get started, go to [https://vscode.dev](https://vscode.dev) in your browser.

VS Code for the Web has many of the features of VS Code desktop that you love, including search and syntax highlighting, along with extension support to work on your codebase. In addition to opening repositories, forks, and pull requests from source control providers like GitHub and Azure Repos, you can also work with code that is stored on your local machine.

Not all extensions can run when VS Code is in the browser. Extensions that are purely declarative, such as themes, snippets, or grammars, can run unmodified in the browser. However, extensions that need to run code must be updated by the extension authors. We'd like to say [thank you](#web-extensions) to the extension authors that already have published their extensions as [web extensions](https://code.visualstudio.com/api/extension-guides/web-extensions).

You can learn more about [https://vscode.dev](https://vscode.dev) in our [blog post](https://aka.ms/vscode.dev-blog), [documentation](https://aka.ms/vscode-web-docs), and [live stream](https://youtu.be/sy3TUb_iVJM).

## Workbench

### Settings editor accessibility

We made various Settings editor accessibility improvements:

* The Settings editor scrolls back to the top after performing a search, so the user does not end up midway through the search results after each search.
* The settings scope switcher is accessible via keyboard.
* Deprecated setting text blocks display an icon. Previously, the deprecated text was distinguished from the rest of the setting text only by color.
* More UI elements within the Settings editor have the setting ID as their name.

### Updated search icons

The search icons are now the same weight and the match whole word icon was updated to be more distinguishable from the rest.

![Example of the updated search icons](images/1_62/search-match-whole-word.png)

### Parameter hint highlight

VS Code now highlights the current parameter in the parameter hint and the color can be themed via `editorHoverWidget.highlightForeground`.

![Example of a parameter hint with a highlighted word](images/1_62/parameter-hints.png)

## Editor

### Improved bracket pair guides

We continued iterating on bracket pair guides. Horizontal lines now outline the scope of a bracket pair. Also, vertical lines now depend on the indentation of the code that is surrounded by the bracket pair.

<video src="images/1_62/bracket-pair-guides.mp4" autoplay loop controls muted title="Bracket pair horizontal lines moving with text indentation"></video>

Bracket pair guides can be enabled by setting `editor.guides.bracketPairs` to `true` (defaults to `false`). We added a third option "`active`" to only show a bracket pair guide for the active bracket pair.

The new setting `editor.guides.bracketPairsHorizontal` controls if and when to render horizontal guides (defaults to `active`).

New themable colors `editorBracketPairGuide.background{1,...,6}` and `editorBracketPairGuide.activeBackground{1,...,6}` can be used to customize the color of bracket pair guides.

### Customizable bracket pairs

You can now configure bracket pairs for a specific programming language through settings. `editor.language.brackets` can be used to configure which bracket characters [should be matched](https://code.visualstudio.com/docs/editing/editingevolved#_bracket-matching). If set, `editor.language.colorizedBracketPairs` independently configures which bracket pairs are colorized when bracket pair colorization or bracket pair guides are enabled.

```json
"[javascript]": {
    "editor.language.brackets": [
        ["[", "]"],
        ["(", ")"]
    ],
    "editor.language.colorizedBracketPairs": [
        ["[", "]"]
    ]
},
```

### Display hovers above or below the current line

You can now choose between displaying IntelliSense hovers above (default) or below the current line. By setting `editor.hover.above` to `false`, hovers will render below the current line.

![IntelliSense hover below the current line in the editor](images/1_62/hover-below.png)

### Unicode directional formatting characters

To address [CVE-2021-42574](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-42574), VS Code now renders Unicode directional formatting characters by default. Consider the following text snippet:

```js

//              from, to, amount
transferBalance(5678,‮6776,4321‬,"USD");

```

The above text snippet contains two explicit directional formatting characters, `U+202E` (`RIGHT-TO-LEFT OVERRIDE`) and `U+202C` (`POP DIRECTIONAL FORMATTING`). These characters can influence Unicode's [Bidirectional Algorithm](https://www.unicode.org/reports/tr9) and could be used to craft source code that renders differently than what compilers would execute.

![Unicode directional formatting characters are rendered](images/1_62/bidi-control-characters.png)

The special rendering of these directional formatting characters can be turned off by setting `editor.renderControlCharacters` to `false`. The setting `editor.renderControlCharacters` is now `true` by default.

## Extensions

### Verified extension publishers

VS Code now shows if the domains of an extension publisher are verified by the Visual Studio Marketplace.

![Verified extension publisher indicators in the Extensions view and details pane](images/1_62/verified-publishers.png)

*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

If you are an extension author, more details about how to become a verified publisher can be found in the [Publishing Extensions](https://code.visualstudio.com/api/working-with-extensions/publishing-extension#verify-a-publisher) topic.

## Terminal

### New default keybindings for special characters

The following keybindings were added that are typically supported in other terminals:

* `ctrl+shift+2`: Inputs the null character (`0x00`).
* `ctrl+shift+6`: Inputs the record separator character (`0x1E`).
* `ctrl+/`: Inputs the unit separator character (`0x1F`).

## Languages

### Configure how HTML completes attributes

There is a new setting `html.completion.attributeDefaultValue` that lets you choose how values are filled in when an HTML attribute is completed:

* `doublequotes`: The value is placed in double quotes (default)
* `singlequotes`: The value is placed in single quotes
* `empty`: The value is left empty

<video src="images/1_62/html-attribute-value-completion.mp4" autoplay loop controls muted title="HTML value completion showing double quotes, single quotes, and left empty"></video>

### Emmet improvements

The new **Emmet: Update Tag** command (`editor.emmet.action.updateTag`) updates the placeholder with the HTML tag to be updated.

<video src="images/1_62/emmet-update-tag.mp4" autoplay loop controls muted title="Emmet update tag command with placeholder"></video>

The extension also activates now when one tries to run any Emmet command. This change should fix an issue where some commands were not found when run from the Command Palette, while also keeping the activation event list more precise so that Emmet does not start up unnecessarily, such as in an empty workspace.

For multi-root workspaces, Emmet now sources snippets from all workspace folders, rather than just the first one. As a result, custom snippets from all workspace folders will be suggested when editing an applicable file in the workspace.

## Notebooks

### Find and Replace supports capturing groups

The Find and Replace widget in the notebook editor now supports regular expression capturing groups.

In the short video below, replace uses the first (and only) captured text ('a').

<video src="images/1_62/notebook-find-capture-groups.mp4" autoplay loop controls muted title="Find and Replace with capturing groups support"></video>

### Better selection of output renderers and mimetypes

Notebook cells can output multiple types of data (mimetypes) that can be rendered in different ways both by VS Code itself and extensions. Previously, VS Code had basic memoization (caching) of the selected renderer for a mimetype, and you could manually configure your preferred mimetypes using the `notebook.displayOrder` setting. However, VS Code wasn't particularly smart about picking the best renderer for a mimetype, and preferred mimetypes had to be updated manually.

Now, preferred renderers are cached on a per-notebook type, level in the workspace. Additionally, if you switch to view a different mimetype, that preference will be updated in-memory for the VS Code session, and you can use the **Notebook: Save Mimetype Display Order** command to easily update the `notebook.displayOrder` setting to the working preference.

## Contributions to extensions

### Jupyter

**Kernels**

In order to make it easier to identify kernels, the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension now groups kernels in the kernel picker.

![Kernels grouped in kernel picker](images/1_62/kernel_picker.png)

You can now filter the list of kernels displayed in the kernel picker, either globally or on a workspace basis. This is helpful if you have a large number of kernels installed but usually only work with a subset.

To manage the list of kernels displayed, you can use the command **Jupyter: Filter Kernels** from the Command Palette.

<video src="images/1_62/filter_kernels.mp4" autoplay loop controls muted title="Kernel filtering via the Filter Kernels command dropdown check boxes"></video>

**Interactive Window**

Automatic cell creation when running the last cells in an **Interactive Window** using `kbstyle(Shift+Enter)` can now be configured via the setting `jupyter.newCellOnRunLast`. If you don't want the default behavior to add a new cell, you can set `jupyter.netCellOnRunLast` to `false`.

### ESLint

A new version of the [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) extension has shipped. Major improvements are:

* Extended support for [ESLint version 8.x](https://www.npmjs.com/package/eslint/v/8.1.0).
* The ability to define the rule set that is applied during code action on save and format via the setting `eslint.codeActionsOnSave.rules`.

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Check out the [changelog for the 0.32.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0320) release of the extension to see the highlights.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.62 include:

* New setting `remote.SSH.foldersSortOrder` to sort SSH targets alphabetically or by recent use.
* Windows Subsystem for Linux indicator lets you quickly know whether you are using WSL 1 or WSL 2.
* Advanced container configuration videos covering how to [persist bash history](https://code.visualstudio.com/remote/advancedcontainers/persist-bash-history) and [work with monorepos](https://code.visualstudio.com/remote/advancedcontainers/change-default-source-mount).

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_62.md).

## Preview features

### TypeScript 4.5 support

This update adds support for the upcoming TypeScript 4.5 release. The [TypeScript 4.5 beta announcement post](https://devblogs.microsoft.com/typescript/announcing-typescript-4-5-beta) has more information about the new language and tooling features. Some tooling highlights:

* [Method signature completions](https://github.com/microsoft/TypeScript/pull/46370).
* [JSX attribute completions](https://github.com/microsoft/TypeScript/issues/38891).
* More accurate reference counts for the references CodeLens.

To start using the TypeScript 4.5 nightly builds, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

Please share your feedback and let us know if you run into any bugs with TypeScript 4.5.

## Extension authoring

### Emoji support in file decorations

The [file decorations](https://github.com/microsoft/vscode/blob/9430f7848503b25ff1a629f2cb81b705e11672f5/src/vs/vscode.d.ts#L6071) API now supports emojis as badge texts.

![Files decorated with emojis in the File Explorer](images/1_62/emoji_decorations.png)

### MarkdownString.supportHtml

The new `supportHtml` property on `MarkdownString` enables rendering of a safe subset of raw HTML that appears inside the Markdown text.

The `supportHtml` property defaults to `false`. When disabled, VS Code will strip out any raw HTML tags that appear in the Markdown text.

## Engineering

### File watching changes

File watching in VS Code changed to a new library, thanks to the work of the Parcel team with their [@parcel/watcher](https://github.com/parcel-bundler/watcher). We will gradually roll out this change to all users in the upcoming weeks. Linux users will be happy to learn that the `files.watcherExclude` now applies natively so that the number of file handles VS Code needs open for file watching can be greatly reduced.

The existing `files.legacyWatcher` setting was changed to an enumeration with these values:

* `on` - The new file watcher will never be used.
* `off` - The new file watcher will always be used.
* `default` - The new file watcher will only be used when you open multi-root workspaces (via a `.code-workspace` file).

You should not notice any difference in your day to day work, but if file watching is broken for you, please report an issue.

### Progress for Electron sandbox support

As we continue to make the VS Code workbench ready for enabling Electron's [sandbox](https://www.electronjs.org/docs/tutorial/sandbox), we made progress on moving Node.js file services out of the workbench window into a different process. The same is true for the file watcher that no longer forks from the workbench window, but from a background process. This change is enabled by default in VS Code Insiders and will be the default in Stable for our November release.

## Notable fixes

* [73061](https://github.com/microsoft/vscode/issues/73061): Enumerable properties mixed with regular properties
* [130868](https://github.com/microsoft/vscode/issues/130868): Emmet suggestions list missing entries after adding custom snippets.json
* [131966](https://github.com/microsoft/vscode/issues/131966): Emmet unable to expand abbreviation with onclick attribute
* [135110](https://github.com/microsoft/vscode/issues/135110): Support different border settings for vertical or horizontal layouts, for in-group editor splitting

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Web extensions

Extension authors for enabling extensions that run code as [web extensions](https://code.visualstudio.com/api/extension-guides/web-extensions) (the list below is as of November 2):

* [Search GitHub Users Extension](https://marketplace.visualstudio.com/items?itemName=adarshpandeyin.search-github-users-extension) ([Adarsh Pandey](https://marketplace.visualstudio.com/publishers/adarshpandeyin))
* [Typescript Language Server - Multi Host mode](https://marketplace.visualstudio.com/items?itemName=asvetliakov.typescript-language-features-multi-host) ([Alexey Svetliakov](https://marketplace.visualstudio.com/publishers/asvetliakov))
* [Git Buttons](https://marketplace.visualstudio.com/items?itemName=anweber.git-buttons) ([Andreas Weber](https://marketplace.visualstudio.com/publishers/anweber))
* [statusbar-commands](https://marketplace.visualstudio.com/items?itemName=anweber.statusbar-commands) ([Andreas Weber](https://marketplace.visualstudio.com/publishers/anweber))
* [Azure Devops Pull Requests](https://marketplace.visualstudio.com/items?itemName=ankitbko.vscode-pull-request-azdo) ([Ankit Sinha](https://marketplace.visualstudio.com/publishers/ankitbko))
* [Keil Studio](https://marketplace.visualstudio.com/items?itemName=arm-debug.keil-studio-vscode) ([Arm Debug](https://marketplace.visualstudio.com/publishers/arm-debug))
* [Svelte for VS Code Web](https://marketplace.visualstudio.com/items?itemName=asafamr.svelte-web) ([Asaf Amrami](https://marketplace.visualstudio.com/publishers/asafamr))
* [Astro](https://marketplace.visualstudio.com/items?itemName=astro-build.astro-vscode) ([Astro](https://marketplace.visualstudio.com/publishers/astro-build))
* [atsushieno.vscode-language-mugene](https://marketplace.visualstudio.com/items?itemName=atsushieno.vscode-language-mugene) ([atsushieno](https://marketplace.visualstudio.com/publishers/atsushieno))
* [Python C++ Debugger](https://marketplace.visualstudio.com/items?itemName=benjamin-simmonds.pythoncpp-debug) ([BeniBenj](https://marketplace.visualstudio.com/publishers/benjamin-simmonds))
* [One Dark Pro](https://marketplace.visualstudio.com/items?itemName=zhuangtongfa.Material-theme) ([binaryify](https://marketplace.visualstudio.com/publishers/zhuangtongfa))
* [COBOL](https://marketplace.visualstudio.com/items?itemName=bitlang.cobol) ([BitLang](https://marketplace.visualstudio.com/publishers/bitlang))
* [blink-fs](https://marketplace.visualstudio.com/items?itemName=BlinkShellInc.blink-fs) ([Blink Shell Inc](https://marketplace.visualstudio.com/publishers/blinkshellinc))
* [Oracle Guid Converter](https://marketplace.visualstudio.com/items?itemName=piklbojan.oracleguidconverter) ([Bojan](https://marketplace.visualstudio.com/publishers/piklbojan))
* [Path Intellisense](https://marketplace.visualstudio.com/items?itemName=christian-kohler.path-intellisense) ([Christian Kohler](https://marketplace.visualstudio.com/publishers/christian-kohler))
* [GitHub Actions](https://marketplace.visualstudio.com/items?itemName=cschleiden.vscode-github-actions) ([Christopher Schleiden](https://marketplace.visualstudio.com/publishers/cschleiden))
* [VSCode DOSBox](https://marketplace.visualstudio.com/items?itemName=xsro.vscode-dosbox) ([clcxsrolau](https://marketplace.visualstudio.com/publishers/xsro))
* [Toggle Light/Dark Theme](https://marketplace.visualstudio.com/items?itemName=danielgjackson.auto-dark-mode-windows) ([Daniel Jackson](https://marketplace.visualstudio.com/publishers/danielgjackson))
* [markdownlint](https://marketplace.visualstudio.com/items?itemName=DavidAnson.vscode-markdownlint) ([David Anson](https://marketplace.visualstudio.com/publishers/DavidAnson))
* [Arrow Function Snippets](https://marketplace.visualstudio.com/items?itemName=deinsoftware.arrow-function-snippets) ([dein Software](https://marketplace.visualstudio.com/publishers/deinsoftware))
* [SHADERed](https://marketplace.visualstudio.com/items?itemName=dfranx.shadered) ([dfranx](https://marketplace.visualstudio.com/publishers/dfranx))
* [SideBar Moc](https://marketplace.visualstudio.com/items?itemName=DiamondYuan.sidebar-moc) ([DiamondYuan](https://marketplace.visualstudio.com/publishers/DiamondYuan))
* [Tiny8BitVS](https://marketplace.visualstudio.com/items?itemName=flohofwoe.tiny8bitvs) ([flohofwoe](https://marketplace.visualstudio.com/publishers/flohofwoe))
* [SQLite Viewer](https://marketplace.visualstudio.com/items?itemName=qwtel.sqlite-viewer) ([Florian Klampfer](https://marketplace.visualstudio.com/publishers/qwtel))
* [spec command](https://marketplace.visualstudio.com/items?itemName=fujidana.spec-command) ([fujidana](https://marketplace.visualstudio.com/publishers/fujidana))
* [spec data](https://marketplace.visualstudio.com/items?itemName=fujidana.spec-data) ([fujidana](https://marketplace.visualstudio.com/publishers/fujidana))
* [spec log](https://marketplace.visualstudio.com/items?itemName=fujidana.spec-log) ([fujidana](https://marketplace.visualstudio.com/publishers/fujidana))
* [GehDoc.vscode-textile-preview](https://marketplace.visualstudio.com/items?itemName=GehDoc.vscode-textile-preview) ([GehDoc](https://marketplace.visualstudio.com/publishers/GehDoc))
* [Angular.io Documentation Utilities](https://marketplace.visualstudio.com/items?itemName=gkalpak.aio-docs-utils) ([George Kalpakas](https://marketplace.visualstudio.com/publishers/gkalpak))
* [GitLive](https://marketplace.visualstudio.com/items?itemName=TeamHub.teamhub) ([GitLive](https://marketplace.visualstudio.com/publishers/TeamHub))
* [Duplicate selection or line](https://marketplace.visualstudio.com/items?itemName=geeebe.duplicate) ([Greg Bacchus](https://marketplace.visualstudio.com/publishers/geeebe))
* [Dance](https://marketplace.visualstudio.com/items?itemName=gregoire.dance) ([Grégoire Geis](https://marketplace.visualstudio.com/publishers/gregoire))
* [Diff Viewer](https://marketplace.visualstudio.com/items?itemName=caponetto.vscode-diff-viewer) ([Guilherme Caponetto](https://marketplace.visualstudio.com/publishers/caponetto))
* [searKing.preview-vscode](https://marketplace.visualstudio.com/items?itemName=searKing.preview-vscode) ([Haixin Chen](https://marketplace.visualstudio.com/publishers/searKing))
* [BlitzMax](https://marketplace.visualstudio.com/items?itemName=Hezkore.blitzmax) ([Hezkore](https://marketplace.visualstudio.com/publishers/Hezkore))
* [Toggler](https://marketplace.visualstudio.com/items?itemName=hideoo.toggler) ([HiDeoo](https://marketplace.visualstudio.com/publishers/hideoo))
* [海拍客codereview](https://marketplace.visualstudio.com/items?itemName=hipac.Hicr) ([hipac](https://marketplace.visualstudio.com/publishers/hipac))
* [Hex Editor with Tags](https://marketplace.visualstudio.com/items?itemName=notblank00.hexeditor) ([Igor Gunin](https://marketplace.visualstudio.com/publishers/notblank00))
* [CompStruct VSCode](https://marketplace.visualstudio.com/items?itemName=jamestiotio.compstruct-vscode) ([James Raphael Tiovalen](https://marketplace.visualstudio.com/publishers/jamestiotio))
* [Peacock](https://marketplace.visualstudio.com/items?itemName=johnpapa.vscode-peacock) ([John Papa](https://marketplace.visualstudio.com/publishers/johnpapa))
* [WordPress Hooks IntelliSense](https://marketplace.visualstudio.com/items?itemName=johnbillion.vscode-wordpress-hooks) ([johnbillion](https://marketplace.visualstudio.com/publishers/johnbillion))
* [CodeSwing](https://marketplace.visualstudio.com/items?itemName=codespaces-Contrib.codeswing) ([Jonathan Carter](https://marketplace.visualstudio.com/publishers/codespaces-contrib))
* [CodeTour](https://marketplace.visualstudio.com/items?itemName=vsls-contrib.codetour) ([Jonathan Carter](https://marketplace.visualstudio.com/publishers/vsls-contrib))
* [GistPad](https://marketplace.visualstudio.com/items?itemName=vsls-contrib.gistfs) ([Jonathan Carter](https://marketplace.visualstudio.com/publishers/vsls-contrib))
* [WikiLens](https://marketplace.visualstudio.com/items?itemName=lostintangent.wikilens) ([Jonathan Carter](https://marketplace.visualstudio.com/publishers/lostintangent))
* [C# XML Documentation Comments](https://marketplace.visualstudio.com/items?itemName=k--kato.docomment) ([Keisuke Kato](https://marketplace.visualstudio.com/publishers/k--kato))
* [IntelliJ IDEA Keybindings](https://marketplace.visualstudio.com/items?itemName=k--kato.intellij-idea-keybindings) ([Keisuke Kato](https://marketplace.visualstudio.com/publishers/k--kato))
* [HarmonyLang Lite](https://marketplace.visualstudio.com/items?itemName=kevinsun-dev-cornell.harmonylang-lite) ([Kevin Sun](https://marketplace.visualstudio.com/publishers/kevinsun-dev-cornell))
* [Highlight Bad Chars](https://marketplace.visualstudio.com/items?itemName=wengerk.highlight-bad-chars) ([Kevin Wenger](https://marketplace.visualstudio.com/publishers/wengerk))
* [Web assets compiler](https://marketplace.visualstudio.com/items?itemName=kiennp.web-assets-compiler) ([Kien NP](https://marketplace.visualstudio.com/publishers/kiennp))
* [Csound](https://marketplace.visualstudio.com/items?itemName=kunstmusik.csound-vscode-plugin) ([kunstmusik](https://marketplace.visualstudio.com/publishers/kunstmusik))
* [abaplint](https://marketplace.visualstudio.com/items?itemName=larshp.vscode-abaplint) ([Lars Hvam](https://marketplace.visualstudio.com/publishers/larshp))
* [vscode-source-map](https://marketplace.visualstudio.com/items?itemName=larshp.vscode-source-map) ([Lars Hvam](https://marketplace.visualstudio.com/publishers/larshp))
* [bmwebtest - for testing - will be deleted soon](https://marketplace.visualstudio.com/items?itemName=leodevbro.bmwebtest) ([leodevbro](https://marketplace.visualstudio.com/publishers/leodevbro))
* [Glimmer Templates Syntax for VS Code](https://marketplace.visualstudio.com/items?itemName=lifeart.vscode-glimmer-syntax) ([lifeart](https://marketplace.visualstudio.com/publishers/lifeart))
* [Unstable Ember Language Server](https://marketplace.visualstudio.com/items?itemName=lifeart.vscode-ember-unstable) ([lifeart](https://marketplace.visualstudio.com/publishers/lifeart))
* [LuatIDE](https://marketplace.visualstudio.com/items?itemName=luater.luatide) ([luater](https://marketplace.visualstudio.com/publishers/luater))
* [One Dark Theme](https://marketplace.visualstudio.com/items?itemName=mskelton.one-dark-theme) ([Mark Skelton](https://marketplace.visualstudio.com/publishers/mskelton))
* [Marp for VS Code](https://marketplace.visualstudio.com/items?itemName=marp-team.marp-vscode) ([Marp team](https://marketplace.visualstudio.com/publishers/marp-team))
* [Iteria Json editor](https://marketplace.visualstudio.com/items?itemName=MartinBilansky.iteria-json-editor) ([MartinBilansky](https://marketplace.visualstudio.com/publishers/MartinBilansky))
* [PHP DocBlocker](https://marketplace.visualstudio.com/items?itemName=neilbrayfield.php-docblocker) ([Neil Brayfield](https://marketplace.visualstudio.com/publishers/neilbrayfield))
* [PHP DocBlocker](https://marketplace.visualstudio.com/items?itemName=neilbrayfield.php-docblocker-test) ([Neil Brayfield](https://marketplace.visualstudio.com/publishers/neilbrayfield))
* [Scout](https://marketplace.visualstudio.com/items?itemName=ncbradley.scout) ([Nick Bradley](https://marketplace.visualstudio.com/publishers/ncbradley))
* [Octopus Deploy](https://marketplace.visualstudio.com/items?itemName=octopusdeploy.vscode-octopusdeploy) ([Octopus Deploy](https://marketplace.visualstudio.com/publishers/OctopusDeploy))
* [oderwat.indent-rainbow](https://marketplace.visualstudio.com/items?itemName=oderwat.indent-rainbow) ([oderwat](https://marketplace.visualstudio.com/publishers/oderwat))
* [Colemak-Vim](https://marketplace.visualstudio.com/items?itemName=ollyhayes.colmak-vim) ([Olly Hayes](https://marketplace.visualstudio.com/publishers/ollyhayes))
* [LuatOS Debug](https://marketplace.visualstudio.com/items?itemName=openluat-wendal.luatos-debug) ([OpenLuat Wendal Chen](https://marketplace.visualstudio.com/publishers/openluat-wendal))
* [JavaScript Assistant: Refactoring Hints & Automation](https://marketplace.visualstudio.com/items?itemName=p42ai.refactor) ([P42](https://marketplace.visualstudio.com/publishers/p42ai))
* [quickstarts-preview](https://marketplace.visualstudio.com/items?itemName=PatternFly.quickstarts-preview) ([PatternFly](https://marketplace.visualstudio.com/publishers/PatternFly))
* [ui-builder](https://marketplace.visualstudio.com/items?itemName=PatternFly.ui-builder) ([PatternFly](https://marketplace.visualstudio.com/publishers/PatternFly))
* [Pop File Money Counter](https://marketplace.visualstudio.com/items?itemName=pfwobcke.pop-file-money-counter) ([pfwobcke](https://marketplace.visualstudio.com/publishers/pfwobcke))
* [Markdown Checkbox](https://marketplace.visualstudio.com/items?itemName=PKief.markdown-checkbox) ([Philipp Kief](https://marketplace.visualstudio.com/publishers/PKief))
* [Material Icon Theme](https://marketplace.visualstudio.com/items?itemName=PKief.material-icon-theme) ([Philipp Kief](https://marketplace.visualstudio.com/publishers/PKief))
* [Prettier - Code formatter](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) ([Prettier](https://marketplace.visualstudio.com/publishers/esbenp))
* [BPMN Editor](https://marketplace.visualstudio.com/items?itemName=redhat.vscode-extension-bpmn-editor) ([Red Hat](https://marketplace.visualstudio.com/publishers/redhat))
* [DMN Editor](https://marketplace.visualstudio.com/items?itemName=redhat.vscode-extension-dmn-editor) ([Red Hat](https://marketplace.visualstudio.com/publishers/redhat))
* [PMML Editor](https://marketplace.visualstudio.com/items?itemName=redhat.vscode-extension-pmml-editor) ([Red Hat](https://marketplace.visualstudio.com/publishers/redhat))
* [YAML](https://marketplace.visualstudio.com/items?itemName=redhat.vscode-yaml) ([Red Hat](https://marketplace.visualstudio.com/publishers/redhat))
* [Bulma CSS Class Completion](https://marketplace.visualstudio.com/items?itemName=reliutg.bulma-css-class-completion) ([reliutg](https://marketplace.visualstudio.com/publishers/reliutg))
* [Visual Regex](https://marketplace.visualstudio.com/items?itemName=reliutg.visual-regex) ([reliutg](https://marketplace.visualstudio.com/publishers/reliutg))
* [Date & Time](https://marketplace.visualstudio.com/items?itemName=rid9.datetime) ([rid9](https://marketplace.visualstudio.com/publishers/rid9))
* [Command Variable](https://marketplace.visualstudio.com/items?itemName=rioj7.command-variable) ([rioj7](https://marketplace.visualstudio.com/publishers/rioj7))
* [Context Menu Extra](https://marketplace.visualstudio.com/items?itemName=rioj7.context-menu-extra) ([rioj7](https://marketplace.visualstudio.com/publishers/rioj7))
* [Markdown Header Coloring](https://marketplace.visualstudio.com/items?itemName=satokaz.vscode-markdown-header-coloring) ([satokaz](https://marketplace.visualstudio.com/publishers/satokaz))
* [WarpScript & FLoWS language support](https://marketplace.visualstudio.com/items?itemName=senx.warpscript-language) ([SenX](https://marketplace.visualstudio.com/publishers/senx))
* [Align Text Tokens](https://marketplace.visualstudio.com/items?itemName=SergeLamikhov.aligntokens) ([Serge Lamikhov-Center](https://marketplace.visualstudio.com/publishers/SergeLamikhov))
* [Copy-Gather-Paste](https://marketplace.visualstudio.com/items?itemName=SergeLamikhov.copy-gather-paste) ([Serge Lamikhov-Center](https://marketplace.visualstudio.com/publishers/SergeLamikhov))
* [Color Highlight](https://marketplace.visualstudio.com/items?itemName=naumovs.color-highlight) ([Sergii Naumov](https://marketplace.visualstudio.com/publishers/naumovs))
* [shuaihu.html-embedded-javascript](https://marketplace.visualstudio.com/items?itemName=shuaihu.html-embedded-javascript) ([shuaihu](https://marketplace.visualstudio.com/publishers/shuaihu))
* [SCSS Formatter](https://marketplace.visualstudio.com/items?itemName=sibiraj-s.vscode-scss-formatter) ([Sibiraj](https://marketplace.visualstudio.com/publishers/sibiraj-s))
* [CypressHelper](https://marketplace.visualstudio.com/items?itemName=simonaron.cypresshelper) ([Simon Áron](https://marketplace.visualstudio.com/publishers/simonaron))
* [Simplicite VSCode tools](https://marketplace.visualstudio.com/items?itemName=SimpliciteSoftware.simplicite-vscode-tools) ([Simplicite Software](https://marketplace.visualstudio.com/publishers/simpliciteSoftware))
* [SinoMCU IDE RISC8 (alpha)](https://marketplace.visualstudio.com/items?itemName=SinoMCU.IDE-RISC8) ([SinoMCU](https://marketplace.visualstudio.com/publishers/SinoMCU))
* [Starfall](https://marketplace.visualstudio.com/items?itemName=sndst00m.starfall-visual-studio-code) ([sndst00m](https://marketplace.visualstudio.com/publishers/sndst00m))
* [sndst00m.vscode-native-svg-preview](https://marketplace.visualstudio.com/items?itemName=sndst00m.vscode-native-svg-preview) ([sndst00m](https://marketplace.visualstudio.com/publishers/sndst00m))
* [Print Timestamp](https://marketplace.visualstudio.com/items?itemName=snowcrash30.PrintTimestamp) ([snowcrash30](https://marketplace.visualstudio.com/publishers/snowcrash30))
* [Type4Py](https://marketplace.visualstudio.com/items?itemName=saltud.type4py) ([Software Analytics Lab (TU Delft)](https://marketplace.visualstudio.com/publishers/saltud))
* [Java Code Generators](https://marketplace.visualstudio.com/items?itemName=sohibe.java-generate-setters-getters) ([Sohibe](https://marketplace.visualstudio.com/publishers/sohibe))
* [Bash Options](https://marketplace.visualstudio.com/items?itemName=stenalpj.shell-script-options) ([Stenal P Jolly](https://marketplace.visualstudio.com/publishers/stenalpj))
* [vscode-go-by-example](https://marketplace.visualstudio.com/items?itemName=stuartleeks.vscode-go-by-example) ([Stuart Leeks](https://marketplace.visualstudio.com/publishers/stuartleeks))
* [Boxdraw](https://marketplace.visualstudio.com/items?itemName=taizod1024.boxdraw-extension) ([taizod1024](https://marketplace.visualstudio.com/publishers/taizod1024))
* [Package Logger](https://marketplace.visualstudio.com/items?itemName=taizod1024.package-logger) ([taizod1024](https://marketplace.visualstudio.com/publishers/taizod1024))
* [88d88d8d8d8d8](https://marketplace.visualstudio.com/items?itemName=Thomka.88d88d8d8d8d8) ([Thomka](https://marketplace.visualstudio.com/publishers/Thomka))
* [AutoHotKey2 Language Support](https://marketplace.visualstudio.com/items?itemName=thqby.vscode-autohotkey2-lsp) ([thqby](https://marketplace.visualstudio.com/publishers/thqby))
* [Stationeers ic10](https://marketplace.visualstudio.com/items?itemName=Traineratwot.stationeers-ic10) ([Traineratwot](https://marketplace.visualstudio.com/publishers/Traineratwot))
* [Vz Keymap](https://marketplace.visualstudio.com/items?itemName=tshino.vz-like-keymap) ([tshino](https://marketplace.visualstudio.com/publishers/tshino))
* [Vouch](https://marketplace.visualstudio.com/items?itemName=vouch-dev.vouch) ([vouch-dev](https://marketplace.visualstudio.com/publishers/vouch-dev))
* [Minion Debug](https://marketplace.visualstudio.com/items?itemName=VSCodeMinionPluginProject.minion) ([VSCode Minion Plugin Project](https://marketplace.visualstudio.com/publishers/VSCodeMinionPluginProject))
* [Vscode Outliner](https://marketplace.visualstudio.com/items?itemName=VscodeOutliner.vscode-outliner) ([VscodeOutliner](https://marketplace.visualstudio.com/publishers/VscodeOutliner))
* [VSpaceCode](https://marketplace.visualstudio.com/items?itemName=VSpaceCode.vspacecode) ([VSpaceCode](https://marketplace.visualstudio.com/publishers/VSpaceCode))
* [Which Key](https://marketplace.visualstudio.com/items?itemName=VSpaceCode.whichkey) ([VSpaceCode](https://marketplace.visualstudio.com/publishers/VSpaceCode))
* [Code Translate](https://marketplace.visualstudio.com/items?itemName=w88975.code-translate) ([w88975](https://marketplace.visualstudio.com/publishers/w88975))
* [Wardlt Editor](https://marketplace.visualstudio.com/items?itemName=Wardlt.wardlt) ([Wardlt](https://marketplace.visualstudio.com/publishers/Wardlt))
* [TODO Highlight](https://marketplace.visualstudio.com/items?itemName=wayou.vscode-todo-highlight) ([Wayou Liu](https://marketplace.visualstudio.com/publishers/wayou))
* [AutoMageDev](https://marketplace.visualstudio.com/items?itemName=Webkul.automagedev) ([Webkul](https://marketplace.visualstudio.com/publishers/Webkul))
* [wjrnjknrekwj](https://marketplace.visualstudio.com/items?itemName=wjrnjknrekwj.wjrnjknrekwj-wjrnjknrekwj) ([wjrnjknrekwj](https://marketplace.visualstudio.com/publishers/wjrnjknrekwj))
* [Blitz](https://marketplace.visualstudio.com/items?itemName=wraith13.blitz) ([wraith13](https://marketplace.visualstudio.com/publishers/wraith13))
* [Bracket Lens](https://marketplace.visualstudio.com/items?itemName=wraith13.bracket-lens) ([wraith13](https://marketplace.visualstudio.com/publishers/wraith13))
* [File Path Bar](https://marketplace.visualstudio.com/items?itemName=wraith13.file-path-bar) ([wraith13](https://marketplace.visualstudio.com/publishers/wraith13))
* [Unsaved Files](https://marketplace.visualstudio.com/items?itemName=wraith13.unsaved-files-vscode) ([wraith13](https://marketplace.visualstudio.com/publishers/wraith13))
* [Awesome Emacs Keymap](https://marketplace.visualstudio.com/items?itemName=tuttieee.emacs-mcx) ([Yuichiro Tsuchiya](https://marketplace.visualstudio.com/publishers/tuttieee))

### Issue tracking

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [Lemmingh (@Lemmingh)](https://github.com/Lemmingh)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart): Specific the map generics in history.ts explicitly [PR #134896](https://github.com/microsoft/vscode/pull/134896)
* [@a5hk (Ashkan)](https://github.com/a5hk): Adds support for separate horizontal and vertical borders for side by side editor [PR #135181](https://github.com/microsoft/vscode/pull/135181)
* [@adaex (Aex)](https://github.com/adaex): Update to latest seti-ui icon theme [PR #135484](https://github.com/microsoft/vscode/pull/135484)
* [@AiverReaver (Ashish Vagish)](https://github.com/AiverReaver): fixed minimap slider width when page is scroll horizontally. [PR #135081](https://github.com/microsoft/vscode/pull/135081)
* [@AlexStrNik (AlexStrNik)](https://github.com/AlexStrNik): Fix interactive playground's suggest widget styled everything as links [PR #90022](https://github.com/microsoft/vscode/pull/90022)
* [@asciimike (Mike McDonald)](https://github.com/asciimike): Changing port "privacy" to "visibility" to address Codespaces user confusion [PR #135193](https://github.com/microsoft/vscode/pull/135193)
* [@ashgti (John Harrison)](https://github.com/ashgti): Updating the TerminalInstance to only register link providers once. [PR #135419](https://github.com/microsoft/vscode/pull/135419)
* [@brajkowski (Brandon Rajkowski)](https://github.com/brajkowski): Terminal now supports linking local file paths containing '$' [PR #132027](https://github.com/microsoft/vscode/pull/132027)
* [@byteit101 (Patrick Plenefisch)](https://github.com/byteit101): Fix: Don't ignore the indent settings in advanced wrapping mode [PR #134171](https://github.com/microsoft/vscode/pull/134171)
* [@codeclown (Martti Laine)](https://github.com/codeclown): Feature: New command - Delete Duplicate Lines [PR #119480](https://github.com/microsoft/vscode/pull/119480)
* [@codingLogan (Logan Rasmussen)](https://github.com/codingLogan): Remove angle brackets when checking the scheme [PR #133419](https://github.com/microsoft/vscode/pull/133419)
* [@concatime (Issam Maghni)](https://github.com/concatime): Fix mkdir arguments order in snap [PR #135182](https://github.com/microsoft/vscode/pull/135182)
* [@cyntler (Damian Cyntler)](https://github.com/cyntler): Fix zoom-out on an image [#131080] [PR #134706](https://github.com/microsoft/vscode/pull/134706)
* [@eltociear (Ikko Ashimine)](https://github.com/eltociear): Fix typo in timelinePane.ts [PR #131356](https://github.com/microsoft/vscode/pull/131356)
* [@gabritto (Gabriela Araujo Britto)](https://github.com/gabritto): [typescript-language-features] Add includeCompletionsWithClassMemberSnippets preference [PR #136045](https://github.com/microsoft/vscode/pull/136045)
* [@Gerrit0 (Gerrit Birkeland)](https://github.com/Gerrit0): Fix codeblock detection to catch blocks not on first lines [PR #132821](https://github.com/microsoft/vscode/pull/132821)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Correct doc for `count` property of `SourceControl` (#117765) [PR #132242](https://github.com/microsoft/vscode/pull/132242)
  * fix #134345 - 'workpsace' typo [PR #134348](https://github.com/microsoft/vscode/pull/134348)
* [@j824h (Junghyeon Park)](https://github.com/j824h): Capturing groups support in Notebook Find Widget [PR #135444](https://github.com/microsoft/vscode/pull/135444)
* [@jakevossen5 (Jake Vossen)](https://github.com/jakevossen5): Standardize capitilization of "Go to" [PR #135869](https://github.com/microsoft/vscode/pull/135869)
* [@JaredNeil (Jared Neil)](https://github.com/JaredNeil): Update seti-ui for Bazel icon fix [PR #135188](https://github.com/microsoft/vscode/pull/135188)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes feedback widget appears wrong when invoked from command palette [PR #103738](https://github.com/microsoft/vscode/pull/103738)
  * Fixes drop indicator not shown when dragging a view over panel title [PR #125438](https://github.com/microsoft/vscode/pull/125438)
  * Fix empty HoverWidget is visible at startup [PR #134752](https://github.com/microsoft/vscode/pull/134752)
* [@justanotheranonymoususer](https://github.com/justanotheranonymoususer): Option to preserve focus while showing view [PR #133682](https://github.com/microsoft/vscode/pull/133682)
* [@Lemmingh](https://github.com/Lemmingh): Add source map for every possible element in the Markdown preview [PR #134799](https://github.com/microsoft/vscode/pull/134799)
* [@martinzimmermann (Martin Zimmermann)](https://github.com/martinzimmermann): Fixes #26393 by changing the default behavior of InsertCursorAbove/Below [PR #135805](https://github.com/microsoft/vscode/pull/135805)
* [@rjc](https://github.com/rjc): Fix extended description [PR #134387](https://github.com/microsoft/vscode/pull/134387)
* [@sean-mcmanus (Sean McManus)](https://github.com/sean-mcmanus): Update package.json [PR #135050](https://github.com/microsoft/vscode/pull/135050)
* [@ssigwart (Stephen Sigwart)](https://github.com/ssigwart)
  * Add doNotAddAttributeQuotes setting to disable automatic quotes [PR #129284](https://github.com/microsoft/vscode/pull/129284)
  * Fix search editor scrolled to bottom on first search when editor group changed. [PR #134693](https://github.com/microsoft/vscode/pull/134693)
  * Search editor go to location improvements [PR #135227](https://github.com/microsoft/vscode/pull/135227)
* [@Suven-p (Suven-p)](https://github.com/Suven-p)
  * Set wordwrap to on for testingOutputPeek [PR #134347](https://github.com/microsoft/vscode/pull/134347)
  * Add tooltip for keybinding [PR #134438](https://github.com/microsoft/vscode/pull/134438)
* [@suzmue (Suzy Mueller)](https://github.com/suzmue): disassemble request fix off by one error [PR #134241](https://github.com/microsoft/vscode/pull/134241)
* [@TabithaLarkin (Tabitha Larkin)](https://github.com/TabithaLarkin): Add colorized bracket highlighting colours [PR #132494](https://github.com/microsoft/vscode/pull/132494)
* [@thebinarysearchtree (Andrew Jones)](https://github.com/thebinarysearchtree)
  * Remove backticks from word character fix [PR #135197](https://github.com/microsoft/vscode/pull/135197)
  * Handle apostrophes in TitleCaseAction [PR #135698](https://github.com/microsoft/vscode/pull/135698)
* [@Timmmm (Tim)](https://github.com/Timmmm): Add setting to control hover position [PR #127609](https://github.com/microsoft/vscode/pull/127609)
* [@timretout (Tim Retout)](https://github.com/timretout): Update desktop file cache in Debian package postinst [PR #134840](https://github.com/microsoft/vscode/pull/134840)
* [@wenfangdu (Wenfang Du)](https://github.com/wenfangdu): fix: the quick diff should respect `diffEditor.ignoreTrimWhitespace` [PR #135285](https://github.com/microsoft/vscode/pull/135285)
* [@yuehuang010 (Yue (Felix) Huang)](https://github.com/yuehuang010): Integrate Source Code into Disassembly View [PR #132541](https://github.com/microsoft/vscode/pull/132541)
* [@yume-chan (Simon Chan)](https://github.com/yume-chan): Fix a typo in npm extension package.json [PR #134856](https://github.com/microsoft/vscode/pull/134856)
* [@yuzhishuo (muduo)](https://github.com/yuzhishuo): fix: Terminal editors are always marked dirty. [PR #134861](https://github.com/microsoft/vscode/pull/134861)

Contributions to `vscode-codicons`:

* [@Profesor08](https://github.com/Profesor08): added svg-sprite generation [PR #84](https://github.com/microsoft/vscode-codicons/pull/84)

Contributions to `vscode-debugadapter-node`:

* [@friederbluemle (Frieder Bluemle)](https://github.com/friederbluemle): Fix capitalization of GitHub org [PR #235](https://github.com/microsoft/vscode-debugadapter-node/pull/235)

Contributions to `vscode-eslint`:

* [@dotansimha (Dotan Simha)](https://github.com/dotansimha): Added graphql language to ESLint-server comments [PR #1346](https://github.com/microsoft/vscode-eslint/pull/1346)
* [@jogo-](https://github.com/jogo-): 💄 Fix typo in Changelog [PR #1350](https://github.com/microsoft/vscode-eslint/pull/1350)
* [Yosuke Ota](https://github.com/ota-meshi): Supports ESLint v8. [PR #117](https://github.com/microsoft/vscode-eslint/pull/1317).

Contributions to `vscode-generator-code`:

* [@albinpk (Albin PK)](https://github.com/albinpk): Update comment in webpack.config.js [PR #302](https://github.com/microsoft/vscode-generator-code/pull/302)

Contributions to `vscode-js-debug`:

* [@yyyang1996 (yang)](https://github.com/yyyang1996): fix: use edge if it's the default on macOS [PR #1122](https://github.com/microsoft/vscode-js-debug/pull/1122)

Contributions to `vscode-json-languageservice`:

* [@jgraettinger (Johnny Graettinger)](https://github.com/jgraettinger): interpret `pattern`/`patternProperties` regex as Unicode [PR #111](https://github.com/microsoft/vscode-json-languageservice/pull/111)

Contributions to `vscode-languageserver-node`:

* [@AkatQuas (Akat)](https://github.com/AkatQuas)
  * fix(types): guard the `is` judgement [PR #814](https://github.com/microsoft/vscode-languageserver-node/pull/814)
  * feat: update type definition [PR #816](https://github.com/microsoft/vscode-languageserver-node/pull/816)
* [@bmewburn (Ben Mewburn)](https://github.com/bmewburn): fix unhandled promise rejection in getConfiguration [PR #760](https://github.com/microsoft/vscode-languageserver-node/pull/760)
* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Add support for textDocument/inlineValues [PR #806](https://github.com/microsoft/vscode-languageserver-node/pull/806)
* [@dc-mak (Dhruv Makwana)](https://github.com/dc-mak): Mention dependency of codeDescription on code [PR #830](https://github.com/microsoft/vscode-languageserver-node/pull/830)

Contributions to `vscode-pull-request-github`:

* [@burkeholland (Burke Holland)](https://github.com/burkeholland)
  * Fixes Commit hash should use a monospace font #2957 [PR #2968](https://github.com/microsoft/vscode-pull-request-github/pull/2968)
  * Burkeholland-issue1635 [PR #2993](https://github.com/microsoft/vscode-pull-request-github/pull/2993)
  * Title doesn't have enough horizontal space in small tabs [PR #3029](https://github.com/microsoft/vscode-pull-request-github/pull/3029)
  * Fix 2959 - Merge title box needs top margin [PR #3039](https://github.com/microsoft/vscode-pull-request-github/pull/3039)
* [@nicholas-l (Nicholas Latham)](https://github.com/nicholas-l): Allow authentication for github enterprise [PR #3064](https://github.com/microsoft/vscode-pull-request-github/pull/3064)

Contributions to `vscode-vsce`:

* [@Andrewnt219 (Andrew Nguyen)](https://github.com/Andrewnt219): feat: warn users about missing LICENSE [PR #628](https://github.com/microsoft/vscode-vsce/pull/628)
* [@felipecrs (Felipe Santos)](https://github.com/felipecrs): Migrate to GitHub Actions [PR #579](https://github.com/microsoft/vscode-vsce/pull/579)
* [@SimonSegerblomRex (Simon Segerblom Rex)](https://github.com/SimonSegerblomRex): Fix broken Dockerfile [PR #640](https://github.com/microsoft/vscode-vsce/pull/640)

Contributions to `debug-adapter-protocol`:

* [@getreu (Jens Getreu)](https://github.com/getreu): Rust: Microsoft DAP server implementation [PR #204](https://github.com/microsoft/debug-adapter-protocol/pull/204)

Contributions to `language-server-protocol`:

* [@nektro (Meghan)](https://github.com/nektro): fix the bounding box of `.anchor`s [PR #1355](https://github.com/microsoft/language-server-protocol/pull/1355)

Contributions to `monaco-languages`:

* [@arlosi (Arlo Siemsen)](https://github.com/arlosi): Rust: highlighting raw strings and fix chars with escapes [PR #167](https://github.com/microsoft/monaco-languages/pull/167)

Contributions to `node-jsonc-parser`:

* [@Marcono1234](https://github.com/Marcono1234): Improve README [PR #47](https://github.com/microsoft/node-jsonc-parser/pull/47)
* [@urish (Uri Shaked)](https://github.com/urish): readme: improve ParseOptions documentation [PR #54](https://github.com/microsoft/node-jsonc-parser/pull/54)

Contributions to `vscode-jupyter`:

* [Janosh Riebesell](https://github.com/janosh): Add setting jupyter.newCellOnRunLast [PR #7995](https://github.com/Microsoft/vscode-jupyter/pull/7995)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
