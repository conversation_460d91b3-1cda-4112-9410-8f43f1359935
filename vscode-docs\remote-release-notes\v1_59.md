# July 2021 (version 1.59)

## Containers (version 0.191.x)

### Rebuild without cache

A dev container can now be rebuilt without using <PERSON><PERSON>'s image layer cache. When connected to a dev container, there is the additional **Rebuild Without Cache** command. When a local folder with a dev container configuration is open, there is the additional **Rebuild Without Cache and Reopen in Container** command.

### devcontainer CLI

There is now a `devcontainer` Command Line Interface (CLI) that can be installed. This allows you to [open a folder within a dev container](https://code.visualstudio.com/docs/devcontainers/devcontainer-cli#_opening-a-folder-directly-within-a-dev-container), and to [build the dev container image](https://code.visualstudio.com/docs/devcontainers/devcontainer-cli#_building-a-dev-container-image) from your terminal.
