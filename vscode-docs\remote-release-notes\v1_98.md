# February 2025 (version 1.98)

## Remote - SSH

Remote - SSH `0.118.0` will be released alongside VS Code `1.98`.

### Expanded proxy configurability

Remote - SSH employs various strategies to automatically initialize a connection to the remote host. In most cases, no configuration is required, even in restricted environments. However, in some instances, the ability to manually configure a proxy is desirable.

The Remote - SSH extension now provides two settings to manually specify HTTP and HTTPS proxies. These settings export the `http_proxy` and `https_proxy` environment variables during the installation and launch of the VS Code server on the remote host.

The settings are configurable on a per-host basis:

```json
{
    "remote.SSH.httpProxy": {
        "myhost": "http://proxy.example.com:8080",
        "myhost2": "http://proxy2.example.com"
    },
    "remote.SSH.httpsProxy": {
        "myhost": "https://proxy.example.com:8080",
        "myhost2": "https://proxy2.example.com"
    }
}
```

Or for all hosts, you may specify:

```json
{
    "remote.SSH.httpProxy": "http://proxy.example.com:8080",
    "remote.SSH.httpsProxy": "https://proxy.example.com:8080"
}
```

For editor-wide proxy configuration settings, please refer to the [VS Code documentation](https://code.visualstudio.com/docs/setup/network).

### Improved offline support

In instances where the remote server cannot access the extension marketplace, the client will now handle the installation of default extensions specified in the `remote.SSH.defaultExtensions` setting.

This ensures that extensions are present even if the remote server lacks internet access.

### EOL for Linux legacy server

VS Code release v1.98 (February 2025) is the last release that supports Linux servers with `glibc` < 2.28 or `libstdc++` < 3.4.25. As of release 1.99, you can no longer connect to these servers. As noted in our [1.97 release](/remote-release-notes/v1_97.md#migration-path-for-linux-legacy-server), users that require additional time to complete migration to a supported Linux distro can provide custom builds of `glibc` and `libstdc++` as a workaround. More info on this workaround can be found in the [FAQ](https://aka.ms/vscode-remote/faq/old-linux) section.
