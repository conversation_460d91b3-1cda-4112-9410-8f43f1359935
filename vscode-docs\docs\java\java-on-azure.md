---
ContentId: 01210769-05be-4854-9482-13e342850ad7
DateApproved: 10/18/2022
MetaDescription: How to deploy Java applications to Azure with Visual Studio Code
---
# Deploy Java Web Apps

The [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack) extension pack contains a rich set of extensions that make it easy to discover and interact with the cloud services that power your Java applications.

The extension pack supports the following development workflows:

* Deploy Java applications (including containers) to [Azure App Service](https://azure.microsoft.com/services/app-service).
* Deploy Spring microservices to [Azure Spring Cloud](https://azure.microsoft.com/services/spring-cloud/).
* Deploy serverless code to [Azure Functions](https://azure.microsoft.com/services/functions).

![Azure Tools extension](images/azure/azure-tools.png)

If you are interested in a specific Azure service, you can also directly search for it on the [Visual Studio Code Marketplace](https://marketplace.visualstudio.com/VSCode) to see if there's an available extension.

## Deployment tutorials

The following tutorials below walk you through the details.  You can also check the [Java Azure Developer's Center](https://learn.microsoft.com/azure/developer/java) for all things on Azure for Java developers.

| Tutorial | Description | Related Tools |
| --- | --- | --- |
| [Deploy Java web apps <br> to Azure App Service](/docs/java/java-webapp.md) | Deploy a web app to the cloud | [Apache Maven](https://maven.apache.org/download.cgi) <br> [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice)
| [Deploy Spring Boot apps <br> to Azure Spring Apps](/docs/java/java-spring-apps.md) | Deploy a Spring Boot application<br> to Azure Spring Apps | [Apache Maven](https://maven.apache.org/download.cgi) <br> [Azure Spring Apps](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-azurespringcloud)
| [Create an Azure Functions project <br> using Visual Studio Code](https://learn.microsoft.com/azure/azure-functions/create-first-function-vs-code-java) | Deploy serverless code <br> using Azure Functions | [Apache Maven](https://maven.apache.org/download.cgi) <br> [Azure Functions](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurefunctions)
