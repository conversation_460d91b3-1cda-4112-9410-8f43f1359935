# March 2024 (version 1.88)

## Core

### Alternate server download for extended support distros

We now have a separate server download for [extended support distros](https://aka.ms/vscode-remote/faq/old-linux) that have a dependency on glibc >= 2.17 and libstdc++ >= 3.4.19. For other distro connections, a server with a dependency of glibc >= 2.28 and libstdc++ >= 3.4.25 is downloaded.

### Port forwarding in query

External URIs that have a string that matches `host:port` in the query portion of the URI have that `port` forwarded. This is useful for URLs that include a redirect URI to a server running on `localhost` on the remote.

## Dev Containers

### Automatic start of Docker

When the Dev Containers extension detects that <PERSON><PERSON> is not running on Windows or Mac, it now checks for and automatically starts Docker Desktop. This should avoid a common manual step after rebooting your machine.

## Tunnels

Windows administrators can now restrict access to Dev Tunnels and port forwarding via group policies. Read more on [Microsoft Learn](https://learn.microsoft.com/en-us/azure/developer/dev-tunnels/policies).

## SSH

### New "Exec Server" Mode for SSH

We've been reworking the internals of the `Remote - SSH` extension to a new model. While there are no user-facing changes yet, this release makes the exec server mode the default. If you experience new issues with SSH, you can try disabling this mode by setting `"remote.ssh.useExecServer": false` in your user settings.

Please be sure to [file an issue](https://github.com/microsoft/vscode-remote-release/issues/new) if you run into any such problems.
