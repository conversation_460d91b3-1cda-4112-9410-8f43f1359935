# April 2021 (version 1.56)

## Visual Studio Code Remote Core

### Local terminal indicator and message

Any **local** terminal launched within a remote window will now have a special indicator in the dropdown or terminal tab and will also display a warning message to help differentiate them from remote terminals.

![Warning to say that shell is running on your local machine](images/1_56/local_terminals.png)

### Themable color for forwarded ports

Ports in the **Ports** view that are associated with a running process have a filled circle icon that is now themable with the theme id `ports.iconRunningProcessforeground`.

![Port with a process icon](images/1_56/ports-view-running-icon.png)

### Improved rendering of the remote indicator menu

The remote indicator menu shows when clicking on the remote indicator in the bottom left corner.

![Remote Indicator Menu](images/1_56/remote-indicator-menu.png)

The labels in the remote menu no longer start with Remote-XY. Instead this is now shown as group label.

### Remote indicator menu is now public API

Any extension can now contribute commands to the Remote Indicator menu. See the Extension authoring section in the Visual Studio Code April 2021 Release Notes.

## Containers (version 0.177.x)

### Security update

Version 0.177.2 of the Dev Containers extension includes a fix for a security issue in how cloning a repository in a Docker volume can execute code from that repository without the user necessarily being aware of this. Details are in [CVE-2021-31213](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-31213).

### Clone Repository in Container Volume adds a new management view

The commands **Dev Containers: Clone Repository in Container Volume** and **Dev Containers: Clone GitHub Pull Request in Container Volume** create a Docker volume for the cloned repository. The Remote Explorer now shows a new **DEVVOLUMES** view, where you can inspect and manage these volumes.

![Devvolumes view in Remote Explorer](images/1_56/devvolumes-view.png)

From the context menu shown for volumes, you can:

* Explore the contents of a volume in a new development container.
* Inspect the Docker meta data for a volume,
* Remove the volume.

The **DEVVOLUMES** toolbar provides commands to clone a repository in a container volume and to remove unused volumes. In addition, the hover provides additional information about the volume:

* The cloned repository.
* The branch name or pull request ID.
* The time when the volume was last used.
* The containers that are using the volume.

The advanced scenario where multiple repositories are cloned in a single volume is still supported. The command **Dev Containers: Clone Repository in Named Container Volume** allows you to pick an existing or create a new volume for a cloned repository.

### Launch Docker Desktop installer

On Windows and macOS, the Dev Containers extension offers to download and launch the Docker Desktop installer when Docker is not installed yet.

### Containers view de-densified

The most common case for containers is to have only one folder per container. The **CONTAINERS** view in the Remote Explorer now takes advantage of this by only showing folders listed under each container when you have opened more than one folder in that container. The hover for containers has also been simplified with the use of icons.

![Containers view in Remote Explorer](images/1_56/containers-view.png)

## SSH

### Default port source setting

Previously, the setting `remote.autoForwardPortsSource` would be set to `"output"` as a machine setting when connecting to a remote with the Remote - SSH extension. Now, instead of actively setting the value, the `autoForwardPortSource` default is `"output"` so that your machine settings aren't changed.

## Notable fixes

* [4759](https://github.com/microsoft/vscode-remote-release/issues/4759): UI blocked on port updates
