# January 2023 (version 1.75)

## Core

### remote.forwardOnOpen setting

The setting `remote.forwardOnOpen` can be set to `false` to prevent forwarding of links opened from within VS Code.

## Dev Containers (version 0.275.x)

### Folders with multiple devcontainer.json files

We have added support for `devcontainer.json` files in subfolders of the `.devcontainer` folder. This allows you to have multiple Dev Containers in a single repository and you can then choose which to use when reopening the folder in a container:

![QuickPick to select Dev Container configuration](images/1_75/select-devcontainer-config.png)

Multiple `devcontainer.json` files are not yet supported when cloning a repository into a Docker volume.

### Rebuild container notification

When the `devcontainer.json` of the current Dev Container changes, you are now notified to rebuild the container:

![Rebuild container notification](images/1_75/rebuild-devcontainer-notification.png)

### Docker credential forwarding

When cloning a repository in a Docker volume and when your Dev Container has the Docker CLI installed, we now register a credential helper with <PERSON><PERSON> that forwards requests for credentials from within the container to your host machine's credential helper for Docker.

This allows you to use private Docker images when cloning a repository in a Docker volume and it allows you to work with private images from within your Dev Container.

### Dev Container metadata when attaching to a container

Dev Container metadata on a manually created container is now applied when you attach to that container. The metadata comes from a label that is set on the Docker image when it is being built, for example by the [Dev Containers CLI](https://github.com/devcontainers/cli) distributed under the MIT license.

### X11 & Wayland Forwarding

When your host machine has the `DISPLAY` environment variable set, we forward the X11 display to the Dev Container. This allows you to run GUI applications from within the Dev Container.

Similarly, when your Windows Subsystem for Linux (WSL) distro has the `WAYLAND_DISPLAY` environment variable set, we forward the [Wayland](https://wiki.debian.org/Wayland) display to the Dev Container. This is currently limited to opening WSL folders in Dev Containers.
