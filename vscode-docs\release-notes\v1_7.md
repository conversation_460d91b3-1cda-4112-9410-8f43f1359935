---
Order:
TOCTitle: October 2016
PageTitle: Visual Studio Code October 2016 1.7
MetaDescription: See what is new in the Visual Studio Code October 2016 Release (1.7)
MetaSocialImage: 1_7/release-highlights.png
---
# October 2016 (version 1.7)

## 1.7.2 Recovery Build

We are releasing 1.7.2 today which includes a new version of the TypeScript Server (to 2.0.10). The update to the server will re-enable the Automatic Type Acquisition (ATA) feature by default for JavaScript users. This feature greatly improves the IntelliSense in JavaScript projects and removes the need to manually install TypeScript declaration files (.d.ts files). This feature is powered by a query to the npmjs service to retrieve relevant declaration files for used modules. More details in relation to this feature, including details on how to disable it are [included in the relevant section of these release notes](#better-javascript-intellisense).

The 1.7.2 release also adds the final 1.7 translations and fixes several important [issues](https://github.com/microsoft/vscode/milestone/32?closed=1).

Downloads: [Windows](https://update.code.visualstudio.com/1.7.2/win32/stable) | [Mac](https://update.code.visualstudio.com/1.7.2/darwin/stable) | Linux 64-bit: [.tar.gz](https://update.code.visualstudio.com/1.7.2/linux-x64/stable) [.deb](https://update.code.visualstudio.com/1.7.2/linux-deb-x64/stable) [.rpm](https://update.code.visualstudio.com/1.7.2/linux-rpm-x64/stable) | Linux 32-bit: [.tar.gz](https://update.code.visualstudio.com/1.7.2/linux-ia32/stable) [.deb](https://update.code.visualstudio.com/1.7.2/linux-deb-ia32/stable) [.rpm](https://update.code.visualstudio.com/1.7.2/linux-rpm-ia32/stable)

## 1.7.1 Recovery Build

We are releasing a 1.7.1 recovery build to disable the automatic JavaScript typings file acquisition [feature](#better-javascript-intellisense). We need to tune the acquisition to avoid unnecessary load on the NPM registry and we're working closely with the TypeScript and npmjs.org teams. We expect to enable this feature in a future release.

If you're curious about the details, you can read our [blog post](https://code.visualstudio.com/blogs/2016/11/3/rollback) describing the event and NPM and VS Code's mitigations.

Downloads: [Windows](https://update.code.visualstudio.com/1.7.1/win32/stable) | [Mac](https://update.code.visualstudio.com/1.7.1/darwin/stable) | Linux 64-bit: [.tar.gz](https://update.code.visualstudio.com/1.7.1/linux-x64/stable) [.deb](https://update.code.visualstudio.com/1.7.1/linux-deb-x64/stable) [.rpm](https://update.code.visualstudio.com/1.7.1/linux-rpm-x64/stable) | Linux 32-bit: [.tar.gz](https://update.code.visualstudio.com/1.7.1/linux-ia32/stable) [.deb](https://update.code.visualstudio.com/1.7.1/linux-deb-ia32/stable) [.rpm](https://update.code.visualstudio.com/1.7.1/linux-rpm-ia32/stable)

## October Release Summary

Welcome to the October release of Visual Studio Code. There are a number of significant updates in this version that we hope you will like, some of the key highlights include:

* **[Horizontal layout](#horizontal-layout)** - Organize your editors in either vertical or horizontal groups.
* **[Keyboard Shortcuts Reference](#keyboard-shortcuts-reference)** - New printable keyboard shortcuts PDF cheat sheet.
* **[CSS autocompletion within HTML](#css-completions-in-html)** - Get rich auto completions for CSS embedded in your HTML.
* **[Debug hit count control](#hit-count-conditional-breakpoints)** - Set breakpoint hit count conditions.
* **[Simplified Node.js debugging](#simplified-launch-configuration)** - Easily configure your Node.js debugging sessions.
* **[Keymaps for Sublime and Atom](#keymaps-category)** - You can now use these well-known keyboard shortcuts in VS Code.
* **[Disable extensions](#disable-extensions)** - Quickly disable an Extension globally or for a specific workspace.
* **[Improved TypeScript and JavaScript Grammar](#improved-typescript-and-javascript-grammar)** - The Dark+ themes now colorize variable and function references.
* **[Extension Packs](#extension-packs)** - View dependencies of an extension pack right in the extension details.

The release notes are arranged in the following sections related to VS Code focus areas. Here are some further updates:

* **[Workbench](#workbench)** - Some View states now persisted in user settings. Open multiple files from Quick Open.
* **[Editor](#editor)** - Keyboard shortcuts reference, finer formatting control.
* **[Languages](#languages)** - JavaScript and CSS improvements.
* **[Extensions](#extensions)** - New categories for Keymaps and Formatters for easier search.
* **[Node.js Debugging](#node-debugging)** - Multi-target debugging support, new debugging settings.
* **[Extension Authoring](#extension-authoring)** - Support for extension CHANGELOG.md files. Add to the editor tab context menu.

## Workbench

### Horizontal layout

You can now change the layout of editor groups to be either vertical or horizontal. Both layouts behave the same as before allowing you to move and resize editors and editor groups.

![Horizontal](images/1_7/horizontal.png)

To enable horizontal layout for the current workspace, use:

* The **View** menu **Toggle Editor Group Layout**.
* The Command Palette **Toggle Vertical/Horizontal Editor Group Layout**.
* An action in the header of the **OPEN EDITORS** view.

![toggle horizontal layout](images/1_7/toggle-horizontal-layout.png)

There is also a keyboard shortcut (`kb(workbench.action.toggleEditorGroupLayout)`) to quickly switch between the two layouts.

Once you made your choice for the layout, we persist it for the current workspace and restore it after restart.

### Toggle Maximized Panel

We have introduced a global workbench command that maximizes the size of the panel (80% of the total height). If the panel is already maximized, this command will shrink the panel to its previous size. This is really handy when you have lots of output to review quickly. This command is not currently bound to any keyboard shortcut but you can add easily your own [keybinding](https://code.visualstudio.com/docs/getstarted/keybindings).

### Toggle Sidebar and Hide Status Bar now persisted in user settings

We changed some of the toggles in the View menu to update your user settings file instead of persisting their state in the browser. This will make it easier to restore the same state on a different machine by copying the settings file.

The following settings will now be changed in your user settings file when you change them from the View menu:

* `workbench.sideBar.location` to control the position of the Sidebar (left or right)
* `workbench.statusBar.visible` to control the visibility of the Status Bar

In addition, changing the Zoom level from the View menu now writes to `window.zoomLevel`.

### Open multiple files from Quick Open

Thanks to [Will Prater (@wprater)](https://github.com/wprater), you can now open multiple files from Quick Open by just pressing the Right arrow key. This will open the currently selected file in the background and you can continue selecting files from Quick Open.

## Editor

### Keyboard Shortcuts Reference

**Help** > **Keyboard Shortcuts Reference** brings up a printable PDF reference sheet of VS Code command keyboard shortcuts. Keep this reference handy and you'll be a VS Code power user in no time.

Here's it is for macOS:

![keyboard shortcuts pdf](images/1_7/keyboard-shortcuts-pdf.png)

Below are links to the three platform-specific versions:

* [Windows](https://go.microsoft.com/fwlink/?linkid=832145)
* [macOS](https://go.microsoft.com/fwlink/?linkid=832143)
* [Linux](https://go.microsoft.com/fwlink/?linkid=832144)

>**Note:** We also added a menu command to go to the [Introductory Videos](https://code.visualstudio.com/docs/getstarted/introvideos) (**Help** > **Introductory Videos**).

### Format Document / Format Selection

The editor now has two explicit format actions: **Format Document** (`kb(editor.action.formatDocument)`) and **Format Selection** (`kb(editor.action.formatSelection)`).

![format on context menu](images/1_7/format-context-menu.png)

We also added new configuration options to enable/disable the default formatters for JavaScript, TypeScript, JSON, and HTML. Use these options when you have another extension installed that provides formatting for the same language.

We've added a new `Formatters` [category](#formatters-category) to the Marketplace so you can easily search and find source code [formatting extensions](https://marketplace.visualstudio.com/search?target=VSCode&category=Formatters&sortBy=Downloads).

![marketplace formatter extensions](images/1_7/marketplace-formatters.png)

## Languages

### Better JavaScript IntelliSense

>**Note:** This feature was disabled in the 1.7.1 build. Read on to learn what's coming in a future release.

VS Code uses the TypeScript language server to provide IntelliSense in JavaScript files. In the past, you had to create a `jsconfig.json` file. You also had to manually install typings (Type Declaration) files for all the modules and libraries you were using. That's not an easy chore, particularly when you are a pure JavaScript developer.

We talked with our friends on the TypeScript team and they came up with "Automatic Type Acquisition". ATA makes typings files almost invisible. A TypeScript language server that has ATA enabled watches your `package.json` files and automatically installs the typings files of all dependencies in a cache on your file system. It does the same when it finds references to well known client-side libraries. When you then invoke IntelliSense, the TypeScript server uses the typings files in the cache. The cache is shared between all your workspaces. Without a `jsconfig.json` file the TypeScript server assumes that everything in your workspace belongs to the same project.

We think this is so cool that we wanted you to get your hands on it as soon as possible. Our integration of ATA is still rough around the edges, but we decided to enable it by default. Should you experience issues, you can switch it off by setting `typescript.disableAutomaticTypeAcquisition` to `true` in your user settings.

Here are a few points you should be aware of when using ATA:

- ATA is backward compatible. If you have a `typings` folder or you installed typings using `npm`, then those take precedence over the automatically acquired typings files.
- ATA acquires the latest available version of the typings files. If you depend on an older version of a library or module then there can be discrepancies between the actual API exposed by the library or module and the API described in the typings file. If you require a specific version of a typings files, you can install it manually: `npm install @types/<module name>@x.y.z`.
- An increasing number of npm modules bundle their typings files, meaning the only way to acquire the typings is by installing the module. ATA does not install npm modules for you, so you have to do it yourself.
- There is a short delay between when a change is made to the dependencies in a `package.json` file and when the corresponding typings file is acquired and available for IntelliSense.
- In the implementation, the ATA feature first queries npmjs for a module that includes a list of known declaration files. If your project includes any of these modules, their declaration files will be pulled down and added to the global cache.

### CSS completions in HTML

You can now see CSS language features like code completion, validation and color annotation for CSS styles embedded within your HTML.

![css intellisense in html](images/1_7/css-intellisense-in-html.png)

### Improved TypeScript and JavaScript Grammar

TypeScript and JavaScript syntax highlighting is based on the [TypeScript textmate grammar](https://github.com/microsoft/TypeScript-TmLanguage) authored by the TypeScript team.
In the recent months the grammar has been completely rewritten.

The main goal was to tackle as many of reported issues as possible. Additionally, we wanted to provide a better experience with existing color themes by generating scopes closer to what other popular JavaScript grammars such as the [Atom grammar](https://marketplace.visualstudio.com/items?itemName=ms-vscode.js-atom-grammar) do, while still supporting features like the JavaScript React syntax.

As a result of the effort, over 100 issues has been fixed, including the request to report variable and function references. We decided to take advantage of the new scopes and now also colorize variable and function references in our Default Dark and Light Plus themes. We hope you like it!

Note: If you have already been helping us testing the latest grammar using the [Latest TypeScript and JavaScript grammar extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.typescript-javascript-grammar): You can uninstall that extension now.

### Linter Extensions

The `vscode-eslint` and `vscode-tslint` extensions now provide settings to automatically correct fixable warnings on save.

```json
{
    "eslint.autoFixOnSave": true,
    "tslint.autoFixOnSave": true
}
```

Note: This setting is ignored when `files.autoSave` is set to `afterDelay`.

## Extensions

### Keymaps category

Keyboard shortcuts are vital to productivity and changing keyboarding habits can be tough. To help with this, we added two new extensions and introduced a new category `Keymaps` to the Marketplace. The purpose is to make it easier to switch between editors by removing the need to learn new keyboard shortcuts.

We built an extension for [Atom](https://marketplace.visualstudio.com/items?itemName=ms-vscode.atom-keybindings) and [Sublime Text](https://marketplace.visualstudio.com/items?itemName=ms-vscode.sublime-keybindings). We found some of the most popular keyboard shortcuts and would like your feedback on what we're missing. If we missed a keyboard shortcut, please open an issue or make a PR in the repo.

![Keymaps](images/1_7/keymaps.png)

Additionally, you can create your own keymap extension using the `keybindings` contribution point and adding it to the `Keymaps` category.

### Formatters category

Many extensions exist that contribute source code formatting functionality. Some of the most popular formatters have more than 100K installs! We wanted to make it easier for you to find formatters so we added a new category to the Marketplace called, naturally, `Formatters`. [Search](https://marketplace.visualstudio.com/search?target=VSCode&category=Formatters&sortBy=Downloads) on the Marketplace and install a `Formatter` today.

Here are a couple of our favorites:

* [beautify](https://marketplace.visualstudio.com/items?itemName=HookyQR.beautify)
* [XML Tools](https://marketplace.visualstudio.com/items?itemName=DotJoshJohnson.xml)
* [Clang-Format](https://marketplace.visualstudio.com/items?itemName=xaver.clang-format)
* [PHP Formatter](https://marketplace.visualstudio.com/items?itemName=Sophisticode.php-formatter)
* [Stylesheet Formatter](https://marketplace.visualstudio.com/items?itemName=dbalage.vscode-stylesheet-formatter)

### Disable extensions

Often you have more than one workspace and various extensions installed to support these workspaces. Most of the times, these extensions are exclusive to a single workspace and not needed for others. For example, if you have JavaScript and Go language workspaces, you do not need the JavaScript linting extension in a Go workspace and vice-versa. With this release, you can now run only those extensions which you think are necessary for a workspace and disable rest of them. You can also disable extensions for the complete VS Code application, if you do not want to uninstall them and do not want them to run.

![Disable Extension](images/1_7/disableExtension.png)

You can also disable or enable all extensions with few clicks from VS Code.

![Disable All Extensions](images/1_7/disableAll.png)

### Extension packs

Last release we introduced 'Extension Packs' which are extensions that depends on other extensions. In this release, we added a new tab to the extension editor to show these dependencies which gets installed when installing the pack.

![Extension Pack](images/1_7/extensionPack.png)

When you uninstall an extension pack, all its referenced dependency extensions are also uninstalled.

## Node Debugging

### Simplified launch configuration

In order to make setting up Node.js debugging as easy as possible, we've tried to simplify the initial 'launch.json' experience:

* Required but rarely changed attributes are now rendered in gray to make it more obvious that there is no need to change them for the most common scenarios.
* Less frequently used attributes (with good default values) were removed from the initial configurations to reduce the 'noise'. These attributes are still easily discoverable using IntelliSense.
* Comments and a link to online documentation were added.
* Online debugging [documentation](https://code.visualstudio.com/docs/debugtest/debugging#_launch-configurations) has been rewritten and improved.

![launchjson](images/1_7/launchjson.png)

>**Note:** Since we are only able to change the built-in Node.js debugger of VS Code, we are relying on the authors of the other debug extensions to do something similar (if needed).

### Hit count conditional breakpoints

VS Code now supports setting a 'hit count condition' for a breakpoint (in addition to the 'expression condition' introduced some time ago). The 'hit count condition' controls how many times a breakpoint needs to be hit before it will 'break' execution.

![HitCount](images/1_7/hitCount.gif)

Whether a 'hit count condition' is respected and how the exact syntax of the expression looks like depends on the debugger extension used. In this milestone, only the built-in Node.js debugger supports hit counts (but we hope other debugger extensions will follow soon).

The hit count syntax supported by the Node.js debugger is either an integer or one of the operators `<`, `<=`, `=`, `>`, `>=`, `%` followed by an integer.

Some examples:

- `>10` break always after 10 hits
- `<3` break on the first two hits only
- `10` same as `>=10`
- `%2` break on every other hit

### Multiple target debugging

The October release contains an early implementation of 'multi target debugging', that is multiple debug sessions can be active at the same time within a single instance of VS Code (and a single project folder).

This feature helps with the following scenarios:

* Debug an extension and its debug adapter and/or language server at the same time.
* Debug a client and a server from a single project folder.
* Debug a clustered program.

>**Note:** In this milestone the focus was on getting the internal abstractions for multi target debugging right and not on how to surface this in a UI.
So the UI of multi target debugging and how it is set up is highly **experimental** and will definitely change over the next milestones.

To enable 'multi target debugging' create a new launch configuration with a type `composite` and add an array typed attribute `configurationNames`.
Under that attribute list the names of other launch configurations that should be launched in parallel.

After launching the `composite` configuration, the individual sessions show up as top level elements in the CALL STACK view.
Actions (e.g. all actions in the floating debug widget) are always performed on the currently selected session in the CALL STACK view.

![multiDebug](images/1_7/multiDebug.gif)

### Debug settings

We have introduced the following new settings:

* `debug.allowBreakpointsEverywhere` - Allows breakpoints to be set in any file (and not only in those which are explicitly registered). This is useful if you want to debug a new (transpiled) language with an existing debugger (for example, the Node.js debugger).
* `debug.openExplorerOnEnd` - Controls if the Explorer should be automatically opened at the end of a debug session.

## Extension Authoring

### Breaking Change: `MarkedString[]` semantics

API related implementations which return `MarkedString | MarkedString[]` have changed semantics. Each `MarkedString` will be visually separated from the others with a horizontal line. Both the `HoverProvider` and `Decoration` APIs are affected by this.

### Changelogs

You can now display your extension's CHANGELOG.md directly in the extension details view.

We recommend extension authors move any existing changelogs from their extension's README.md to a separate CHANGELOG.md file. Similar to README.md, if the CHANGELOG.md is placed in the extension's workspace root, it will be automatically included by the `vsce` publishing tool.

### Provide menu entries for the editor tab context menu

We added an additional place where extensions can contribute menu entries, the context menu of an editor tab (this also works when tabs are disabled). The related menu path for contributing here is `editor/title/context`.

For example:

```json
"commands": [{
    "command": "doSomething",
    "title": "Do Something"
}],
"menus": {
    "editor/title/context": [
        {
            "command": "doSomething"
        }
    ]
}
```

### onDidChangeVisibleTextEditors event

We added an [`onDidChangeVisibleTextEditors`](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.d.ts#L3383) event which fires whenever the list of visible editors changes.

### Update Now: Using latest vscode.d.ts

As ever, the latest and greatest version of our extension API is defined in the [vscode.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.d.ts) file.
>**Note:** when you update your extension to use an API version 1.7.0 or newer, make sure you use the latest version of the [vscode](https://www.npmjs.com/package/vscode)-node-module (^1.0.3) and make sure to use TypeScript 2.0.

Please read [here](https://code.visualstudio.com/updates/v1_6#_extension-authoring) to learn how to migrate your existing extensions to the latest vscode module.

## New Commands

Key|Command|Command id
---|-------|----------
`kb(workbench.action.toggleEditorGroupLayout)`|Toggle editor group layout|`workbench.action.toggleEditorGroupLayout`
`unassigned`|Toggle Maximized Panel|`workbench.action.toggleMaximizedPanel`
Formatting||
`kb(editor.action.formatDocument)`|Format document|`editor.action.formatDocument`
`kb(editor.action.formatSelection)`|Format selection|`editor.action.formatSelection`
Integrated Terminal||
`kb(workbench.action.terminal.scrollToTop)`|Scroll to top|`workbench.action.terminal.scrollToTop`
`kb(workbench.action.terminal.scrollToBottom)`|Scroll to bottom|`workbench.action.terminal.scrollToBottom`

## Notable Changes

* [929](https://github.com/microsoft/vscode/issues/929): Windows 10: focus is not put to window but taskbar blinks
* [2814](https://github.com/microsoft/vscode/issues/2814): Windows: Reveal in Explorer feature no longer bring the explorer window in front of vscode
* [6466](https://github.com/microsoft/vscode/issues/6466): Open in Command Prompt doesn't open cwd when using cmder
* [10210](https://github.com/microsoft/vscode/issues/10210): Can't copy the values of debugging elements such as exception messages
* [11334](https://github.com/microsoft/vscode/issues/11334) & [13229](https://github.com/microsoft/vscode/issues/13229): Improvements to terminal IME support
* [11431](https://github.com/microsoft/vscode/issues/11431): RPM package lacks shortcut in PATH
* [12036](https://github.com/microsoft/vscode/issues/12036): VS Code is very laggy when used on a system with touchscreens
* [12260](https://github.com/microsoft/vscode/issues/12260): Not enough storage is available to process this command in terminal
* [12969](https://github.com/microsoft/vscode/issues/12969): HTML: comments inside a script tag no longer sensitive to script language
* [13554](https://github.com/microsoft/vscode/issues/13554): Integrated terminal occasionally wraps some lines
* [14102](https://github.com/microsoft/vscode/issues/14102): Fold the default settings by default and remember editor state

Due to [13919](https://github.com/microsoft/vscode/issues/13919), file extension associations in Windows were broken. You'll need to reset your file extension associations to open with VS Code.

These are the [closed bugs](https://github.com/microsoft/vscode/issues?q=is%3Aissue+label%3Abug+milestone%3A%22October+2016%22+is%3Aclosed) and these are the [closed feature requests](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2016%22+is%3Aclosed+label%3Afeature-request) for the 1.7 update.

## Contributions to Extensions

Our team maintains or contributes to a number of VS Code extensions. Most notably:

* [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python)
* [TSLint](https://marketplace.visualstudio.com/items?itemName=eg2.tslint)
* [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
* [Debugger for Chrome](https://marketplace.visualstudio.com/items?itemName=msjsdiag.debugger-for-chrome)
* [VSCodeVim](https://marketplace.visualstudio.com/items?itemName=vscodevim.vim)

## Thank You

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

* [@barakd](https://github.com/barakd):  Git quick open should correct invalid branch names [PR #12194](https://github.com/microsoft/vscode/pull/12194)
* [Christian Alexander (@ChristianAlexander)](https://github.com/ChristianAlexander)
  *  Use default cursor in feedback menu text. #12926 [PR #13719](https://github.com/microsoft/vscode/pull/13719)
  *  Add --disable-gpu option to cli help. (#13706). [PR #13760](https://github.com/microsoft/vscode/pull/13760)
* [Cliff Koh (@cliffkoh)](https://github.com/cliffkoh):  Fix typos and consistency issues [PR #14078](https://github.com/microsoft/vscode/pull/14078)
* [Sergey Shakhnazarov (@daserge)](https://github.com/daserge):  Avoiding "write EPIPE process.send" error on exit [PR #13774](https://github.com/microsoft/vscode/pull/13774)
* [xzper (@f111fei)](https://github.com/f111fei):  Fixed #13670 [PR #13739](https://github.com/microsoft/vscode/pull/13739)
* [@greams](https://github.com/greams):  Add option for --list-extensions arg [PR #13131](https://github.com/microsoft/vscode/pull/13131)
* [Kei Son (@heycalmdown)](https://github.com/heycalmdown):  Add an indicator for the current window [PR #13113](https://github.com/microsoft/vscode/pull/13113)
* [Huachao Mao (@Huachao)](https://github.com/Huachao):  Update handlebars block comments symbols [PR #12271](https://github.com/microsoft/vscode/pull/12271)
* [Michael Hudson (@Huddo121)](https://github.com/Huddo121):  Increase coverage of tests for IAction.isAction() [PR #13992](https://github.com/microsoft/vscode/pull/13992)
* [Yuki Ueda (@Ikuyadeu)](https://github.com/Ikuyadeu):  fix ini #13648 [PR #13923](https://github.com/microsoft/vscode/pull/13923)
* [Jeong Woo Chang (@inspiredjw)](https://github.com/inspiredjw):  Cannot read property 'uri' of null fix [PR #13263](https://github.com/microsoft/vscode/pull/13263)
* [Jess Chadwick (@jchadwick)](https://github.com/jchadwick)
  *  Increasing test coverage for collections [PR #13817](https://github.com/microsoft/vscode/pull/13817)
  *  Adding tests for editor state [PR #13792](https://github.com/microsoft/vscode/pull/13792)
* [Kai Wood (@kaiwood)](https://github.com/kaiwood):  Add setting to prevent copying empty selections [PR #13678](https://github.com/microsoft/vscode/pull/13678)
* [Krzysztof Cieślak (@Krzysztof-Cieslak)](https://github.com/Krzysztof-Cieslak)
  *  Fix #1939 - Show tag name instead of commit in GitStatusbar [PR #12584](https://github.com/microsoft/vscode/pull/12584)
  *  Fix #1798 -  Disable UndoLastCommit when no commits [PR #12583](https://github.com/microsoft/vscode/pull/12583)
  *  Implement Push To Remote  [PR #12550](https://github.com/microsoft/vscode/pull/12550)
  *  Fix #13242 - Escape command title for CodeLens [PR #13244](https://github.com/microsoft/vscode/pull/13244)
* [Michael (@michaelchiche)](https://github.com/michaelchiche):  fix typo in type definition [PR #13159](https://github.com/microsoft/vscode/pull/13159)
* [Michael Chou (@MikeChou)](https://github.com/MikeChou):  Follow symlinks recursively in Linux launch script [PR #14046](https://github.com/microsoft/vscode/pull/14046)
* [Denis Malinochkin (@mrmlnc)](https://github.com/mrmlnc)
  *  [emmet] Support for more languages [PR #12382](https://github.com/microsoft/vscode/pull/12382)
  *  Update vscode-extension schema [PR #12739](https://github.com/microsoft/vscode/pull/12739)
* [Renfred Harper (@renfredxh)](https://github.com/renfredxh):  Enable line highlighting for read only editors [PR #14022](https://github.com/microsoft/vscode/pull/14022)
* [Robin Munn (@rmunn)](https://github.com/rmunn):  Remove incorrect ANSI escape code for LF [PR #13345](https://github.com/microsoft/vscode/pull/13345)
* [Sirisak Lueangsaksri (@spywhere)](https://github.com/spywhere):  Sort the installed extension list [PR #13399](https://github.com/microsoft/vscode/pull/13399)
* [Tereza Tomcova (@the-ress)](https://github.com/the-ress)
  * Call AllowSetForegroundWindow before sending IPC [PR #13255](https://github.com/microsoft/vscode/pull/13255)
  * Convert strings passed from node using UTF-8, not the system code page [Issue #7727](https://github.com/microsoft/vscode/issues/7727)
  * Follow Windows conventions when composing cmdline [Issue #8429](https://github.com/microsoft/vscode/issues/8429)
* [Ivan Samoylenko (@The-Smallest)](https://github.com/The-Smallest):  Typo in project.json [PR #13461](https://github.com/microsoft/vscode/pull/13461)
* [Will Prater (@wprater)](https://github.com/wprater)
  *  fix UI/UX regression [PR #13922](https://github.com/microsoft/vscode/pull/13922)
  *  quick open files in background [PR #13925](https://github.com/microsoft/vscode/pull/13925)
* [Toru Nagashima (@mysticatea)](https://github.com/mysticatea): Fixes zero location support [PR-ESLint #153](https://github.com/microsoft/vscode-eslint/pull/153)
* [Dario Fuzinato (@fussinatto)](https://github.com/fussinatto): Fixing typos in readme [PR-ESLint #157](https://github.com/microsoft/vscode-eslint/pull/157)
* [Morton Fox (@mortonfox)](https://github.com/mortonfox): Fix license link [PR-LSP #74](https://github.com/microsoft/language-server-protocol/pull/74)
* [Asad Saeeduddin (@masaeedu)](https://github.com/masaeedu): Fix typo in protocol documentation [PR-LSP #85](https://github.com/microsoft/language-server-protocol/pull/85)
* [Anton Kosyakov (@akosyakov)](https://github.com/akosyakov)
  * Fixes Microsoft/language-server-protocol #87 [PR-LSP #90](https://github.com/microsoft/language-server-protocol/pull/90)
  * Fixes issues #72 and #78 [PR-LSP #91](https://github.com/microsoft/language-server-protocol/pull/91)
* [Richard Lasjunies (@rlasjunies)](https://github.com/rlasjunies): support of fixes provided by TSLint engine [PR #96](https://github.com/microsoft/vscode-tslint/pull/96)
* [Robert Stoll (@robstoll)](https://github.com/robstoll):  fixed regexp pattern, violated rule was missing [PR #107](https://github.com/microsoft/vscode-tslint/pull/107)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
