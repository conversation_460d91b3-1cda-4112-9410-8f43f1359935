---
Order: 99
TOCTitle: May 2024
PageTitle: Visual Studio Code May 2024
MetaDescription: Learn what is new in the Visual Studio Code May 2024 Release (1.90)
MetaSocialImage: 1_90/release-highlights.png
Date: 2024-6-5
DownloadVersion: 1.90.2
---
# May 2024 (version 1.90)

**Update 1.90.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22May+2024+Recovery+2%22+is%3Aclosed).

**Update 1.90.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22May+2024+Recovery+1%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the May 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [Editor tabs multi-select](#editor-tabs-multi-select) - Select and perform actions on multiple tabs simultaneously.
* [Profiles](#configure-custom-profile-for-new-window) - Open new windows with your preferred profile.
* [Editor actions](#always-show-editor-actions) - Immediately access editor actions across editor groups.
* [Copilot extensibility](#chat-and-language-model-api) - Build AI into your extensions with the Chat and Language Model API.
* [VS Code Speech](#vs-code-speech) - Automatically read out Copilot Chat responses with text-to-speech.
* [Find in notebooks](#find-in-cell-selection) - Restrict search to selected cells in notebooks.
* [Chat context](#attach-context-to-chat) - Quickly attach different types of context in chat.
* [IntelliSense in chat responses](#intellisense-in-chat-code-blocks) - Better understand generated code with IntelliSense.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Set keybindings from the accessibility help dialog

Accessibility help dialogs give you an overview of important commands for a feature or view. When a command lacks a keybinding assignment, you can now configure it from within the accessibility help dialog with `kb(editor.action.accessibilityHelpConfigureKeybindings)`.

### Experimental: signal delay settings

When the setting <code codesetting="accessibility.signalOptions.debouncePositionChanges">Debounce position changes</code> is enabled, you can use the setting <code codesetting="accessibility.signalOptions.delays">Signal options delays</code> to customize the debouncing time for various accessibility signals.

## Workbench

### Editor tabs multi select

You can now select multiple tabs simultaneously, enabling you to apply actions to multiple editors at once. This new feature enables you to move, pin, or close several tabs with a single action. To add another tab to your selection, use `Ctrl + Click` (`Cmd + Click` on macOS). To select a range of tabs, use `Shift + Click`.

<video src="images/1_90/tabs-multi-select.mp4" title="Select multiple tabs and move/close them" autoplay loop controls muted></video>

### Always show editor actions

We are introducing the <code codesetting="workbench.editor.alwaysShowEditorActions">Always Show Editor Actions</code> setting. When you enable this setting, the editor title actions of each editor group are always shown, regardless of whether the editor is active or not.

When the setting is not enabled (default value), the editor actions are only shown when the editor is active:

![Editor Actions of each group when the setting is disabled](images/1_90/editor-actions-default.png)

If you enable the setting, the editor actions are always available, even when the editor is not active:

![Editor Actions of each group when the setting is enabled](images/1_90/editor-actions-always.png)

### Set disable-lcd-text as a runtime argument

With `disable-lcd-text`, you can disable RGB subpixel rendering on Windows. The `disable-lcd-text` setting is now supported as a runtime argument in the `argv.json` file. Previously, it was only available as an undocumented CLI flag. Use the **Preferences: Configure Runtime Arguments** command to configure the runtime arguments.

In the following image you can see a side-by-side comparison, where on the left side `disable-lcd-text` is `true`, and on the right side it is `false`.

![Comparison showing that disable-lcd-text disables RGB subpixel rendering](images/1_90/h-side-by-side.png)

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### Configure custom profile for new window

Previously, when you opened a new VS Code window, it used the [profile](https://code.visualstudio.com/docs/configure/profiles) of the active window or the default profile, if there was no active window. You can now specify which profile should be used when opening a new window by configuring the <code codesetting="window.newWindowProfile">window.newWindowProfile</code> setting.

![Configure custom profile for new window](images/1_90/profile-new-window.png)

## Source Control

### Focus input/resource group commands

This milestone, we have added several workbench commands, so that you can create keyboard shortcuts for them:

- Focus on the next or previous source control input field: `workbench.scm.action.focusNextInput`, `workbench.scm.action.focusPreviousInput`
- Focus on the next or previous resource group within a repository: `workbench.scm.action.focusNextResourceGroup`, `workbench.scm.action.focusPreviousResourceGroup`

## Notebooks

### Find in cell selection

When you're in a notebook, you can now use the Find control to search within specific ranges of selected cells.

After you set <code codesetting="notebook.experimental.find.scope.enabled">notebook.experimental.find.scope.enabled</code> to `true`, the **Find in Cell Selection** toggle will be available in the Find control. You can then select a range of cells and either open the Find control or, if it's already open, select the "Find in Cell Selection" button.

<video src="images/1_90/notebook-find-in-cell-selection.mp4" title="Notebook Find in Cell Selection" autoplay loop controls muted></video>

### Notebook Format Code Actions

Notebooks now support a new kind of Code Action, which is defined with the `notebook.format` Code Action Kind prefix. These Code Actions can be triggered automatically via an explicit formatting request (using the command **Notebook: Format Notebook**) or a formatting on save request.

These can be used to provide more powerful formatting, through the use of Workspace Edits and Notebook Edits. To get started, check out an example extension in the [vscode-extension-samples](https://github.com/microsoft/vscode-extension-samples/tree/main/notebook-format-code-action-sample) repository.

## Terminal

### ⚠️ Removal of the canvas renderer

The canvas renderer was deprecated in the VS Code 1.89 release and is now removed completely. This means that on the small number of machines that do not support WebGL2, the terminal now uses the DOM-based renderer. You can read more about GPU acceleration in the [terminal documentation](https://code.visualstudio.com/docs/terminal/appearance#_gpu-acceleration).

### Rescaling overlapping glyph in the terminal

The setting <code codesetting="terminal.integrated.rescaleOverlappingGlyphs">terminal.integrated.rescaleOverlappingGlyphs</code>, introduced as a preview feature in the VS Code 1.88 release, is now enabled by default. This feature rescales glyphs that overlap following cells that are intended to cover ambiguous-width characters, and which might have font glyphs that don't match what the backing pty/unicode version thinks it is.

For example, in most fonts the roman numeral unicode characters (`U+2160+`) typically takes up multiple cells, so they are rescaled horizontally when this setting is enabled.

Without rescaling:

![Before the glyphs for Ⅷ and Ⅻ depending on the font would always overlap the following cells](images/1_88/terminal-glyphs-before.png)

With rescaling:

![After the glyphs for Ⅷ and Ⅻ depending on the font are rescaled horizontally to fit a single cell](images/1_88/terminal-glyphs-after.png)

## Contributions to extensions

### GitHub Copilot

#### Attach context to chat

To make your chat prompts more specific, you can add context to your chat messages. You can now attach more types of context to a chat message, such as workspace symbols. Previously, you used the '#' symbol to reference a file, or the current selection. Now, you can attach context  to a chat message by selecting the 📎 icon in the Chat view input field, or by typing `kb(workbench.action.chat.attachContext)`.

> **Tip**: Use the right arrow key to quickly attach context in the background while you keep the context picker open. When you're in the editor, you can also right-click on a selection and choose **Copilot > Add Selection to Chat**.

<video src="images/1_90/chat-context-attachments.mp4" title="Attach context to chat" autoplay loop controls muted></video>

#### Ask questions using Bing search and enterprise knowledge bases

[GitHub Copilot Enterprise](https://docs.github.com/en/enterprise-cloud@latest/copilot/github-copilot-enterprise/overview/about-github-copilot-enterprise) users in VS Code can now ask questions that are enriched with context from web results and your enterprise's [knowledge bases](https://docs.github.com/en/enterprise-cloud@latest/copilot/github-copilot-enterprise/copilot-chat-in-github/managing-copilot-knowledge-bases). To try out this functionality, install the latest pre-release of Copilot Chat.

In the Chat view, you can ask questions like `@github What is the latest LTS of Node.js? #web` to take advantage of web search. Any search results referenced by Copilot are displayed in the `Used References` section of the chat response.

![Web search results in Copilot Chat](images/1_90/copilot-enterprise-bing-search.png)

You can also ask questions about your enterprise's knowledge bases, which are collections of Markdown repositories containing documentation, directly from VS Code. Simply type `@github #kb` to pick from the knowledge bases available to you. Similarly, any knowledge base snippets referenced by Copilot are displayed in the `Used References` section of the chat response.

This enables Copilot Enterprise users to combine search results and internal documentation with editor context by using existing chat variables, such as `#file` and `#selection`. Please try it out and share your feedback with us at https://github.com/microsoft/vscode-copilot-release!

#### IntelliSense in chat code blocks

We now support basic IntelliSense within Copilot-generated code blocks. This lets you use many of the same IntelliSense tools that you might already use in the editor and can help you better understand the generated code.

![Hover IntelliSense in Copilot chat code block](images/1_90/copilot-hover-intellisense.png)

The supported IntelliSense features include:

- Go to definition by using `kbstyle(ctrl)` `kbstyle(click)` / `kbstyle(cmd)` `kbstyle(click)` or `kb(editor.action.revealDefinition)`
- Hovers
- Go to implementation
- Go to type definition

IntelliSense can even be used with `@workspace` to learn about any workspace symbols that are used in Copilot responses.

<video src="images/1_90/copilot-workspace-intellisense.mp4" title="IntelliSense on a @workspace code block" autoplay loop controls muted></video>

IntelliSense for TypeScript, JavaScript, HTML, and CSS code blocks is available out of the box. For additional language support, try installing an extension for that language, although not every language extension already has support for code block IntelliSense. Please file feature requests for any languages that aren't yet supporting this.

#### Improved links in chat responses

We improved chat responses by adding links for file names and symbols. By selecting these links, you can navigate to the corresponding file or symbol in the editor.

![Clickable links for symbols used with /explain](images/1_90/copilot-link-explain.png)

![Clickable file and symbol links for an @workspace question](images/1_90/copilot-link-workspace.png)

#### Roam active chat between inline chat and Chat view

You can now move a chat request that is completed or still active from inline chat to the Chat view. You might use this feature to clean up inline chat and move conversations to a more persistent place. To move a request, select the chat icon next to the chat input box.

![Move a chat conversation from inline chat to the Chat view](images/1_90/move-chat-conversation-to-chat-view.png)

#### Automatic rename suggestions

If you use the [Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension, the Copilot-powered rename suggestions are now triggered automatically when you rename a symbol. You can turn this feature off by using the setting <code codesetting="github.copilot.renameSuggestions.********************">github.copilot.renameSuggestions.********************</code>.

<video src="images/1_90/rename-suggestions.mp4" title="Automatically-triggered rename suggestions" autoplay loop controls muted></video>

### Python

#### Testing bug fixes

The experience with pytest when using the Python Testing Rewrite has been improved to offer better support for setting pytest's cwd when it is adjacent to the VS Code workspace root, and for displaying parameterized tests on the test explorer when function names are repeated across classes.

Additionally, we reduced some test discovery failure scenarios by adding the system configuration script path to PATH to enable shell for test execution.

#### Experimental: Python Native REPL with Intellisense and Syntax Highlighting

You can now run your Python code in an editor-like REPL environment equipped with features like Intellisense and syntax highlighting to make interactions with the REPL more efficient. To enable this feature, set `"python.REPL.sendToNativeREPL": true` in your `settings.json` file. This will execute code in the Python Native REPL on `kbstyle(Shift+Enter)` and **Run Selection/Line**.

You can opt to use the in-terminal Python REPL ( >>> ) by setting `"python.REPL.sendToNativeREPL": false` in your `settings.json`. Moreover, the Python Native REPL will smartly execute on `kbstyle(Enter)`, similar to Python’s original interactive interpreter, if you add the setting `"interactiveWindow.executeWithShiftEnter": false`, in your `settings.json`.

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which enables you to work on, create, and manage pull requests and issues. Review the [changelog for the 0.90.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0900) release of the extension to learn about the other highlights.

### VS Code Speech

We added support for text-to-speech capabilities to the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension. A new setting <code codesetting="accessibility.voice.autoSynthesize">accessibility.voice.autoSynthesize</code> can be enabled to automatically read out aloud Copilot Chat responses when voice was also used as input.

<video src="images/1_90/text-to-speech.mp4" title="Text to Speech in Copilot Chat" autoplay loop controls muted></video>

Notice how the microphone icon in the input field changes, indicating that text is read out aloud. To interrupt the synthesis, select the icon or press `kb(workbench.action.speech.stopReadAloud)`.

Each chat response also shows a new speaker icon, so that you can selectively read out a response aloud:

![Text to Speech for a Chat Response](images/1_90/text-to-speech.png)

You can change the language that is used for text-to-speech via the existing <code codesetting="accessibility.voice.speechLanguage">accessibility.voice.speechLanguage</code> setting.

## Preview Features

### VS Code-native IntelliSense for PowerShell

In addition to several reliability improvements, we made the following changes to PowerShell IntelliSense in the terminal:

- `terminal.integrated.shellIntegration.suggestEnabled` has changed to `terminal.integrated.suggest.enabled`
- The new `terminal.integrated.suggest.quickSuggestions` controls whether suggestions are shown when you type after whitespace
- The new `terminal.integrated.suggest.suggestOnTriggerCharacters` controls whether suggestions are shown when you type `/`, `\` or `-`

### TypeScript 5.5

We continued improving our support for the upcoming TypeScript 5.5 release. Check out the [TypeScript 5.5 beta blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-5-5-beta/) and [iteration plan](https://github.com/microsoft/TypeScript/issues/57475) for details on this release.

To start using the TypeScript 5.5 beta, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with TypeScript 5.5.

### Issue Reporter for Web

We have improved the issue reporting flow in VS Code for the Web to match what users currently have on desktop. Selecting **Help: Report Issue** opens the issue reporter page in a new window, where users can select a bug type, source, and extension if necessary. Extension information, system information, and more is automatically attached to the issue that is created in GitHub.

This feature is currently disabled by default on this release, but please share feedback on it by turning on the <code codesetting="issueReporter.experimental.webReporter">issueReporter.experimental.webReporter</code> setting.

## Extension authoring

### Use esbuild for extensions

The `yo code` extension generators for `TypeScript` and `Web` now have an option to use `esbuild` as bundler. When you select `esbuild`, this creates a `esbuild.js` build script and adds script entries in `package.json` and build tasks in `.vscode/tasks.json`.

To use `esbuild` in existing extensions, check out the [bundling extensions](https://code.visualstudio.com/api/working-with-extensions/bundling-extension) and the [web extensions](https://code.visualstudio.com/api/extension-guides/web-extensions) guides.

You can find a sample project at [vscode-extension-samples/esbuild-sample](https://github.com/microsoft/vscode-extension-samples/tree/main/esbuild-sample).

### Chat and Language Model API

We have finalized APIs that enable extensions to participate in chat and to access language models. See the [extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/chat-sample) and the [chat extensions doc page](https://code.visualstudio.com/api/extension-guides/chat) for more information, or watch the [Enhancing VS Code extensions with GitHub Copilot](https://www.youtube.com/watch?v=YI7kjWzIiTM) talk we delivered at the Microsoft Build conference.

> **Important**: These APIs are finalized but are currently only available in VS Code Insiders.

#### Chat Participants

The Chat Participants API enables extensions to extend GitHub Copilot Chat with a chat participant that can be invoked in the chat input field with `@`. The participant can reply to user requests with markdown, a file tree, buttons to run VS Code commands, or other types of content.

![Chat Participant example in the Chat view](images/1_90/chat-example.png)

#### Language Model

The Language Model API enables access to Copilot's chat models, such as gpt-3.5 and gpt-4. This API can be used for chat participants but also to enrich other features. The API is built around [`LanguageModelChat`](https://github.com/microsoft/vscode/blob/54dd0ecc653b89e6032369c6f4b1808bc8e37ec5/src/vscode-dts/vscode.d.ts#L19032) objects, which are used for chat requests and for counting tokens.

The only way to access chat objects is the `vscode.lm.selectChatModels` function. The function accepts a selector to narrow down on different properties of chat models, for example by vendor, family, version, or identifier. The values are relatively free-form and must be looked up in the documentation of the extensions that provide them. Today, only the Copilot Chat extension contributes chat models. It uses the `copilot` vendor and current families are `gpt-3.5-turbo` and `gpt-4` but are subject to change.

The snippet shows how to select all chat models from the `copilot`-vendor:

```ts
const models = await vscode.lm.selectChatModels({
	vendor: 'copilot',
});

if(models.length === 0) {
	// no models available
	return;
}
```

Two things are very **important** when calling `selectChatModels`

1. The function returns an _empty_ array if no models are available and extensions must handle this case.
2. Copilot's chat models require consent from the user before an extension can use them. Consent is implemented as authentication dialog. Because of that, `selectChatModels` should be called as part of a user-initiated action like a command, and not "out of the blue".

With a chat object at hand, extensions can now use it to send chat requests. The following snippet shows how to send a chat request and process the response stream.

```ts
// take the first model and say "Hello"
const [chat] = models;
const messages = [vscode.LanguageModelChatMessage.User('Hello')];
const response = await chat.sendRequest(messages);

// the response is always an async iterable that can be consumed with for-await
for await (const part of response.text) {
	console.log(part)
}
```

This is the gist of the language model API. Refer to the [extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/chat-sample) for a more complete example. Stay tuned for more samples, documentation, and further extensions of the API.

The Java extension for VS Code is already using the Language Model API to provide Copilot-based rewrite capabilities for your Java code. Learn more about these updates in the [Java in Visual Studio Code May 2024](https://devblogs.microsoft.com/java/java-on-visual-studio-code-update-may-2024/) blog post.

##### `@vscode/prompt-tsx` library

To aid with developing GitHub Copilot extensions for VS Code, we've developed and published a TSX-based library for declaring complex prompts and converting them to chat messages, subject to your LLM's context window limits. In developing this, we took inspiration from Anysphere's [`priompt`](https://github.com/anysphere/priompt) library. If you're an extension author who is planning to use the chat and language model APIs, consider trying out the latest alpha release of this library: [@vscode/prompt-tsx](https://www.npmjs.com/package/@vscode/prompt-tsx).

### Extending GitHub Copilot through GitHub Apps

It is also possible to extend GitHub Copilot by contributing a GitHub App. This GitHub App can contribute a chat participant in the Chat view, which you can invoke with `@`. A GitHub App is backed by a service and works across all GitHub Copilot surfaces, such as github.com, Visual Studio, or VS Code. GitHub Apps do not have full access to the VS Code API. To extend GitHub Copilot through a GitHub App, you should join the [Copilot Partner Program](https://github.com/features/preview/copilot-partner-program). You can learn more by watching the [Extending GitHub Copilot](https://build.microsoft.com/en-US/sessions/0121ee70-fd82-419f-a7ad-75bbb70cfd41) talk we delivered at the Microsoft Build conference.

### Debug Stack Focus API

VS Code now exposes what stack frame and thread are focused in the Debug view via a new API. `vscode.debug.activeStackItem` retrieves what stack item (thread or stack frame) is currently focused, and `vscode.debug.onDidChangeActiveStackItem` is an event that fires when that changes.

This is useful in conjunction with APIs that extend VS Code's debug capabilities, such as ones that use the `DebugAdapterTracker`. Learn more about creating a [debugger extension](https://code.visualstudio.com/api/extension-guides/debugger-extension).

### `TestRunRequest.preserveFocus` API

Previously, test runs that were triggered by extensions would never move focus into the Test Results view in the same way that UI-initiated runs did. This behavior is now configurable via a `preserveFocus` flag that can be set when creating a `TestRunRequest`. This flag defaults to `true` to maintain backwards compatibility.

## Proposed APIs

### Attributable test coverage

We're working on an API that enables attributing test coverage on a per-test basis. This lets users see which tests ran which code, filtering both the coverage shown in the editor and that in the **Test Coverage** view. Check [vscode#212196](https://github.com/microsoft/vscode/issues/212196) for more information and updates.

### Hover Verbosity Level

Last milestone, a new API was proposed to provide hovers for which the verbosity can be increased or decreased. This milestone, the API has changed so that the `HoverVerbosityRequest` uses a `verbosityDelta` to signal the relative increase or decrease in the hover verbosity level. Previously, the `HoverVerbosityRequest` used an enum `HoverVerbosityAction` to signal if the verbosity should be increased or decreased.

## Engineering

### Tracking memory efficiency on startup

We measure startup performance of VS Code insiders every day across Windows, macOS and Linux. Our main interest is how fast startup is until a text file is opened.

This month we added another metric that we plan to improve to make startup even faster: how much memory do we consume and how much of that memory ends up being garbage collected by V8. If we can bring that number down, startup time will be less affected by V8 garbage collection runs.

![Memory consumption statistics for VS Code](images/1_90/memory-perf.png)

### Electron 29 update

In this milestone, we are promoting the Electron 29 update to users on our stable release. This update comes with Chromium 122.0.6261.156 and Node.js 20.9.0. We want to thank everyone who self-hosted on Insiders builds and provided early feedback.

## Notable fixes

* [212386](https://github.com/microsoft/vscode/issues/212386) Local history: does not preserve entries from previously deleted file
* [213645](https://github.com/microsoft/vscode/issues/213645) Aux window does not work in Firefox
* vscode-js-debug#[2000](https://github.com/microsoft/vscode-js-debug/pull/2000)/[2002](https://github.com/microsoft/vscode-js-debug/pull/2002) the JavaScript debugger is faster, especially dealing with source map renames

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@starball5 (starball)](https://github.com/starball5)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@BrunoSoaresEngineering](https://github.com/BrunoSoaresEngineering): feat(markdown-language-features): #208398 add avif as image extension [PR #212547](https://github.com/microsoft/vscode/pull/212547)
* [@bsShoham (Shoham Ben Shitrit)](https://github.com/bsShoham): remove global enablement message [PR #213128](https://github.com/microsoft/vscode/pull/213128)
* [@CharlesHGong (Hanning Gong (Charles))](https://github.com/CharlesHGong): Fix an issue where defaultLinesDiffComputer does not pass in the timeout variable [PR #213035](https://github.com/microsoft/vscode/pull/213035)
* [@cpendery (Chapman Pendery)](https://github.com/cpendery): refactor: support dynamic terminal prompt detection without regex on windows [PR #211382](https://github.com/microsoft/vscode/pull/211382)
* [@DatN99 (Dat Nguyen)](https://github.com/DatN99): Added setting for notebook cell markdown lineheight [PR #212531](https://github.com/microsoft/vscode/pull/212531)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Make codelenses work after switching from webview editor (fix #198309) [PR #211999](https://github.com/microsoft/vscode/pull/211999)
  * Revive `TimelineChangeEvent.uri` if passed in `TimelineProvider.onDidChange` event [PR #212927](https://github.com/microsoft/vscode/pull/212927)
* [@kdy1 (Donny/강동윤)](https://github.com/kdy1): feat: Use official json schema for SWC [PR #212158](https://github.com/microsoft/vscode/pull/212158)
* [@mahmoudsalah1993 (Mahmoud Salah)](https://github.com/mahmoudsalah1993): Fire onDidRegisterAllSupported executions if any execution type is re… [PR #212163](https://github.com/microsoft/vscode/pull/212163)
* [@Maximetinu (Miguel Medina Ballesteros)](https://github.com/Maximetinu): Add `AccessibilitySignal.terminalCommandSucceeded` and `success.mp3` (issue #178989)  [PR #204430](https://github.com/microsoft/vscode/pull/204430)
* [@OccasionalDebugger](https://github.com/OccasionalDebugger)
  * Respect stackframe deemphasize in getTopStackFrame [PR #211855](https://github.com/microsoft/vscode/pull/211855)
  * Pass full function breakpoint options from plugin [PR #211895](https://github.com/microsoft/vscode/pull/211895)
* [@pouyakary (Pouya Kary ✨)](https://github.com/pouyakary): Feat: Bolder Typeface + Configurable Letter Spacing for Minimap's Section Header Labels ✨ [PR #209990](https://github.com/microsoft/vscode/pull/209990)
* [@sean-mcmanus (Sean McManus)](https://github.com/sean-mcmanus): Add /** */ to cpp/language-configurations.json [PR #211202](https://github.com/microsoft/vscode/pull/211202)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke)
  * fix: dispose template data disposables in source column renderer [PR #202618](https://github.com/microsoft/vscode/pull/202618)
  * feature: enable typescript isolated modules [PR #212913](https://github.com/microsoft/vscode/pull/212913)
  * Add editor.findMatchForeground [PR #213497](https://github.com/microsoft/vscode/pull/213497)
  * fix wrong colors when editor findMatchForeground is not defined [PR #213686](https://github.com/microsoft/vscode/pull/213686)
* [@walkerdb (Walker Boyle)](https://github.com/walkerdb): fix: tsserver no longer crashes when log path includes spaces [PR #212752](https://github.com/microsoft/vscode/pull/212752)
* [@wenfangdu (Wenfang Du)](https://github.com/wenfangdu): Add 'git-rebase-todo' to COMMON_FILES_FILTER in WorkspacesHistoryMainService [PR #211614](https://github.com/microsoft/vscode/pull/211614)
* [@Yesterday17 (Yesterday17)](https://github.com/Yesterday17): fix: remove temp dir if extension is installed by another source [PR #213379](https://github.com/microsoft/vscode/pull/213379)

Contributions to `vscode-eslint`:

* [@sapegin (Artem Sapegin)](https://github.com/sapegin): feat: Allow eslint.rules.customizations to target all fixable rules [PR #1841](https://github.com/microsoft/vscode-eslint/pull/1841)

Contributions to `vscode-extension-samples`:

* [@moushicheng (某时橙)](https://github.com/moushicheng): fix: lsp-embedded-language-serviceadd  add activationEvents to invoke  client [PR #936](https://github.com/microsoft/vscode-extension-samples/pull/936)

Contributions to `vscode-generator-code`:

* [@1chooo (Hugo ChunHo Lin)](https://github.com/1chooo): Remove Unnecessary Spaces in `ext-command-ts/vsc-extension-quickstart.md` [PR #467](https://github.com/microsoft/vscode-generator-code/pull/467)
* [@k35o (k8o)](https://github.com/k35o): convert spaces to tabs in files in `vscode` folder on `templates` folder [PR #458](https://github.com/microsoft/vscode-generator-code/pull/458)

Contributions to `vscode-hexeditor`:

* [@lorsanta (Lorenzo Santangelo)](https://github.com/lorsanta): add copy selection as different formats and paste hexstring support [PR #498](https://github.com/microsoft/vscode-hexeditor/pull/498)
* [@tomilho (Tomás Silva)](https://github.com/tomilho): Add copyOffsetAsHex/Dec [PR #521](https://github.com/microsoft/vscode-hexeditor/pull/521)

Contributions to `vscode-languageserver-node`:

* [@hyangah (Hyang-Ah Hana Kim)](https://github.com/hyangah): Add SemanticTokenTypes.label [PR #1423](https://github.com/microsoft/vscode-languageserver-node/pull/1423)
* [@imbant (imbant)](https://github.com/imbant): fix “Semantic tokens that are not in ascending order will not be highlighted” [PR #1467](https://github.com/microsoft/vscode-languageserver-node/pull/1467)
* [@rchiodo (Rich Chiodo)](https://github.com/rchiodo): Support pulling diagnostics for notebooks too [PR #1465](https://github.com/microsoft/vscode-languageserver-node/pull/1465)

Contributions to `vscode-mypy`:

* [@hamirmahal (Hamir Mahal)](https://github.com/hamirmahal): fix: deprecated document getting usage [PR #302](https://github.com/microsoft/vscode-mypy/pull/302)

Contributions to `vscode-remote-try-dotnet`:

* [@cmaneu (Christopher MANEU)](https://github.com/cmaneu): Migrate demo app to .NET 6 [PR #31](https://github.com/microsoft/vscode-remote-try-dotnet/pull/31)

Contributions to `language-server-protocol`:

* [@asukaminato0721 (Asuka Minato)](https://github.com/asukaminato0721): add-make-lsp [PR #1941](https://github.com/microsoft/language-server-protocol/pull/1941)
* [@fbricon (Fred Bricon)](https://github.com/fbricon): Add LSP4IJ client to tools.md [PR #1940](https://github.com/microsoft/language-server-protocol/pull/1940)
* [@macnetic (Magnus Oksbøl Therkelsen)](https://github.com/macnetic): Add Verible language server for SystemVerilog [PR #1929](https://github.com/microsoft/language-server-protocol/pull/1929)
* [@ssbarnea (Sorin Sbarnea)](https://github.com/ssbarnea): Correct link to Ansible Language Server [PR #1930](https://github.com/microsoft/language-server-protocol/pull/1930)
* [@wiremoons (Simon Rowe)](https://github.com/wiremoons): Update servers.md - add OLS for Odin language [PR #1931](https://github.com/microsoft/language-server-protocol/pull/1931)
* [@ybiquitous (Masafumi Koba)](https://github.com/ybiquitous): Add LanguageServer::Protocol in Ruby to SDKs [PR #1937](https://github.com/microsoft/language-server-protocol/pull/1937)

Contributions to `monaco-editor`:

* [@htcfreek (Heiko)](https://github.com/htcfreek): Add extension to `csp.contribution.ts` [PR #4504](https://github.com/microsoft/monaco-editor/pull/4504)
* [@jakebailey (Jake Bailey)](https://github.com/jakebailey): Call clearFiles on internal EmitOutput diagnostics, pass args down [PR #4482](https://github.com/microsoft/monaco-editor/pull/4482)
* [@johnyanarella (John Yanarella)](https://github.com/johnyanarella): Update TypeScript to TS 5.4.5 in all projects, vendored files [PR #4305](https://github.com/microsoft/monaco-editor/pull/4305)
* [@samstrohkorbatt](https://github.com/samstrohkorbatt): Adding Python f-string syntax support [PR #4401](https://github.com/microsoft/monaco-editor/pull/4401)


<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
