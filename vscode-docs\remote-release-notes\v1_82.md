# August 2023 (version 1.82)

## Core

### serve-web CLI command

A standalone VS Code server can be started using the `serve-web` command on the VS Code CLI. By default, it'll serve a web experience on your localhost.

Note that in order to access the server on non-localhost origins, you will need to serve the UI over TLS using a reverse proxy server such as [<PERSON><PERSON><PERSON>](https://caddyserver.com/).

## Tunnels

Several long-standing bugs that caused connection unreliability have been fixed this iteration.

## Dev Containers

### Install Docker in WSL (Windows Subsystem for Linux)

On Windows, you can now install Docker packages in WSL with the command **Dev Containers: Install Docker in WSL**. If you don't have Docker installed yet, we prompt you to install it in WSL.

We hope this will make it easier to get started with Dev Containers on Windows.

### Prebuild Guide

Getting Dev Containers up and running for your projects is exciting - you've unlocked environments that include all the dependencies your projects need to run. Once your dev container has everything it needs, you might start thinking more about ways to optimize it, especially if it takes a while to build.

After configuring your dev container, a great next step is to **prebuild your image**.

We've published a [new guide](https://containers.dev/guide/prebuild) all about prebuilding your dev containers, where we'll explore what it means to prebuild an image and the benefits of doing so, such as speeding up your workflow, simplifying your environment, and pinning to specific versions of tools.
