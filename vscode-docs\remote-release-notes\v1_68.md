# May 2022 (version 1.68)

## Development Container specification

Our development container teams across Microsoft and GitHub continue active development on the new [Dev Container specification](https://github.com/devcontainers/spec). This iteration had several exciting highlights with the spec and reference implementation, and you may read more in the [main VS Code release notes](https://code.visualstudio.com/updates/v1_68#_development-container-specification).

## SSH (0.82.x)

### Localization

The [Remote - SSH](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh) extension is now localized! This means the settings commands and other text from the extension will be translated to your preferred language if you have a [Language Pack](https://marketplace.visualstudio.com/search?term=language%20pack&target=VSCode&category=All%20categories) installed and set active in VS Code.

### Enable external SSH_ASKPASS

SSH_ASKPASS is an environment variable used by the SSH binary on your machine, OpenSSH, to configure the application that should handle authentication for connection attempts.

The Remote - SSH extension utilizes SSH_ASKPASS in local server mode, enabled with the setting `remote.SSH.useLocalServer`, to set the SSH_ASKPASS variable to VS Code. This designates VS Code as the application to handle authentication, which causes prompts to show up inside VS Code. However, you might not want VS Code to set SSH_ASKPASS if you have another application that you would like to handle authentication, such as you have a YubiKey and you'd like to authenticate with it instead.

To prevent VS Code from overriding the SSH_ASKPASS variable you had set, you can use the experimental setting `remote.SSH.externalSSH_ASKPASS`, while `remote.SSH.useLocalServer` is true, to keep and use your own SSH_ASKPASS.

## WSL (0.68.3)

### Localization

The [WSL](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl) extension 0.68.3 and newer now come with translated labels for Czech, German, Spanish, French, Italian, Japanese, Korean, Polish, Brazilian Portuguese, Russian, Turkish and Simplified and Traditional Chinese.

Configure your display language in VS Code with the **Configure Display Language** command.
