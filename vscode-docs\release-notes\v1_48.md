---
Order:
TOCTitle: July 2020
PageTitle: Visual Studio Code July 2020
MetaDescription: Learn what is new in the Visual Studio Code July 2020 Release (1.48)
MetaSocialImage: 1_48/release-highlights.png
Date: 2020-8-13
DownloadVersion: 1.48.2
---
# July 2020 (version 1.48)

**Update 1.48.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22July+2020+Recovery%22+is%3Aclosed+).

**Update 1.48.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22July+2020+Recovery+2%22+is%3Aclosed+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the July 2020 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Settings Sync to Stable](#settings-sync)** - Settings Sync is now available for preview in stable.
* **[Updated Extensions view menu](#extensions-overflow-menu-cleanup)** - Simplified menu with additional filtering options.
* **[New Git View submenus](#git-overflow-menu-cleanup)** - Refactored overflow menu for Git in the Source Control view.
* **[Updated in-browser debugging](#debug-open-link-command)** - Debug in the browser without writing a launch configuration.
* **[Publish a public repository](#github-publish-to-a-public-repository)** - Choose whether to publish to a public or private GitHub repository.
* **[Notebook UX updates](#notebooks)** - New Cell menu, enhanced drag and drop.
* **[New Dev Container topics](#new-dev-container-topics)** - Learn how to attach to a container and create a new dev container.
* **[Java lightweight mode](#java-lightweight-mode)** - Start working with your Java source files more quickly.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/events) on Monday, August 17 at 9am Pacific (5pm London), to see a demo of what's new in this release and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Accessibility

This milestone, we again received helpful feedback from our community, which helped us identify and tackle [accessibility issues](https://github.com/microsoft/vscode/issues?q=label%3Aaccessibility+milestone%3A%22July+2020%22+is%3Aclosed).

## Workbench

### Search Editor

We added an **Open Search Editor** command to open an existing search editor if one exists, or to otherwise create a new one. The pre-existing command **Open new Search Editor** has been renamed to **New Search Editor**, and will always create a new Search Editor.

Search Editors also now support custom `search.sortOrder` configurations, such as sorting by file type, modified date, or result count.

### Extensions: Overflow menu cleanup

The Extensions view filter actions are now displayed under a separate filter action (funnel button):

![Extensions filter menu](images/1_48/extensions-filter-menu.png)

*Theme: GitHub Light*

The Extensions view **Views and More Actions** (**...**) menu has been refactored for better organization of extension queries (such as Installed or Recommended) and other actions like **Check for Extension Updates** and **Enable All Extensions**:

![Extensions overflow menu](images/1_48/extensions-overflow-menu.png)

*Theme: GitHub Light*

## Source Control

### Always show repositories

There's a new setting, `scm.alwaysShowRepositories`, that makes the Source Control view always show the repository rows, even if there's only a single repository open:

![Always show repositories](images/1_48/scm-always-show-repositories.gif)

### Better keyboard navigation

As you navigate the Source Control view, pressing `kbstyle(Space)` on a change will now open it as a preview editor and keep the focus in the Source Control view, for easier keyboard navigation.

### Git: Overflow menu cleanup

Thanks to the new submenu proposed API, the Git **View and More Actions** (**...**) menu has been refactored for better organization of several commands:

![Git menu with submenus](images/1_48/git-submenus.png)

### GitHub: Publish to a public repository

When publishing a repository to GitHub, you now have the option to make the repository public, as opposed to the previous default, private:

![Publish public repo](images/1_48/github-publish-public.png)

## Debugging

### UX improvements

* The default value of the `debug.openDebug` setting has been changed to `openOnFirstSessionStart`. As a result, the Debug view will only be automatically opened when the first debug session is started.

* We have updated the debug icon in the Status bar to be better aligned with the icon we use in the Activity bar. This new icon should represent more clearly that the breakpoints will be respected when the program is started this way.

  ![Debug status new icon](images/1_48/debug-status.png)

### Debug: Open Link command

A new **Debug: Open Link** command has been added to quickly debug any URL. Previously, to debug a browser, you had to install the Debugger for Chrome extension and write a `launch.json` config file to debug a page. This command allows you to debug any URL without needing additional launch configurations.

  ![Debug URL](images/1_48/debug-open-link.gif)

*Theme: Earthsong*

If you have a URL selected in your active editor, it will open that automatically. Otherwise, VS Code will prompt you to enter a URL, pre-filling with the URL in your clipboard, if any.

You can adjust the debug configuration used in this command via the `debug.javascript.debugByLinkOptions` setting.

### JavaScript Debugger improvements

The previous release of VS Code included our new JavaScript debugger. We'd like to thank the community for your feedback on it, and this release includes dozens of resulting [fixes and improvements](https://github.com/issues?q=is%3Aopen+is%3Aclosed+label%3Abug+assignee%3Aconnor4312+milestone%3A%22July+2020%22). If you run into any issues with debugging, please make sure to [file an issue](https://github.com/microsoft/vscode-js-debug/issues/new/choose) if you haven't already.

## Browser support

### Text file encoding support

All of the text file encodings of the desktop version of VS Code are now also supported when running in a browser.

![Web encoding support](images/1_48/web-encoding.gif)

As such, the settings `files.encoding` and `files.autoGuessEncoding` can be now configured for web and work, in the same way as in the desktop version.

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### Settings Sync

Settings Sync is now available for preview in the stable release 🎉. Refer to the [user guide](https://aka.ms/vscode-settings-sync-help) for more information & FAQs.

This feature is now called **Settings Sync** and all of its references and settings have been adjusted to align with the new name.

![Settings Sync in gear menu](images/1_48/settings-sync.png)

*Theme: GitHub Light*

**Manual merge**

In this milestone, we made the flow for turning on Settings Sync much smoother when you have done a sync before by introducing the manual merge feature:

![Settings Sync Manual Merge](images/1_48/settings-sync-manual-merge.gif)

**Settings Sync Insiders service**

We added a new Settings Sync Insiders service for VS Code Insiders, separate from the stable build. You can always sync your Insiders with stable using the **Settings Sync: Select Service...** command, which is available only in VS Code Insiders.

![Settings Sync Insiders switch](images/1_48/settings-sync-switch.png)

*Theme: GitHub Light*

### Settings editor improvements

The highlight of this milestone was our work on improving the Settings editor accessibility, which is still a work in progress. Over the past couple iterations, we have been listening to feedback and exploring options for making the Settings editor easier to use with a screen reader. We investigated two possible solutions to these issues and have produced two prototypes, and we are now looking for your feedback to help us learn what works best. Please see GitHub [issue #104318](https://github.com/microsoft/vscode/issues/104318) for details, download links, and to leave your feedback.

### TypeScript 4.0 support

We will be picking up [TypeScript 4.0](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-rc/) next iteration, but the current VS Code release includes support for all the upcoming features if you want to try the [4.0-RC](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-rc/) ahead of time. Some highlights:

* Partial IntelliSense while a project is loading.
* Highlight calls to deprecated symbols in the editor with strikethrough.
* Explain reasons why a given refactoring cannot be applied.
* Improved auto imports - Read more in the [TypeScript 4.0 blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-rc/#smarter-auto-imports).

You can try out these features today by installing the [TypeScript 4.0-rc](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-rc/) into your workspace or by installing the [TypeScript nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

## Contributions to extensions

### Hex Editor

The [HexEditor extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.hexeditor) sees further improvements this iteration with support for copy and paste, file watching, and find and replace. A full list of notable changes can be found in the [CHANGELOG](https://github.com/microsoft/vscode-hexeditor/blob/main/CHANGELOG.md). Any feedback or issues experienced can be filed against the [vscode-hexeditor repository](https://github.com/microsoft/vscode-hexeditor/issues).

**Copy and Paste Support**

We've added the ability to copy values to your clipboard and paste them either inside the editor, or into whatever other source you want.

![Copy and Paste](images/1_48/hex-copy-paste.gif)

**Find and Replace Support**

The hex editor now comes with a similar find and replace widget to the  one found within VS Code. It supports searching hexadecimal fields using wildcards (for example, `FF ?? DD`) and regex searching on the decoded text section.

![Find and Replace](images/1_48/hex-find-replace.gif)

**Multi Select**

You can now use drag and drop selection, `kbstyle(Shift)` and `(Ctrl+click)` selection, and holding `kbstyle(Shift)` and navigating with the keyboard to create a selection.

![Multi Select](images/1_48/hex-selection.gif)

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.48 include:

* Dev Containers: Pull environment variables from login and shell scripts.
* Remote - SSH: Fewer prompts when starting or maintaining an SSH connection.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_48.md).

### Notebooks

The VS Code team is continuing work on native support for Notebooks. To help with development, we've created a [GitHub Issue Notebooks](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-github-issue-notebooks) extension, which lets you search GitHub issues and pull requests. This extension is still in preview and requires using VS Code [Insiders](https://code.visualstudio.com/insiders), but it lets you experience Notebooks first hand, and we welcome your feedback.

**Notebook UX**

We continue to tweak the general UX of Notebooks. This month we made cells even more compact and added a **Cell menu** (**...**) to the toolbar to make cell-related actions even easier to find.

![Cell menu](images/1_48/notebook-ux.png)

**Drag and drop enhancement**

We improved the experience of drag and drop in Notebooks. Dragging a collapsed Markdown cell will move all nested cells inside the folding region.

![Drag grouped cells](images/1_48/notebook-dnd-grouped-cells.gif)

**Reopen with editor**

You can now reopen the Notebook document in the text editor, or any other available editor types directly from the context menu of the editor title.

![Reopen with](images/1_48/notebook-reopen-with.gif)

**Collapsing cells and outputs**

You can now collapse cell outputs and inputs, which are useful when you want to clean up your Notebook view to focus on other parts of it.

![Collapse cells](images/1_48/cell-collapse.gif)

**Jupyter notebooks using the Python Extension**

The Python Extension team has announced a preview for using Jupyter with VS Code's native notebooks. You can read details in their [Python Notebooks blog post](https://devblogs.microsoft.com/python/notebooks-are-getting-revamped/).

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Some updates for this release include:

* Offer to fork a repository when you don't have permissions, when you want to push a commit or **Start Working on an  Issue**.
* Upgraded the version of the GitHub rest API we use for better performance.

To learn about all the new features and updates, you can see the full [changelog for the 0.19.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0190) release of the extension.

## Extension authoring

### Task definition when clause

Task definitions now have an optional `when` property. You can use the `when` property to set a condition for when tasks of that type are available. If there is a case where even if one of your tasks has been configured in the users `tasks.json` file but it shouldn't be available anywhere from the VS Code UI, you can set a `when` condition to prevent the task from showing anywhere.

### Tree view initial visibility

Tree views can set a `visibility` on their `package.json` contribution. The options are `visible` (default), `collapsed`, and `hidden`. The visibility state is only used the first time the user opens a workspace, after that, the visibility is always restored to whatever the user set it to.

### Git: Get remote sources command

The Git extension now exposes a new extension API command to show a remote source Quick Pick interface: `git.api.getRemoteSources`. Read more about this in [issue #102394](https://github.com/microsoft/vscode/issues/102394#issuecomment-658195488).

### Using Authentication Providers

The API for retrieving authentication information from Authentication Providers is now stable. Using `getSession` and `onDidChangeSession`, extensions are able to request login information and listen for changes for the two built- in Authentication Providers, Microsoft and GitHub. See [here](https://github.com/microsoft/vscode-extension-samples/tree/main/github-authentication-sample) for a sample extension on how to authenticate to GitHub.

### New Webview with codicons extension sample

We've added a new [Webview extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/webview-codicons-sample) that includes a reference to our [codicon library](https://code.visualstudio.com/api/references/icons-in-labels). This uses the [vscode-codicons npm package](https://www.npmjs.com/package/vscode-codicons) and references the packages from a webview.

![Webview codicons sample](images/1_48/codicons-webview.png)

## Language Server Index Format

The [TypeScript LSIF generator](https://github.com/microsoft/lsif-node) added support for multi project setups. In addition, memory consumption was optimized, especially around symbols exported via explicit export statements.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Submenus

The menu API has been extended to support submenus. This has already been adopted by the Git extension to clean its overflow menu. Check out the [syntax](https://github.com/microsoft/vscode/issues/100172#issuecomment-*********) as well as the [possibilities](https://github.com/microsoft/vscode/pull/102784#issuecomment-*********) that this API enables.

![Git menu with submenus](images/1_48/git-submenus.png)

### Stopping debug sessions

VS Code uses the function `vscode.debug.startDebugging` for starting debug sessions. In this milestone, we introduced the matching function `vscode.debug.stopDebugging` to stop a specific session (or all sessions). With this new API, it is no longer necessary to use the less predictable **Debug: Stop** command (command ID: `workbench.action.debug.stop`) which only stops the "active" debug session.

## Documentation and extensions

### New Dev Container topics

There are new topics for working with Containers in VS Code:

* [Attach to Container](https://code.visualstudio.com/docs/devcontainers/attach-container) - Attach to an already running Docker Container.
* [Create a Dev Container](https://code.visualstudio.com/docs/devcontainers/create-dev-container) - Create a custom container matching your development environment.
* [devcontainer.json reference](https://containers.dev/implementors/json_reference) - Control container creation through `devcontainer.json` properties.

And check out this blog post on using [Dev Containers in Education](https://code.visualstudio.com/blogs/2020/07/27/containers-edu) to help educators and students create stable, reproducible classroom setups.

### Java lightweight mode

The [Language Support for Java](https://marketplace.visualstudio.com/items?itemName=redhat.java) extension now supports a [lightweight mode](https://code.visualstudio.com/docs/java/java-project#_lightweight-mode), for when you want to quickly work with your Java source files. You'll still get powerful language features such as code completion, navigation, outlining, and syntax checking for your source code, as well as the JDK.

### Azure Cache extension

The new [Azure Cache](https://marketplace.visualstudio.com/items?itemName=ms-azurecache.vscode-azurecache) extension makes it easy to browse through data in your [Azure Caches](https://azure.microsoft.com/services/cache).

## Notable fixes

* [33720](https://github.com/microsoft/vscode/issues/33720): Wrong guess encoding as Windows 1252
* [102037](https://github.com/microsoft/vscode/issues/102037): If a debug adapter fails to send a `terminated` event, VS Code gets stuck in debug mode
* [103463](https://github.com/microsoft/vscode/issues/103463): When docked, the "disconnect" and "stop" icons in the debug toolbar don't update properly

## Thank you

Last but certainly not least, a big *__Thank You__* to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)

Contributions to `vscode`:

* [Alvin Tang (@alvintangz)](https://github.com/alvintangz): Feature #88480: Empty Lines Not Getting Commented-Out [PR #93160](https://github.com/microsoft/vscode/pull/93160)
* [Andrew Branch (@andrewbranch)](https://github.com/andrewbranch)
  * Change includePackageJsonAutoImports options [PR #103732](https://github.com/microsoft/vscode/pull/103732)
  * [typescript-language-features] Add setting for package.json auto imports [PR #103037](https://github.com/microsoft/vscode/pull/103037)
  * [typescript-language-features] Add telemetry for package.json auto imports [PR #103126](https://github.com/microsoft/vscode/pull/103126)
* [Charles Gagnon (@Charles-Gagnon)](https://github.com/Charles-Gagnon): Only throw error if releaseNotesUrl doesn't exist [PR #102149](https://github.com/microsoft/vscode/pull/102149)
* [Shelley Vohr (@codebytere)](https://github.com/codebytere): fix: add missing property to deserializeRunnable [PR #103436](https://github.com/microsoft/vscode/pull/103436)
* [Connor Skees (@connorskees)](https://github.com/connorskees)
  * use ^H to delete word left in cmd.exe [PR #98494](https://github.com/microsoft/vscode/pull/98494)
  * only emit ctrl+h for cmd.exe when the terminal is in focus [PR #102508](https://github.com/microsoft/vscode/pull/102508)
* [Damien Martin-Guillerez (@damienmg)](https://github.com/damienmg): Use the search order settings for the search editor [PR #103627](https://github.com/microsoft/vscode/pull/103627)
* [Daniel Rosenwasser (@DanielRosenwasser)](https://github.com/DanielRosenwasser): Add check to ensure TS 4.0-RC and forward use '--serverMode'. [PR #104123](https://github.com/microsoft/vscode/pull/104123)
* [Ed Jeffreys (@edjeffreys)](https://github.com/edjeffreys): #98942 Clear Search Pattern Fields [PR #100024](https://github.com/microsoft/vscode/pull/100024)
* [Ikko Ashimine (@eltociear)](https://github.com/eltociear): Fixed typo [PR #103297](https://github.com/microsoft/vscode/pull/103297)
* [Alexander Fadeev (@fadeevab)](https://github.com/fadeevab): Add new Makefile test case for upgraded syntax [PR #101719](https://github.com/microsoft/vscode/pull/101719)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
  * fix #100437 Improve 'terminal.integrated.commandsToSkipShell' info [PR #100445](https://github.com/microsoft/vscode/pull/100445)
  * Improve text of SCM count badge settings [PR #101677](https://github.com/microsoft/vscode/pull/101677)
  * Show SCM uppercase in settings search titles [PR #101679](https://github.com/microsoft/vscode/pull/101679)
* [Fedor Nezhivoi (@gyzerok)](https://github.com/gyzerok)
  * Move read and write logic from nativeTextFileService to textFileService for #79275 [PR #100804](https://github.com/microsoft/vscode/pull/100804)
  * remove jschardet typings workaround for #79275 [PR #101705](https://github.com/microsoft/vscode/pull/101705)
  * fix #79275 enable encodings for web [PR #101706](https://github.com/microsoft/vscode/pull/101706)
* [Evgeny Gryaznov (@inspirer)](https://github.com/inspirer): Add missing code-import-patterns for files and directories targeting web. [PR #102961](https://github.com/microsoft/vscode/pull/102961)
* [Jason Williams (@jasonwilliams)](https://github.com/jasonwilliams)
  * fix h1 oversized bottom margin on markdown preview [PR #102520](https://github.com/microsoft/vscode/pull/102520)
  * fix styling to improve heading spacing fixes #102036 [PR #102427](https://github.com/microsoft/vscode/pull/102427)
  * fix paragraphs inside of `<ul>`s which are too spaced apart. [PR #102719](https://github.com/microsoft/vscode/pull/102719)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fixes quick find symbol finder shows 'no matching results' after backspace [PR #101844](https://github.com/microsoft/vscode/pull/101844)
  * Fixes regression: cannot open image with special characters '#', '?', '%' [PR #102189](https://github.com/microsoft/vscode/pull/102189)
  * Fixes command 'markdown.api.render' generates different html content for the same markdown string [PR #103578](https://github.com/microsoft/vscode/pull/103578)
* [James Lave (@jlave-dev)](https://github.com/jlave-dev): Add GitHub public repo option [PR #102406](https://github.com/microsoft/vscode/pull/102406)
* [David Linskey (@Linskeyd)](https://github.com/Linskeyd): Fix webview focus steal that occurs when the application is re-focused [PR #98847](https://github.com/microsoft/vscode/pull/98847)
* [Logan Ramos (@lramos15)](https://github.com/lramos15): Support quickpick on open anyways [PR #103712](https://github.com/microsoft/vscode/pull/103712)
* [Ludovic Galibert (@ludokx)](https://github.com/ludokx): Add #97640: Added options to enable codelens for diff editors [PR #97644](https://github.com/microsoft/vscode/pull/97644)
* [He Linming (@MrHeer)](https://github.com/MrHeer): Fixes #103129 [PR #103130](https://github.com/microsoft/vscode/pull/103130)
* [@NotWearingPants](https://github.com/NotWearingPants)
  * Retry fetching release notes on failure, and display failures [PR #101158](https://github.com/microsoft/vscode/pull/101158)
  * Allow to reopen closed webviews if the extension supports it [PR #100979](https://github.com/microsoft/vscode/pull/100979)
  * Treat all files with `npmrc`/`npmignore`/`gitignore` extensions as properties/ignore files [PR #103044](https://github.com/microsoft/vscode/pull/103044)
  * Treat all files with `git{config,attributes,modules}`/`editorconfig` extensions as properties files [PR #103326](https://github.com/microsoft/vscode/pull/103326)
* [@nrayburn-tech](https://github.com/nrayburn-tech)
  * Add check for shadow dom root before closing submenu [PR #93667](https://github.com/microsoft/vscode/pull/93667)
  * Change from a dialog to a notification for extension actions [PR #103338](https://github.com/microsoft/vscode/pull/103338)
* [Ye-hyoung Kang (@pastelmind)](https://github.com/pastelmind): Add filename patterns for jsconfig.json [PR #103360](https://github.com/microsoft/vscode/pull/103360)
* [Pascal Fong Kye (@pfongkye)](https://github.com/pfongkye): fix: remove flexbox to enable ellipsis [PR #102640](https://github.com/microsoft/vscode/pull/102640)
* [Pierce Boggan (@pierceboggan)](https://github.com/pierceboggan): Fix runtime exception and add Teams [PR #103209](https://github.com/microsoft/vscode/pull/103209)
* [Tony Xia (@tony-xia)](https://github.com/tony-xia)
  * saveUntitedBeforeShutdown -> saveUntitledBeforeShutdown [PR #101740](https://github.com/microsoft/vscode/pull/101740)
  * Typo: ICommontTelemetryPropertiesResolver -> ICommonTelemetryPropertiesResolver [PR #101739](https://github.com/microsoft/vscode/pull/101739)
* [@troy351](https://github.com/troy351): chore: remove redundant code [PR #102471](https://github.com/microsoft/vscode/pull/102471)
* [Tyler Brockett (@tylerbrockett)](https://github.com/tylerbrockett): Fix typo in preferences sync - Canelled to Cancelled [PR #103431](https://github.com/microsoft/vscode/pull/103431)
* [Alan Zhang (@zcfan)](https://github.com/zcfan): fix #102289 [PR #102660](https://github.com/microsoft/vscode/pull/102660)

Contributions to `vscode-json-languageservice`:

* [Adrián Panella (@ianchi)](https://github.com/ianchi): Expose getMatchingSchemas [PR #47](https://github.com/microsoft/vscode-json-languageservice/pull/47)

Contributions to `vscode-vsce`:

* [Jeffrey (@JeffreyCA)](https://github.com/JeffreyCA): Prevent mailto links from being joined with prefix [PR #472](https://github.com/microsoft/vscode-vsce/pull/472)

Contributions to `vscode-eslint`:

* [Matt Lubner (@mattlubner)](https://github.com/mattlubner): Add a NODE_ENV configuration setting for eslint [PR #988](https://github.com/microsoft/vscode-eslint/pull/988)
* [(@NotWearingPants)](https://github.com/NotWearingPants): Treat all files with an `eslintignore` extension as ignore files [PR #1025](https://github.com/microsoft/vscode-eslint/pull/1025)
* [Dimitri Mitropoulos (@dimitropoulos)](https://github.com/dimitropoulos): typo: missing double quote in example json [PR #1031](https://github.com/microsoft/vscode-eslint/pull/1031)
* [David Turesson (@mrxdst)](https://github.com/mrxdst): Disable rule for entire file inserts the comment under the shebang. [PR #1036](https://github.com/microsoft/vscode-eslint/pull/1036)

Contributions to `language-server-protocol`:

* [Remy Suen (@rcjsuen)](https://github.com/rcjsuen): Fix typos in document change notifications [PR #1043](https://github.com/microsoft/language-server-protocol/pull/1043)
* [Florian Loitsch (@floitsch)](https://github.com/floitsch): Fix typo. [PR #1055](https://github.com/microsoft/language-server-protocol/pull/1055)
[Danny Tuppeny (@DanTup)](https://github.com/DanTup): Minor tweaks for consistency to improve parsing [PR #1058](https://github.com/microsoft/language-server-protocol/pull/1058)
* [Zhang Zhi (@fytriht)](https://github.com/fytriht): Update broken links [PR #1060](https://github.com/microsoft/language-server-protocol/pull/1060)

Contributions to `debug-adapter-protocol`:

* [kuafuwang (@kuafuwang)](https://github.com/kuafuwang): Add link to JCIDE which support DAP [PR #129](https://github.com/microsoft/debug-adapter-protocol/pull/129)
* [actboy168 (@actboy168)](https://github.com/actboy168): Add Lua Debug Adapter [PR #131](https://github.com/microsoft/debug-adapter-protocol/pull/131)

Contributions to `vscode-recipes`:

* [Ellis Kenyő (@elken)](https://github.com/elken): Update README.md [PR #252](https://github.com/microsoft/vscode-recipes/pull/252)
* [tasdevani21 (@tasdevani21)](https://github.com/tasdevani21): Update Vuejs readme to include latest cli commands and improve debugging [PR #263](https://github.com/microsoft/vscode-recipes/pull/263)

Contribution to `vscode-hexeditor`:

* [Jean Pierre (@jeanp413)](https://github.com/jeanp413): Improvements to cell selection logic [PR #92](https://github.com/microsoft/vscode-hexeditor/pull/92)

Contributions to `vscode-js-debug`:

* [Ashik Paul (@Ashikpaul)](https://github.com/Ashikpaul): Fixed a minor typo [PR #614](https://github.com/microsoft/vscode-js-debug/pull/614)

Contributions to `vscode-generator-code`:

* [undefined (@masnn)](https://github.com/masnn): Update index.js [PR #216](https://github.com/microsoft/vscode-generator-code/pull/216)
* [Sibiraj (@sibiraj-s)](https://github.com/sibiraj-s): Update Eslint, Mocha [PR #213](https://github.com/microsoft/vscode-generator-code/pull/213)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
