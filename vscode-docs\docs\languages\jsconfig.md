---
ContentId: 201cd81d-523c-4f62-b1f5-ed26c091657b
DateApproved: 05/08/2025
MetaDescription: View the reference for jsconfig.json.
---
# jsconfig.json

## What is jsconfig.json?

The presence of `jsconfig.json` file in a directory indicates that the directory is the root of a JavaScript Project. The `jsconfig.json` file specifies the root files and the options for the features provided by the [JavaScript language service](https://github.com/microsoft/TypeScript/wiki/JavaScript-Language-Service-in-Visual-Studio).

> **Tip:** If you are not using JavaScript, you do not need to worry about `jsconfig.json`.

> **Tip:** `jsconfig.json` is a descendant of [tsconfig.json](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html), which is a configuration file for TypeScript. `jsconfig.json` is `tsconfig.json` with `"allowJs"` attribute set to `true`.

## Why do I need a jsconfig.json file?

Visual Studio Code's JavaScript support can run in two different modes:

* **File Scope - no jsconfig.json**: In this mode, JavaScript files opened in Visual Studio Code are treated as independent units. As long as a file `a.js` doesn't reference a file `b.ts` explicitly (either using `import` or **CommonJS** [modules](https://wiki.commonjs.org/wiki/Modules/1.0)), there is no common project context between the two files.

* **Explicit Project - with jsconfig.json**: A JavaScript project is defined via a `jsconfig.json` file. The presence of such a file in a directory indicates that the directory is the root of a JavaScript project. The file itself can optionally list the files belonging to the project, the files to be excluded from the project, as well as compiler options (see below).

The JavaScript experience is improved when you have a `jsconfig.json` file in your workspace that defines the project context. For this reason, we offer a hint to create a `jsconfig.json` file when you open a JavaScript file in a fresh workspace.

### Location of jsconfig.json

We define this part of our code, the client side of our website, as a JavaScript project by creating a `jsconfig.json` file. Place the file at the root of your JavaScript code as shown below.

![jsconfig setup](images/javascript/jsconfig_setup.png)

In more complex projects, you may have more than one `jsconfig.json` file defined inside a workspace. You will want to do this so that the code in one project is not suggested as IntelliSense to code in another project. Illustrated below is a project with a `client` and `server` folder, showing two separate JavaScript projects.

![multiple jsconfigs](images/javascript/complex_jsconfig_setup.png)

## Examples

By default the JavaScript language service will analyze and provide IntelliSense for all files in your JavaScript project. You will want to specify which files to exclude or include in order to provide the proper IntelliSense.

### Using the `"exclude"` property

The `exclude` attribute (a [glob pattern](/docs/editor/glob-patterns.md)) tells the language service what files are not part of your source code. This keeps performance at a high level. If IntelliSense is slow, add folders to your `exclude` list (VS Code will prompt you to do this if it detects the slow down).

```json
{
    "compilerOptions": {
        "module": "CommonJS",
        "target": "ES6"
    },
    "exclude": [
        "node_modules"
    ]
}
```

> **Tip:** You will want to `exclude` files generated by a build process (for example, a `dist` directory). These files will cause suggestions to show up twice and will slow down IntelliSense.

### Using the `"include"` property

Alternatively, you can explicitly set the files in your project using the `include` attribute (a [glob pattern](/docs/editor/glob-patterns.md)). If no `include` attribute is present, then this defaults to including all files in the containing directory and subdirectories. When a `include` attribute is specified, only those files are included. Here is an example with an explicit `include` attribute.

```json
{
    "compilerOptions": {
        "module": "CommonJS",
        "target": "ES6"
    },
    "include": [
        "src/**/*"
    ]
}
```

> **Tip:** The file paths in `exclude` and `include` are relative to the location of `jsconfig.json`.

## jsconfig Options

Below are `jsconfig` `"compilerOptions"` to configure the JavaScript language support.

> **Tip:** Do not be confused by `compilerOptions`, since no actual compilation is required for JavaScript. This attribute exists because `jsconfig.json` is a descendant of `tsconfig.json`, which is used for compiling TypeScript.

Option  | Description
----------------|-----
`noLib` | Do not include the default library file (lib.d.ts)
`target`| Specifies which default library (lib.d.ts) to use. The values are "ES3", "ES5", "ES6", "ES2015", "ES2016", "ES2017", "ES2018", "ES2019", "ES2020", "ES2021", "ES2022", "ES2023", "ESNext".
`module` | Specifies the module system, when generating module code. The values are "AMD", "CommonJS", "ES2015", "ES2020", "ES2022", "ES6", "Node16", "NodeNext", "ESNext", "None", "System", "UMD".
`moduleResolution` | Specifies how modules are resolved for imports. The values are "Node", "Classic", "Node16", "NodeNext", "Bundler".
`checkJs` | Enable type checking on JavaScript files.
`experimentalDecorators`|Enables experimental support for proposed ES decorators.
`allowSyntheticDefaultImports`|Allow default imports from modules with no default export. This does not affect code emit, just type checking.
`baseUrl`|Base directory to resolve non-relative module names.
`paths`|Specify path mapping to be computed relative to baseUrl option.

You can read more about the available `compilerOptions` in the [TypeScript compilerOptions documentation](https://www.typescriptlang.org/tsconfig#compilerOptions).

## Using webpack aliases

For IntelliSense to work with webpack aliases, you need to specify the `paths` keys with a [glob pattern](/docs/editor/glob-patterns.md).

For example, for alias 'ClientApp':

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "ClientApp/*": ["./ClientApp/*"]
    }
  }
}
```

and then to use the alias

```js
import Something from 'ClientApp/foo'
```

## Best Practices

Whenever possible, you should exclude folders with JavaScript files that are not part of the source code for your project.

>**Tip:** If you do not have a `jsconfig.json` in your workspace, VS Code will by default exclude the `node_modules` folder.

Below is a table, mapping common project components to their installation folders that are recommended to exclude:

Component | folder to exclude
----------|-----------
`node` | exclude the `node_modules` folder
`webpack`, `webpack-dev-server` | exclude the content folder, for example `dist`.
`bower` | exclude the `bower_components` folder
`ember` | exclude the `tmp` and `temp` folders
`jspm` | exclude the `jspm_packages` folder

When your JavaScript project is growing too large and performance slows, it is often because of library folders like `node_modules`. If VS Code detects that your project is growing too large, it will prompt you to edit the `exclude` list.
