---
Order: 2
Area: students
TOCTitle: Over the Moon Lessons
ContentId: 59288bcc-97b7-411e-86da-c022a00e25ec
PageTitle: Get Started Tutorial for Over the Moon project in Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: A Over the Moon project tutorial using the Python extension in Visual Studio Code.
---

# Learn Python with Over The Moon

[Over the Moon](https://www.youtube.com/watch?v=26DIABx44Tw) is a Netflix film about <PERSON><PERSON>, a young girl who builds a rocket to go to the moon on a mission to prove the existence of a legendary Moon Goddess. These lessons were inspired by <PERSON><PERSON>'s story and the story of real NASA engineers and astronauts. Try out these space-travel themed challenges that will introduce you to data science, machine learning, and artificial intelligence with tools like Python, Visual Studio Code, and Azure.

Get a glimpse into the Python programming language with this introductory learning path that requires no prior background.

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/explore-space-using-python/?WT.mc_id=python-0000-cxa"><h2 class="title faux-h3">Explore space with Python and Visual Studio Code</h2></a>
    </div>
    <p class="description">Plan a moon mission, predict meteor showers, and use artificial intelligence to recognize objects in this learning path inspired by the Netflix film "Over the Moon".</p>
    <a href="https://learn.microsoft.com/training/paths/explore-space-using-python/?WT.mc_id=python-0000-cxa" title="Over The Moon module">
        <img src="/assets/learn/students/over-the-moon-python/otm-python.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>
