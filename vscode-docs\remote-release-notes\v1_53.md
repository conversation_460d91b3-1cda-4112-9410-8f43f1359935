# January 2021 (version 1.53)

## Visual Studio Code Remote Core

### Extension management in the remote CLI

The `code` command in the Integrated Terminal of remote windows now supports:

- `code --install-extension idOrPathToVSIX` in combination with `--force`
- `code --uninstall-extension idOrPathToVSIX` in combination with `--force`
- `code --list-extensions` in combination with `--show-versions` and `--category`

The commands are restricted to extensions on the remote extension host. To install an extension locally, the local CLI or the extensions viewlet must be used.

### Restoring terminal UI state

In our October 2020 release, we gave VS Code Remote and Codespaces the ability to persist your terminal sessions and reattach to them later, such as after a window reload.

This month, we improved this by adding the ability to restore the UI state of reconnected terminals. This means that split terminals will be restored with the same split dimensions, and the same active terminal will be selected.

![In a remote window, two split terminals are created and resized. On reload, the layout appears unchanged.](images/1_53/terminal-splits-persist.gif)

### Smart update of machine settings

Machine scoped settings are written into a remote settings file when updated through `vscode.workspace.getConfiguration().update()` API.

### Connection compression

The communication between VS Code and the remote machine is now compressed, using a WebSocket extension called `permessage-deflate`. The compression is especially helpful for weak network connections.

Here is the data usage when opening a workbench and typing character-for-character the following in an empty TypeScript file:

```ts
window.addEventListener('load', function() {
    console.log(`hello world!`);
})
```

| Direction | Data (bytes) | "on the wire" (bytes) | Compression ratio |
|---|---|---|---|
| client->server | 1,910,822 | 182,247 | **10.4x** |
| server->client | 619,639 | 80,085 | **7.7x** |

### Less disruptive disconnects

Previously, after a short duration of failing to communicate with the remote, a modal dialog would interrupt the user's coding session. We have increased this duration and opted for a subtler indication beforehand.

![Brief disconnect state](images/1_53/reconnecting.png)

### Port attributes

Attributes can now be configured for ports in settings. These attributes let you control the label that shows in the Ports view, the action that occurs when the port is autoforwarded, and whether that that port should always prompt for elevation if the local port is privileged (currently no Remote extensions support this last one).

* The port number can be a number or a range (ex. `3000` or `30000-40000`).
* `label`: A label for the port in the "Ports" view.
* `onAutoForward`: Specifies the action do be taken when the port is autoforwarded. See the setting `"remote.autoForwardPorts"` for more information about autoforwarding. Can be one of the following:
  * `notify`: Shows a notification, default
  * `silent`: The port will be autoforwarded, but no notification will be shown
  * `openBrowser`: Immediately opens the browser when the port is autoforwarded
  * `ignore`: The port will not be autoforwarded
* `elevateIfNeeded`: If the remote extension you are using supports elevating for privileged ports (for example, ports below 1024 on Linux or macOS), then setting this to `true` will cause the elevation prompt to immediately show when a local privileged port is used for forwarding. **Note:** At the time of this release, remote extensions do not support elevating for privileged ports.

![Setting port attributes](images/1_53/ports-attributes.gif)

### Privileged port forwarding

Previously, privileged ports (ports < 1024) could not be detected on Linux and macOS. Now, privileged ports will be detected if a url (ex. http://localhost:80) is printed to the Debug Console or Terminal Output and will be auto forwarded.

### More port details

The process ID of the process associated with the forwarded port is now shown in the Ports view. You can also see more details about the forwarded port's CLI when the Ports view is wide.

![Ports view with more info](images/1_53/ports-view-more-info.png)

### Terminal Link Hover

The hover text for links in the terminal now hints that the port is forwarded when in a remote context.

![Terminal link hover](images/1_53/terminal-link-hover.gif)

## Containers (version 0.158.x)

### Support for SSH connection with password entry

If you connect to Docker using SSH, we now support prompting for a password (and passphrase) when required.

## WSL

### Install extensions from a WSL shell

The `code` CLI in the WSL shell now also supports
`--list-extensions`, `--uninstall-extension`, and `--uninstall-extension`. You can use them to list, remove, or add extensions installed on the WSL side. This works for extensions from the Marketplace and from VSIXes.

Again, the command is restricted to extensions on the WSL side. To install an extension locally, use `code.cmd` from the Windows side or the Extensions view.

### Shortcuts to install WSL and WSL distros

The [WSL extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl) now guides users how to install WSL and WSL distros when not yet present. It uses the new simplified [`wsl --install`](https://learn.microsoft.com/windows/wsl/install#install-wsl-command) feature available in Windows Preview builds.
