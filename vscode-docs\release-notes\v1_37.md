---
Order:
TOCTitle: July 2019
PageTitle: Visual Studio Code July 2019
MetaDescription: See what is new in the Visual Studio Code July 2019 Release (1.37)
MetaSocialImage: 1_37/release-highlights.png
Date: 2019-08-08
DownloadVersion: 1.37.1
---
# July 2019 (version 1.37)

**Update 1.37.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22July+2019+Recovery%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the July 2019 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Full product icon refresh](#new-product-icons)** - New modern icons across all of VS Code.
* **[Edit string arrays in the Settings UI](#settings-editor-support-for-string-arrays)** - Add to string arrays directly from the Settings editor.
* **[Reveal search result in File Explorer](#reveal-search-result-in-the-explorer)** - Quickly jump to file locations in the File Explorer.
* **[Show whitespace in selection](#show-whitespace-in-selection)** - See whitespace characters just for selected text.
* **[Find and Replace preserves casing](#preserve-case-in-find-and-replace)** - Replace text without losing letter casing.
* **[Terminal search UX improvements](#search-ux-improvements)** - More intuitive bottom-up search order for the terminal.
* **[Better SCSS @import navigation](#improved-navigation-for-scss-import)** - VS Code now checks more locations for SCSS files.
* **[Remote Development tutorials](#remote-development-tutorials)** - New tutorials for working over SSH and in WSL.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).<br>
>You can also check out this 1.37 release [highlights video](https://youtu.be/KYOGhpST-_g) from Developer Advocate [Brian Clark](https://twitter.com/_clarkio).

**Insiders:** Want to see new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Workbench

### New product icons

We've updated all of our product icons to be uniform in style, color, and size. We went through [various iterations](https://github.com/microsoft/vscode/issues/8017) and the community was helpful in providing feedback through every step of the process.

**Note**: This does not cover our use of Octicons, those icons appear in the Status bar and Quick pick.

![VS Code product icons](images/1_37/icons.gif)

### Settings editor support for string arrays

The Settings editor now allows editing array-of-string settings. The **Add Item** button will bring up an input box to add a new string.

![Settings UI for array-of-string settings](images/1_37/settings-ui-array-of-string.gif)

Extension authors can enable this support by [contributing a configuration](https://code.visualstudio.com/api/references/contribution-points#contributes.configuration) with `type` and `items` keys:

```json
{
  "type": "array",
  "items": {
    "type": "string"
  }
}
```

### Removed warnings from settings files

You will no longer see warnings for unknown or unsupported settings in your `settings.json` files. Instead inactive settings will be faded, similar to unused source code, with a hover explaining why. In addition, you won't see warnings for settings from extensions that are currently disabled.

![Faded unused settings](images/1_37/unused-settings.png)

### Reveal search result in the Explorer

The file context menu in the Search view now has a **Reveal in Explorer** command to navigate to that file in the File Explorer.

![Reveal in Explorer command in search result](images/1_37/search-reveal-explorer.png)

### Search regex features

Back in the 1.29 release, we added the setting `search.usePCRE2` to make search use the PCRE2 regex engine. This enabled searching with some advanced regex features such as [backreferences](https://www.regular-expressions.info/backref.html) and [lookahead](https://www.regular-expressions.info/lookaround.html) assertions. Thanks to some upstream work in [ripgrep](https://github.com/BurntSushi/ripgrep), you can now use these features without enabling a special setting. ripgrep will fall back to the PCRE2 engine automatically if the regex uses a feature that isn't supported by the Rust regex engine, and `search.usePCRE2` is now deprecated. As a reminder, VS Code only supports regexes that are valid in JavaScript, because open editors are still searched using the editor's JavaScript-based search.

### NPM Scripts Explorer visible by default

Previously, you had to enable the setting `npm.enableScriptExplorer` to make the NPM Scripts Explorer visible and this was difficult to discover. The NPM Scripts Explorer is now shown collapsed in the File Explorer, when there is a `package.json` file at the root of the workspace.

![NPM Scripts Explorer collapsed](images/1_37/npm-scripts-explorer.png)

When you have no `package.json` at the root in your workspace, you can still use the setting to enable the NPM Scripts Explorer. If you want to hide the explorer, you can right-click on its title and select **Hide** from the context menu.

### focusedView context key

There is a new keybinding `when` clause context key, `focusedView`, to display or enable a command when a given view is focused.

```json
"when": "focusedView == nodeDependencies"
```

### Accessibility improvements

We fixed accessibility issues across the workbench. A full list of the issues fixed can be found on GitHub with the ['accessibility' label](https://github.com/microsoft/vscode/issues?q=milestone%3A%22July+2019%22+label%3Aaccessibility+is%3Aclosed).

### New tree widget adopted in tree view

Extensions that contribute tree views will now get the [new tree widget](https://code.visualstudio.com/updates/v1_31#_new-tree-widget) experience. The tree widget provides better performance as well as improved keyboard navigation, filtering, custom indenting, horizontal scrolling, and more. If your favorite extensions have custom tree views, you should see the same UX improvements that were added earlier to the built-in VS Code explorers.

## Editor

### Show whitespace in selection

The `editor.renderWhitespace` setting now supports a `selection` option. With this option set, whitespace characters will be shown only on selected text:

![Whitespace rendered on selection](images/1_37/render-whitespace-on-selection.png)

This is handy for quickly checking whitespace without filling the entire editor.

### Improved minimap search results visibility

In the previous release, we introduced search decorations in the minimap (code outline). These decorations were difficult to see, so this iteration, we've worked on increasing their visibility. Now, the entire line will now be highlighted with low opacity in the minimap, and the actual match shown with high opacity.

![Minimap search results](images/1_37/minimap-search-decorations.png)

You can also change the color of the decorations using the new `minimap.findMatchHighlight` theme color.

### Preserve case in Find and Replace

You can now preserve case when doing replacement in the editor's Find widget. The feature is turned on when the Preserve Case option (**AB** button) is turned on in the editor's Replace input box.

![Preserve Case button](images/1_37/preserve-case-button.png)

Currently VS Code only supports preserve **Full Upper Case**, **Full Lower Case**, and **Title Case**.

![Preserve case example](images/1_37/preserve-case.gif)

### Button toggle active state in Find widget

We've updated the Find widget's button toggle active state with a filled background so that you can now easily tell when you are focused on an active toggle. This appears in the Search view, Find and Replace, and Terminal Find.

With this update, we've introduced a new theme color, `inputOption.activeBackground`, to control the background color of the toggle active state. This color reuses `focusBorder` so that it works better with themes by default. We've also set the default for `inputOption.activeBorder` to transparent so that the border is only used for focus.

![Button toggle active state](images/1_37/button-toggle-active-state.gif)

### Updated info colors

Following the [unification of the warning colors](https://code.visualstudio.com/updates/v1_36#_updated-warning-colors), we've also updated `editorInfoForeground` and `editorInfoBorder` to match the rest of the information diagnostics colors. You'll see this updated color in the Peek error view and editor squiggles:

![Info display colors](images/1_37/info-colors.png)

## Integrated Terminal

### Search UX improvements

Previously searching within the terminal would start at the top of the terminal's viewport and search downwards. When it reached the bottom, it would wrap to the top. The new behavior aligns with several other terminal emulators and starts from the bottom of the buffer and searches upwards, leading to a much less disorienting experience.

![Terminal search now searches upwards by default](images/1_37/terminal-search-ux.gif)

### Terminal accessibility improvements

The terminal now features a line navigation mode when screen readers are used that enables convenient navigation of lines via `kb(workbench.action.terminal.navigationModeFocusNext)` and `kb(workbench.action.terminal.navigationModeFocusPrevious)`. The mode can be escaped with `kb(workbench.action.terminal.navigationModeExit)`.

These are the new commands if you want to [customize the keybindings](https://code.visualstudio.com/docs/getstarted/keybindings#_advanced-customization):

```json
workbench.action.terminal.navigationModeExit
workbench.action.terminal.navigationModeFocusNext
workbench.action.terminal.navigationModeFocusPrevious
```

Other accessibility improvements were also made:

* The non-breaking space character is now used for blank lines instead of an explicit string, allowing the screen reader to announce properly.
* Screen readers will now announce characters when typed and removed. There are some cases where this will won't work but the majority of cases are covered.

### Variables resolved in settings

[Variables](https://code.visualstudio.com/docs/editor/variables-reference) can now be used within the following settings:

```json
terminal.integrated.cwd
terminal.integrated.shell.<platform>
terminal.integrated.shellArgs.<platform>
```

For example, you no longer need an extension to open a terminal in the current file's folder, just add this setting:

```json
"terminal.integrated.cwd": "${fileDirname}"
```

### Improved error reporting and diagnostics

Previously when the terminal launched with a bad configuration, it would only indicate that the process exited with an error code. Better error messages are now provided that guide the user to solutions for several common scenarios.

![Using an invalid shell setting will tell you the shell is invalid](images/1_37/terminal-invalid-shell.png)

## Languages

### Improved navigation for SCSS @import

VS Code used to resolve SCSS imports statically. For example, `@import './foo'` would resolve to `_foo.scss`. However, SCSS is more flexible in the way it resolves imports, allowing `./foo` to resolve to one of the five locations:

* `foo.scss`
* `_foo.scss`
* `foo/index.scss`
* `foo/_index.scss`
* `foo.css`

VS Code now resolves `@import './foo'` to one of these locations if the resolved file exists on disk. Note that if a link ends with `/`, it will only resolve to either `/index.scss` or `/_index.scss`. If a link ends with suffix such as `.css` or `.scss`, VS Code will not try the alternative file locations.

### Improved CSS property value completion

CSS completion now offers detailed documentation for an additional 754 CSS property values, including less commonly used values.

![CSS property value completion documentation](images/1_37/css-property-value-completion-documentation.png)

### Use syntax only feature of JavaScript and TypeScript more quickly

Last release, [we added an experimental setting](https://code.visualstudio.com/updates/v1_36#_use-syntax-only-feature-of-javascript-and-typescript-more-quickly) that splits the TypeScript server that powers VS Code's JavaScript and TypeScript language features into separate syntax and semantic servers.

With VS Code 1.37, the split server is now enabled by default. This means that for large JavaScript and TypeScript projects, syntax-based operations, such as code folding or document outlines, should be available more quickly.

## Tasks

### TypeScript detected tasks warning

Last release, the TypeScript task auto-detection changed from using OS-specific path separators in the `tsconfig` path to a more cross-platform friendly `/`. VS Code now shows a warning if there is `\\` in a tsconfig path.

```ts
{
  "type": "typescript",
  "tsconfig": "src\\tsconfig.json", // This will cause a warning to show. It should be "tsconfig": "src/tsconfig.json"
  "option": "watch",
  "problemMatcher": [
    "$tsc-watch"
  ]
}
```

### ${relativeFileDirname} variable for task and debug configurations

A new variable `${relativeFileDirname}` resolves to the workspace folder relative path of the current open file's directory. The variable can be used in task and debug configurations (`tasks.json` and `launch.json`).

### resolveTask support

Task providers can now implement `resolveTask` to reduce the overhead for extensions proffering tasks. `resolveTask` is called after `provideTasks`, so if your extension has a setting that turns off `provideTasks` and auto-detection (like the built-in gulp extension), but a user still references one of your tasks in `tasks.json`, then `resolveTask` will be called and your extension can return that single task.

## Contributions to extensions

### Remote Development (Preview)

Work has continued on the [Remote Development](https://aka.ms/vscode-remote/download/extension) extensions, which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment. You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_37.md).

You can also read two recent blog posts describing:

* [Linux development with WSL and Visual Studio Code](https://devblogs.microsoft.com/commandline/an-in-depth-tutorial-on-linux-development-on-windows-with-wsl-and-visual-studio-code)
* [Remote development over SSH](https://code.visualstudio.com/blogs/2019/07/25/remote-ssh).

### Java supports smart selection

The [Microsoft Java extension](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-pack) now supports [smart semantic selection](https://code.visualstudio.com/docs/java/java-editing#_smart-selection). You can expand or shrink the selection range based on the semantic information of the caret position in your source code.

* To expand the selection, use `kb(editor.action.smartSelect.expand)`.
* To shrink the selection, use `kb(editor.action.smartSelect.shrink)`.

## Extension authoring

### vscode.workspace.fs

There is new API for extensions to read, write, and manage files and folders. This is an alternative to Node.js's [fs](https://nodejs.org/dist/latest-v10.x/docs/api/fs.html) file system module and more importantly, it allows access to files from contributed file systems, such as the SSH or WSL remote file systems.

The snippet below shows how to write to a file, more usage samples can be found in the [fsconsumer-sample](https://github.com/microsoft/vscode-extension-samples/tree/0b3a31bf2bdd388ac4fdc0ccea2fb1315abfe3e3/fsconsumer-sample).

```ts
const writeStr = '1€ is 1.12$ is 0.9£';
const writeBytes = Buffer.from(writeStr, 'utf8');

await vscode.workspace.fs.writeFile(someUri, writeBytes);
```

**Call to Action**: If your extension is currently using the `fs` module from Node.js, consider migrating to the new `vscode.workspace.fs` API.

### DiagnosticsTag.Deprecated

Diagnostic tags enable extension authors to enrich the representation of diagnostics inside the editor. This release adds `DiagnosticTag.Deprecated`, which the editor will render with a strike-through line.

![Strikeout Deprecated decoration](images/1_37/diag-tag-deprecated.png)

The diagnostic in the image above is created by the `validate` function shown below. The line `diag.tags = [vscode.DiagnosticTag.Deprecated];` adds the deprecation tag.

```ts
let diagnostics = vscode.languages.createDiagnosticCollection('Sample Errors');
let validate = (editor: vscode.TextEditor | undefined) => {
    if (!editor) {
        return;
    }
    let result: vscode.Diagnostic[] = [];
    for (let line = 0; line < editor.document.lineCount; line++) {
        let pos = 0;
        while ((pos = editor.document.lineAt(line).text.indexOf('guys', pos)) >= 0) {
            let diag = new vscode.Diagnostic(new vscode.Range(line, pos, line, pos + 'guys'.length), 'This is deprecated, use "folks", "people", or "you all" instead.');
            diag.severity = vscode.DiagnosticSeverity.Warning;
            diag.tags = [vscode.DiagnosticTag.Deprecated];
            result.push(diag);
            pos += 'guys'.length;
        };
    }
    diagnostics.set(editor.document.uri, result);
}
```

### vscode.workspace.workspaceFile

There is a new workspace property `workspace.workspaceFile` returning the location of the workspace file. For example: `file:///Users/<USER>/Development/myProject.code-workspace` or `untitled:1555503116870` for a workspace that is untitled and not yet saved.

Depending on the workspace that is opened, the value will be:

* `undefined` when no workspace or a single folder is opened.
* The path of the workspace file as `Uri`.

If the workspace is untitled, the returned URI will use the `untitled:` scheme.

One use of the workspace file location is to call the `vscode.openFolder` command to open the workspace again after it has been closed:

```typescript
  vscode.commands.executeCommand('vscode.openFolder', uriOfWorkspace);
```

**Note:** It is not recommended using the `workspace.workspaceFile` location to write configuration data directly into the file. Instead use the `workspace.getConfiguration().update()` method, which will work both when a single folder is opened as well as an untitled or saved workspace.

### vscode.env.shell

The proposed `vscode.env.shell` API is now stable. This should be used to get the default shell for the terminal instead of the `terminal.integrated.shell.<platform>` setting.

```ts
const shell = vscode.env.shell;
if (shell.search(/(powershell|pwsh)/i) !== -1) {
    // Do something special that PowerShell needs
}
```

### Support for OR in when clauses

It is now possible to use logical OR (`||`) in various contribution points where a `when` clause is expected.

For example, when adding menu contributions:

```json
"contributes": {
  "menus": {
    "editor/title": [{
      "command": "showPreviewToSide",
      "when": "editorLangId == markdown || editorLangId == html",
      "group": "navigation"
    }]
  }
}
```

The AND `&&` operator has a higher precedence over OR `||`.

### New theme color for active input options

A new color, `inputOption.activeBackground`, allows you to control the background color of activated options in input fields. This color reuses `focusBorder` so that it works better with themes by default. We've also set the default for `inputOption.activeBorder` to transparent so that the border is only used for focus.

### Install extensions before running extension integration test

With `vscode-test@1.1.0`, extension authors can resolve the CLI path for the downloaded copy of VS Code and use that path for extension management.

For example:

```ts
const cp = require('child_process');
const { downloadAndUnzipVSCode, resolveCliPathFromExecutablePath } = require('vscode-test');

(async () => {
  const vscodeExecutablePath = await downloadAndUnzipVSCode('1.36.0');
  const cliPath = resolveCliPathFromExecutablePath(vscodeExecutablePath);

  cp.spawnSync(cliPath, ['--install-extension', '<EXTENSION-ID-OR-PATH-TO-VSIX>'], {
    encoding: 'utf-8',
    stdio: 'inherit'
  });
})();
```

Read more in the updated [Testing Extension documentation](https://code.visualstudio.com/api/working-with-extensions/testing-extension).

### Updated Travis CI script for running extension integration tests on Linux

The recommended configuration (see below) for extension integration tests might not work in the latest Travis CI Linux environment:

```bash
if [ $TRAVIS_OS_NAME == "linux" ]; then
  export CXX="g++-4.9" CC="gcc-4.9" DISPLAY=:99.0;
  sh -e /etc/init.d/xvfb start;
  sleep 3;
fi
```

You can now use the Xvfb script below instead:

```bash
if [ $TRAVIS_OS_NAME == "linux" ]; then
  export DISPLAY=':99.0'
  /usr/bin/Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
fi
```

Read more in the updated [Continuous Integration documentation](https://code.visualstudio.com/api/working-with-extensions/continuous-integration).

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project.

Note that you cannot publish an extension that uses a proposed API. We may likely make breaking changes in the next release and we never want to break existing extensions.

### Extension terminals

The proposed TerminalRenderer API introduced last year has gone through a major change and is now deprecated in favor of "Extension Terminals". The new API aligns much more with how the terminal actually works, the extension now implements a `Pseudoterminal` object, which is similar to a *nix-style pty.

Here's the current proposal:

```ts
export namespace window {
  export function createTerminal(options: ExtensionTerminalOptions): Terminal;
}

export interface ExtensionTerminalOptions {
  name: string;
  pty: Pseudoterminal;
}

interface Pseudoterminal {
  onDidWrite: Event<string>;
  onDidOverrideDimensions?: Event<TerminalDimensions | undefined>;
  onDidClose?: Event<void>;
  open(initialDimensions: TerminalDimensions | undefined): void;
  close(): void;
  handleInput?(data: string): void;
  setDimensions?(dimensions: TerminalDimensions): void;
}
```

Here are some of the big changes:

* An extension terminal is now done in much the same way as regular terminals, just using a different options object. This also removes the confusing access of the `Terminal` object via `TerminalRenderer.terminal`.
* The idea of an OS process is gone and having to return an exit code. The `Pseudoterminal` can simply close the terminal and all error display/notifications are driven by the extension.
* `Pseudoterminal.open` is called with the initial dimensions if they are available (the terminal panel has been displayed before).

Here is a simple example of how you could do a simple terminal that echoes back your input:

```ts
const writeEmitter = new vscode.EventEmitter<string>();
let line = '';
const pty = {
  onDidWrite: writeEmitter.event,
  open: () => writeEmitter.fire('Type and press enter to echo the text\r\n\r\n'),
  close: () => {},
  handleInput: (data: string) => {
    if (data === '\r') {
      writeEmitter.fire(`\r\necho: "${line}"\r\n\n`);
      line = '';
    } else {
      line += data;
      writeEmitter.fire(data);
    }
  }
};
const terminal = vscode.window.createTerminal({ name: `My Extension REPL`, pty });
terminal.show();
```

We hope to stabilize this and remove the deprecated terminal renderers relatively quickly, likely between v1.38 and v1.39.

### vscode.CustomExecution2

This API is the evolution of `vscode.CustomExecution`. It uses the new and improved `vscode.Pseudoterminal` to allow task providers to create tasks that enable code within an extension to fully control a terminal, instead of just executing a script or process.

```ts
export class CustomExecution2 {
  /**
    * @param process The [Pseudoterminal](#Pseudoterminal) to be used by the task to display output.
    * @param callback The callback that will be called when the task is started by a user.
    */
  constructor(callback: (thisArg?: any) => Thenable<Pseudoterminal>);

  /**
    * The callback used to execute the task. Cancellation should be handled using
    * [Pseudoterminal.close](#Pseudoterminal.close). When the task is complete fire
    * [Pseudoterminal.onDidClose](#Pseudoterminal.onDidClose).
    */
  callback: (thisArg?: any) => Thenable<Pseudoterminal>;
}
```

### vscode.commands.onDidExecuteCommand

There is a new event that fires whenever a command has been executed and the event's payload identifies the command and arguments.

```ts
export interface CommandExecutionEvent {
  command: string;
  arguments: any[];
}
export namespace commands {
  /**
   * An event that is emitted when a [command](#Command) is executed.
   */
  export const onDidExecuteCommand: Event<CommandExecutionEvent>;
}
```

### Webview.toWebviewResource and Webview.cspSource

The [previously proposed Webview.resourceRoot API](https://code.visualstudio.com/updates/v1_36#_webviewresourceroot) has been reworked.

There are now two proposed properties on webviews:

* `Webview.toWebviewResource` - Convert a URI for the local file system to one that can be used inside webviews.

  For desktop VS Code, this will convert `file:` URIs into `vscode-resource:` URIs.

* `Webview.cspSource` - The content security policy source for webview resources.

  For desktop VS Code, this would be the string `vscode-resource:`.

```ts
const panel = vscode.window.createWebviewPanel(CatCodingPanel.viewType, 'Cat Coding', vscode.ViewColumn.One, {
    // Restrict the webview to only loading local content from our extension's `media` directory.
    localResourceRoots: [vscode.Uri.file(path.join(extensionPath, 'media'))]
});

const imagePath = vscode.Uri.file(path.join(extensionPath, 'media'));

panel.html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${panel.webview.cspSource} https:;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cat Coding</title>
</head>
<body>
    <img src="${panel.webview.toWebviewResource(imagePath)}/cat.gif" width="300" />
</body>
</html>`
```

## Engineering

### Removing `suppressImplicitAnyIndexErrors` in the VS Code codebase

When we [enabled strict null checking for VS Code's source](https://code.visualstudio.com/blogs/2019/05/23/strict-null), we also turned on `"suppressImplicitAnyIndexErrors": true` in the TypeScript compiler. This setting allows using bracket accessors on values, even if the value's type does not specifically have an index access signature.

This is problematic because it can easily end up hiding errors that TypeScript could have caught:

```ts
const map = new Map()
map.set('property', true);

...

if (map['property']) { // This is not an TS error but it does not do what you want!
    doImportantStuff();
}
```

This iteration, we removed the `suppressImplicitAnyIndexErrors` workaround and fixed the 250 or so errors it was suppressing. The two most common fixes were to add explicit index signatures and to switch to using a proper `Map` type for storing a map instead of using an object literal.

### Progress on grid-based workbench layout

This milestone, we picked up on the efforts started a few milestones regarding a grid-based layout for the workbench. Once our initial solution is complete, the new version of the workbench should function similarly, but it will be more flexible for adding new layout options and features.

One new behavior we are testing is the ability to drag the terminal all the way up, completely hiding the editor. For now, the layout is still behind a flag `workbench.useExperimentalGridLayout`. We enabled it by default in Insiders during the last milestone, and this will continue for the next milestone as well.

### Continued exploration of iframe-based webviews

We continued our exploration of using normal iframes to host VS Code's webviews. As part of this, we now support loading every iframe-based webview in a unique origin. This ensures that webviews from multiple extensions cannot affect one another.

You can now also enable iframe-based webviews in desktop VS Code by setting: `"webview.experimental.useExternalEndpoint": true`. Note that using iframe-based webviews currently requires an internet connection. While iframe-based webviews are still experimental, we would appreciate feedback from webview extension authors on the parts of your extension that don't work when run using iframe-based webviews.

## New documentation

### Remote Development tutorials

There are new introductory tutorials to walk you through running Visual Studio Code with the Remote Development extensions.

* [Remote via SSH](https://code.visualstudio.com/docs/remote/ssh-tutorial) - Connect to remote and virtual machines with Visual Studio Code via SSH.
* [Working in WSL](https://code.visualstudio.com/docs/remote/wsl-tutorial) - Run Visual Studio Code in Windows Subsystem for Linux.
* [Dev Containers](https://code.visualstudio.com/docs/devcontainers/tutorial) - Run Visual Studio Code in a Docker Container.

## Notable fixes

* [422](https://github.com/microsoft/vscode/issues/422): Preserve window size when restoring a maximized window
* [70371](https://github.com/microsoft/vscode/issues/70371): emmet tab completion is slow in large files
* [76922](https://github.com/microsoft/vscode/issues/76922): Ordering of Side bar views is persisted across all windows
* [76989](https://github.com/microsoft/vscode/issues/76989): disable extensions not working for extension debugging
* [77110](https://github.com/microsoft/vscode/issues/77110): Debug integrated terminal support doesn't get default terminal
* [77926](https://github.com/microsoft/vscode/issues/77926): Typing "string;" sometimes inserts "strings;"

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to `vscode`:

* [kaoru (@0x6b)](https://github.com/0x6b): Lowercase 'For' in 'Checking For Updates...' menu item [PR #77972](https://github.com/microsoft/vscode/pull/77972)
* [Akshita Agarwal (@akshita31)](https://github.com/akshita31): Modify dotnet build templates [PR #76318](https://github.com/microsoft/vscode/pull/76318)
* [Angelo (@angelozerr)](https://github.com/angelozerr)
  * Add '.ent' and '.mod' file extensions  for the XML language [PR #77496](https://github.com/microsoft/vscode/pull/77496)
  * [XML] Use the same HTML language configuration for XML [PR #77751](https://github.com/microsoft/vscode/pull/77751)
* [Brett Cannon (@brettcannon)](https://github.com/brettcannon)
  * Remove snakemake files from being classified as Python files [PR #76625](https://github.com/microsoft/vscode/pull/76625)
  * Fix a spellling mistake in a doc string [PR #77766](https://github.com/microsoft/vscode/pull/77766)
* [Shiva Prasanth (@cedric05)](https://github.com/cedric05): in multiple git repositories workspace, can't view/focus changes on keypress up arrow and enter in `source control provider view` [PR #73139](https://github.com/microsoft/vscode/pull/73139)
* [Christian Oliff (@coliff)](https://github.com/coliff): Fix 'Untitled' typo [PR #78272](https://github.com/microsoft/vscode/pull/78272)
* [Connor Peet (@connor4312)](https://github.com/connor4312): fix(sash): dragging being difficult with iframe-containing extensions [PR #77121](https://github.com/microsoft/vscode/pull/77121)
* [Connor Shea (@connorshea)](https://github.com/connorshea): Add the .rbi file extension as a Ruby file type [PR #75155](https://github.com/microsoft/vscode/pull/75155)
* [Daniel Schildt (@d2s)](https://github.com/d2s): fix: spelling mistake correction [PR #77618](https://github.com/microsoft/vscode/pull/77618)
* [Dave Alongi (@dalongi)](https://github.com/dalongi): Configuration + UI to remember extensions allowed to handle URIs [PR #70844](https://github.com/microsoft/vscode/pull/70844)
* [Eric Amodio (@eamodio)](https://github.com/eamodio)
  * Renames ${qs} to ${query} [PR #78190](https://github.com/microsoft/vscode/pull/78190)
  * Fixes #68201 - adds qs.* label formatting tokens [PR #71874](https://github.com/microsoft/vscode/pull/71874)
* [Gabriel DeBacker (@GabeDeBacker)](https://github.com/GabeDeBacker): Use correct resolved TDO and task for custom execution [PR #77759](https://github.com/microsoft/vscode/pull/77759)
* [George Batalinski (@georgebatalinski)](https://github.com/georgebatalinski): search-widget(aria-live) status for results found [PR #77031](https://github.com/microsoft/vscode/pull/77031)
* [Haneef Mohammed (@haneefdm)](https://github.com/haneefdm): Preserve tree state in debug Variables view [PR #76476](https://github.com/microsoft/vscode/pull/76476)
* [Harry Hedger (@hedgerh)](https://github.com/hedgerh): onDidExecuteCommand API [PR #72345](https://github.com/microsoft/vscode/pull/72345)
* [Hung-Wei Hung (@hwhung0111)](https://github.com/hwhung0111)
  * Fix #76885 - Add icons for Configure File Association [PR #76900](https://github.com/microsoft/vscode/pull/76900)
  * Fix typos [PR #77407](https://github.com/microsoft/vscode/pull/77407)
  * Fix #76519 - Implmented resolveTask for jake [PR #76616](https://github.com/microsoft/vscode/pull/76616)
* [@ironyman](https://github.com/ironyman): Add border between tabs and breadcrumbs in HC [PR #76157](https://github.com/microsoft/vscode/pull/76157)
* [Julien Brianceau (@jbrianceau)](https://github.com/jbrianceau)
  * Fix 'unkown' typos [PR #76297](https://github.com/microsoft/vscode/pull/76297)
  * Fix 'postion' typos [PR #76187](https://github.com/microsoft/vscode/pull/76187)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Invalidate _breadcrumbsPickerIgnoreOnceItem when invoking breadcrumbs.focusNext/focusPrevious [PR #75982](https://github.com/microsoft/vscode/pull/75982)
  * Use active indent guide when active tree item is folder [PR #77852](https://github.com/microsoft/vscode/pull/77852)
  * Update precondition for ShowExtensionEditorFindCommand [PR #76241](https://github.com/microsoft/vscode/pull/76241)
  * Stop event propagation in `onEditSettingClicked` [PR #75967](https://github.com/microsoft/vscode/pull/75967)
* [Joel Day (@joelday)](https://github.com/joelday): Adding support for TaskProvider.resolveTask [PR #71027](https://github.com/microsoft/vscode/pull/71027)
* [Malige Julien (@JulienMalige)](https://github.com/JulienMalige): #42382 Preserve case while search and replace [PR #60311](https://github.com/microsoft/vscode/pull/60311)
* [Justin Grant (@justingrant)](https://github.com/justingrant): Fix JSDoc typo: "beginPattern" -> "beginsPattern" [PR #73141](https://github.com/microsoft/vscode/pull/73141)
* [Kamran Ayub (@kamranayub)](https://github.com/kamranayub)
  * chore(build): Add generated warning to monaco.d.ts [PR #77774](https://github.com/microsoft/vscode/pull/77774)
  * feat(Diagnostics): Add proposed DiagnosticTag.Deprecated enum member [PR #77760](https://github.com/microsoft/vscode/pull/77760)
* [Kostya Bushuev (@kostyabushuev)](https://github.com/kostyabushuev): Fixed #71134 [PR #71500](https://github.com/microsoft/vscode/pull/71500)
* [Maik Riechert (@letmaik)](https://github.com/letmaik): Fix incrementing of variables in cpuUsage.sh [PR #77512](https://github.com/microsoft/vscode/pull/77512)
* [Leonardo Carreiro (@lmcarreiro)](https://github.com/lmcarreiro): Fix #48403 broken UNC paths in markdown images [PR #74332](https://github.com/microsoft/vscode/pull/74332)
* [Mason Liu (@masliu)](https://github.com/masliu): Save resourceColumn for previews, fixes #74008 [PR #76647](https://github.com/microsoft/vscode/pull/76647)
* [Mathieu Plourde (@matpl)](https://github.com/matpl): Add shadow root support for isInDOM function [PR #72313](https://github.com/microsoft/vscode/pull/72313)
* [@mflan48](https://github.com/mflan48): Pull Request for issue #71683 [PR #72381](https://github.com/microsoft/vscode/pull/72381)
* [@Neonit](https://github.com/Neonit): Adjust Code Tab Size to 4 in Markdown Preview [PR #77745](https://github.com/microsoft/vscode/pull/77745)
* [Ryo Nishimura (@niharu)](https://github.com/niharu): Fix typo transitionDisposables [PR #76723](https://github.com/microsoft/vscode/pull/76723)
* [Orta (@orta)](https://github.com/orta): Updates to the latest icon theme from seti-ui [PR #76890](https://github.com/microsoft/vscode/pull/76890)
* [Dima Ovcharenko (@ovcharenko-di)](https://github.com/ovcharenko-di): Changed id to displayName, fixes #77141 [PR #77147](https://github.com/microsoft/vscode/pull/77147)
* [@pi1024e](https://github.com/pi1024e)
  * Fix for "whiteSpace" typo: Parameter name and word is one command word spelled "whitespace". [PR #77758](https://github.com/microsoft/vscode/pull/77758)
  * Clear disposables in the clearInput method on Disposables [PR #77479](https://github.com/microsoft/vscode/pull/77479)
* [Robert Ancell (@robert-ancell)](https://github.com/robert-ancell): snapcaft.yaml: Set the AppStream ID in the Snap metadata [PR #76432](https://github.com/microsoft/vscode/pull/76432)
* [@sharkykh](https://github.com/sharkykh): Put current git branch name as value when renaming [PR #72957](https://github.com/microsoft/vscode/pull/72957)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan)
  * Fix-72650 Shell paths should be validated before launched to prevent ambiguous errors - $Path check [PR #76349](https://github.com/microsoft/vscode/pull/76349)
  * Fix-75259 - Interact with files in search results  [PR #78337](https://github.com/microsoft/vscode/pull/78337)
  * Fix-76141 Add border around image in image preview [PR #76387](https://github.com/microsoft/vscode/pull/76387)
  * Fix-76662 add *.hpp.in to the file.associations in vscode out of the box [PR #76680](https://github.com/microsoft/vscode/pull/76680)
  * Fix-74281 Standard themes force SourceControlResourceGroup titles to UPPERCASE [PR #76286](https://github.com/microsoft/vscode/pull/76286)
  * Fix-73341 Installing extension from extension page "Install" button keeps the notification balloon in bottom right [PR #74795](https://github.com/microsoft/vscode/pull/74795)
  * Fix-75259 - Interact with files in search results [PR #76828](https://github.com/microsoft/vscode/pull/76828)
  * Fix-76315 add *.podspec to file associations  [PR #76447](https://github.com/microsoft/vscode/pull/76447)
* [@tomkv](https://github.com/tomkv): Add sk to locales than need region [PR #77942](https://github.com/microsoft/vscode/pull/77942)
* [Tony Xia (@tony-xia)](https://github.com/tony-xia)
  * Fixed a typo in vscode.executeCodeLensProvider args [PR #76357](https://github.com/microsoft/vscode/pull/76357)
  * onHighlghtRemoved -> onHighlightRemoved [PR #76359](https://github.com/microsoft/vscode/pull/76359)
  * Fixed variables wording issues [PR #76360](https://github.com/microsoft/vscode/pull/76360)
  * Fixed the wrong method name [PR #78041](https://github.com/microsoft/vscode/pull/78041)
* [Alexander (@usernamehw)](https://github.com/usernamehw): Revert "Add CLI flag to be able to disable smooth scroll in webviews" [PR #76944](https://github.com/microsoft/vscode/pull/76944)
* [Vanessa Phipps (@vlmphipps)](https://github.com/vlmphipps): Replace: honor preserveCase when replacing all [PR #78003](https://github.com/microsoft/vscode/pull/78003)
* [Wesley Wigham (@weswigham)](https://github.com/weswigham): Use a newer version of @types/node in the build folder to match all other folders [PR #77784](https://github.com/microsoft/vscode/pull/77784)
* [Xhulio Hasani (@xuhas)](https://github.com/xuhas): git HEAD info in tab title [PR #76329](https://github.com/microsoft/vscode/pull/76329)
* [Zero (@ZhangDezhi)](https://github.com/ZhangDezhi): avoid extensionTestsLocationURI.fsPath as it normalizes path (for #69… [PR #74214](https://github.com/microsoft/vscode/pull/74214)

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [Eric Amodio (@eamodio)](https://github.com/eamodio)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
* [Danny Tuppeny (@DanTup)](https://github.com/DanTup)

Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues.

Contributions to `language-server-protocol`:

* [Marcin Krzyzanowski (@krzyzanowskim)](https://github.com/krzyzanowskim): Typos fixes [PR #789](https://github.com/microsoft/language-server-protocol/pull/789)

Contributions to `debug-adapter-protocol`:

* [Pierson Lee (@pieandcakes)](https://github.com/pieandcakes): Update C/C++ data [PR #64](https://github.com/microsoft/debug-adapter-protocol/pull/64)
* [Jonah Graham (@jonahgraham)](https://github.com/jonahgraham): Update version to match changelog's latest entry [PR #67](https://github.com/microsoft/debug-adapter-protocol/pull/67)

Contributions to `vscode-generator-code`:

* [Tomáš Hübelbauer (@TomasHubelbauer)](https://github.com/TomasHubelbauer): Make this generator work with require.resolve [PR #179](https://github.com/microsoft/vscode-generator-code/pull/179)
* [Yoichi Kawasaki (@yokawasa)](https://github.com/yokawasa): Update quickstart page for ext-command-ts template [PR #178](https://github.com/microsoft/vscode-generator-code/pull/178)

Contributions to `vscode-vsce`:

* [Eric Amodio (@eamodio)](https://github.com/eamodio): Honors --yarn flag for prepublish step [PR #376](https://github.com/microsoft/vscode-vsce/pull/376)
* [Leo Hanisch (@HaaLeo)](https://github.com/HaaLeo): Support GitHub Issue Links [PR #345](https://github.com/microsoft/vscode-vsce/pull/345)
* [James George (@jamesgeorge007)](https://github.com/jamesgeorge007): Minor refactor resulting in concise code [PR #370](https://github.com/microsoft/vscode-vsce/pull/370)

Contributions to `inno-updater`:

* [Ta Thanh Dinh (@tathanhdinh)](https://github.com/tathanhdinh): Use is_null method to check if pointer is null [PR #10](https://github.com/microsoft/inno-updater/pull/10)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Bosnian:** Ismar Bašanović, Ernad Husremovic.
* **Bulgarian:** Hristiqn Hristov, Иван Иванов, Любомир Василев, Gheorghi Penkov.
* **Czech:** Tadeáš Cvrček, Daniel Padrta, David Jareš, Jan Hajek, Jakub Stibůrek, Michal Franc, Jan Kos, Radim Hampl, Jiří Hofman, Samuel Tulach, Jan Brudný.
* **Danish:** René Pape, Lasse Stilvang, Allan Kimmer Jensen, Lars Vange Jørgensen, Alexander Matzen, Martin Liversage, Johan Fagerberg, Thomas Larsen, Anders Lund, Anton Ariens.
* **Dutch:** Leroy Witteveen, Maxim Janssens, Luc Sieben, Hans Zoons, Jos Verlinde, Eric Algera, Damien van Gageldonk, Maxim Van Damme, Sven Klaasen, Tom Meulemans, Sujith Quintelier.
* **English (United Kingdom):** Martin Littlecott, Alexander Ogilvie, Tobias Collier, William Wood, Fabio Zuin, Mohit Nain, Dan Jacobs, Sulkhan Ninidze, Giorgi Jambazishvili, alshyab wa3ed, YASHU MITTAL, Tejas kale, Mohammad Idrees, Chris Dias.
* **Finnish:** Petri Niinimäki, Kiti Suupohja, Lasse Leppänen, Sebastian de Mel, Riku Riikonen, Valtteri Vatanen.
* **French:** Antoine Griffard, Thierry DEMAN-BARCELÒ, Corwin D'Ambre, Mohamed Sahbi, Rodolphe NOEL, Maxime Coquerel, Steven Dugois, Cédric M., Michael VAUDIN, Bastien Boussouf, franto.
* **German:** Julian Pritzi, Jakob von der Haar, Frank Lindecke, Dejan Dinic, Florian Erbs, Patrick Burke, Mario Koschischek, Florian Berger, Christof Opresnik, Hans Meiser, Carsten Kneip, Ettore Atalan, Thorsten Hans, Meghana Garise, Sebastian Seidl.
* **Greek:** Δημήτρης Παπαϊωάννου, Sotiris Koukios-Panopoulos, Jim Spentzos, Θοδωρής Τσιρπάνης, Stratos Kourtzanidis, Charalampos Fanoulis, John D, Vassilis Vouvonikos.
* **Hebrew:** חיים לבוב, Shalom Craimer, Matan Amos, Eyal Ellenbogen, Snir Broshi, Kyle Orin.
* **Hindi:** Satish Yadav, Amit Gusain, Sanyam Jain, Abhirav Kushwaha, Ashok Kumar Rathore, nirav adatiya, Rajeev Desai, shaswat rungta, Kumar Vaibhav, Pramit Das, Jagjeet Singh, Pratishek PII, Kiren Paul, Chethana S, Kishan K, Dhanvi Kapila.
* **Hungarian:** Levente Borbély, Péter Nagy, Levente Hallai Seiler, Dániel Tar, Richard Borcsik, Boldi Kemény.
* **Chinese Simplified:** Tingting Yi, 斌 项, paul cheung, Yiting Zhu, 建东 薛, feiyun0112 feiyun0112, Joel Yang, 刘瑞恒 刘瑞恒, Edi Wang, Junzhe Liu, Dave Young, 张锐, Yizhi Gu, Fan Su, 昊宇 周, Justin Liu, Pluwen, Tony Xia, Yu Zhang, XIANG ZUO, 少民 谈, 彦佐 刘, Zhiqiang Li, 福永 叶, G.Y. Z, Hao Hu, 志乐 龚, meng shao, Horie Yuan, Xiangrui Kong, Liam Kennedy, 王文杰, 赵畅畅, 松阳 樊, anson zhang, Young Bige, Shi Liu, Wang Debang, Jessica Zhang, Licheng Ren, 驰 雷, sun qing, xiong Fu, Lynne Dong, zhao yu, 吉姆 舒, 红东 黄, 游 尤, 剑秋 陶, Hanlin Yang, , bh wu, Bravo Yeung, Michael Zhang, Hanyu ZHANG, Y F, WangCG duoduobear, ziqiang sun, 越 郑, 正元 刘, Jiang LI, 擎 钟, peng wei, 涛 徐, 逍遥 许, Charles Lee, 瑜 周, WL, 伟 全, rsy iridescent, Simon Chan, 海龙 黄, 仁松 陈, Jieting Xu, panda small, 翼 张, Chen Yang, Wang Weixuan, 舜杰 杨, Ludi Fang, 雨齐 刘, 建 周, 涛 罗, cuibty wong, 立飞 李, LI ZHAO, 九鼎 谭.
* **Chinese Traditional:** 謝政廷, Winnie Lin, LikKee 沥祺 Richie, Martin Lau, Jeremy, 奕濬 潘, 小克, 煾 雪.
* **Indonesian:** Jakka Prihatna, Septian Adi, Arif Fahmi, Laurensius Dede Suhardiman, maziyank, Heston Sinuraya, Hendra Widjaja, Don Nisnoni, Rachmat Wahidi, Franky So, Bervianto Leo Pratama, Eriawan Kusumawardhono, Rifani Arsyad, Afnizar Nur Ghifari, Christian Elbrianno, Pradipta Hendri, Azhe Kun.
* **Italian:** Alessandro Alpi, Bruni Luca, Luigi Bruno, Andrea Dottor, Riccardo Cappello, Aldo Donetti, andrea falco, Emanuele Meazzo, Michael Longo, Marco Dal Pino.
* **Japanese:** EbXpJ6bp -, nh, Kyohei Uchida, Yoshihisa Ozaki, Hasefumi, TENMYO Masakazu, Takayuki Fuwa, Michihito Kumamoto, Koichi Makino, Seiji Momoto, Aya Tokura, Yosuke Sano, Makoto Sakaguchi, Kyohei Moriyama, 裕子 知念, Rie Moriguchi, Fujio Kojima, 美穂 山本, taniokae, 太郎 西岡, Hiroomi Kurosawa, Kazuya Ujihara, Yasuaki Matsuda.
* **Korean:** Hongju, 우현 조, Jong Heun Shin, 형섭 이, Kyunghee Ko, Youngjae Kim, siin lee, Jae Yong Kum, Hoyeon Han, Kevin Lee, SeungJin Jeong, Hong Kwon.
* **Latvian:** Kaspars Bergs, Andris Vilde.
* **Lithuanian:** Andrius Svylas, Augustas Grikšas, Tautvydas Derzinskas, Karolis Kundrotas, Martynas J..
* **Norwegian:** Andreas Rødland, Cookius Monsterius, Dag H. Baardsen, Ole Kristian Losvik, Stephan Eriksen.
* **Polish:** Warchlak, Wojciech Maj, Marcin Weksznejder, Rafał Całka, Tomasz Świstak, Tomasz Wiśniewski, Marek Biedrzycki, Szymon Seliga, Michal Szulc, Jakub Żmidziński, Bartek PL, Igor 05, Rafał Wolak, Tomasz Chojnacki, Piotr Grędowski, Czech Kaczmarek, Artur Pelczar, Marcin Floryan, Paweł Modrzejewski, Jakub Jedryszek, ‎Michał Stojke, Artur Zdanowski.
* **Portuguese (Brazil):** Alessandro Trovato, Thiago Dupin Ugeda, Weslei A. de T. Marinho, Rafael Lima Teixeira, Gerardo Magela Machado da Silva, Marcos Albuquerque, Loiane Groner, Alessandro Fragnani, Judson Santiago, Andrei Bosco, Fábio Corrêa, Roberto Fonseca, Fabio Lux, Emmanuel Gomes Brandão, Guilherme Pais, Rodrigo Vieira, André Gama.
* **Portuguese(Portugal):** Ana Rebelo, Nuno Carapito, Pedro Daniel, Pedro Teixeira, João Carvalho, Diogo Barros, José Rodrigues, Sandro Pereira, Vitor Barbosa, Tiago Antunes.
* **Romanian:** Mihai Marinescu, Stefan Gabos, Alexandru Staicu.
* **Russian:** Andrey Veselov, Дмитрий Кирьянов, michael v, Иван Лещенко, nata kazakova, Анатолий Калужин, Валерий Батурин, Эдуард Тихонов.
* **Serbian:** Milos Zivkovic, Radovan Skendzic.
* **Spanish:** Andy Gonzalez, Alvaro Enrique Ruano, Engel Aguilar, Carlos Mendible, José María Aguilar, David Fernández Aldana, Ricardo Rubio, Thierry DEMAN, julian3xl, Ing. Sergio Uziel Tovar Lemus, Mario Mendieta, Jorge Serrano Pérez, Ricardo Estrada Rdez.
* **Swedish:** Johan Spånberg, Notetur Nomen.
* **Tamil:** Merbin J Anselm, Kondasamy Jayaraman, Mani M, Boopesh Kumar, Vignesh Rajendran, Jeyanthinath Muthuram, Jaganathan B, Nithun Harikrishnan, Purusothaman Ramanujam, Krishna Pravin, Jeffin R P, Karunakaran Samayan, Vetri ..
* **Turkish:** Meryem Aytek, BlueAngel-MG, Fıratcan Sucu, Ahmetcan Aksu, Ömer Sert, Mehmet Yönügül, Anıl MISIRLIOĞLU, Sinan Açar, Misir Jafarov, Umut Can Alparslan, Yakup Ad, Hüseyin Fahri Uzun, S. Ferit Arslan, Mesut Pişkin, Okan Çetin, Bruh Moment, Muhammed Emin TİFTİKÇİ, Burak Göksel.
* **Ukrainian:** Dmytro Kutianskyi, Arthur Murauskas, Sviatoslav Ivaskiv, Yaroslav Y, George Molchanyuk, Did Kokos, Alexander Varchenko, Вадим Шашков, Евгений Коростылёв.
* **Vietnamese:** Khôi Phạm, Van-Tien Hoang, Belikhun, Việt Anh Nguyễn, Spepirus Shouru, Vuong Bui, Chủ Tất.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
