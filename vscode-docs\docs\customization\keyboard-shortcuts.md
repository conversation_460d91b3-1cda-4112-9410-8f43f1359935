## General

Key|Command
---|---
`kb(workbench.action.showCommands)`, `kbstyle(F1)`|Show Command Palette
`kb(workbench.action.quickOpen)`|Quick Open
`kb(workbench.action.newWindow)`|New window/instance
`kb(workbench.action.closeWindow)`|Close window/instance

## Basic Editing

Key|Command
---|---
`kb(editor.action.clipboardCutAction)`|Cut line (empty selection)
`kb(editor.action.clipboardCopyAction)`|Copy line (empty selection)
`kb(editor.action.movelinesdownAction)`|Move line down
`kb(editor.action.movelinesupAction)`|Move line up
`kb(editor.action.copylinesdownAction)`|Copy line down
`kb(editor.action.copylinesupAction)`|Copy line up
`kb(editor.action.deletelines)`|Delete line
`kb(editor.action.insertlineAfter)`|Insert line below
`kb(editor.action.insertlineBefore)`|Insert line above
`kb(editor.action.jumpToBracket)`|Jump to matching bracket
`kb(editor.action.indentlines)`|Indent line
`kb(editor.action.outdentlines)`|Outdent line
`kb(cursorHome)`|Go to beginning of line
`kb(cursorend)`|Go to end of line
`kb(cursorTop)`|Go to beginning of file
`kb(cursorBottom)`|Go to end of file
`kb(scrolllineup)`|Scroll line up
`kb(scrolllinedown)`|Scroll line down
`kb(scrollPageup)`|Scroll page up
`kb(scrollPagedown)`|Scroll page down
`kb(editor.fold)`|Fold (collapse) region
`kb(editor.unfold)`|Unfold (uncollapse) region
`kb(editor.toggleFold)`|Toggle Fold region
`kb(editor.foldRecursively)`|Fold (collapse) all subregions
`kb(editor.unfoldRecursively)`|Unfold (uncollapse) all subregions
`kb(editor.foldAll)`|Fold (collapse) all regions
`kb(editor.unfoldAll)`|Unfold (uncollapse) all regions
`kb(editor.action.addcommentline)`|Add line comment
`kb(editor.action.removecommentline)`|Remove line comment
`kb(editor.action.commentline)`|Toggle line comment
`kb(editor.action.blockcomment)`|Toggle block comment
`kb(editor.action.toggleWordWrap)`|Toggle word wrap

## Search and Replace

Key|Command
---|---
`kb(actions.find)`|Find
`kb(editor.action.startFindReplaceAction)`|Replace
`kb(editor.action.nextMatchFindAction)`|Find next
`kb(editor.action.previousMatchFindAction)`|Find previous
`kb(editor.action.selectAllMatches)`|Select all occurrences of Find match
`kb(editor.action.addSelectionTonextFindMatch)`|Add selection to next Find match
`kb(editor.action.moveSelectionTonextFindMatch)`|Move last selection to next Find match
`kb(toggleFindCaseSensitive)`|Toggle Find case sensitive
`kb(toggleFindRegex)`|Toggle Find regex
`kb(toggleFindWholeWord)`|Toggle Find whole word

## Multi-cursor and Selection

Key|Command
---|---
`kbstyle(Alt+Click)`|Insert cursor
`kb(editor.action.insertCursorabove)`|Insert cursor above
`kb(editor.action.insertCursorbelow)`|Insert cursor below
`kb(cursorUndo)`|Undo last cursor operation
`kb(editor.action.insertCursorAtendOfEachlineSelected)`|Insert cursor at end of each line selected
`kb(expandlineSelection)`|Select current line
`kb(editor.action.selectHighlights)`|Select all occurrences of current selection
`kb(editor.action.changeAll)`|Select all occurrences of current word
`kb(editor.action.smartSelect.expand)`|Expand selection
`kb(editor.action.smartSelect.shrink)`|Shrink selection
`kbstyle(Shift+Alt)` + drag mouse |Column selection
`kb(cursorColumnSelectup)`|Column selection up
`kb(cursorColumnSelectdown)`|Column selection down
`kb(cursorColumnSelectLeft)`|Column selection left
`kb(cursorColumnSelectRight)`|Column selection right
`kb(cursorColumnSelectPageup)`|Column selection page up
`kb(cursorColumnSelectPagedown)`|Column selection page down

## Rich Languages Editing

Key|Command
---|---
`kb(editor.action.triggerSuggest)`|Trigger suggestion
`kb(editor.action.triggerParameterHints)`|Trigger parameter hints
`kb(editor.action.formatDocument)`|Format document
`kb(editor.action.formatSelection)`|Format selection
`kb(editor.action.revealDefinition)`|Go to Definition
`kb(editor.action.peekDefinition)`|Peek Definition
`kb(editor.action.revealDefinitionAside)`|Open Definition to the side
`kb(editor.action.quickFix)`|Quick Fix
`kb(editor.action.goToReferences)`|Go to References
`kb(editor.action.rename)`|Rename Symbol
`kb(editor.action.inPlaceReplace.down)`|Replace with next value
`kb(editor.action.inPlaceReplace.up)`|Replace with previous value
`kb(editor.action.trimTrailingWhitespace)`|Trim trailing whitespace
`kb(workbench.action.editor.changeLanguageMode)`|Change file language

## Navigation

Key|Command
---|---
`kb(workbench.action.showAllSymbols)`|Show all Symbols
`kb(workbench.action.gotoline)`|Go to Line...
`kb(workbench.action.quickOpen)`|Go to File...
`kb(workbench.action.gotoSymbol)`|Go to Symbol...
`kb(workbench.actions.view.problems)`|Show Problems panel
`kb(editor.action.marker.nextInFiles)`|Go to next error or warning
`kb(editor.action.marker.prevInFiles)`|Go to previous error or warning
`kb(workbench.action.openpreviousRecentlyUsedEditorInGroup)`|Navigate editor group history
`kb(workbench.action.navigateBack)`|Go back
`kb(workbench.action.quickInputBack)`|Go back to previous step, when in the QuickInput UI
`kb(workbench.action.navigateForward)`|Go forward
`kb(editor.action.toggleTabFocusMode)`|Toggle Tab moves focus

## Editor Management

Key|Command
---|---
`kb(workbench.action.closeActiveEditor)`, `kbstyle(Ctrl+W)`|Close editor
`kb(workbench.action.closeFolder)`|Close folder
`kb(workbench.action.splitEditor)`|Split editor
`kb(workbench.action.focusFirstEditorGroup)`|Focus into first editor group
`kb(workbench.action.focusSecondEditorGroup)`|Focus into second editor group
`kb(workbench.action.focusThirdEditorGroup)`|Focus into third editor group
`kb(workbench.action.moveEditorLeftInGroup)`| Move editor left
`kb(workbench.action.moveEditorRightInGroup)`| Move editor right
`kb(workbench.action.moveActiveEditorGroupLeft)`|Move active editor group left/up
`kb(workbench.action.moveActiveEditorGroupRight)`|Move active editor group right/down

## File Management

Key|Command
---|---
`kb(workbench.action.files.newUntitledfile)`|New File
`kb(workbench.action.files.openfile)`|Open File...
`kb(workbench.action.files.openFileFolder)`|Open File... (macOS)
`kb(workbench.action.files.save)`|Save
`kb(workbench.action.files.saveAs)`|Save As...
`kb(saveAll)`|Save All
`kb(workbench.action.closeActiveEditor)`|Close
`kb(workbench.action.closeAllEditors)`|Close All
`kb(workbench.action.reopenClosedEditor)`|Reopen closed editor
`kb(workbench.action.keepEditor)`|Keep Open
`kb(workbench.action.opennextRecentlyUsedEditorInGroup)`|Open next
`kb(workbench.action.openpreviousRecentlyUsedEditorInGroup)`|Open previous
`kb(workbench.action.files.copyPathOfActivefile)`|Copy path of active file
`kb(workbench.action.files.revealActivefileInWindows)`|Reveal active file in Explorer
`kb(workbench.action.files.showOpenedfileInNewWindow)`|Show active file in new window/instance

## Display

Key|Command
---|---
`kb(workbench.action.toggleFullScreen)`|Toggle full screen
`kb(workbench.action.toggleEditorGroupLayout)`|Toggle editor layout
`kb(workbench.action.zoomIn)`|Zoom in
`kb(workbench.action.zoomOut)`|Zoom out
`kb(workbench.action.toggleSidebarVisibility)`|Toggle Sidebar visibility
`kb(workbench.view.explorer)`|Show Explorer / Toggle focus
`kb(workbench.view.search)`|Show Search
`kb(workbench.view.scm)`|Show Source Control
`kb(workbench.view.debug)`|Show Run
`kb(workbench.view.extensions)`|Show Extensions
`kb(workbench.action.replaceInfiles)`|Replace in files
`kb(workbench.action.search.toggleQueryDetails)`|Toggle Search details
`kb(workbench.action.terminal.openNativeConsole)`|Open new command prompt/terminal
`kb(workbench.action.output.toggleOutput)`|Show Output panel
`kb(markdown.showPreview)`|Toggle Markdown preview
`kb(markdown.showPreviewToSide)`|Open Markdown preview to the side

## Debug

Key|Command
---|---
`kb(editor.debug.action.toggleBreakpoint)`|Toggle breakpoint
`kb(workbench.action.debug.start)`|Start
`kb(workbench.action.debug.continue)`|Continue
`kb(workbench.action.debug.stepInto)`|Step into
`kb(workbench.action.debug.stepOut)`|Step out
`kb(workbench.action.debug.stepOver)`|Step over
`kb(workbench.action.debug.stop)`|Stop
`kb(editor.action.showHover)`|Show hover

## Integrated Terminal

Key|Command
---|---
`kb(workbench.action.terminal.toggleTerminal)`|Show integrated terminal
`kb(workbench.action.terminal.new)`|Create new terminal
`kb(workbench.action.terminal.copySelection)`|Copy selection
`kb(workbench.action.terminal.paste)`|Paste into active terminal
`kb(workbench.action.terminal.scrollup)`|Scroll up
`kb(workbench.action.terminal.scrolldown)`|Scroll down
`kb(workbench.action.terminal.scrollupPage)`|Scroll page up
`kb(workbench.action.terminal.scrolldownPage)`|Scroll page down
`kb(workbench.action.terminal.scrollToTop)`|Scroll to top
`kb(workbench.action.terminal.scrollToBottom)`|Scroll to bottom

