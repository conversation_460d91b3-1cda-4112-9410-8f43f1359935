# November 2019 (version 1.41)

## VS Code Remote Core

### Remote Explorer make-over

The Remote Explorer has a new look to optimize your workflow and make it less busy.

Below you can see the new remote targets and containers dropdown as well as the **Help and Feedback** options:

![Remote Explorer dropdowns](images/1_41/remote-explorer.gif)

### Download extensions locally

You can now configure VS Code to always download extensions locally while installing extensions in a remote window. To enable this feature, set `remote.downloadExtensionsLocally` to `true`.

## WSL

### Support for ARM

The VS Code Remote Server now also runs on Windows 10 ARM-based PCs such as the Surface Pro X.

### Server download and install improvements

Downloading the server behind proxies has been improved. Downloads and the unpacked bits are now verified to avoid corrupted installs.

### Multi distro support for WSL on Windows 10 builds 1803 and 1809

Users on Windows 10 1803 and 1809 now can open any Linux distribution. If the distro is not the default, the WSL extension will ask you if you want to change the default distro.

### Open folder and reload performance

Changing the input of a WSL window is now much faster.

## Containers

### Simpler to try out the Dev Container samples

You can now try a Dev Container sample in a single step. Run the **Dev Containers: Try a Dev Container Sample...** command and select the repository you want to try.

![Try a remote sample dropdown](images/1_41/remote-try-sample.png)

This command clones the selected sample repository into a Docker volume and opens it inside a container.

### Improved provisioning and startup performance

Performance has improved for several scenarios:

* Creating new containers.
* Reusing existing containers.
* Reloading the VS Code window.

See [issue #1823](https://github.com/microsoft/vscode-remote-release/issues/1823#issuecomment-557157018) for example measurements.

### More flexible opening of a repository into a container

The command [Dev Containers: Clone Repository in Container Volume...](https://code.visualstudio.com/docs/devcontainers/containers#_quick-start-open-a-public-git-repository-in-an-isolated-container-volume) is now more flexible and supports picking the Docker volume the repository should clone into. This enables you to open the same repository into different volumes, for example to investigate different branches of the repository.

You can now also clone multiple repositories into the same volume so that you do not have to manage multiple volumes.

### New configuration options for the VS Code Server

The `devcontainer.json` and the configuration files for attaching to containers have new options for the VS Code Server:

Property | Description
-|-
`"remoteUser"` | The user for starting the VS Code Server.
`"remoteEnv"` | A map of environment variables and values the VS Code Server will be started with.

### New configuration options for single containers

The `devcontainer.json` for containers based on a Dockerfile or a Docker image has new options for the container:

Property | Description
-|-
`"containerUser"` | The user for starting the container.
`"containerEnv"` | A map of environment variables and values the container will be started with.
`"mounts"` | Additional mount points for the container.

### Settings when attaching to containers

Configuration files for attaching to containers can now also have a `"settings"` property for machine settings. These settings are copied to the container when connecting to it for the first time. Changes to the machine settings of a container are then recorded in the configuration file.

### Forwarding SSH agent

The local SSH agent is now forwarded into the container. For example, if you are using SSH to checkout your repository, Git support seamlessly works when opening the folder in a container and the key is registered with the local SSH agent.

### Support for 'ssh://' as Docker host

The `"docker.host"` setting (or `DOCKER_HOST` environment variable) can now be set to a `ssh://user@host` URI to connect to a Docker host using SSH. Currently a private key needs to be registered with the local SSH agent for authentication to succeed.

### ARM support in preview

Support for ARMv7 32-bit and ARMv8 64-bit is available as a preview feature.

### Updated UID/GID on Linux

On Linux, when using a single container based on a Dockerfile or a Docker image, the user the VS Code Server will be run with is updated to use the same UID and GID as the local user running VS Code. This ensures the files created with VS Code in the container are also owned by the local user.

This only works when the container's user is not root and the target UID and GID are not already used by an existing user and group.

There is a new property `"updateRemoteUserUID"` in the `devcontainer.json ` that can be set to false to disable this feature.

## Remote extension authoring

### Multi extensionKind support

VS Code now supports running extensions defined with multiple extension kinds.

The following table shows given the extension with extensionKind(s), where VS Code enables/installs the extension.

| Extension Kind \ Location   | Local (Only) | Remote (Only)     | Both |
| :---        |    :----:   |          :----:   | :---   |
|    ["ui"]  |  ✅        | ❌    | ✅ (Local) |
| ["workspace"]   | ❌        | ✅     | ✅ (Remote) |
| ["ui", "workspace"]   | ✅       | ✅     | ✅ (Local) |
| ["workspace", "ui"]   | ✅       | ✅     | ✅ (Remote) |

See the [Supporting Remote Development documentation](https://code.visualstudio.com/api/advanced-topics/remote-extensions#incorrect-execution-location) for more information.
