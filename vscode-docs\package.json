{"name": "vscode-docs", "version": "0.10.3", "author": {"name": "Microsoft Corporation"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-docs.git"}, "scripts": {"check-lfs": "node ./build/check-lfs.js", "lint": "eslint **/*.js", "prepare": "husky install"}, "devDependencies": {"eslint-plugin-security": "^1.7.1", "gulp": "~5.0.0", "husky": "^7.0.0", "lint-staged": "^12.3.3", "shelljs": "^0.8.5"}, "lint-staged": {"*.{gif,mp4,jpg,png,GIF,MP4,JPG,PNG}": ["npm run check-lfs"]}}