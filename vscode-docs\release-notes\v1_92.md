---
Order: 101
TOCTitle: July 2024
PageTitle: Visual Studio Code July 2024
MetaDescription: Learn what is new in the Visual Studio Code July 2024 Release (1.92)
MetaSocialImage: 1_92/release-highlights.png
Date: 2024-8-1
DownloadVersion: 1.92.2
---
# July 2024 (version 1.92)

**Update 1.92.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22July+2024+Recovery+1%22+is%3Aclosed).

**Update 1.92.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22July+2024+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the July 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [Default browser](#configure-the-browser-to-open-links) - Configure which browser to use for opening links in VS Code.
* [Revert PRs](#github-pull-requests-and-issues) - Easily create a revert PR for a merged PR.
* [Extension updates](#improved-extension-update-experience) - More easily configure auto updating of extensions.
* [Override profiles](#override-existing-profile) - Override an existing profile with the Profiles Editor.
* [Paste files in CSS](#paste-and-drop-files-in-css) - Quickly add CSS file references with paste or drag and drop.
* [Move Panel to top](#move-panel-to-top) - Position the Panel section at the top of the workbench.
* [Copilot uses GPT-4o](#github-copilot) - GitHub Copilot Chat upgraded to OpenAI's GPT-4o.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Improved debugging experience

#### Accessibility help dialogs

We added accessibility help dialogs for the Run and Debug view and Debug Console. You get a hint about opening the accessible help dialog when either view is focused. Configure this hint via the setting <a href="vscode://settings/accessibility.verbosity.debug" codesetting="true">`accessibility.verbosity.debug`</a>.

#### Debug Console Accessible View

Run the command **Open Accessible View** (`kb(editor.action.accessibleView)`) to inspect Debug Console content character by character, line by line.

The setting <a href="vscode://settings/debug.autoExpandLazyVariables" codesetting="true">`debug.autoExpandLazyVariables`</a> is now enabled when in `Screen Reader Optimized Mode`, for ready access to variable values.

When an expression is evaluated in the Debug Console, its value is now announced to screen reader users.

#### Dynamic watch variable announcement

When debugging with a screen reader enabled, you now hear when watch variable values change. Configure this with <a href="vscode://settings/accessibility.debugWatchVariableAnnouncements" codesetting="true">`accessibility.debugWatchVariableAnnouncements`</a>.

### Configure keybindings action

In the previous milestone, we introduced an action to configure unassigned keybindings in the accessibility help dialog. We've now added the **Configure Assigned Keybindings** command (`kb(editor.action.accessibilityHelpConfigureAssignedKeybindings)`) to complement this action.

## Workbench

### Move Panel to top

You can now move the [Panel](https://code.visualstudio.com/docs/getstarted/userinterface) to the top of the workbench, above the editor area. By default, the Panel is placed at the bottom and includes views, such as the terminal, Output panel, and Debug Console. This enhancement complements the existing options of positioning the panel to the left, right, and bottom.

<video src="images/1_92/panel-top.mp4" title="Move Workbench Panel to the top" autoplay loop controls muted></video>

### Profiles Editor preview

In this milestone, we continued to improve the Profiles Editor by making it more user-friendly and by having a look and feel that's consistent with the Settings Editor.

![Profiles Editor showing all user profiles and their settings.](images/1_92/profiles-editor.png)

The Profiles Editor is available as an experimental feature behind the <a href="vscode://settings/workbench.experimental.enableNewProfilesUI" codesetting="true">`workbench.experimental.enableNewProfilesUI`</a> setting. Once enabled, you can access the Profiles Editor from the Settings gear icon in the bottom left corner of the window.

![Settings menu showing the Profiles menu item to open the Profiles Editor.](images/1_92/profiles-editor-action.png)

### Override existing profile

You can now override an existing profile, including the default profile, by creating a new profile with the same name.

<video src="images/1_92/override-profile.mp4" title="Override existing Profile" autoplay loop controls muted></video>

### Improved extension update experience

We made several improvements to the extension update experience to give you more control over updating extensions and make it easier to manage auto updating of extensions.

#### Auto updating all extensions

We have changed the global extension auto update action in the Extension view title area to **Enable Auto Update for All Extensions** and **Disable Auto Update for All Extensions** actions. With these actions, you can enable or disable auto update for all extensions at once.

![Manage Auto Updating Extensions.](images/1_92/manage-autoupdate.png)

#### Auto updating individual extensions

We improved the individual extension auto update experience by always showing the action to enable or disable auto update for the extension. This makes it easier to manage auto updating of extensions.

![Auto update configuration per extension.](images/1_92/extension-autoupdate.png)

#### Disable auto update for extensions installed via VSIX

When you install an extension via VSIX, auto update for that extension is disabled by default. This enables you to work with the version of the extension you have installed, without it being updated automatically.

#### More control over updating extensions

User consent is now required when you update an installed extension version that has no executable code to a version that has executable code. This gives you control to review such updates before they are applied. The following video demonstrates the experience when updating an extension with no code to a version with code.

<video src="images/1_92/auto-update-review.mp4" title="User consent required to update extension with no code to a version with code" autoplay loop controls muted></video>

Selecting the **Review** button opens the extension change log or the extension repository in the browser. You can review the changes and then decide if you want to update the extension.

### Settings Editor jump issue fixed

The Settings Editor used to jump after modifying a setting and changed focus to another one. And this jump was worse the more one scrolled before modifying a setting.
We changed the way the Settings Editor re-renders settings after modification, and the Settings Editor no longer jumps after modifying a setting.

<video src="images/1_92/settings-editor-nojitter.mp4" title="Settings Editor not jumping after setting check mark checked" autoplay loop controls muted></video>

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### URL handling for settings

VS Code can now handle "settings" URLs of the format `vscode://settings/setting.name` (`vscode-insiders://settings/setting.name` for Insiders, and `code-oss` for the OSS version) and will open the Settings Editor to the specified setting. If no setting is given, then the Settings Editor is still opened. When an anchor tag with a settings URL is used in the release notes, and the release notes are open in VS Code, then we do special handling and rendering, as described in the previous `codesetting` feature.

![Setting URL in release notes](./images/1_92/setting-url-in-release-notes.gif)

### Configure the browser to open links

A new setting <a href="vscode://settings/workbench.externalBrowser" codesetting="true">`workbench.externalBrowser`</a> enables you to configure which browser to use for opening links. By default, the operating system standard browser is used. You can configure this setting on a per-workspace level and is also Settings Sync enabled.

Specify the full path to the browser executable as the settings value. Alternatively, to ensure correct functioning across devices, you can also use browser aliases, such as `edge`, `chrome`, or `firefox`.

![Configure the browser to open links.](images/1_92/default-browser.gif)

### Disable auto file open on drag and drop

Previously, dragging and dropping a file into the explorer would also automatically open it in the editor. In some cases, this might be undesirable. A new setting <a href="vscode://settings/explorer.autoOpenDroppedFile" codesetting="true">`explorer.autoOpenDroppedFile`</a> enables you to toggle this behavior. By default, the file continues to be opened when dragged and dropped, but when set to `false`, this behavior is disabled.

## Editor

### Lightbulb control improvements

At times, the lightbulb control might block code in the editor. To address this, we introduced an improved heuristic for the lightbulb control, which displays the lightbulb in the gutter when there is no space, instead of blocking code in the editor.

![Lightbulb overlaps with code (left) versus positioned in the gutter (right).](images/1_92/lightbulb-positioning.png)

You can toggle the lightbulb control in the editor with the <a href="vscode://settings/editor.lightbulb.enabled" codesetting="true">`editor.lightbulb.enabled`</a> setting.

## Diff Editor

### More compact diffs in Chat

We iterated on the diff editor layout in the Chat view/inline Chat and made it more compact.

**Before**:

![Previous diff editor layout, which is less compact.](images/1_92/diffEditor_inlineChat_before.png)

**After**:

![More compact diff editor layout.](images/1_92/diffEditor_inlineChat_after.png)

## Source Control

### Incoming/Outgoing changes graph

This milestone, we are enabling the visualization of the incoming and outgoing changes using a graph. The graph contains the current branch, the current branch's upstream branch, and an optional base branch. The root of the graph is the common ancestor of these branches.

We have made several improvements to the history item hover:

* Enabled multi-select to see changes across multiple history items that belong to the same branch.
* Added options to the `...` menu to filter history items from the remote/base branches.
* Added actions to fetch, pull, and push history items.

Give it a try and let us know what you think!

You can disable the graph visualization of incoming/outgoing changes by toggling the <a href="vscode://settings/scm.showHistoryGraph" codesetting="true">`scm.showHistoryGraph`</a> setting.

![Source control view showing a graph visualization of the incoming and outgoing changes.](images/1_92/incoming-outgoing-changes.png)

### Terminal shell integration

The Source Control view is refreshed after each source control operation as well as file-system events scoped to specific files/folders. When a git command is executed in the integrated terminal, there might be a delay between the completion of the command and refresh of the Source Control view. To reduce this delay, starting this milestone, we are using the terminal shell integration API to detect the successful completion of various git commands (for example, `add`, `checkout`, `commit`, `fetch`, `pull`, `push`, and more) executed in the integrated terminal and refresh the Source Control view.

## Notebooks

### Multi-cell commenting

The Notebook Editor now supports toggling comments on one or multiple cells at a time. Do this by selecting one or multiple cell containers, followed by the keyboard shortcut `kb(notebook.commentSelectedCells)`.

<video src="images/1_92/notebook-multi-cell-comment.mp4" title="Notebook Multi Cell Commenting" autoplay loop controls muted></video>

## Terminal

### New scroll bar

The scroll bar in the terminal now looks just like the scroll bar in the editor:

![The terminal scroll bar now looks just like the editor's scroll bar](images/1_92/terminal-scroll-bar.png)

The overview ruler is the same as before, with successful commands on left, find result in middle, failed commands on right. But now it perfectly aligns with the scroll bar slider.

## Debug

### Show variable types while debugging

VS Code now shows the data types of variables while debugging when the setting <a href="vscode://settings/debug.showVariableTypes" codesetting="true">`debug.showVariableTypes`</a> is turned on:

![Show variable data types in the Variables section while debugging.](./images/1_92/debug-types.png)

## Languages

### Update Markdown links on paste

VS Code can now help you move sections of text between Markdown documents by updating links in any copy and pasted text. With this feature, VS Code fixes all relative path links, reference links, and all images/videos with relative paths, so that they work in the new document.

<video src="images/1_92/md-paste-link-update.mp4" title="Links being updated when copy and pasting between Markdown files" autoplay loop controls muted></video>

This feature kicks in whenever you copy and paste text with links between two Markdown files. If there are links that can be updated, you will see the paste widget after pasting. Select **Paste and Update Pasted Links**, and VS Code fixes up the links for you.

You can disable this feature entirely by setting <a href="vscode://settings/markdown.updateLinksOnPaste" codesetting="true">`markdown.updateLinksOnPaste`</a> to `false`.

### Paste and drop files in CSS

Do you need to use an image in your CSS? Now you can quickly insert a `url()` reference just by dragging and dropping, or copy and pasting the image file into the CSS editor:

<video src="images/1_92/css-paste-url.mp4" title="Dropping and pasting an image file to insert a url()" autoplay loop controls muted></video>

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), enable you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* GPG keyboxd support
* Local port range configuration

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_92.md).

## Contributions to extensions

### GitHub Copilot

#### GPT-4o Upgrade for Copilot Chat

We've upgraded the Copilot Chat view from GPT-4-Turbo to GPT-4o, OpenAI's most powerful model to date. Our experiments show that GPT-4o significantly enhances Copilot’s coding capabilities, providing faster, more accurate, and higher-quality code and explanations directly in your editor. This was evident during the rollout, as developers increasingly used the Copilot Chat view and incorporated more of its code suggestions.

We are constantly experimenting to identify the best models for different tasks, balancing performance and capability while being committed to quality, safety, and security. For more details on the adoption of GPT-4o in GitHub Copilot, check out [GitHub's changelog](https://aka.ms/github-copilot-chat-gpt-4o).

#### Public code matching in chat

You can allow GitHub Copilot to return code that could match publicly available code on GitHub.com. When this functionality is enabled for your [organization subscription](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/setting-policies-for-copilot-in-your-organization/managing-policies-for-copilot-in-your-organization#policies-for-suggestion-matching) or [personal subscription](https://docs.github.com/en/copilot/managing-copilot/managing-copilot-as-an-individual-subscriber/managing-copilot-policies-as-an-individual-subscriber#enabling-or-disabling-suggestions-matching-public-code), Copilot code completions already provide you with details about the matches that were detected. We are working on enabling showing you these matches for public code in Copilot Chat as well.

We are rolling this out gradually for Copilot Chat users. Once it's enabled for your machine, you might see a message at the end of the response with a **View matches** link. If you select the link, an editor opens that shows you the details of the matching code references with more details.

![Chat code referencing example.](images/1_92/code-references.png)

#### Attachments in chat requests

Copilot Chat supports adding explicit attachments to your chat request via the **Attach Context** (`kb(workbench.action.chat.attachContext)`) command. These are now rendered as part of submitted chat requests in the chat history. Selecting a file attachment opens the corresponding file and range in the editor.

![Attachments in chat requests](images/1_92/chat-attachments.png)

Additionally, we now make it clearer when large explicit attachments that overflow the context window were partially or completely omitted from the request.

![Warning and hover for large attachments](images/1_92/chat-attachments-warning.png)

#### Improvements to `/new`

We updated the `/new` slash command to support quick file generation. You can provide additional context to Copilot during file or project creation by using chat variables, such as `#selection`. Additionally, `/new` was enhanced to enable saving the generated files and folders in an existing workspace.

![Create a new single file with /new in Copilot Chat.](images/1_92/copilot-new-single-file.png)

#### Access VS Code commands from Chat

A new slash command on the `@vscode` chat participant, `/runCommand`, enables you to search for and execute a core VS Code command. For example, to toggle the Developer Tools:

![Toggle Developer Tools with the /runCommand slash command in Copilot Chat.](images/1_92/copilot-runcommand-developer-mode.png)

You can enable this new slash command with <a href="vscode://settings/github.copilot.chat.runCommand.enabled" codesetting="true">`github.copilot.chat.runCommand.enabled`</a>.

### Python

#### Improved Python discovery using python-environment-tools

In the last release, we announced the [Python environment tools](https://github.com/microsoft/vscode-docs/blob/main/release-notes/v1_91.md#python-environment-discovery-using-python-environment-tools), which redesigned the Python discovery infrastructure focused on performance. This approach reduces the need for executing python binaries to probe for information and thus improving performance.

Starting in this release, we are rolling out this enhancement as part of an experiment. If you are interested in trying this out, you can set `"python.locator"` to `"native"` in your User `settings.json` and reload your VS Code window. Visit the [python-environment-tools repo](https://github.com/microsoft/python-environment-tools) to learn more about this feature, ongoing work, and provide feedback.

#### Display execution status for native REPL

The experimental native REPL (`"python.REPL.sendToNativeREPL": true`) now displays success/failure UI, similar to that in a Jupyter cell, depending on the execution outcome. Furthermore, we made improvements so that we no longer display an empty line on cells that generate no output.

#### Inline variable values in source code

The [Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) extension introduced an Inline Values feature to enhance your Python debugging experience. With Inline Values, you can view the value of variables inline in the editor, next to the corresponding line of code during a debugging session. This helps you to quickly understand the state of your program, without having to hover over variables or checking the Variables section in the Run and Debug view.

![Show Python Inline Variables.](images/1_92/show-python-inline-variables.png)

To enable this feature, set the configuration value <a href="vscode://settings/debugpy.showPythonInlineValues" codesetting="true">`debugpy.showPythonInlineValues`</a> to `true` in your User settings.

> **Note**: This feature is currently in exploration state and improvements are still being made. Please provide any feedback you may have in the [vscode-python-debugger repo](https://github.com/microsoft/vscode-python-debugger)!

#### Improved Debug Welcome view

The Debug Welcome view now includes a button for quick access to automatic Python configurations when a Python file is open in the editor.

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which enables you to work on, create, and manage pull requests and issues. New features include:

* Revert pull requests by using the **Revert** button in the pull request description of merged PRs
* PRs whose branch has been deleted can now be viewed in the Pull Requests view.
* The **Open Pull Request on GitHub.com** action shows even when you have multiple PRs checked out.

Review the [changelog for the 0.94.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0940) release of the extension to learn about the other highlights.

## Extension authoring

### Basic authentication for network proxies

Extensions that use the `https` Node.js module can now use network proxies that require basic authentication.

## Preview Features

### True inline diffs

This iteration, we introduced the <a href="vscode://settings/diffEditor.experimental.useTrueInlineView" codesetting="true">`diffEditor.experimental.useTrueInlineView`</a> setting (off by default). When you enable this setting and the inline view is used, single-line changes are rendered inline:

![True Inline View in the diff editor.](images/1_92/diffEditor_trueInlineView.png)

This is how the inline view looks without this experimental flag turned on:

![Default Inline View in the diff editor.](images/1_92/diffEditor_defaultInlineView.png)

This feature is experimental and future changes are to be expected.

### VS Code-native IntelliSense for PowerShell

The VS Code-native PowerShell IntelliSense experimental feature has seen some significant improvements this release. To enable this feature on Windows or macOS:

```json
"terminal.integrated.suggest.enabled": true
```

In addition to the feature being much more reliable, many other improvements were made as seen below.

#### Completions are not truncated

Completions now show _exactly_ what is typed, instead of a truncated version. For example, when searching for directory names, the `.\` is now included and is highlighted correctly.

**Before**:

![Before the completion for "src" would show "src"](images/1_92/terminal-truncate-before.png)

**After**:

![After the completion for "src" would show ".\src"](images/1_92/terminal-truncate-after.png)

#### Optimized completions for navigating directories

Directory names now include a trailing `\` or `/`, which, when completed, refresh completions for the new directory.

<video src="images/1_92/terminal_cd_flow.mp4" title="Completing directories will complete the final \, and refresh the completions for that directory" autoplay loop controls muted></video>

#### Configure Enter behavior

The new `terminal.integrated.suggest.runOnEnter` setting enables you to configure the `Enter` behavior to run the command if certain conditions are met. The following values are available:

- `"always"`: Always run on `Enter`.
- `"exactMatch"`: Run on `Enter` when the suggestion is typed in its entirety.
- `"exactMatchIgnoreExtension"` (default): Run on `Enter` when the suggestion is typed in its entirety or when a file is typed without its extension included.
- `"never"` (old behavior): Never run on `Enter`.

This change is especially important to not break existing muscle memory as often this will result in the same set of keystrokes as without the feature enabled. An example where this is useful is running `cd ..`. Before this change, you would need to type `cd ..<enter><enter>` but now it's a single `Enter`, thanks to this new setting.

<video src="images/1_92/terminal_run_on_enter.mp4" title="Typing 'cd ..' only requires a single Enter key press" autoplay loop controls muted></video>

#### Improved file completions

File completions are now sorted by file name length ascending, file name alphabetically, and then by file extension alphabetically. File completions run as commands (not arguments) now also get a boost, depending on their file extension and current operating system. For example, `.ps1`, `.bat`, and `.cmd` files are boosted when running on Windows, which makes them show up higher in the list.

![Results are now sorted by extensions with platform-specific boosts](images/1_92/terminal-file-ext.png)

Not only do these changes improve the relevance of the top item, they also help muscle memory by aligning the behavior closer to native PowerShell tab completion. For example, a command commonly run in the VS Code codebase on Windows is `./scripts/code.bat`. With these changes, `./sc<tab>/c<enter>` completes and runs `./scripts/code.bat`.

<video src="images/1_92/terminal_scripts_code.mp4" title="Completions are much less in the way now, not breaking your muscle memory" autoplay loop controls muted></video>

#### Global completion caching

Global completions for commands are cached across sessions, which improves shell startup performance significantly. This also fixes an issue where completions would not work correctly for reconnected terminals. Currently, these can be cleared and refreshed by running the `Terminal: Clear Suggest Cache` command. Measured on an Intel i7-12700KF, this reduced the time taken to activate shell integration from ~600ms to ~50ms.

#### Built-in terminal completions in PowerShell

We now ship built-in completions for `git`. These are based on those from the `posh-git` project but with some general and VS Code-specific improvements. Here's an example of the completions in action:

<video src="images/1_92/terminal_git_flow.mp4" title="Git completions show for subcommands like branch and will immediately complete branches that were just created" autoplay loop controls muted></video>

Some of the other improvements are:

* Aliases show their expanded state on the right:

    ![Aliases show their expanded state on the right](images/1_92/terminal-git-alias.png)
* Git-specific icons for branches, tags, stashes and remotes:

    ![Branches and tabs have their own icon beside the completions](images/1_92/terminal-git-icons.png)
* Subcommand descriptions on the right:

    ![Subcommands like 'checkout' show their description on the right](images/1_92/terminal-git-descriptions.png)

We also ship built-in completions for `code` and `code-insiders`, though these are very basic and will be improved in future releases.

![code now shows basic completions for everything in code --help](images/1_92/terminal-code-completions.png)

These completions may conflict with those from other PowerShell modules. You can disable them with the following setting:

```json
"terminal.integrated.suggest.builtinCompletions": {
  "pwshCode": false,
  "pwshGit": false
}
```

### TypeScript 5.6 support

This release includes support for the upcoming TypeScript 5.6 release. Check out the [TypeScript 5.6 iteration plan](https://github.com/microsoft/TypeScript/issues/59250) for details on what's in store for this release.

To start using preview builds of TypeScript 5.6, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Share your feedback and let us know if you run into any bugs with TypeScript 5.6.

## Proposed APIs

### `QuickInputButtonLocation` to show buttons to the right of the input

This milestone, we introduced a proposed API on `QuickInputButton` that enables extensions to choose where they want a button to be rendered. The options are:

* `Title`: This renders the button in the title bar of the quick pick. This is the default behavior and maintains the existing behavior of buttons.
* `Inline`: This renders the button to the right of the input box. This is useful if you aren't planning on using the title bar for anything but want to show buttons.

This functionality was adopted in the Git extension's **Create branch** input box, which you can enable with <a href="vscode://settings/git.branchRandomName.dictionary" codesetting="true">`git.branchRandomName.dictionary`</a>.

Check out [vscode#221397](https://github.com/microsoft/vscode/issues/221397) for more information and updates.

### Testing Enhancements

#### Associate code to tests

We're working on an API that enables an extension to associate code to tests, and vice versa. This lets users jump between both, and enables actions, such as **Run Tests at Cursor**, to also work in implementation code. We anticipate building more experiences as the API develops.

Check out [vscode#126932](https://github.com/microsoft/vscode/issues/126932#issuecomment-2243510427) for more information and updates.

#### Call stacks in test failures

We're working on an API that enables associating a rich call stack with unit test failures. This enables users to see, at a glance, what code led up to their failure.

Check out [vscode#214488](https://github.com/microsoft/vscode/issues/214488) for more information and updates.

#### Attributable test coverage

We're working on an API for attributing test coverage on a per-test basis. This enables users to see which tests ran which code, filtering both the coverage shown in the editor, and that in the **Test Coverage** view.

Check out [vscode#212196](https://github.com/microsoft/vscode/issues/212196) for more information and updates.

### Search APIs

In the last few months, we have been working on finalizing three proposed Search APIs:

* [`FindTextInFiles`](https://github.com/microsoft/vscode/issues/59924): find text in workspace files using VS Code's text search.
* [`FileSearchProvider`](https://github.com/microsoft/vscode/issues/73524): provide file search results for custom file schemes that cannot be searched properly with VS Code's existing search functionality. For example, extension-provided results could show up when searching in Quick Open from within a virtual file system.
* [`TextSearchProvider`](https://github.com/microsoft/vscode/issues/59921): like `FileSearchProvider`, but for text search results. For example, extension-provided results could show up when searching in the Search View from within a virtual file system.

In addition, we are revamping the [`workspace.findFiles`](https://github.com/microsoft/vscode/issues/48674) API, which uses VS Code's workspace file search to find files. The new version should allow for more options, and should handle exclusion options more clearly. The existing function signature should still be functional when we introduce the revamped version.

This table illustrates how the different APIs are related:

|                     | Using API to find | Using API to provide results |
| --------:           | :-------:         | :-------:                    |
|**For Files**        | FindFiles         | FileSearchProvider           |
|**For Text in Files**| FindTextInFiles   | TextSearchProvider           |

If you're interested, visit the links above to provide feedback!

## Website

We refreshed the design of the [VS Code website](https://code.visualstudio.com/) and added support for light and dark themes.

![VS Code website light mode.](images/1_92/landing-page-light.png)

The website's new design defaults to your system's theme (light or dark mode), and you can also toggle it manually by using the sun/moon icon in the top right.

![VS Code website dark mode.](images/1_92/landing-page-dark.png)

We look forward to getting your feedback! File any feature requests or bugs in the [vscode-docs repo](https://github.com/microsoft/vscode-docs/issues).

## Engineering

### Move Markdown language server to separate repository

We moved the language server that powers VS Code's built-in Markdown IntelliSense into [its own repository](https://github.com/microsoft/vscode-markdown-languageserver). Previously, this project was published from a subfolder of the main VS Code repository. This change makes it easier to contribute to the project.

The project is still published under the same name on npm: `vscode-markdown-languageserver`.

### Progress on using ESM for VS Code

In this milestone, we picked up the work again to adopt ESM for VS Code core. Our goal in the future is to use ECMAScript Modules (ESM) loading and drop AMD entirely. This is a multi-milestone effort that will modernize overall code loading and bundling.

### xterm.js depending upon VS Code

The new scroll bar in the terminal required a significant amount of behind-the-scenes work. The terminal in VS Code is built on the [xterm.js OSS project](https://github.com/xtermjs/xterm.js), and in its upcoming release will also ship a small portion of VS Code's codebase, specifically part of the `base/` folder.

### Electron 30 update

In this milestone, we are promoting the Electron 30 update to users on our stable release. This update comes with Chromium 124.0.6367.243 and Node.js 20.14.0. We want to thank everyone who self-hosted on Insiders builds and provided early feedback.

**Notice of breaking API change when spawning `.bat` or `.cmd` files:**

The Node version with this Electron update contains a [**breaking change**](https://nodejs.org/en/blog/vulnerability/april-2024-security-releases-2#command-injection-via-args-parameter-of-child_processspawn-without-shell-option-enabled-on-windows-cve-2024-27980---high), in response to a CVE, which may affect you if you execute `.bat` or `.cmd` files on Windows. You can follow the [guidelines](https://nodejs.org/api/child_process.html#spawning-bat-and-cmd-files-on-windows) set by Node.js when spawning these files using the `shell` option.

We have proactively notified extensions who may be affected based on a simple source code scan, but it's possible we may have missed yours. If you are affected, you will encounter an EINVAL exception when spawning `.bat` or `.cmd` files on Windows. Please refer to the [Node.js documentation](https://nodejs.org/api/child_process.html#spawning-bat-and-cmd-files-on-windows) for guidance on how to handle these.

We recommend extension authors to always test their extension with our [Insiders release](https://code.visualstudio.com/insiders/) to catch these changes in advance.

## Notable fixes

* [211199](https://github.com/microsoft/vscode/issues/211199) Ctrl+c in terminal does not scroll to the very bottom when smooth scroll is enabled

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@manav014 (Manav Agarwal)](https://github.com/manav014)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@starball5 (starball)](https://github.com/starball5)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart): Ensure titlebar is at least as tall as the bounding rect of WCO [PR #211440](https://github.com/microsoft/vscode/pull/211440)
* [@aaronchucarroll](https://github.com/aaronchucarroll): Adds support for Github-style fenced math blocks in markdown editor and preview [PR #213750](https://github.com/microsoft/vscode/pull/213750)
* [@c-claeys (Cristopher Claeys)](https://github.com/c-claeys): Fix exponential runtime in service instantiation [PR #218393](https://github.com/microsoft/vscode/pull/218393)
* [@cobey (Cody Beyer)](https://github.com/cobey): adding js/py lib for tagging [PR #219213](https://github.com/microsoft/vscode/pull/219213)
* [@etcadinfinitum (Lee Zee)](https://github.com/etcadinfinitum): shellscript: Register .eclass extension as shell-like [PR #219631](https://github.com/microsoft/vscode/pull/219631)
* [@gabritto (Gabriela Araujo Britto)](https://github.com/gabritto): \[typescript-language-features\] Add diagnostics performance telemetry [PR #220127](https://github.com/microsoft/vscode/pull/220127)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Hide Local History commands when `"workbench.localHistory.enabled": false` [PR #212936](https://github.com/microsoft/vscode/pull/212936)
  * Hide Panel Maximize when at top but not center-aligned [PR #221549](https://github.com/microsoft/vscode/pull/221549)
  * Implement /spam issue triaging command [PR #221940](https://github.com/microsoft/vscode/pull/221940)
* [@hotpxl (Yutian Li)](https://github.com/hotpxl): Use the builtin local command in shell integration scripts [PR #221772](https://github.com/microsoft/vscode/pull/221772)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fix port label not applied when forwarding port with vscode.env.asExternalUri [PR #220029](https://github.com/microsoft/vscode/pull/220029)
* [@kapodamy (kapodamy)](https://github.com/kapodamy): fix: place regex in variables on `shellIntegration-bash.sh` script [PR #221998](https://github.com/microsoft/vscode/pull/221998)
* [@Krzysztof-Cieslak (Krzysztof Cieślak)](https://github.com/Krzysztof-Cieslak)
  * Inline edit - implement side by side rendering of suggestions [PR #219444](https://github.com/microsoft/vscode/pull/219444)
  * Inline edit - UI fixes for side-by-side rendering [PR #221352](https://github.com/microsoft/vscode/pull/221352)
  * Inline edits - UI fixes for word wrap and scrolling [PR #223076](https://github.com/microsoft/vscode/pull/223076)
* [@mering (Marcel)](https://github.com/mering): Setup rust in Devcontainer [PR #221301](https://github.com/microsoft/vscode/pull/221301)
* [@mxts (Teik Seong)](https://github.com/mxts): add option to dock terminal at top [PR #207721](https://github.com/microsoft/vscode/pull/207721)
* [@Parasaran-Python (Parasaran)](https://github.com/Parasaran-Python): Fixes #218254 [PR #219312](https://github.com/microsoft/vscode/pull/219312)
* [@rehmsen (Ole)](https://github.com/rehmsen)
  * Support comments on notebook markup cells [PR #219657](https://github.com/microsoft/vscode/pull/219657)
  * Show the first comment on a notebook cell, for any owner. [PR #219926](https://github.com/microsoft/vscode/pull/219926)
  * Update notebook comment view in response to mutations [PR #219927](https://github.com/microsoft/vscode/pull/219927)
* [@scop (Ville Skyttä)](https://github.com/scop): Fix terminal `__vsc_first_prompt` errors with bash in `nounset` mode [PR #221980](https://github.com/microsoft/vscode/pull/221980)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke)
  * feature: add setting whether or not to auto open a file after dropping it into the explorer [PR #213498](https://github.com/microsoft/vscode/pull/213498)
  * reduce impact of memory leaks related to editor [PR #219297](https://github.com/microsoft/vscode/pull/219297)
  * fix: memory leak in extension tabs [PR #219726](https://github.com/microsoft/vscode/pull/219726)
  * feature: allow configuring default browser [PR #219885](https://github.com/microsoft/vscode/pull/219885)
  * fix: memory leak in SelectBoxList [PR #221507](https://github.com/microsoft/vscode/pull/221507)
  * fix: memory leak in settings widget [PR #221518](https://github.com/microsoft/vscode/pull/221518)
  * fix: memory leak in StickyScrollFocus [PR #221622](https://github.com/microsoft/vscode/pull/221622)
* [@syi0808 (Sung Ye In)](https://github.com/syi0808): fix(terminal): remove fixedRows line in add scrollbar [PR #221976](https://github.com/microsoft/vscode/pull/221976)
* [@tisilent (xiejialong)](https://github.com/tisilent)
  * Add Icons. [PR #219816](https://github.com/microsoft/vscode/pull/219816)
  * Window title use execution path. [PR #221258](https://github.com/microsoft/vscode/pull/221258)
  * Add revealTerminal and focusInstance in terminalService [PR #221684](https://github.com/microsoft/vscode/pull/221684)
* [@wdhongtw (Weida Hong)](https://github.com/wdhongtw): Reset shell-type context when the type is unknown [PR #221277](https://github.com/microsoft/vscode/pull/221277)

Contributions to `vscode-black-formatter`:

* [@shayhurley (Shay Hurley)](https://github.com/shayhurley): Update README.md [PR #524](https://github.com/microsoft/vscode-black-formatter/pull/524)

Contributions to `vscode-hexeditor`:

* [@tomilho (Tomás Silva)](https://github.com/tomilho): fix: removes duplicate webview/context [PR #527](https://github.com/microsoft/vscode-hexeditor/pull/527)

Contributions to `vscode-languageserver-node`:

* [@benmcmorran (Ben McMorran)](https://github.com/benmcmorran): Add supportThemeIcons in MarkdownString [PR #1504](https://github.com/microsoft/vscode-languageserver-node/pull/1504)

Contributions to `vscode-markdown-tm-grammar`:

* [@RedCMD (RedCMD)](https://github.com/RedCMD): Fix `FrontMatter` integration [PR #162](https://github.com/microsoft/vscode-markdown-tm-grammar/pull/162)

Contributions to `vscode-pull-request-github`:

* [@Santhoshmani1 (Santhosh Mani )](https://github.com/Santhoshmani1): Feature : Add open pr on github from pr description node [PR #6020](https://github.com/microsoft/vscode-pull-request-github/pull/6020)

Contributions to `vscode-textmate`:

* [@aleclarson (Alec Larson)](https://github.com/aleclarson): feat: add child combinator ">" (and fix a specificity bug) [PR #233](https://github.com/microsoft/vscode-textmate/pull/233)

Contributions to `debug-adapter-protocol`:

* [@dawedawe (dawe)](https://github.com/dawedawe): Update overview.md to link to renamed package [PR #489](https://github.com/microsoft/debug-adapter-protocol/pull/489)

Contributions to `language-server-protocol`:

* [@asukaminato0721 (Asuka Minato)](https://github.com/asukaminato0721)
  * Update servers.md [PR #1967](https://github.com/microsoft/language-server-protocol/pull/1967)
  * add Sonar [PR #1981](https://github.com/microsoft/language-server-protocol/pull/1981)
* [@flaribbit (梦飞翔)](https://github.com/flaribbit): Update servers.md [PR #1968](https://github.com/microsoft/language-server-protocol/pull/1968)
* [@PrasangAPrajapati (Prasang A Prajapati)](https://github.com/PrasangAPrajapati): Add implementor for JCL LSP Server [PR #1955](https://github.com/microsoft/language-server-protocol/pull/1955)
* [@StachuDotNet (Stachu Korick)](https://github.com/StachuDotNet): Clarify deltaLine and deltaStart of SemanticTokens payload [PR #1966](https://github.com/microsoft/language-server-protocol/pull/1966)

Contributions to `monaco-editor`:

* [@ScottCarda-MS (Scott Carda)](https://github.com/ScottCarda-MS): Update Q# Keywords [PR #4586](https://github.com/microsoft/monaco-editor/pull/4586)

Contributions to `node-request-light`:

* [@remcohaszing (Remco Haszing)](https://github.com/remcohaszing): Add package exports [PR #25](https://github.com/microsoft/node-request-light/pull/25)

Contributions to `python-environment-tools`:

* [@cclauss (Christian Clauss)](https://github.com/cclauss): interpreterInfo.py: Create the object in a single operation [PR #93](https://github.com/microsoft/python-environment-tools/pull/93)
* [@hamirmahal (Hamir Mahal)](https://github.com/hamirmahal): style: simplify string formatting [PR #88](https://github.com/microsoft/python-environment-tools/pull/88)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
