---
Order: 
TOCTitle: February 2023
PageTitle: Visual Studio Code February 2023
MetaDescription: Learn what is new in the Visual Studio Code February 2023 Release (1.76)
MetaSocialImage: 1_76/release-highlights.png
Date: 2023-3-1
DownloadVersion: 1.76.2
---
# February 2023 (version 1.76)

**Update 1.76.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22February+2023+Recovery+1%22+is%3Aclosed).

**Update 1.76.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22February+2023+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the February 2023 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[Profiles](#profiles)** - Active profile badge, quickly switch profiles via the Command Palette.
* **[Accessibility improvements](#accessibility)** - New audio cues, improved terminal screen reader mode.
* **[Moveable Explorer view](#moveable-explorer-view)** - Place the Explorer in the secondary side bar or a panel.
* **[Notebook kernel MRU list](#kernel-picker-default-mode-mru)** - Find and select recently used notebook kernels.
* **[Markdown header link suggestions](#markdown-workspace-header-link-completions)** - Easily link to headers in files across your workspace.
* **[Remote Development usability](#remote-menu)** - New keyboard shortcut, streamlined remote options list.
* **[New Git/GitHub topics](#git-and-github-documentation)** - Articles for beginner and advanced Git source control users.
* **[Improved Marketplace search](#improved-extension-search-relevance)** - Better results for multi-word queries.
* **[Jupyter IPyWidgets 8 support](#ipywidgets)** - Use the latest IPyWidgets version in your Jupyter notebooks.
* **[Python pytest IntelliSense](#improved-intellisense-support-for-pytest)** - Completions for pytest fixtures and parameterized arguments.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Terminal command failure audio cue

When a command exits with a non-zero code in a terminal with shell integration, sighted users can glance at the command decoration to quickly understand that there was a failure. To convey a failure to screen reader users, VS Code now plays the `audioCues.terminalCommandFailed` audio cue.

You can listen to the available audio cue sounds by scrolling through the **Help: List Audio Cues** dropdown.

![List Audio Cues dropdown with Terminal Command Failed cue selected](images/1_76/list-audio-cues-dropdown.png)

### Improved error audio cue responsiveness

The `audioCues.lineHasError` audio cue now plays during character navigation so that screen reader users have improved insight into where the errors are in the editor.

### Terminal accessible buffer improvements

Last iteration, we introduced the terminal accessible buffer, which provides screen reader users access to the terminal contents via the **Terminal: Focus Accessible Buffer** command.

This iteration, we've improved the accessible buffer by adding selection and link support, making it read-only, and using shell integration insights to limit output to only the important terminal content - the command that was run, the exit code if any, and the output. We plan to add configuration of this feature next iteration.

### Context specific Tab focus mode

The **Toggle Tab Key Moves Focus** command is useful for determining whether the workbench or editor/terminal receive `kbstyle(Tab)` key input.

The new `editor.tabFocusMode` setting allow users to set the default behavior. When the value is set to `true`, the workbench receives the `kbstyle(Tab)` key input instead of the editor or terminal.

### Screen reader shell integration support on Windows

Shell integration was previously disabled when a screen reader was detected on Windows due to problems with PowerShell support in this context. We have determined that this is no longer an issue in most cases, so have enabled it.

### Terminal accessible help additions

Discussions with the screen reader community have yielded additions to the **Terminal Accessibility Help** dialog (`kb(workbench.action.terminal.showAccessibilityHelp)`) such as mentioning the **Create New Terminal (With Profile)** and **Preferences: Open Accessibility Settings** commands.

## Profiles

Profiles, [released to Stable](https://code.visualstudio.com/updates/v1_75#_profiles) last milestone, let you quickly switch VS Code configurations depending on your current workflow and project. You can save a set of customizations such as settings, extensions, and keybindings, sync them across your machines, and easily share them with colleagues.

### Profile Badge

VS Code now indicates the current custom profile by showing the first two letters of the profile name as a profile badge on the **Manage** Activity bar icon.

![Two profile badges showing the first two letters of the profiles](images/1_76/profile-badge.png)

Themes can customize the profile badge background and foreground colors by configuring the two new theme colors:

* `profileBadge.background`
* `profileBadge.foreground`

### Switch profiles

You can now quickly switch between profiles with the **Profiles: Switch Profile** command in the Command Palette (`kb(workbench.action.showCommands)`), which presents a dropdown listing your available profiles.

![Switch Profile command dropdown listing available profiles](images/1_76/switch-profile.png)

### Profiles in Remote Workspaces

You can now create and customize profiles that include [remote extensions](https://code.visualstudio.com/api/advanced-topics/remote-extensions) and switch between them in [Remote Development](https://code.visualstudio.com/docs/remote/remote-overview)  workspaces.

Below two different profiles ("Doc Writing" and "Code") are active in two instances over [Remote - SSH](https://code.visualstudio.com/docs/remote/ssh).

![Profiles active in two Remote - SSH instances](images/1_76/remote-profiles.png)

### Profiles documentation

Check out the new [Profiles in Visual Studio Code](https://code.visualstudio.com/docs/configure/profiles) topic if you'd like to learn more about profiles and scenarios when they are useful. This article goes into detail on how to create, modify, share, and reuse profiles.

## Workbench

### Moveable Explorer view

Most views and view containers within VS Code are moveable except for a few built-in ones. In this release, the Explorer view container (`kb(workbench.view.explorer)`) is now moveable and can be placed into the secondary side bar or the bottom panel to allow for further workbench customization.

Below the File Explorer view has been moved to the Panel area.

![File Explorer view moved to the panel area](images/1_76/file-explorer-panel.png)

If you'd like to reset all views back to the default layout, you can run **Views: Reset View Locations** from the Command Palette.

### Fixed-width centered layout

The **View: Toggle Centered Layout** command (`workbench.action.toggleCenteredLayout`) now has a fixed-width layout option. This option is enabled with the new `workbench.editor.centeredLayoutFixedWidth` setting. When enabled and centered layout is active, the editor is centered and attempts to maintain its width across window and panel resizes.

### Multiple quick diffs

With the introduction of the proposed [multiple quick diff API](https://code.visualstudio.com/updates/v1_75#_quick-diff), multiple extensions can provide a quick diff. When there are multiple quick diffs in a file, you'll now see a dropdown to choose which diff base to view:

![Multiple quick diffs and diff base dropdown](images/1_76/multiple-quick-diff.png)

### Comments

The **Comments** view has a new **Expand All** command to go with the existing **Collapse All**. The gutter icon for unresolved comments is now the same icon that is used in the **Comments** view.

### Configure default log level

You can now configure the default log level for VS Code or for an extension from the **Developer: Set Log Level...** command. This is persisted across restarts and is synced across machines.

![Default log level dropdown with several extensions listed](images/1_76/default-log-level.png)

Selecting an extension displays the same log level dropdown as for the default log level.

### Verified publisher domain in Extension editor

The Extension editor now displays the verified publisher domain next to the verified indicator. Below, the `prettier.io` domain is shown to the right of the verified badge.

![Prettier extension in the extension editor displaying verified publisher domain prettier.io](images/1_76/verified-publisher-domain.png)

## Editor

### JSONC document sorting

It is now possible to sort JSONC (JSON documents with comments) files by key. To use this feature, select **JSON: Sort Document** from the Command Palette.

### Independent bracket pairs for matching and colorization

In this iteration, we changed the behavior when both `editor.language.brackets` and `editor.language.colorizedBracketPairs` are configured. Before, setting `editor.language.colorizedBracketPairs` would override `editor.language.brackets` for bracket matching (as both use the same [bracket pair tree](https://code.visualstudio.com/blogs/2021/09/29/bracket-pair-colorization#_the-basic-algorithm)). Now, the union of both settings is used for bracket matching, but only brackets configured in `editor.language.colorizedBracketPairs` are colorized.

## Source Control

### Git commit syntax highlighting

VS Code has adopted a new Git grammar, which provides syntax highlighting for Git commit message files. The new grammar has better support for languages other than English.

### Git and GitHub documentation

Whether you are new to source control or an experienced Git user, you can learn more about VS Code's Git integration in the [Source Control](https://code.visualstudio.com/docs/sourcecontrol/overview) section of our documentation. Articles include:

* [Using Git source control in VS Code](https://code.visualstudio.com/docs/sourcecontrol/overview) - An overview of VS Code's Git integration features.
* [Introduction to Git](https://code.visualstudio.com/docs/sourcecontrol/intro-to-git) - Beginner level Git operations in VS Code.
* [Working with GitHub](https://code.visualstudio.com/docs/sourcecontrol/github) - Move your code to [GitHub](https://github.com) to share and collaborate with others.
* [Frequently Asked Questions](https://code.visualstudio.com/docs/sourcecontrol/faq) - Get help for common scenarios and gotchas.

## Notebooks

### Kernel picker default mode: MRU

The kernel picker now shows the most recently used (MRU) kernel by default. This is a change from the previous behavior, which was to show all available kernels. You can still see all kernels by a secondary picker **Select Another Kernel...**, which will group kernels by their source (for example: Jupyter Kernel, Python Environment, etc.) when you have latest [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) and [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) extensions installed.

![Notebook kernel picker with MRU list and using the Select Another Kernel option](images/1_75/noterbook-kernel-picker.gif)

### Notebook renderer performance diagnostics

When running notebook cells, some notebook output renderers may take a long time to render. To help identify these renderers, we've added diagnostics to the hover in the notebook cell Status bar. This view shows the time it took for each renderer to execute and the total execution time. Additionally, the links in the hover will open the issue reporter with the renderer's name and execution time pre-filled so that you can easily file an issue if you find the renderer is taking too long to execute.

![Image showing the cell Status bar hover with renderer execution times](images/1_76/notebook-renderer-performance-diagnostics.png)

### Better link support for built-in error renderer

The built-in error renderer now supports links to files and line numbers. This allows you to click on a file path in the error message and open the file in the editor.

Jupyter extension's error renderer was deprecated in favor of the built-in error renderer.

![Notebook built-in error renderer](images/1_76/notebook-error-renderer.gif)

## Languages

### Markdown workspace header link completions

Need to link to a header in another Markdown document but don't remember or want to type out the full file path? Try using workspace header completions! To start, just type `##` in a Markdown link to see a list of all Markdown headers from the current workspace:

![Suggestions for all Markdown headers in the current workspace](images/1_76/md-workspace-header-suggestion.png)

Accept one of these completions to insert the full link to that header, even if it's in another file:

![Adding a link to the selected header in another file](images/1_76/md-workspace-header-suggestion-insert.png)

You can configure if/when workspace header completions show with the **Markdown > Suggest > Paths: Include Workspace Header Completions** setting (`markdown.suggest.paths.includeWorkspaceHeaderCompletions`).

Valid setting values are:

* `onDoubleHash` (the default) - Show workspace header completions only after you type `##`.
* `onSingleOrDoubleHash` - Show workspace header completions after you type `#` or `##`.
* `never` - Never show workspace header completions.

Keep in mind that finding all headers in the current workspace can be expensive, so there may be a slight delay the first time they are requested, especially for workspaces with lots of Markdown files.

### Configure preferred file extension style for Markdown links

The new `markdown.preferredMdPathExtensionStyle` setting configures if VS Code prefers using file extensions for links to Markdown files. This preference is used for language tools such as [path completions](https://code.visualstudio.com/docs/languages/markdown#_path-completions) and [link renames](https://code.visualstudio.com/docs/languages/markdown#_rename-headers-and-links).

Valid setting values are:

* `auto` - Try to preserve the existing style of the file extension.
* `includeExtension` - Include the trailing `.md` in the link.
* `removeExtension` - Remove the trailing `.md` from the link.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_76.md).

### Remote menu

This milestone we made several usability improvements to the remote menu, accessible by clicking on the remote indicator in the lower left of the Status bar:

1. There is now a default keybinding to open the remote menu: `kb(workbench.action.remote.showMenu)`.

   <video src="images/1_76/remote-indicator.mp4" placeholder="images/1_76/remote-indicator.mp4" autoplay loop controls muted title="Opening the remote menu with the new default keybinding">
    Sorry, your browser doesn't support HTML 5 video.
   </video>

2. We reduced our remote extensions' contributions to the remote menu to make it easy to view all actions for connecting to a remote window at a glance.
   * Basic actions for opening a remote window are now always available in the remote menu, so you can discover actions to connect to another remote even when you are already in a remote window.
   * Most actions for configuring or getting started with a remote have moved from the remote menu to the Command Palette to conserve space in the remote menu.

3. The **Install Additional Remote Extensions** option in the remote menu now points to a list of curated Remote Development extensions and is hidden from the remote menu if all such extensions are already installed.

### Remote Repositories

This milestone, we introduced the **Browse & Edit Remote Repositories without Cloning** walkthrough in the [Remote Repositories](https://marketplace.visualstudio.com/items?itemName=ms-vscode.remote-repositories) extension to help you search, edit, and create quick commits in GitHub and Azure Repos without cloning locally.

You can also check out the [Remote Repositories tutorial](https://code.visualstudio.com/blogs/2021/06/10/remote-repositories) to help you get started.

## VS Code for the Web

This milestone we have added experimental readonly support for [Git LFS](https://git-lfs.com/)-tracked files hosted in GitHub and Azure Repos repositories on [vscode.dev](https://vscode.dev).

In the short video below, images stored on Git LFS in the vscode-docs repository are visible when scrolling the Markdown Preview on vscode.dev.

<video src="images/1_76/web-git-lfs.mp4" placeholder="images/1_76/web-git-lfs.mp4" autoplay loop controls muted title="Viewing a Markdown preview of a file containing Git LFS image references in vscode.dev">
    Sorry, your browser doesn't support HTML 5 video.
</video>

When you view a file in a GitHub or Azure Repos repository, based on your `.gitattributes` configuration, we detect whether the file is LFS-tracked and needs to be fetched from GitHub's or Azure Repos's LFS servers.

You can also use the [GitHub Repositories](https://marketplace.visualstudio.com/items?itemName=GitHub.remotehub) and [Azure Repos](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-repos) extensions with VS Code Desktop to browse repositories containing Git LFS-tracked files without cloning.

This behavior can be disabled with the following settings:

* `"githubRepositories.experimental.lfs.read.enabled": false`
* `"azureRepos.experimental.lfs.read.enabled": false`

VS Code does not yet support committing Git LFS-tracked files, and [github.dev](https://github.dev) does not have Git LFS read support.

## Extensions

### Improved extension search relevance

We have improved the relevance of extension search results in the [Extensions view](https://code.visualstudio.com/docs/configure/extensions/extension-marketplace#_search-for-an-extension) and on the [Marketplace](https://marketplace.visualstudio.com/vscode) gallery. Results should now be more appropriate, especially for multi-word queries.

| Before      | After |
| ----------- | ----------- |
| ![Search results for "mono debugger" showing Java debugger extension as the top result](images/1_76/search_before.png)      | ![Search results for "mono debugger" showing Mono debug extension as the top result](images/1_76/search_after.png)       |

## Contributions to extensions

### Jupyter

#### IPyWidgets

The [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension now supports version `8` of [IPyWidgets](https://pypi.org/project/ipywidgets/#description). Python Widgets relying on `IPyWidgets 7.*` will continue to work, as both versions `7.*` and `8.*` are supported.

#### Interactive Window kernel selection

Auto-selecting a kernel for the [Interactive Window](https://code.visualstudio.com/docs/python/jupyter-support-py) was modified to work better with the new kernel picker UI. The current active interpreter is used unless another kernel was previously chosen for an Interactive Window in that workspace, in which case the previously selected kernel is used.

### Python

#### Improved IntelliSense support for pytest

The [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) extension now has powerful features that can help you work more efficiently and effectively when writing clear, concise, and easy to understand tests with [pytest](https://docs.pytest.org):

* Support for completions, **Go to Definition**, **Find All References**, and **Rename Symbol** (`kb(editor.action.rename)`) for pytest fixtures and parameterized arguments.
* Type annotation support for pytest parameters through inlay hints (enabled by the ``"python.analysis.inlayHints.pytestParameters"`` setting) and through Code Actions.
* Type inference of parameterized pytest arguments, based on the parameter values provided to the decorator.

The short video below highlights these new pytest features:

<video src="images/1_76/pylance-pytest-intellisense.mp4" placeholder="images/1_76/pylance-pytest-intellisense.mp4" autoplay loop controls muted title="Showcasing new pytest IntelliSense features">
      Sorry, your browser doesn't support HTML 5 video.
</video>

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Highlights include:

* Permalink rendering in the PR description and in comments.
* Re-requesting reviews from the description page.
* Quick diffs for files in a checked-out PR.

Check out the [changelog for the 0.60.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0600) release of the extension to see the other highlights.

## Preview features

### Rich content notebook search

Global search now supports showing results from notebooks as they would be displayed in the notebook editor. Enable `search.experimental.notebookSearch` to try this out, and let us know about any bugs that you run into! This currently only supports searching cell inputs and Markdown sources in cells.

<video src="images/1_76/experimental_notebook_search.mp4" placeholder="images/1_76/experimental_notebook_search.mp4" autoplay loop controls muted title="Notebook rich content search demo">
    Sorry, your browser doesn't support HTML 5 video.
</video>

_Theme: [Community Material Theme](https://marketplace.visualstudio.com/items?itemName=Equinusocio.vsc-community-material-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/Equinusocio.vsc-community-material-theme))_

### Asynchronous tokenization

This release ships the experimental asynchronous tokenization feature. This feature allows the editor to tokenize documents in a separate web worker, which can improve the responsiveness of the editor when the document is large.

For now, asynchronous tokenization is disabled by default but can be enabled by setting `editor.experimental.asyncTokenization` to `true`.

### TypeScript 5.0 support

This update includes support for the upcoming TypeScript 5.0 release. See the [TypeScript 5.0 Beta blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-5-0-beta/) and  [TypeScript 5.0 iteration plan](https://github.com/microsoft/TypeScript/issues/51362) for more details on what the TypeScript team is currently working on. Some editor tooling highlights:

* New `switch` and `case` completions help you fill in both sections of `switch` statements more quickly.
* Work on enabling project-wide IntelliSense on [github.dev](https://github.dev) and [vscode.dev](https://vscode.dev).

To start using the TypeScript 5.0 nightly builds, install the [TypeScript Nightly](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next) extension or install the latest [VS Code 1.76+ Insiders build](https://code.visualstudio.com/insiders).

## Extension authoring

### Link support in InputBox prompts and validation messages

This iteration, we introduced support for links in `InputBox` prompts and validation messages. In those strings, you can now use the following syntax to create links:

```ts
[link text](link target)
```

The link target can be a URL or a command ID. When the user clicks on the link, the link target is opened in the browser or executed as a command, respectively.

Example:

```ts
const result = await vscode.window.showInputBox({
   prompt: 'Please enter a valid email address [more info](https://aka.ms/vscode-email-validation)',
   validateInput: text => {
      if (text.indexOf('@') === -1) {
         return 'Please enter a valid email address, [more info](https://aka.ms/vscode-email-validation)';
      }
      return undefined;
   }
});
```

which will result in the following prompt:

![Prompt contains a link that is underlined](images/1_76/quickpick-prompt-links.png)

and the following validation message:

![Validation message contains a link that is underlined](images/1_76/quickpick-validation-links.png)

_Theme: [Panda Theme](https://marketplace.visualstudio.com/items?itemName=tinkertrain.theme-panda) (preview on [vscode.dev](https://vscode.dev/editor/theme/tinkertrain.theme-panda))_

This behavior works for both the `showInputBox` and `createInputBox` APIs.

### Information diagnostic for * activation event

When an extension uses the `*` (star) activation event, it is activated while VS Code is in the process of starting up. This can delay the startup of VS Code. To help extension authors understand the impact of using the `*` activation event, we have added an information diagnostic that is shown when an extension uses the `*` activation event. The diagnostic is displayed in the Problems view and is also visible in the extension editor:

![Diagnostic for * activation event](images/1_76/star-activation.png)

For more information about `*` (star) activation, you can read the [Activation Events](https://code.visualstudio.com/api/references/activation-events#Start-up) documentation.

### Upcoming changes in when clause contexts parsing

[When clauses](https://code.visualstudio.com/api/references/when-clause-contexts) used in extension manifest files (`package.json`) allow extensions to selectively enable and disable contributions such as commands and UI elements (for example, menus or views). The next VS Code release is planned to include a new parser for when clauses. The new parser offers more features and correctness, but it also enforces stricter rules for when clauses and may result in some breakages for existing when clauses. Review [issue #175540](https://github.com/microsoft/vscode/issues/175540) to learn more about the breakages and new features. You are also encouraged to subscribe to that issue for updates on the new parser, including new features, breakages, and migration guidelines.

### Upcoming Electron update may require mandatory changes to native modules

We plan to update to [Electron 22](https://www.electronjs.org/blog/electron-22-0) in our next stable release. This comes with implications for extensions that leverage native modules due to the enablement of the [V8 memory cage](https://www.electronjs.org/blog/v8-memory-cage): `ArrayBuffers` which point to external ("off-heap") memory are no longer allowed. This means that native modules which rely on this functionality in V8 will need to be refactored to continue working in VS Code `1.77` and later.

Please follow the advise in the [Electron Blog post](https://www.electronjs.org/blog/v8-memory-cage#i-want-to-refactor-a-node-native-module-to-support-electron-21-how-do-i-do-that) for how to make your native module work with future versions of VS Code.

## Language Server Protocol

New versions of the [Language Server Protocol](https://microsoft.github.io/language-server-protocol) (LSP) client (8.1.0) and server (8.1.0) npm packages have been released. These are bug fix releases (mainly to address problems with request ordering problem when using full text document sync) and contain API additions for custom message handling to the JSON-RPC library.

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [vscode-dts](https://www.npmjs.com/package/vscode-dts) and run `vscode-dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### OpenDialogOptions allowUIResources

Extensions that are aware when they are running in a remote extension host can use `allowUIResources` to cause the **Show local** button to display in open file dialogs. Extensions that use `allowUIResources` should always check the scheme of the URI that's returned.

### Tooltips for QuickPickItems

This iteration we are introducing a new proposed API for a `tooltip` property on `QuickPickItem`s. This property allows you to specify a tooltip for each item in a Quick Pick. The tooltip is displayed when the user hovers over the item or it can be toggled with `kbstyle(Ctrl+Space)`.

Example:

```ts
await vscode.window.showQuickPick([
   {
      label: 'label',
      tooltip: 'tooltip',
   },
   {
      label: 'label2',
      tooltip: new vscode.MarkdownString('tooltip2 [link](https://github.com)'),
   }
], {
   placeHolder: 'placeholder'
});
```

which will result in the following Quick Pick:

<video src="images/1_76/quickpick-tooltip.mp4" placeholder="images/1_76/quickpick-tooltip.mp4" autoplay loop controls muted title="Quick Pick with tooltips">
    Sorry, your browser doesn't support HTML 5 video.
</video>

_Theme: [Panda Theme](https://marketplace.visualstudio.com/items?itemName=tinkertrain.theme-panda) (preview on [vscode.dev](https://vscode.dev/editor/theme/tinkertrain.theme-panda))_

Obviously, this is a simple example that doesn't need a tooltip. Tooltips are useful when you want to provide long descriptions that might have multiple lines or links to additional resources.

We think that this property is a good addition to the Quick Pick API and we are looking forward to your feedback.

## Engineering

### More Electron UtilityProcess adoption

As part of our [process sandbox journey](https://code.visualstudio.com/blogs/2022/11/28/vscode-sandbox), we want to make sure that there are no more Node.js enabled browser windows in our application. This milestone we started to convert our last remaining process from a hidden Node.js browser window to an Electron [UtilityProcess](https://www.electronjs.org/docs/latest/api/utility-process). As part of this work, the file watcher process had to move out into its own utility process as well.

A new setting `window.experimental.sharedProcessUseUtilityProcess` enables the use of `UtilityProcess` and we plan to make this the default in the next release.

### macOS 10.11 and 10.12 support has ended

As mentioned in our [1.75 release notes](https://code.visualstudio.com/updates/v1_75#_eol-warning-for-macos-1011-and-1012), `1.76` is the last release that supports macOS 10.11 (OS X El Capitan) and 10.12 (macOS Sierra). Refer to our [FAQ](https://code.visualstudio.com/docs/supporting/faq#_can-i-run-vs-code-on-old-macos-versions) for additional information.

## Notable fixes

* [168939](https://github.com/microsoft/vscode/issues/168939) Persist state of badge hiding across reloads and view moves.
* [164397](https://github.com/microsoft/vscode/issues/164397) Layout control in the wrong place for RTL macOS.

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)
* [@starball5 (starball)](https://github.com/starball5)
* [@tjx666 (余腾靖)](https://github.com/tjx666)

### Pull requests

Contributions to `vscode`:

* [@davidnx](https://github.com/davidnx): Escape special glob chars when using "Find in Folder" [PR #166318](https://github.com/microsoft/vscode/pull/166318)
* [@DerDemystifier (Bassim EL BAKKALI EL GAZUANI)](https://github.com/DerDemystifier): Set Opacity to MatchHighlight in QuietLight [PR #174296](https://github.com/microsoft/vscode/pull/174296)
* [@dtivel (Damon Tivel)](https://github.com/dtivel)
  * Improve error handling and logging for verification of signed extensions [PR #174730](https://github.com/microsoft/vscode/pull/174730)
  * Bump distro [PR #175115](https://github.com/microsoft/vscode/pull/175115)
* [@eronnen (Ely Ronnen)](https://github.com/eronnen)
  * Fix #173869 marking active line for code elements too in markdown pre… [PR #173870](https://github.com/microsoft/vscode/pull/173870)
  * fix #174711 Show Source command in Markdown custom editor [PR #174712](https://github.com/microsoft/vscode/pull/174712)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Point comment zone widget 'beak' arrow at single-line range (#173690) [PR #174291](https://github.com/microsoft/vscode/pull/174291)
  * Show unresolved comment glyph in gutter when appropriate (#149162) [PR #174418](https://github.com/microsoft/vscode/pull/174418)
* [@hamzahamidi (Hamza Hamidi)](https://github.com/hamzahamidi): fix(keybindings): fix hashcode for scan codes & simple keys [PR #173456](https://github.com/microsoft/vscode/pull/173456)
* [@hermannloose (Hermann Loose)](https://github.com/hermannloose): Add "Expand All" action to Comments panel [PR #173132](https://github.com/microsoft/vscode/pull/173132)
* [@ibuky (ibuky)](https://github.com/ibuky): fix#171379 [PR #172175](https://github.com/microsoft/vscode/pull/172175)
* [@jongwooo (Jongwoo Han)](https://github.com/jongwooo): Replace deprecated `set-output` command with environment file [PR #172691](https://github.com/microsoft/vscode/pull/172691)
* [@jsanjose (JSJ)](https://github.com/jsanjose): Fixed OpenWithCodeContextMenu text [PR #173468](https://github.com/microsoft/vscode/pull/173468)
* [@MarkZuber (Mark Zuber)](https://github.com/MarkZuber)
  * Add custom path for vscode-server socket files and named pipe length [PR #172481](https://github.com/microsoft/vscode/pull/172481)
  * Send large RPC messages in chunks [PR #174278](https://github.com/microsoft/vscode/pull/174278)
* [@maski07 (Masaki Mori)](https://github.com/maski07): Fix #173735 [PR #174149](https://github.com/microsoft/vscode/pull/174149)
* [@rtjoa (Ryan Tjoa)](https://github.com/rtjoa): Use markdown in the description of `Workbench › List: Fast Scroll Sensitivity` [PR #173878](https://github.com/microsoft/vscode/pull/173878)
* [@Schamper (Erik Schamper)](https://github.com/Schamper): Only raise OfflineError when actually offline [PR #172708](https://github.com/microsoft/vscode/pull/172708)
* [@thernstig (Tobias Hernstig)](https://github.com/thernstig): Add shellscript `filenamePatterns` for `.env.*` [PR #173426](https://github.com/microsoft/vscode/pull/173426)
* [@Viijay-Kr (Vijaya Krishna)](https://github.com/Viijay-Kr): #162727: 'Debug:Run to cursor' command is now available in inactive d… [PR #169819](https://github.com/microsoft/vscode/pull/169819)
* [@walles (Johan Walles)](https://github.com/walles): Unfreeze Git Commit Message grammar II [PR #173195](https://github.com/microsoft/vscode/pull/173195)

Contributions to `vscode-pull-request-github`:

* [@joshuaobrien](https://github.com/joshuaobrien)
  * Unify style of re-request review button [PR #4539](https://github.com/microsoft/vscode-pull-request-github/pull/4539)
  * Ensure `re-request-review` command is handled in activityBarViewProvider [PR #4540](https://github.com/microsoft/vscode-pull-request-github/pull/4540)
  * Prevent timestamp in comments overflowing [PR #4541](https://github.com/microsoft/vscode-pull-request-github/pull/4541)
* [@kabel (Kevin Abel)](https://github.com/kabel): Ignore more files from the vsix [PR #4530](https://github.com/microsoft/vscode-pull-request-github/pull/4530)

Contributions to `vscode-vsce`:

* [@calebcartwright (Caleb Cartwright)](https://github.com/calebcartwright): mention yarn testing requirement in docs [PR #830](https://github.com/microsoft/vscode-vsce/pull/830)
* [@lucyydotp (Lucy)](https://github.com/lucyydotp): Don't append .js extension to .cjs files [PR #825](https://github.com/microsoft/vscode-vsce/pull/825)

Contributions to `monaco-editor`:

* [@mathedu4all (Bao Hongchang)](https://github.com/mathedu4all): Update api doc url in README.md [PR #3550](https://github.com/microsoft/monaco-editor/pull/3550)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
