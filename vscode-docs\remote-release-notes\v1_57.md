# May 2021 (version 1.57)

## Visual Studio Code Remote Core

### Install additional remote extensions in the remote menu

The remote menu now has a command **Install Additional Remote Extensions** that will show all extensions that contribute commands to the remote menu.

If your extension contributes commands to the remote menu but they don't show up, make sure to use the latest version of `vsce` when publishing your extension.

### Setting a protocol for ports

The `remote.portsAttributes` setting now allows you to set a `protocol` for a forwarded port. The protocol can be `http` or `https` and will be used when that forwarded port is opened in a browser, like with the **Open in Browser** and **Open in Preview** commands.

```json
  "remote.portsAttributes": {
    "3000": {
      "label": "Application",
    "protocol": "https",
    "onAutoForward": "openPreview"
  }
  }
```

### Warnings on port collisions

If your development setup requires that you always get the same local port as remote port when a port is forwarded, you can now set `requireLocalPort` in the `remote.portsAttributes` setting. This property will cause a modal dialog to show when the matching local port cannot be used for port forwarding.

```json
  "remote.portsAttributes": {
    "3000": {
      "requireLocalPort": true
    }
  }
```

### Port forwarding tunnels are bound to localhost

Previously, all port forwarding tunnels were bound to all-interfaces. Now, all tunnels are bound to localhost. To use all-interfaces instead, change the setting `remote.localPortHost` to `"allInterfaces"`.

### Multiselect to delete ports

The Ports view supports multiselect to delete multiple forwarded ports.

![Multiselect and delete several ports](images/1_57/port-multi-delete.gif)

## Containers (version 0.183.x)

### Workspace Trust

When opening a folder in a container, you are now asked to first trust the local folder. Workspace Trust is required because the dev container's configuration is given control over the building of the dev container, including access to the host machine. You can learn more in the [Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust) user guide.

### Environment probing on default

Environment variables are now read using an interactive login shell (sourcing, for example `~/.profile` and `~/.bashrc`) by default. This should fix many cases where environment variables were missing when executing tasks and launch configurations. The type of shell (login / interactive / none) can be controlled with the `"userEnvProbe"` property in the `devcontainer.json`.

### Additional user commands

We have added `"onCreateCommand"` to the `devcontainer.json` that runs before `"postCreateCommand"` and can be used to run a command or script that should run before VS Code connects while `"postCreateCommand"` can be used to run a command that can run while VS Code is already connected. The additional `"updateContentCommand"` is reserved for future use.

The additional `"waitFor"` property in the `devcontainer.json` changes which user command in the `devcontainer.json` (`"onCreateCommand"`, `"updateContentCommand"`, `"postCreateCommand"`, `"postStartCommand"` and `"postAttachCommand"`) to wait for before VS Code connects to the dev container. The default is to wait for `"updateContentCommand"` and have VS Code connect to the dev container while `"postCreateCommand"` can still be running.