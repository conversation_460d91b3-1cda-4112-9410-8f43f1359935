---
Order: 81
TOCTitle: VS Code Day
PageTitle: Visual Studio Code Day 2023
MetaDescription: Learn about the latest Visual Studio Code features and extensions during VS Code Day 2023 on April 26, 2023
Date: 2023-04-13
Author: <PERSON>
---

# VS Code Day: An event for an editor?

April 13, 2023 by <PERSON>, [@burkeholland](https://twitter.com/burkeholland)

VS Code Day is a one-day "Virtually Live" event starting at 10:00 AM PST on April 26. Naming things is hard, but we feel like this title says it all – a full day of community, learning, and all things Visual Studio Code. Registration is now open, and you can grab your spot today: [https://aka.ms/vscodeday](https://aka.ms/vscodeday).

![VS Code Day Banner](vscodeday-header.png)

The event kicks off with "Inside VS Code: How we build and ship it" - a keynote from Microsoft Technical Fellow <PERSON> and Partner Engineering Manager <PERSON>. Prior to joining Microsoft and creating VS Code, <PERSON> and <PERSON> both worked on Eclipse at IBM. Between the two of them, they have ~60 years of experience building developer tools. 😳

We're following that with a full schedule of sessions on AI, Remote Development, TypeScript, Python, Accessibility, and much more...

| Session  | Speaker   |
|-------------- | -------------- |
| **Keynote**: Inside VS Code - How we build and ship It   | [Erich Gamma](https://en.wikipedia.org/wiki/Erich_Gamma), [Kai Maetzel](https://www.linkedin.com/in/kai-maetzel-88ba9857)    |
| Elevate your VS Code experience   | [Sandeep Somavarapu](https://twitter.com/sandy081)    |
| Develop anywhere with VS Code   | [Brigit Murtaugh](https://twitter.com/BrigitMurtaugh), [Connor Peet](https://twitter.com/ConnorPeet)    |
| VS Code tips and tricks for TypeScript   | [Matthew Pocock](https://twitter.com/mattpocockuk)    |
| Accessibility in VS Code   | [Megan Rogge](https://twitter.com/MeganRogge_)    |
| Using VS Code, Copilot, and Codespaces to level up to Rust from Python   | [Noah Gift](https://ene.duke.edu/faculty/noah-gift)    |
| Creating a Dataset from scratch with GitHub Copilot   | [Alfredo Deza](https://twitter.com/alfredodeza)    |
| Data Science for everyone and everywhere | [Soojin Choi](https://www.linkedin.com/in/soojinmin)    |
| Writing Python web apps with VS Code | [Pamela Fox](https://twitter.com/pamelafox)    |
| What's new in GitHub Next | [Amelia Wattenberger](https://twitter.com/Wattenberger)    |
| GitHub integration in VS Code for Web | [Joyce Er](https://twitter.com/joyceerhl)   |

We're very excited about VS Code Day. But we're also engineers. We can't help but think about efficiency and simplicity, so we've taken a step back (like you may have at this point) and thought, do we need an event...for an editor?

## An event for...an editor?

The question is a fair one. After all, VS Code is just an editor - right? And how much have editors really changed over the years?

In his [keynote from VS Code Day 2021](https://www.youtube.com/watch?v=hilznKQij7A&ab_channel=VisualStudioCode), Erich Gamma talks about how VS Code started with the goal of "Focus on coding in the browser", without feeling like you are coding in the browser. The first deliverable on that vision was the [Monaco Editor](https://microsoft.github.io/monaco-editor/) - a lightweight and highly performant editor that runs in the browser. You will still see Monaco embedded in many applications today, including the [TypeScript Playground](https://www.typescriptlang.org/play), [CodeSandbox](https://codesandbox.io/), and many Microsoft products, including Azure.

The first actual IDE built with Monaco Editor was called "Monaco Workbench". It was used internally by Microsoft, and it looked like this. Shoutout to Internet Explorer 11.

![Monaco Workbench](monaco-workbench.png)

Eventually, this became "Visual Studio Online", which was embedded in Azure as a way to edit websites. And you can see the VS Code you know starting to emerge here...

![Visual Studio Online](visual-studio-online.png)

VS Code as it is today was announced at Microsoft Build in 2015. Fast-forward to 2023, and the world of developer tools is seeing a renaissance of new functionality.

## Things are getting interesting

Today, the original vision of VS Code is a reality with vscode.dev - VS Code running entirely in the browser. Containerized compute and the open source [dev container spec](https://containers.dev/) means that we can isolate dev environments and run them anywhere. Combine that with an editor that runs in the browser and you get GitHub Codespaces - a 100% hosted developer environment that you can access from any browser. Or run VS Code on your own machine and access your compute from the browser with [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels).

These changes are tectonic. They redefine how we think about when, where, and who can build software. When all you need for a complete dev environment is a browser, software development becomes accessible to everyone everywhere.

But in the last few months, things have gotten really interesting.

About a year ago, we welcomed an AI pair programmer into the editor with GitHub Copilot. Since then, ChatGPT and GPT4 have completely changed what we expect from AI and the implications for developers are enormous. We stand on the cusp of a new day for developer creativity, and we've been thinking (and working) on how to bring AI to developers in a way that feels natural and lights up [everywhere you might need it in VS Code](https://code.visualstudio.com/blogs/2023/03/30/vscode-copilot).

It's an exciting time to be a developer as we are truly approaching "Build anything from anywhere".

It's also a lot to keep up with and it can feel intimidating. Which makes this the perfect time to meet as the world's largest community of developers and talk about these things - together.

## So yes, an event for an editor

And now might be the best time for it. We're going to be serving up sessions on VS Code itself, but also on AI with GitHub Copilot, Python, Remote Development, Accessibility, and more. We're pleased to welcome several of our friends from around the community, including GitHub, Duke University, IBM, and more.

So [join us](https://aka.ms/vscodeday) as we look forward and see what the near future holds for developers everywhere. It's never a bad time to be a developer, but it's also never been this good.

Happy Coding!

Burke Holland ([@burkeholland](https://twitter.com/burkeholland))
