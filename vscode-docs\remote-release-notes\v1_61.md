# September 2021 (version 1.61)

## Core

### Port protocol for 443 and 8443 defaults to https

Since ports 443 and 8443 are often `https`, VS Code will now default them to use `https`. This can be overridden with the `"remote.portsAttributes"` setting if the new behavior is not wanted.

### DNS names in forwarded ports

DNS names can now be specified when forwarding a port. For example, if you have a MongoDB database available at `db:1234` on your remote, you can enter `"db:1234"` in the **Ports** view and your remote `db:1234` will be available locally at `localhost:1234`. For remotes with a `devcontainer.json`, you can also specify the DNS name in `forwardPorts`:

```json
{
    "forwardPorts": [ "db:5432" ]
}
```

Forwarded ports that include a non-localhost DNS name show the name in the **Ports** view:

![Forwarded port with a DNS name](images/1_61/forwarded_port_dns.png)

## Containers (version 0.202.x)

### Container features

Many of the dev container definitions now support adding tools and languages as easy to drop-in features. The **Dev Containers: Add Dev Container Configuration Files...** command at the end shows the list of available features and the **Dev Containers: Configure Container Features** command allows you to update an existing configuration.

![Configure container features list](images/1_61/configure_container_features.png)

There is also support for IntelliSense to edit the new `"features"` property in the `devcontainer.json` directly.

![Container features IntelliSense](images/1_61/container_features_intellisense.png)

### Execute in WSL setting

On Windows, to support having Docker CE or Podman installed in Windows Subsystem for Linux (WSL), the new **Execute in WSL** (`dev.containers.executeInWSL`) user setting lets the Dev Containers extension run all CLI commands in WSL.

![Execute in WSL Setting.](images/1_61/execute_in_wsl.png)

When not enabled, CLI commands are still run in WSL for workspace folders on a WSL filesystem.
