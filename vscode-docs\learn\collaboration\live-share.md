---
Order: 1
Area: collaboration
TOCTitle: Live Share
ContentId: 42123170-4394-4932-b5d3-a94c1397733c
PageTitle: Use Microsoft Live Share to collaborate with Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: Use Microsoft Live Share to collaborate with Visual Studio Code
---
# Collaborate with Live Share

## What is Live Share?

[Live Share](https://learn.microsoft.com/visualstudio/liveshare) enables you to quickly collaborate with a friend, classmate, or professor on the same code without the need to sync code or to configure the same development tools, settings, or environment.

When it comes to Live Share, seeing is believing. Check out this video to see Live Share in action:

<iframe src="https://www.youtube-nocookie.com/embed/A2ceblXTBBc?rel=0&amp;disablekb=0&amp;modestbranding=1&amp;showinfo=0" frameborder="0" allowfullscreen title="Collaborate with Live Share"></iframe>

>**Note**: The video above mentions the deprecated Live Share Extension Pack and Live Share Audio extension. To follow along with the video, you only need to install the [Live Share](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare) extension.

When you share a collaborative session, the person you're working with sees the context of the workspace in their editor. This means your classmate can read the code you shared without having to clone a repo or install any dependencies your code relies on. They can help you with your code in the Visual Studio Code environment that's familiar to them.

Each of you can open files, navigate, edit code, or highlight - and changes are instantly reflected. As you edit you can see your classmate's cursor, jump to the same location, and follow their actions.

You can also debug together using VS Code's debugging features, like hovers, locals and watches, the stack trace or the debug console. You are both able to set breakpoints and advance the debug cursor to step through the session.

For more details about what you can do with Live Share, visit the [how-to-guide](https://learn.microsoft.com/visualstudio/liveshare/use/install-live-share-visual-studio-code) or read the quick-start below.

## Get started with Live Share

To get started with using Live Share in VS Code, you'll need to download the [Live Share](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare) extension from the VS Code Marketplace.

![Live Share extension](images/live-share/live-share-extension.png)

This extension includes everything you need to start collaboratively editing and debugging in real time. This provides you and your team/class with a one-click installation, in order to begin pair programming, performing remote code reviews, driving interactive lectures, and more, without needing to leave Visual Studio Code.

Once you log into your GitHub account, you'll see the Live Share icon in the Activity Bar.

![Live Share icon in the Activity Bar](images/live-share/liveshare-icon.png)

### Starting a Live Share session

If you select **Start Collaboration session** from the Session Details menu, an invitation link to your session will automatically be copied to your clipboard. You can share this link with anyone you'd like to collaborate with, as long as they also have VS Code and the Live Share extension installed.

![Live Share invitation](images/live-share/liveshare-invitation.png)

When your classmate joins the session, you'll get a notification, and see their name come up under Participants.

![Live Share joined](images/live-share/liveshare-joined.png)

### Joining a Live Share session

If you select **Join Collaboration session** from the Session Details menu, you're able to enter the URL you received.

![Joining a Live Share session](images/live-share/liveshare-join-session.png)

You should be able to see and interact with your classmate's code on your screen!
