# October 2020 (version 1.51)

## Remote-SSH

### Security update

Version 0.61.0 of the Remote-SSH extension includes a fix for a security issue in the way that remote hostnames are parsed. Details are in [CVE-2020-17148](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2020-17148).

## Visual Studio Code Remote Core

### Terminal Reconnection

This month, we gave VS Code Remote the ability to persist your terminal sessions and reattach to them later, such as after a window reload. This also applies to Github Codespaces - if you reload the browser tab, you will stay connected to your terminal sessions.

When a VS Code Remote window closes, your terminal sessions will remain running in the background for some amount of time while we wait for a window to reconnect. The exact amount of time that they will be available for will vary based on a few factors. During this short period of time, the first VS Code window that connects to the remote will reattach to any terminals that were originally started in the same workspace.

You can even reattach manually to terminals from other workspaces that are still running in the background, with the new command `Terminal: Attach to Session`. This will show a list of terminals that are not currently attached to any window. Selecting one will attach it to the current window.

![Terminal attach to session](images/1_51/terminal-attach.gif)

### Ports view improvements

The Ports view (formerly the Forwarded Ports view) can be moved into the Panel or another view container. It is no longer confined to the Remote Explorer.

![Moveable ports view](images/1_51/moveable-ports-view.gif)

There is also a status bar indicator showing how many ports you currently have forwarded.

![Ports status bar](images/1_51/ports-statusbar.png)

When you Run or Debug and a port is printed to the debug console, that port is now automatically forwarded so that you can access your application locally.

![Automatic forward from console](images/1_51/auto-forward-console.gif)

When you don't have any forwarded ports, we show a welcome view.

![Ports welcome view](images/1_51/ports-welcome-view.png)

Finally, if you have a setup that doesn't work well with automatic port forwarding, you can disable it with the setting `"remote.autoForwardPorts": false`

### Contributing built-in extensions

There's a new command line argument `--install-builtin-extension` to install an extension as a built-in extension on server.

* It is shown in the built-in extensions view.
* It cannot be uninstalled and can only be enabled/disabled.
* It can be updatable.

## Containers (version 0.148.x)

Only announcing Dev Containers releases with version 0.148.0. (Issue grooming milestone.)
