# August 2019 (version 1.38)

## VS Code Remote Core

### Install an extension from VSIX from remote machine

You can now install an extension from a packaged VSIX that is located in the remote machine.

### Remote Explorer

We generalized the SSH Explorer and moved it into VS Code core as the Remote Explorer. Remote extensions can contribute available remote targets, connection details, and help information into the Remote Explorer. The Remote Explorer will group and sort the views based on the sections they are contributing to.

### Reconnect Now command

Previously, if you were disconnected from a remote session for some reason, you had to wait for a timer to count down before reconnection would occur. Now, you can run the **Reconnect Now** action to get right back to work.

## WSL

### Alpine WSL

Alpine WSL is now also enabled for VS Code Stable builds. Alpine support is still in preview mode as some extensions installed in Alpine Linux may not work due to glibc dependencies in native code inside the extension.

## Containers

### Alpine Linux

Support for containers using Alpine Linux is now enabled for VS Code Stable builds. Alpine support is still in preview mode as some extensions installed in Alpine Linux may not work due to glibc dependencies in native code inside the extension.

Note that there is a [known issue](https://github.com/microsoft/vscode-remote-release/issues/1026) with Alpine 3.10 and we removed logging through the `spdlog` library as a workaround while we continue to investigate.

### Multi-folder workspace support

There is now support for multi-folder workspaces. You can reopen an already open workspace in a container and pick a container definition to start from.

Currently all folders must have the folder with the `.workspace` file as their parent folder and they must be referenced in the `.workspace` file using relative paths.

### Mount consistency

The consistency for the default mount point is now `cached`, which should improve [file performance on macOS](https://docs.docker.com/docker-for-mac/osxfs-caching/). This can be configured with the Workspace Mount Consistency setting.

![Edit Remote Settings](images/1_38/consistency-setting.png).

### Variable for local workspace folder

In the `devcontainer.json`, there is a new variable `${localWorkspaceFolder}` for the local folder's path.

### Guidance for existing Dockerfile / docker-compose.yml

When creating the `devcontainer.json` from an existing `Dockerfile` or `docker-compose.yml`, we now use a full-fledged definition from the [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers), providing additional guidance in the `devcontainer.json` with comments and examples of configuration options.

### List of service to start / stop

When using Docker Compose, there is now a configuration option `"runServices"` in the `devcontainer.json` for choosing which services should be started and stopped when opening / closing the DevContainer using Compose. The default remains to start and stop all services. The `"shutdownAction"` can be used to override what happens during shutdown.

### Remote Explorer integration

Containers had been added to the new Remote Explorer. You can see and manage your containers from the **Targets (Containers)** view and when you're connected to a container you can see more information about it in the **Details (Containers)** view. From the Targets view, you can open dev container folders, attach to containers, and start/stop/remove containers. From the Details view, you can forward ports and open the ports you have forwarded in a browser.

![Containers Remote Explorer](images/1_38/containers-explorer.png)

## SSH

### ARMv7l support in Stable

A couple months ago, we added experimental support for ARMv7l 32-bit when using VS Code Insiders. With 1.38, this is now supported in Stable VS Code as well. This means you can now connect to a Raspberry Pi 2/3 via SSH and edit files using Stable VS Code.

### Experimental ARMv8l (AArch64) Linux support

Experimental support for ARMv8l (AArch64) is now available when using [VS Code Insiders](https://code.visualstudio.com/insiders/) only.

We are holding this back from the Stable release to allow for extension authors to catch up with the additional platform, see [Supporting Remote Development](https://code.visualstudio.com/api/advanced-topics/remote-extensions) for details.

### Experimental features turned on by default

Last month we added experimental support for SSH's `ForwardAgent` option and "dynamic forwarding", which provides a better experience when using password authentication. Those features are now enabled by default.
