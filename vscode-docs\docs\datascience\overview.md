---
ContentId: 23ce059e-95ec-4eaa-975c-d4cf76159516
DateApproved: 1/9/2023
MetaDescription: Doing Data Science in Visual Studio Code.
---
# Data Science in Visual Studio Code

You can do all of your data science work within VS Code. Use Jupyter Notebooks and the [Interactive Window](/docs/python/jupyter-support-py.md) to start analyzing and visualizing your data in minutes! Power your Python coding experience with IntelliSense support and build, train, and deploy machine learning models to the cloud or the edge with Azure Machine Learning service.

![Preview of Jupyter Notebooks in VS Code](images/overview/jupyter-notebook-preview.png)

## Extensions

The VS Code Marketplace offers a family of extensions that provide a first-class data science experience for Python data science. In order to get started with Python, Microsoft recommends the following extensions:

<div class="marketplace-extensions-datascience-python"></div>

### Build an extension for notebooks

We welcome (and encourage you to!) build Visual Studio Code extensions for your favorite notebooks tools. After going through [Your First Extension](/api/get-started/your-first-extension.md) tutorial, check out some of the extensions that augment the notebooks development experience in VS Code:

<div class="marketplace-extensions-notebook-tools-curated"></div>

You can explore the source code for these extensions by selecting the repository link under the **Project Details** section in the Visual Studio Marketplace.

You can also contribute directly to the [Jupyter extension](https://github.com/microsoft/vscode-jupyter).

## Data Science profile template

[Profiles](https://code.visualstudio.com/docs/configure/profiles) let you quickly switch your extensions, settings, and UI layout depending on your current project or task. To help you get started with Data Science in VS Code, you can use the [Data Science profile template](/docs/configure/profiles.md#data-science-profile-template), which is a curated profile with useful extensions, settings, and snippets. You can use a profile template as is or use it as a starting point to customize further for your own workflows.

You select a profile template through the **Profiles** > **Create Profile...** dropdown:

![Create Profile dropdown with profile templates](images/overview/profile-template-dropdown.png)

Once you select a profile template, you can review the settings and extensions, and remove individual items if you don't want to include them in your new profile. After creating the new profile based on the template, changes made to settings, extensions, or UI are persisted in your profile.

## What else can you use notebooks for?

The support for mixing executable code, equations, visualizations, and rich Markdown makes notebooks useful for breaking down new concepts in a storytelling form. This makes notebooks an exceptional tool for educators and students!

Learn C#, F#, Powershell, JavaScript, HTML, SQL, KQL (Kusto Query Language), and [Mermaid](https://mermaid.js.org/intro/) with the [Polyglot Notebooks](https://marketplace.visualstudio.com/items?itemName=ms-dotnettools.dotnet-interactive-vscode) extension.

![Polyglot Notebooks in VS Code](images/overview/polyglot-nb-ext.png)
