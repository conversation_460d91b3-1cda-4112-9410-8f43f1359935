# January 2022 (version 1.64)

## Containers (version 0.217.x)

### Always run feature entrypoints

Container features can now run their own entrypoint, for example, to start a daemon process like `sshd`, or when you choose to not override your container's entrypoint (`"overrideCommand": false` in the devcontainer.json, which is the default for Docker Compose).

### Container definition documentation links

Container definitions now have an inline action to open documentation on the definition in the browser.

### Pre-release versions

The Dev Containers extension is now using the pre-release feature for extensions, making it simple to switch between release and pre-release versions.

## SSH (version 0.74.x)

### Pre-release version

The Remote-SSH extension has adopted the pre-release extension model to allow anyone to switch between using the stable release of the extension, that is released along with every stable release of VS Code, and the pre-release version of the extension that is released everyday with the most up-to-date features and fixes.

![GIF showing how you can switch between pre-release and release version of Remote SSH on the Marketplace page in VS Code](images/1_64/ssh-pre-release.gif)

### Enable RemoteCommand

You can now use RemoteCommands defined in your SSH Configuration entries. With the settings `remote.SSH.useLocalServer` and `remote.SSH.enableRemoteCommand` set to `true`. Note that only commands that don't close the shell will work properly. If you try the same connection with your shell and you see the command executes on the remote and then the connection immediately closes, that command will not work with the Remote-SSH extension. Only commands that leave the SSH connection in an interactive state will work as expected.

![Enable RemoteCommand setting in the VS Code Settings GUI](images/1_64/ssh-remote-command.png)

## WSL (version 0.64.x)

The WSL extension now also supports Alpine distros running on arm64 .
