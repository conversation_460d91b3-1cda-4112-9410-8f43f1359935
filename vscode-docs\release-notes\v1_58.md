---
Order:
TOCTitle: June 2021
PageTitle: Visual Studio Code June 2021
MetaDescription: Learn what is new in the Visual Studio Code June 2021 Release (1.58)
MetaSocialImage: 1_58/release-highlights.png
Date: 2021-7-8
DownloadVersion: 1.58.2
---
# June 2021 (version 1.58)

**Update 1.58.1**: The update addresses these security [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2021+Recovery%22+is%3Aclosed).

**Update 1.58.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2021+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the June 2021 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Terminals in the editor](#terminals-in-the-editor-area)** - Create or move terminals into the editor area.
* **[Workspace Trust](#workspace-trust)** - Quickly customize Workspace Trust settings, disable Restricted Mode banner.
* **[Editor scroll bar customization](#editor-scroll-bar-customization)** - Set bar visibility and width, click navigation behavior.
* **[Markdown preview KaTeX support](#math-formula-rendering-in-the-markdown-preview)** - Math support in the Markdown preview.
* **[Settings editor improvements](#settings-editor)** - Support for multiple checkboxes and enumeration dropdowns.
* **[Sticky debug environment choice](#remember-chosen-environment-per-file)** - Debugger remembers your previous environment choice.
* **[Jupyter Interactive window](#jupyter-interactive-window)** - Run Jupyter code directly in an interactive window.
* **[Jupyter notebook debugging](#jupyter-notebook-debugging)** - Debug Jupyter notebook code from within VS Code.
* **[Data Science tutorials](#documentation)** - Learn about Jupyter Notebooks, PyTorch, and more.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/livestream) on Tuesday, July 13 at 8am Pacific (4pm London) to see a demo of what's new in this release, and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Workbench

### Workspace Trust

The [Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust) feature was a big change that came in the [1.57 release](https://code.visualstudio.com/updates/v1_57#_workspace-trust) and we believe it is important that developers can safely browse code, even when they are unfamiliar with the source. This milestone we've been absorbing customer feedback and have focused on fixing issues and following up on suggestions.

In order to help people discover the user settings that can [customize Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust#_settings), there is a new **Configure your settings** action in the header of the Workspace Trust editor. This action will bring up the Settings editor filtered by `@tag:workspaceTrust`.

![Configure your settings action in the Workspace Trust editor](images/1_58/workspace-trust-settings.png)

Speaking of settings, there is a new setting to configure when the banner appears indicating that the current window is in Restricted Mode. Similar to the `security.workspace.trust.startupPrompt` setting, the new `security.workspace.trust.banner` allows you to disable the banner after you've become familiar with the Workspace Trust feature and don't need a reminder that you are working in Restricted Mode. By default, this banner shows once per workspace and remains until it is dismissed (`"untilDismissed"`). The other options for displaying the Restricted Mode banner are `"always"` and `"never"`.

You can learn more about the rationale and development of Workspace Trust in the recent [Workspace Trust blog post](https://code.visualstudio.com/blogs/2021/07/06/workspace-trust). The post explains how Restricted Mode helps protect you from unintended, and potentially malicious, code execution and has tips to set up your development machines so you can work both quickly and safely.

### Settings editor

This iteration adds support to the Settings editor for settings objects with a fixed number of Boolean properties, by rendering the properties as a list of checkboxes.

![Settings editor Boolean object renderer demo](images/1_58/bool-object-renderer.gif)

This iteration also adds additional support for arrays of enumerations, by using dropdowns in editing mode instead of plain textboxes.

![Settings editor enum array renderer demo](images/1_58/enum-array-renderer.gif)

### Transient workspaces

There is now a new flavor of workspace, called a "transient" workspace, that behaves differently than usual workspaces, specifically:

* Restarting or reloading VS Code will not attempt to open the workspace again.
* Workspaces set as transient do not show up in the list of recently opened workspaces.

Designate a transient workspace by adding the `transient` property set to `true` in the workspace `.code-workspace` file:

```json
{
    "folders": [],
    "transient": true
}
```

### Settings Sync

There is now a **Troubleshoot** view for Settings Sync that contains all logs and the last sync states. You can access this view by running the command **Settings Sync: Show Synced Data** and enabling it by opening the **Views** submenu and checking **Troubleshoot**:

![Settings Sync Troubleshoot command in the Settings Sync view menu](images/1_58/settings-sync-troubleshoot.png)

*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

### Search max results setting

There is a new setting, `search.maxResults`, which lets you set the maximum number of text search results. The default is 20,000, up from 10,000 previously. You may see some slowness when loading a very large number of search results.

## Integrated Terminal

### Terminals in the editor area

Terminals can now be created in or moved to the editor area, enabling a multi-dimensional grid layout that persists and remains visible regardless of panel state.

To use terminals in the editor area, there are several options:

* Create via the **Create Terminal in Editor Area** command.
* Drag and drop a terminal from the tabs list to the editor.
* Run **Move Terminal into Editor Area** with a terminal focused.
* Select **Move into Editor Area** on the terminal tab context menu.

![4 terminals in the editor area are laid out in a 2 by 2 grid](images/1_58/terminal-editor-grid.png)

The new `terminal.integrated.defaultLocation` setting can be set to `editor` to direct newly created terminals to the editor area by default.

### Explicitly setting canvas renderer

In a prior iteration, we enabled the WebGL terminal renderer by default and replaced the `terminal.integrated.rendererType` setting with the simpler `terminal.integrated.gpuAcceleration`. Unfortunately, some machines see degraded experience with WebGL and the only option available was to disable GPU acceleration all together and move to the much slower DOM-based renderer. `terminal.integrated.gpuAcceleration` can now be set explicitly to `canvas`, which may be preferable for these environments.

### Hide link hovers

The new `terminal.integrated.showLinkHover` setting lets you disable link hovers in the terminal. This can be useful if you find hovers on terminal links distracting.

## Editor

### Editor scroll bar customization

There are new settings to customize the editor's scroll bars:

* Control scroll bar visibility using `editor.scrollbar.horizontal` and `editor.scrollbar.vertical`.
* Change their thickness with `editor.scrollbar.horizontalScrollbarSize` and `editor.scrollbar.verticalScrollbarSize`.
* Determine whether clicking scrolls by page or jumps to the click position (default behavior) using `editor.scrollbar.scrollByPage`.

Below the editor's vertical scroll bar width is set to 30 pixels.

![Vertical editor scroll bar width set to 30 pixels](images/1_58/scroll-bar-width.png)

## Languages

### Math formula rendering in the Markdown preview

VS Code's [built-in Markdown preview](https://code.visualstudio.com/docs/languages/markdown#_markdown-preview) can now render math equations using [KaTeX](https://katex.org).

![Math rendering in the built-in Markdown preview](images/1_58/markdown-math.png)

Inline math equations are wrapped in single dollar signs:

```markdown
Inline math: $x^2$
```

You can create a math equation block with double dollar signs:

```markdown
Math block:

$$
\displaystyle
\left( \sum_{k=1}^n a_k b_k \right)^2
\leq
\left( \sum_{k=1}^n a_k^2 \right)
\left( \sum_{k=1}^n b_k^2 \right)
$$
```

You can set `"markdown.math.enabled": false` to disable this feature.

### Markdown math formula syntax highlighting

VS Code also now supports highlighting of math equations in Markdown source:

![Syntax highlighting of a math equation in a Markdown file](images/1_58/markdown-math-highlighting.png)

This works both in normal Markdown files and in Markdown cells inside notebooks.

### TypeScript 4.3.5

We now bundle TS 4.3.5. This minor update fixes a few important bugs, including [auto imports not working in JSX](https://github.com/microsoft/TypeScript/issues/44657).

## Debugging

### Remember chosen environment per file

When debugging is started without a `launch.json` file, VS Code looks at the active editor and based on the language mode of the editor decides what debug extension to use. However, for some languages, multiple debug extensions are possible, and in that case, VS Code prompts you to choose one of them. To make debugging smoother, VS Code now remembers the chosen debugger per file, so that the next time you start debugging, the session starts without any prompts.

In the short video below, the user selects the Node.js debug environment for debugging a single JavaScript file and that choice is remembered when a new debug session starts for that file.

![Debug is started first time and the environment prompt is presented. The next time there is no prompt when the debug session starts](images/1_58/debug-env.gif)

### Debug console suggestions no longer accepted on Enter

The Debug Console suggestions are no longer accepted on `Enter`, but only on `Shift+Enter` and `Tab`. Previously, it was easy to accidentally accept a suggestion when you just wanted to evaluate what is already typed in the Debug Console input.

In order to make the `Tab` key more discoverable as a way to accept suggestions, VS Code now has a hint Status bar ("Insert (Tab)") in the Debug Console input.

![Debug console input box showing a Status bar rendered in the bottom that says "Insert (Tab)"](images/1_58/repl.png)

## Contributions to extensions

### Jupyter Interactive window

We have started working on providing a built-in interactive window experience on top of the notebook ecosystem and the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension has adopted it behind a setting `jupyter.enableNativeInteractiveWindow`. If the feature is enabled, the Jupyter extension will open the built-in editor instead of the webview implementation, when running code from Python files or directly launching from the Command Palette. The built-in editor works with your customized keybindings/keymaps or language extensions since it's deeply integrated with the workbench.

![Jupyter built-in interactive window demo](images/1_58/builtin-interactive-window.gif)

### Jupyter notebook debugging

We have been working on supporting debugging in Jupyter notebooks, so that you can set breakpoints in notebook cells, execute cells step-by-step, and use all other VS Code debugger features. This is experimental, but you can try it out by setting `"jupyter.experimental.debugging": true`, installing version 6 of ipykernel in your selected kernel, then clicking the **Debug** button in the notebook toolbar.

![Jupyter debugging](images/1_58/notebook-debug.png)

### Remote Repositories

In this iteration, we largely focused on bug fixes and some performance improvements for the [Remote Repositories](https://marketplace.visualstudio.com/items?itemName=GitHub.remotehub) extension, as well as better integration with the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension. Additionally, you can now remove a remote repository from the **Remote Repositories** view in the **Remote Explorer**, and there is better support for opening `\blob` URLs with line ranges.

### GitHub Pull Requests and Issues

The focus this iteration for the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension was on fixing bugs. Check out the [changelog](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0280) to see the highlights.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.58 include:

* Option to only open a browser once for forwarded ports.
* `devcontainer.json` support for cached images.
* Dev Containers now checks "hostRequirements".

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_58.md).

### Live Preview

We have developed a [Live Preview](https://marketplace.visualstudio.com/items?itemName=ms-vscode.live-server) extension, which hosts a local server for web development previewing! 🎉

It features an in-editor browser preview, live preview refreshing (on file change or on save), multi-root support, and more!

![Live Preview Quick Walkthrough](images/1_58/live-preview-summary-1.gif)

The extension also has a `Live Preview` task, which runs a persistent server and allows you to view server traffic and its associated files.

![Live Preview Tasks](images/1_58/live-preview-summary-2.gif)

To learn more about what it can do, you can find details [in the Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.live-server) or [check out its repo](https://github.com/microsoft/vscode-livepreview).

## Preview Features

### TypeScript 4.4 support

This release includes initial support for the upcoming TypeScript 4.4 release. You can read more about the new language features and improvements in TypeScript 4.4 [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-4-beta/).

To start using the TypeScript 4.4 nightly builds, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

Please share your feedback and let us know if you run into any bugs with TypeScript 4.4.

### Move terminals between windows

It's now possible to move terminals between windows by detaching via **Terminal: Detach Session** in one window and attaching to another with **Terminal: Attach to Session**. In the future, this should help enable cross-window drag and drop!

## Extension authoring

### Detailed completion item labels

We have added new API for more detailed and structured completion item labels. You can now use the [vscode.CompletionItemLabel](https://github.com/microsoft/vscode/blob/3cca8288130cb5e0df113571b51c5b62142bb5b0/src/vs/vscode.d.ts#L3978-L4001) type for item's labels and it allows you to specify label, details, and description.

These new properties allow language extensions to show signatures or qualifiers but also other completion provider can utilize this. For instance, the [GitHub Pull Request and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension now shows full names alongside aliases.

![Completion for Github aliases with full names](images/1_58/completion-item-label.png)

### Details for modal messages

The API for showing modal information, warning, and error messages now supports providing details. Detail text is rendered below and less prominently than the actual message.

![A modal dialog with details](images/1_58/modal-and-detail.png)

The dialog above is produced with the following code snippet. Note that detail text is only supported for modal messages (`modal: true`).

```ts
vscode.window.showInformationMessage('This is the message', {
  modal: true,
  detail: 'This is the detail. Rendered less prominent, but with more space for, well, details.'
});
```

### Contribute terminal profiles

Terminal profiles can now be contributed by extensions and they will be displayed in the profile picker:

![A contributed profile will show up along side detected and configured terminal profiles](images/1_58/terminal-profiles.png)

To contribute terminal profiles, you will need to do three things:

First, add the contribution to your `package.json`:

```json
"contributes": {
  "terminal": {
    "profiles": [
      {
        "title": "Custom Profile",
        "id": "custom_profile"
      }
    ]
  },
}
```

Next, add the activation event so the extension is activated when the user requests the profile:

```json
"activationEvents": [
  "onTerminalProfile:custom_profile"
]
```

Finally, register the terminal profile provider that will return the set of options used to create the terminal. The options can be either the standard process-based `TerminalOptions` or the custom `ExtensionTerminalOptions`:

```ts
vscode.window.registerTerminalProfileProvider('custom_profile', {
  provideTerminalProfile() {
    return {
      options: {
        name: 'Profile from extension',
        shellPath: 'cmd.exe'
      }
    };
  }
})
```

### Change ExtensionTerminalOptions-based terminal names

The new `Pseudoterminal.onDidChangeName` event allows changing the name of terminals created with `ExtensionTerminalOptions`.

```ts
const writeEmitter = new vscode.EventEmitter<string>();
const nameEmitter = new vscode.EventEmitter<string>();
const pty = {
  onDidWrite: writeEmitter.event,
  onDidChangeName: nameEmitter.event,
  open: () => writeEmitter.fire('Press and key to set the terminal title'),
  close: () => { /* noop*/ },
  handleInput: (data: string) => {
    writeEmitter.fire(`Set title to "${data}"`);
    nameEmitter.fire(data);
  }
};
const terminal = (<any>vscode.window).createTerminal({ name: `My Extension REPL`, pty });
terminal.show();
```

### Set the icon for terminals created via the extension API

`window.createTerminal` now accepts an `iconPath`, associating an icon with the terminal in its tab.

```ts
const term = vscode.window.createTerminal({
  name: `Serve`,
  iconPath: new vscode.ThemeIcon('server-process')
});
term.show();
```

![The iconPath will show up next to the terminal's name](images/1_58/terminal-icon.png)

### Enumerate saved Memento keys

There is a new `Memento.keys()` API to enumerate the set of saved `Memento` keys. This API can make it easier to deal with previously saved data and be helpful if you need to migrate saved data.

## Language Server Protocol

A new version of the language server protocol, together with the corresponding npm modules, has been published. The version contains the final proposal for the [diagnostic pull model specification](https://github.com/microsoft/vscode-languageserver-node/blob/main/protocol/src/common/proposed.diagnostics.md#L1).

## Debug Adapter Protocol

### Progress on "Memory write" request and "Memory changed" event

We are planning to integrate a memory viewer in a future version of VS Code's debug experience. For this support, a `ReadMemory` request has existed in the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol/) for some time. We are now working on a corresponding `WriteMemory` request and a `Memory` event. The `WriteMemory` request has a [final proposal](https://github.com/microsoft/debug-adapter-protocol/issues/163#issuecomment-865864401) that will be added to DAP in the next milestone. The `Memory` event proposal is still [under discussion](https://github.com/microsoft/debug-adapter-protocol/issues/194). If you are a debug extension author and interested in these DAP protocol additions, we'd appreciate your feedback.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Rich Status bar hover

There is a new proposed API to support rich hovers on Status bar items. To try it out, provide a `MarkdownString` in `StatusBarItem.tooltip2`.

![Rich Status bar hover](images/1_58/rich-statusbar-hover.png)

* If `MarkdownString.supportThemeIcons` is true, you can use icons with the `$(iconName)` syntax.
* If the `MarkdownString` is trusted, you can also add command links. Syntax: `([test](command:vscode.newWindow))`

### Testing

The march to finalize the testing APIs continues. This iteration, we refactored some of the testing APIs to better match the style of other APIs in the extension host. To track the finalization process and hear about changes to the APIs, follow [issue #122208](https://github.com/microsoft/vscode/issues/122208).

The primary changes we foresee are to how "run configurations" are provided. To track or weigh in on the progress of these runner APIs, follow [issue #127096](https://github.com/microsoft/vscode/issues/127096).

Finally, we began investigation and the initial implementation of [test coverage](https://github.com/microsoft/vscode/issues/123713), which will receive surface area in the UI in the coming weeks.

### Inline Suggestions

The inline suggestions API allows extensions to provide inline suggestions that are decoupled from the suggestion widget. An inline suggestion is rendered as if it was already accepted, but with a gray color. Users can cycle through suggestions and accept them with the `Tab` key.

```ts
vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' },
    {
        provideInlineCompletionItems: async (document, position) => {
            return [{ text: "< 2) {\n\treturn 1;\n\t}" }];
        }
    }
);
```

![Inline suggestions in the editor](images/1_58/inline-suggestions.gif)

## Engineering

### ES2020

We have updated our TypeScript projects to allow using recent JavaScript APIs, like `Promise.allSettled` or `String.replaceAll`. We also output more modern JavaScript, ES2020, which reduces the overall size of our main bundle slightly.

### Progress for Electron sandbox support

This milestone we continued to make the VS Code window ready for enabling Electron's [sandbox](https://www.electronjs.org/docs/tutorial/sandbox) and [context isolation](https://www.electronjs.org/docs/tutorial/context-isolation) features.

Specifically:

* We removed our Node.js dependency for loading the resources needed to open VS Code windows in general.
* We added searching to `iframe` elements to get behavior closer to the deprecated `webview` component, allowing us to drop `webview` in the future. This feature can be enabled by setting `"webview.experimental.useIframes": true` in the `settings.json` file.

## Documentation

### Data Science tutorials and topics

The Data Science tutorials and topics are now under their own section in the [https://code.visualstudio.com](https://code.visualstudio.com/docs/datascience) table of contents. You can find tutorials on using [Jupyter Notebooks](https://code.visualstudio.com/docs/datascience/jupyter-notebooks), the [Python Interactive window](https://code.visualstudio.com/docs/datascience/python-interactive), and even a [Data Science tutorial](https://code.visualstudio.com/docs/datascience/data-science-tutorial) analyzing passenger data from the Titanic.

![Data Science table of contents on code.visualstudio.com](images/1_58/data-science-toc.png)

## Notable fixes

* [120956](https://github.com/microsoft/vscode/issues/120956): iPad: can not trigger context menu outside of Editor
* [122448](https://github.com/microsoft/vscode/issues/122448): Auto disable extensions if their dependencies are disabled
* [124169](https://github.com/microsoft/vscode/issues/124169): Try to not indent the active debugging line
* [125144](https://github.com/microsoft/vscode/issues/125144): When thread is no longer stopped the focus is not automatically passed to another thread
* [125528](https://github.com/microsoft/vscode/issues/125528): Deselect breakpoint in breakpoint view when no longer stopped on it
* [126211](https://github.com/microsoft/vscode/issues/126211): Node process started via debugger is not killed when quitting VS Code
* [126702](https://github.com/microsoft/vscode/issues/126702): macOS: command line `code .` not working
* [127716](https://github.com/microsoft/vscode/issues/127716): Debug Hover: Allow keyboard to search through properties

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

* [@Aaaaash (大表哥)](https://github.com/Aaaaash)
  * fix: areLanguageDiagnosticSettingsEqual always return true [PR #125365](https://github.com/microsoft/vscode/pull/125365)
  * Files watcher exclude - do not ignore changes to the node_modules folder itself. [PR #125801](https://github.com/microsoft/vscode/pull/125801)
* [@barbaravaldez (Barbara Valdez)](https://github.com/barbaravaldez): Modify treeDataChange event [PR #125214](https://github.com/microsoft/vscode/pull/125214)
* [@conwnet (netcon)](https://github.com/conwnet): fix: web typescript extension can not found definitions [PR #125279](https://github.com/microsoft/vscode/pull/125279)
* [@cpsauer (Christopher Sauer)](https://github.com/cpsauer): Accept editor line height as multiple of font size [PR #125601](https://github.com/microsoft/vscode/pull/125601)
* [@danielgary (Daniel Gary)](https://github.com/danielgary): use title for terminal selection [PR #123478](https://github.com/microsoft/vscode/pull/123478)
* [@datho7561 (David Thompson)](https://github.com/datho7561): Add `wordPattern` for XML and XSL languages [PR #127334](https://github.com/microsoft/vscode/pull/127334)
* [@fannheyward (Heyward Fann)](https://github.com/fannheyward): fix: importStatementSuggestions config section [PR #124822](https://github.com/microsoft/vscode/pull/124822)
* [@forivall (Emily Marigold Klassen)](https://github.com/forivall): chore(typescript-language-features): fix spelling of deprecated [PR #125693](https://github.com/microsoft/vscode/pull/125693)
* [@hantatsang (Sang)](https://github.com/hantatsang): Improve DocumentFilter doc [PR #125734](https://github.com/microsoft/vscode/pull/125734)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes terminal drop overlay moves horizontally instead of vertically when pane is on the side [PR #124985](https://github.com/microsoft/vscode/pull/124985)
  * Fixes error while viewing contributions tab in extensions editor [PR #125607](https://github.com/microsoft/vscode/pull/125607)
  * Fixes error after dropping invalid element over editor area [PR #125943](https://github.com/microsoft/vscode/pull/125943)
  * Fixes error when drag and drop invalid element into terminal [PR #126553](https://github.com/microsoft/vscode/pull/126553)
  * Fixes rich hover focus outline shows under status bar hovers [PR #127603](https://github.com/microsoft/vscode/pull/127603)
* [@ksyx](https://github.com/ksyx): fix(window.ts): unresponsive window popup message [NFC] [PR #126351](https://github.com/microsoft/vscode/pull/126351)
* [@LiangchengJ (Liangcheng Juves)](https://github.com/LiangchengJ): Updated "Code - OSS" icon for macOS Big Sur [PR #124416](https://github.com/microsoft/vscode/pull/124416)
* [@markxoe (Mark Oude Elberink)](https://github.com/markxoe): Optimize english please actions [PR #125154](https://github.com/microsoft/vscode/pull/125154)
* [@mghalayini (Mohammad Ghalayini)](https://github.com/mghalayini): support links that begin with a root folder [PR #125615](https://github.com/microsoft/vscode/pull/125615)
* [@movermeyer (Michael Overmeyer)](https://github.com/movermeyer): Add a camelCase transform for Snippet variables [PR #127257](https://github.com/microsoft/vscode/pull/127257)
* [@nrayburn-tech (Nicholas Rayburn)](https://github.com/nrayburn-tech)
  * Add hover css to the hover element [PR #123384](https://github.com/microsoft/vscode/pull/123384)
  * add option to not show link hovers for terminal [PR #124926](https://github.com/microsoft/vscode/pull/124926)
* [@qchateau (Quentin Chateau)](https://github.com/qchateau): Fix duplicated code format [PR #126183](https://github.com/microsoft/vscode/pull/126183)
* [@richardtallent (Richard Tallent)](https://github.com/richardtallent): Fix doc of renderWhitespace default value [PR #125591](https://github.com/microsoft/vscode/pull/125591)
* [@rickpmartin (Rick Martin)](https://github.com/rickpmartin): Add search.maxResults setting [PR #126762](https://github.com/microsoft/vscode/pull/126762)
* [@ryyppy (Patrick Ecker)](https://github.com/ryyppy): Update to latest seti-ui & add ReScript to the icon theme [PR #125659](https://github.com/microsoft/vscode/pull/125659)
* [@stefanloerwald (Stefan Lörwald)](https://github.com/stefanloerwald): Fixes #125303 [PR #125304](https://github.com/microsoft/vscode/pull/125304)
* [@suzmue (Suzy Mueller)](https://github.com/suzmue): Set top level stopped reason when threadId not specified [PR #126852](https://github.com/microsoft/vscode/pull/126852)
* [@turara](https://github.com/turara): Fixes [Terminal: Clear] command [PR #107454](https://github.com/microsoft/vscode/pull/107454)
* [@Vtec234 (Wojciech Nawrocki)](https://github.com/Vtec234)
  * Add WASM MIME type [PR #125782](https://github.com/microsoft/vscode/pull/125782)
  * Support Content-Length and Last-Modified for webview local resources [PR #125994](https://github.com/microsoft/vscode/pull/125994)

Contributions to `vscode-extension-samples`:

* [@barbaravaldez (Barbara Valdez)](https://github.com/barbaravaldez): Implement drag and drop controller [PR #431](https://github.com/microsoft/vscode-extension-samples/pull/431)

Contributions to `vscode-html-languageservice`:

* [@dummdidumm (Simon H)](https://github.com/dummdidumm): Add some missing mouse and pointer events [PR #108](https://github.com/microsoft/vscode-html-languageservice/pull/108)
* [@hardikshah197 (Hardik Sharma)](https://github.com/hardikshah197): Minor Documentation type change to "value" [PR #107](https://github.com/microsoft/vscode-html-languageservice/pull/107)

Contributions to `vscode-js-debug`:

* [@rinselmann (Ryan Inselmann)](https://github.com/rinselmann): fix: Error when trying to set a breakpoint in index.html [PR #1029](https://github.com/microsoft/vscode-js-debug/pull/1029)

Contributions to `vscode-languageserver-node`:

* [@dsherret (David Sherret)](https://github.com/dsherret): Ensure exit notification is sent before closing connection [PR #776](https://github.com/microsoft/vscode-languageserver-node/pull/776)

Contributions to `language-server-protocol`:

* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Add source.fixAll constant [PR #1308](https://github.com/microsoft/language-server-protocol/pull/1308)

Contributions to `monaco-editor`:

* [@anthony-c-martin (Anthony Martin)](https://github.com/anthony-c-martin): Add Bicep sample [PR #2541](https://github.com/microsoft/monaco-editor/pull/2541)

Contributions to `monaco-languages`:

* [@qwefgh90 (Changwon Choe)](https://github.com/qwefgh90): improve a rule which freeze a page in restructuredText [PR #141](https://github.com/microsoft/monaco-languages/pull/141)

Contributions to `monaco-typescript`:

* [@spahnke (Sebastian Pahnke)](https://github.com/spahnke): Build tag text correctly for all tags [PR #81](https://github.com/microsoft/monaco-typescript/pull/81)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
