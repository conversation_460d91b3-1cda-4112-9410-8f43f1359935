---
Order: 94
TOCTitle: November 2023
PageTitle: Visual Studio Code November 2023
MetaDescription: Learn what is new in the Visual Studio Code November 2023 Release (1.85)
MetaSocialImage: 1_85/release-highlights.png
Date: 2023-12-7
DownloadVersion: 1.85.2
---
# November 2023 (version 1.85)

**Update 1.85.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2023+Recovery+1%22+is%3Aclosed).

**Update 1.85.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2023+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the November 2023 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[Floating editor windows](#floating-editor-windows)** - Drag and drop editors onto your desktop.
* **[Accessible View workflow](#accessibility)** - Smoother transitions to and from the Accessible View.
* **[Finer extension update control](#extension-auto-update-control)** - Choose which extensions to auto update.
* **[Source Control incoming and outgoing view](#source-control)** - Easily review pending repository changes.
* **[JavaScript heap snapshots](#javascript-debugger)** - Visualize heap snapshots including memory object graphs.
* **[TypeScript Go to Definition from inlay hints](#jump-to-definition-for-inlay-hints)** - Jump to definition from inlay hint hovers.
* **[Python type hierarchy display](#python)** - Quickly review and navigate complex type relationships.
* **[GitHub Copilot updates](#github-copilot)** - Inline chat improvements, Rust code explanation.
* **[Preview: expanded Sticky Scroll support](#preview-features)** - Sticky Scroll in tree views and the terminal.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## GitHub Universe, Copilot, and VS Code

If you were able to watch or attend [GitHub Universe](https://githubuniverse.com/) this year, you saw that [GitHub Copilot](https://github.com/features/copilot) was center stage. Copilot was featured in the [Opening Keynote](https://www.youtube.com/watch?v=NrQkdDVupQE) and breakout sessions such as [GitHub Copilot: the AI pair programmer for today and tomorrow](https://www.youtube.com/watch?v=AAT4zCfzsHI).

The VS Code team has been working hard to integrate Copilot into the editor and you can learn more about the team's progress leading up to GitHub Universe in the recent [Pursuit of "wicked smartness" in VS Code](https://code.visualstudio.com/blogs/2023/11/13/vscode-copilot-smarter) blog post. The post details the development of Copilot Chat **agents**, that behave as subject matter experts on code bases and technologies, and describes how extension authors will be able to contribute their own custom agents to VS Code.

## Accessibility

### Accessible View

Last iteration, we introduced automatic closing of the [Accessible View](https://code.visualstudio.com/docs/configure/accessibility/accessibility#_accessible-view) when a key is pressed for a seamless flow between a UI component and its Accessible View. In some cases, this behavior might not be desirable and can now be disabled via the `accessibility.accessibleView.closeOnKeyPress` setting.

If you find yourself toggling between the terminal and the terminal's Accessible View, you might want to enable `terminal.integrated.accessibleViewFocusOnCommandExecution`, which automatically opens the Accessible View after a command is executed in the terminal.

### Tooltips shown on keyboard focus

To improve the experience for keyboard users, tooltips are now shown on keyboard focus for items with custom hovers such as Activity Bar and Status Bar items.

![Tooltip for Extensions view icon in the Activity Bar](images/1_85/tooltip-keyboard-focus.png)

### Speech detection timeout

A new setting `accessibility.voice.speechTimeout` controls how long to wait during silence before accepting speech input, for example to Copilot Chat. By default, voice input is automatically submitted after `1.2` seconds of silence. You can set this to `0` to disable accepting speech input entirely.

## Workbench

### Floating editor windows

We are happy to announce that with this release you can move editors out of the main window into their own lightweight windows. Changes to an editor in one window apply immediately to all other windows where the editor is open.

The easiest way to create a floating editor window is to drag an editor out of the current window and drop it on an empty space on your desktop:

<video src="images/1_85/float_1.mp4" autoplay loop controls muted title="Floating window by drag and drop"></video>

_Theme: [GitHub Dark](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Dark))_

The new `workbench.editor.dragToOpenWindow` setting can disable this behavior.

There are also new global and contextual commands to move or copy editors and editor groups into their own windows:

<video src="images/1_85/float_2.mp4" autoplay loop controls muted title="Command to Copy Editor into New Window"></video>

* **View: Copy Editor into New Window** (`workbench.action.editor.copyWithSyntaxHighlightingAction`)
* **View: Move Editor into New Window** (`workbench.action.editor.moveEditorToNextWindow`)
* **View: Copy Editor Group into New Window** (`workbench.action.editor.copyGroupToNextWindow`)
* **View: Move Editor Group into New Window** (`workbench.action.editor.moveGroupToNextWindow`)
* **View: New Empty Editor Window** (`workbench.action.newWindow`)

The editor area in floating windows can be arranged with any [complex layout](http://code.visualstudio.com/docs/configure/custom-layout) that you want. And since both terminals and search results can be opened as editors, you can now have these features in separate windows as well!

![Terminal and Search as editors in a floating window](images/1_85/float_3.png)

Give this new feature a try and report back any issues you encounter. Make sure to [review our existing issues](https://github.com/microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Aworkbench-auxwindow) and cast your vote on missing features.

### Native paste support in the File Explorer

VS Code now supports natively pasting files from your operating system's Explorer into the VS Code File Explorer.

<video src="images/1_85/native-file-copy.mp4" autoplay loop controls muted title="Copy a file from Windows Explorer to the VS Code File Explorer"></video>

### Extension auto update control

You can now choose which extensions to auto update. This is helpful if you do not want to auto update all extensions but selectively choose which ones to auto update. You can either select an extension or all extensions from a publisher. If you choose to auto update all extensions from a publisher, you can then unselect individual extensions from that publisher.

![Choose extensions to auto update](images/1_85/select-auto-update-extensions.png)

You should have auto updates either disabled (**None**) or enabled for selected extensions (**Selected Extensions**) to use this feature.

![Auto update mode options with Selected Extensions checked](images/1_85/auto-update-mode.png)

### New Profile icons

The following new Profile icons are available to add to your profiles.

![New Profile icons](images/1_85/new-profile-icons.png)

* `vr`
* `piano`
* `coffee`
* `snake`
* `robot`
* `game`
* `chip`
* `music`

### Settings editor search improvements and bug fixes

The Settings editor is back to sorting search results by match type first, rather than only by the table of contents. In other words, title and keyword matches show up at the top, so you don't have to scroll down to find a setting with a matching title.

![Settings editor showing the windows.titleBarStyle setting appearing first when searching "title bar style"](images/1_85/se-search-fixed.png)

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

The Settings editor still orders settings by the table of contents for tie-breakers, meaning that extension authors' `order` keys are respected for search queries such as `@ext:<extension-id>`.

Many Settings editor regressions were also fixed this iteration, including the Settings editor failing to load due to network issues and hanging on certain search queries.

## Editor

### Code Actions on Save and Auto

You can now enable Code Actions on Auto Save in the editor, and **Editor: Code Actions On Save** (editor.codeActionsOnSave) settings have been migrated over to corresponding enum values. When set to `always`, Code Actions can be run when you save or Auto Save with window change (`onWindowChange`) or focus change (`onFocusChange`). To enable this feature, check **Editor: Code Actions On Save** (editor.codeActionsOnSave) and change each Code Action's setting to `always`.

The setting value updates are as follows, with the previous boolean values to be deprecated in favor of the string equivalent.

The options are:

* `explicit` - Triggers Code Actions when explicitly saved. Same as `true`.
* `always` -  Triggers Code Actions when explicitly saved and on Auto Saves from window or focus changes.
* `never` - Never triggers Code Actions on save. Same as `false`.

### Multi document highlighting

Multi document highlighting has additional support from a new proposed [MultiDocumentHighlightProvider](#multi-document-highlighting-api) API. There is now built-in support for semantic occurrence highlighting for the TypeScript language that can be enabled by changing the **Editor: Occurrences Highlight** (`editor.occurrencesHighlight`) setting value from `singleFile` to `multiFile`. For languages besides TypeScript, multi document occurrences are highlighted based off of textual occurrences rather than semantic occurrences until more language-specific providers are implemented.

<video src="images/1_85/multi-document-highlight.mp4" autoplay loop controls muted title="Semantic multi document highlighting across TypeScript files"></video>

## Source Control

### Incoming/Outgoing changes

This milestone we have introduced a new **Incoming/Outgoing** section in the Source Control view to display incoming and outgoing changes for the current branch compared to its remote. The new section displays both the individual changes with the number of resources changed along with insertions and deletions, as well as an **All Changes** entry that summarizes all resources across all changes. The visibility of the new section can be controlled using the `scm.showIncomingChanges`, and `scm.showOutgoingChanges` settings. Both settings support the following values: `always`, `auto` (default), and `never`.

![Incoming/Outgoing changes in the Source Control view](images/1_85/scm-incoming-outgoing.png)

### Input maximum lines

Previously, Source Control input would auto-grow to display at most 6 lines of text, which was sufficient space for most commit messages. However there were cases where more space would be helpful, and there is a new setting, `scm.inputMaxLines`, that controls the maximum number of Source Control input lines.

## Terminal

### Sticky scroll

Sticky scroll has landed in the terminal! Using knowledge provided by [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration), the prompt of the command at the top of the viewport sticks to the top of the terminal, similar to how [Sticky Scroll works in the editor](https://code.visualstudio.com/updates/v1_71#_sticky-scroll).

![Running 'ls' command in the terminal will show the 'ls' prompt at the top of the terminal](images/1_85/terminal-sticky-scroll.png)

Clicking a Sticky Scroll element will scroll to that part of the terminal buffer.

This is currently disabled by default but can be enabled by setting `"terminal.integrated.stickyScroll.enabled": true`. We plan on enabling this by default in the future, at which point you will be able to opt out by right-clicking and toggling it off.

### Command highlighting

Hovering a command in the terminal now shows a highlight bar to its left. This is useful for plain terminal prompts where it's not clear where one command starts and another ends.

![Hovering a command will show a line to the left that highlights the command and its output](images/1_85/terminal-command-highlighting.png)

### Shell integration and command navigation improvements

With the introduction of Sticky Scroll, many improvements were made to shell integration. In particular to terminals running on Windows, where the markers received from shell integration aren't totally reliable. There is now logic that intelligently scans the terminal contents and adjusts the markers before making the terminal command available.

Shell integration is also now capable of detecting the distinct parts of a prompt; the prompt and its input. This determines what part of the command displays when using Sticky Scroll. This will also trim empty lines from the top of the prompt, commonly used to split up output and make the terminal easier to read.

The existing [command navigation](https://code.visualstudio.com/docs/terminal/shell-integration#_command-navigation) feature also benefits as you can navigate to the more reliable prompt used for Sticky Scroll, rather than the less reliable prompt line.

Before:

![Before only a single line would be highlighted](images/1_85/terminal-command-nav-before.png)

After:

![When navigating commands, the entire prompt is now highlighted](images/1_85/terminal-command-nav-after.png)

### Improved underline rendering

Dashed and dotted underlines in the terminal are now rendered in a pixel perfect pattern:

![Dotted and dashed underlines are now pixel perfect](images/1_85/terminal-underline.png)

![The underline improvements also work when zoomed in](images/1_85/terminal-underline-zoom.png)

### Git pull Quick Fix

If a Git branch checkout can be fast forwarded, a new terminal Quick Fix provides the option to run `git pull`.

## Tasks

The `npm.packageManager` setting can now be set to `bun` to enable detection and running of [Bun](https://bun.sh) scripts defined in `package.json`.

## Debug

### JavaScript Debugger

#### Visualize heap snapshots

V8 heap snapshots, saved as `.heapsnapshot`, can now be visualized in VS Code. There is both a traditional tabular view as well as a graphical representation of the retainers of a given memory object.

![Graphical view of a heap snapshot showing references to a specific memory object](images/1_85/heap-snapshot.png)

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

Heap snapshots can be captured using the **Take Performance Profile** command while debugging any JavaScript code. They can also be captured via the **Memory** tab in browser DevTools.

#### Improved Event Listener Breakpoints view

The **Event Listener Breakpoints** view, shown while debugging the Microsoft Edge or Google Chrome browsers, has been improved. It's now a checkbox list, and supports pausing on XHR/fetch requests based on the URL.

![Event Listener Breakpoints view with "XHR/fetch URL" checked and "Add new URL" option highlighted](images/1_85/event-listener-bps.png)

#### WebAssembly debugging with Rust

Both VS Code and `wasm-bindgen` made changes that allow Rust compiled to WebAssembly to be debugged in VS Code. See our documentation on [WebAssembly debugging](https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_debugging-webassembly) for more information.

## Testing

The **Find** control is now supported in the **Test Results** view terminal.

## Languages

### TypeScript 5.3

This release includes [TypeScript 5.3](https://devblogs.microsoft.com/typescript/announcing-typescript-5-3/). This major update adds support for import attributes, better type narrowing, and more. It also includes new language tooling features and bug fixes.

You can read more about TypeScript 5.3 in the [TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-5-3).

### node_module symbols excluded from workspace symbol search

[Go to Symbol in Workspace](https://code.visualstudio.com/docs/getstarted/tips-and-tricks#_go-to-symbol-in-workspace) now excludes symbols from `node_modules` by default. This makes it easier to find symbols in your code without getting overwhelmed by symbols from installed packages.

You can revert to the previous behavior that included all symbols in the project by setting `"typescript.workspaceSymbols.excludeLibrarySymbols": false`.

### Jump to definition for inlay hints

Types in JavaScript and TypeScript [inlay hints](https://code.visualstudio.com/docs/editing/editingevolved#_inlay-hints) are now interactive. Quickly jump to a type's definition by hovering over the type and clicking while holding `kbstyle(Ctrl)` on Windows and Linux or `kbstyle(Cmd)` on macOS:

![Inlay hint hover showing Go to Definition by using Ctrl/Cmd + click](images/1_85/ts-inlay.png)

### Prefer using 'type' for auto imports

[Type-only imports](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-8.html#type-only-imports-and-export) let you import a type while ensuring the import gets fully erased at runtime. If you prefer to always default to `type` imports, you can now set `"typescript.preferences.preferTypeOnlyAutoImports": true` and auto imports will use type-only import. This setting is off by default.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* Automatically install the [GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot) and [Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extensions in Dev Containers.
* Finer control over which extensions are installed in Dev Containers.
* Reuse local machine certificates in local Dev Containers and WSL sessions.

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_85.md).

## Contributions to extensions

### GitHub Copilot

#### Inline chat UI improvements

The inline chat prompt history is now persisted across VS Code sessions. The keyboard shortcuts to see the previous and next prompts were also changed to be `kbstyle(Up)` and `kbstyle(Down)` to align with other previous and next item shortcuts in VS Code.

When inline chat proposes creating new files, such as when using the `/test` slash command, you can now choose the file name and location by selecting **Create As** from the **Create** drop down.

![Create and Create As actions for newly suggested files from inline chat](images/1_85/inline-chat-create.png)

Last, there is a new **experimental** lightweight UI mode for inline chat. It provides a smoother streaming experience and renders diffs only on demand. You can enable it via the `"inlineChat.mode": "live3"` setting.

![Inline chat lightweight mode with diff on demand](images/1_85/inline-chat-live3.png)

#### Inline chat for fixes shows summary and follow-up action

When using inline chat for fixes (`/fix`), you now also get a short summary what the fix does. If there are remaining errors, you can refine the fix by clicking on the offered follow-up action.

![Inline chat with summary](images/1_85/inline-chat-message.png)

#### Progressive message rendering in inline chat

Inline chat now uses the same progressive rendering as the Chat view:

![Inline chat progressive rendering](images/1_85/inline-chat-word-by-word.gif)

#### Terminal generate commit message Quick Fix

Last release, a Copilot [Generate Commit Message "sparkle"](https://code.visualstudio.com/updates/v1_84#_commit-message-generation) was added to the Source Control view input box. For terminal users, there is now a Quick Fix to generate an editable commit message in the terminal after running the `git add...` command.

#### Terminal agent and command suggestion improvements

All terminal related features have moved to the `@terminal` agent. This was done to make it clear that the terminal agent might not pull in workspace information, to consolidate the functionality, and make it more convenient to use.

Here are the mappings from the old to new prompts:

| Old | New
|---|---
| `@workspace /terminal how do I list files?` | `@terminal how do I list files?`
| `@workspace /explain #terminalSelection` | `@terminal #terminalSelection`
| `@workspace /explain #terminalLastCommand` | `@terminal #terminalLastCommand`

In addition, command suggestions saw significant improvements this release. Terminal command suggestions now know about the operating system and the shell used. Workspace information is also conditionally pulled in, based on whether Copilot thinks the question is related to the workspace. The workspace context collection should see further improvements, both in terms of speed and what exactly gets referenced.

![Progress is displayed while fetching workspace details](images/1_85/copilot-terminal-context.png)

Notice in this example how the `@terminal` agent knows how file paths are formed when used in the `microsoft/vscode` repository:

![The terminal agent is capable of answering questions that need knowledge about file naming standards in the repository](images/1_85/copilot-terminal-suggest.png)

There are also convenient follow-ups to explain suggested commands, which are presented as blue sparkle links just above the chat input box:

![The blue sparkle link just before the input box explains the suggestion](images/1_85/copilot-terminal-explain-link.png)

Activating the **Explain** follow-up gives a detailed explanation of the suggested command:

![The detailed explanation typically explains the command and each argument](images/1_85/copilot-terminal-explanation.png)

#### Authentication upgrade dialog when using GitHub remote search capabilities on private repos

If enabled for your user account, when you use the `@workspace` agent, Copilot Chat searches your workspace using remote search capabilities. In order to use remote search with private repositories, an authentication token with more permission is required. If there isn't a token with the needed permissions already, you are prompted for additional permission:

![Authentication dialog when searching the workspace](images/1_85/auth-dialog.png)

You'll only see this dialog one time and future queries to the `@workspace` agent will use the cached token.

#### Send a request to @workspace more easily

Since the `@workspace` agent is used in many Copilot queries, we wanted to make sure it's as easy as possible to activate. You can now type a question in the chat input, then press `kb(workbench.action.chat.submitSecondaryAgent)` to send the question and automatically prepend `@workspace`.

#### Explain Rust code with Copilot

Copilot Chat now collects cross-file context from your codebase when you ask it to explain Rust code and have a Rust language service extension installed such as [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer). To view a Copilot explanation, select some code in your active editor, then use the **Copilot > Explain This** from the context menu or `/explain` from inline chat or the Chat view.

![Copilot > Explain This action in the editor context menu](images/1_85/copilot-explain-rust.png)

#### Potential vulnerability detection in code blocks

We want to make sure that you are aware of any possible issues with Copilot generated source code, so we are now running code in Chat view codeblocks through a code vulnerability detection model and flagging any detected issues. You might not see this feature at first, but we will be gradually rolling it out to Copilot Chat users, and also tuning the types of vulnerabilities that are detected.

When a codeblock is determined to contain a possible vulnerability, it will be annotated at the bottom of the codeblock. The vulnerability detection model is one we're piloting, so be sure to make your best determination when reviewing Copilot's suggestions and any potential vulnerabilities.

![Copilot code vulnerability warning displayed in the Chat view](images/1_85/copilot-code-vulnerabilities.png)

#### Copilot videos and livestream sessions

Don't miss the recent [VS Code Copilot](https://www.youtube.com/playlist?list=PLj6YeMhvp2S5_hvBl2SE-7YCHYlLQ0bPt) videos on YouTube. Learn about the [latest Copilot Chat features](https://www.youtube.com/watch?v=Dlt-DCLHnxM) and [how Copilot "just got a whole lot smarter"](https://www.youtube.com/watch?v=SZVCJRUADc4).

And if you haven't been tuning in to the [VS Code livestreams](https://code.visualstudio.com/livestream), you'll want to watch the Copilot demos featured in the [1.84 release party](https://www.youtube.com/watch?v=i63DjsjdR3s).

### Python

#### Show Type Hierarchy with Pylance

You can now more conveniently explore and navigate through your Python projects' types relationships when using [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance). This can be helpful when working with large codebases with complex type relationships.

When you right-click on a symbol, you can select **Show Type Hierarchy** to open the type hierarchy view. From there you can navigate through the symbols' subtypes as well as supertypes.

<video src="images/1_85/show-type-hierarchy-pylance.mp4" autoplay loop controls muted title="Show Type Hierarchy with Pylance"></video>

_Theme: [Catppuccin Macchiato](https://marketplace.visualstudio.com/items?itemName=Catppuccin.catppuccin-vsc) (preview on [vscode.dev](https://vscode.dev/editor/theme/Catppuccin.catppuccin-vsc/Catppuccin%20Macchiato))_

#### Configurable debugging option under the Run button menu

The [Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) extension now has a configurable debug option under the **Run** button menu. When you select **Python Debugger: Debug using launch.json** and there is an existing `launch.json` in your workspace, it shows all the available debug configurations that you can pick to start the debugger. If there aren't any configurations, you are prompted to select a debug configuration template to use to create a `launch.json` file for your Python application.

![Python Debugger: Debug using launch.json option under the Run button menu](images/1_85/python-debug-with-config-button.png)

#### Deactivate command supported when activated using environment variables

The [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) extension has a new activation mechanism that activates the selected environment in your default terminal without running any explicit activation commands. This is currently behind an experimental flag and can be enabled through the following **User** setting: `"python.experiments.optInto": ["pythonTerminalEnvVarActivation"]`.

However, one initial drawback with this activation mechanism is that it didn't support the `deactivate` command. We received feedback that this is an important part of some users' workflow, so we have added support for `deactivate` when the selected default terminal is PowerShell or Command Prompt. We have plans to add support for additional terminals in the future.

#### Warning message and setting for REPL Smart Send

When attempting to use [Smart Send](https://code.visualstudio.com/updates/v1_84#_python) via `kbstyle(Shift+Enter)` on a Python file that contains invalid or deprecated code, there is now a warning message and an option to deactivate REPL Smart Send. Users can change their user and workspace specific behavior for REPL Smart Send via the **Python.REPL: Enable REPLSmart Send** (`python.REPL.enableREPLSmartSend`) setting.

![Settings editor entry for Python REPL Smart Send](images/1_85/enable-repl-smart-send.png)

#### Testing architecture rewrite

The Python [test adapter rewrite experiment](https://devblogs.microsoft.com/python/python-in-visual-studio-code-june-2023-release/#test-discovery-and-execution-rewrite) has been rolled out to 100% of users. Currently, you can opt out by adding `"python.experiments.optOutFrom" : "pythonTestAdapter"` in your `settings.json`, but we will soon drop this experimental flag and adopt this new architecture.

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

* Merge queues are now supported in the PR description and **Create** view.
* A new setting `"githubPullRequests.allowFetch": false` prevents `fetch` from being run.
* Submodule support was improved.

Review the [changelog for the 0.78.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0780) release of the extension to learn about the other highlights.

## Preview Features

### Sticky Scroll in trees

Building on the success of Sticky Scroll in the editor, we've extended this feature to all tree views, enabling users to more easily navigate project trees. Sticky Scroll for trees can be enabled by setting `workbench.tree.enableStickyScroll: true`. To ensure Sticky Scroll does not take too much space, it can only take up to 40% of the view height. Additionally, users can customize the maximum number of sticky elements by configuring `workbench.tree.stickyScrollMaxItemCount`, which is set to 7 by default.

<video src="images/1_85/sticky-scroll-file-explorer.mp4" autoplay loop controls muted title="Sticky Scroll in the File Explorer"></video>

For an improved tree navigation experience, you can select a sticky element to jump directly to it within the tree, or press the chevron of a parent element to hide all its child elements. Additionally, accessing checkboxes and action items is easier when Sticky Scroll is enabled.

<video src="images/1_85/sticky-scroll-extension-trees.mp4" autoplay loop controls muted title="Sticky Scroll in the GitHub Pull Requests and Issues extension Pull Request tree view"></video>

### Multi-file diff editor

This release ships a preview of the **multi diff editor**. The multi diff editor lets you view changes in multiple files in one scrollable view:

<video src="images/1_85/multiFileDiffEditor.mp4" autoplay loop controls muted title="Multiple file diff view"></video>

To enable the multi diff editor, set `"multiDiffEditor.experimental.enabled": true`. Currently, the multi diff editor can be used to review local changes, staged changes, incoming/outgoing changes, and changes from pull requests. Note that the multi diff editor is not yet feature complete and might not work in all scenarios.

### Alternate character filtering for Korean

For various features across the workbench that use filtering, VS Code will now also search the QWERTY keyboard equivalent as it's common to accidentally forget to switch the language Input Method Editor (IME). This works similar to search engines but in real time. For example, `debug` when typed in a Korean IME is `ㅇ듀ㅕㅎ`, which is nonsensical:

![Searching for a "ㅇ듀ㅕㅎ" command will now present results for "debug"](images/1_85/korean-filtering.png)

Currently this does not work when filtering from the middle of the word.

### Hide Problem decorations

There is a new setting to hide Problem decorations in the editor and throughout the workbench (excluding the Problems view). The setting **Problems: Visibility** (`problems.visibility`) is enabled by default to show all problems.

Some Problems UI settings are disabled when **Problems: Visibility** is off:

* **Outline > Problems: Badges** (`outline.problems.badges`)
* **Outline > Problems: Colors** (`outline.problems.colors`)
* **Outline > Problems: Enabled** (`outline.problems.enabled`)
* **Problems > Decorations: Enabled** (`problems.decorations.enabled`)

A warning is shown in the Status Bar when **Problems: Visibility** is off.

![Problems: Visibility off Status Bar item and hover](images/1_85/problems-visibility-off.png)

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [@vscode/dts](https://github.com/microsoft/vscode-dts) and run `npx @vscode/dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Test coverage

This iteration we revived work on test coverage, with initial UI integration and some minor updates to the longstanding proposal. While the API is too lengthy to include here, we believe it to be fairly straightforward, and would welcome your input on the proposal in [issue #123713](https://github.com/microsoft/vscode/issues/123713).

### Chat Agents

As mentioned in our recent blog post, [Pursuit of "wicked smartness" in VS Code](https://code.visualstudio.com/blogs/2023/11/13/vscode-copilot-smarter#_extensibility), we are developing a model for extensions to contribute chat agents to the Copilot Chat view. The chat agent API is proposed, but you can experiment with adding your own chat agent now. Subscribe to [issue #199908](https://github.com/microsoft/vscode/issues/199908) for updates.

### Multi document highlighting API

As introduced in the previous release, there is now support for multi document highlighting within VS Code. This iteration, we added a [proposed MultiDocumentHighlightProvider API](https://github.com/microsoft/vscode/blob/main/src/vscode-dts/vscode.proposed.multiDocumentHighlightProvider.d.ts) to register multi document highlight providers. This adds the ability to provide semantic occurrence highlighting for specific programming languages. Providers return a new `MultiDocumentHighlight` structure with a Map of `URI` to `DocumentHighlight`. Feedback and further updates can be tracked via [issue #196354](https://github.com/microsoft/vscode/issues/196354).

## Engineering

### New CDN

We're rolling out the deployment to a new CDN endpoint: `vscode.download.prss.microsoft.com`. For system administrators, make sure to configure network rules to allow traffic from this endpoint.

### macOS 10.13 and 10.14 support has ended

VS Code `1.85` is the last release that supports macOS 10.13 (macOS High Sierra) and 10.14 (macOS Mojave). Refer to our [FAQ](https://code.visualstudio.com/docs/supporting/faq#_can-i-run-vs-code-on-old-macos-versions) for additional information.

## Notable fixes

* [195796](https://github.com/microsoft/vscode/issues/195796) Searching for text after localization is not supported in the Settings editor
* [197319](https://github.com/microsoft/vscode/issues/197319) vscode://file// links no longer working
* [194094](https://github.com/microsoft/vscode/issues/194094) Do not dismiss Profiles Icon picker when moving mouse outside of the picker
* [197070](https://github.com/microsoft/vscode/pull/197070) Support positioning debug toolbar on custom title bar

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@starball5 (starball)](https://github.com/starball5)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart): Simplify `'solid' || ''` in viewPaneContainer.ts [PR #198515](https://github.com/microsoft/vscode/pull/198515)
* [@abhijit-chikane (Abhijit Chikane)](https://github.com/abhijit-chikane): treeStickyScroll change default value to boolean [PR #198849](https://github.com/microsoft/vscode/pull/198849)
* [@amaust (Andrew Maust)](https://github.com/amaust): Adds "verified domain" identifier to url tooltip [PR #197037](https://github.com/microsoft/vscode/pull/197037)
* [@andrewbranch (Andrew Branch)](https://github.com/andrewbranch)
  * Add setting for preferring type-only imports [PR #196123](https://github.com/microsoft/vscode/pull/196123)
  * [typescript-language-features] Add missing preference description for preferTypeOnlyAutoImports [PR #197403](https://github.com/microsoft/vscode/pull/197403)
* [@arvid220u (Arvid Lunnemark)](https://github.com/arvid220u)
  * fix blank settings page [PR #198261](https://github.com/microsoft/vscode/pull/198261)
  * Update condition names to allow node [PR #198274](https://github.com/microsoft/vscode/pull/198274)
* [@cobey (Cody Beyer)](https://github.com/cobey)
  * Add missing py azure packages [PR #195508](https://github.com/microsoft/vscode/pull/195508)
  * Cobey add missing js [PR #197600](https://github.com/microsoft/vscode/pull/197600)
* [@d-mahard (Dipta Mahardhika)](https://github.com/d-mahard): chore: rename color var for comment input box [PR #197950](https://github.com/microsoft/vscode/pull/197950)
* [@elseifthen](https://github.com/elseifthen): Display line numbers in front of search results (#190742) [PR #195452](https://github.com/microsoft/vscode/pull/195452)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Add 'Lock Group' button on aux windows (#182265) [PR #182294](https://github.com/microsoft/vscode/pull/182294)
  * Support condition and hit count on data breakpoints (fix #188721) [PR #195710](https://github.com/microsoft/vscode/pull/195710)
  * Remove redundant task system message and early return affecting FSPs (fix #192490) [PR #196247](https://github.com/microsoft/vscode/pull/196247)
  * Avoid empty or ambiguous repository label in Repositories submenu (fix #196613) [PR #196623](https://github.com/microsoft/vscode/pull/196623)
  * Add `typescript.implementationsCodeLens.showOnInterfaceMethods` setting (#136282) [PR #198419](https://github.com/microsoft/vscode/pull/198419)
  * Remove obsolete migration code for Timeline setting [PR #198542](https://github.com/microsoft/vscode/pull/198542)
  * Add 'Collapse All Diffs' action button to multi-diff editor [PR #199064](https://github.com/microsoft/vscode/pull/199064)
  * Multi-diff editor: add Expand All Diffs action [PR #199623](https://github.com/microsoft/vscode/pull/199623)
* [@gtritchie (Gary Ritchie)](https://github.com/gtritchie): aria-hide search icon [PR #197577](https://github.com/microsoft/vscode/pull/197577)
* [@hamirmahal (Hamir Mahal)](https://github.com/hamirmahal): feat: allow keyboard shortcut creation for terminal copy commands [PR #197099](https://github.com/microsoft/vscode/pull/197099)
* [@hsfzxjy (Xie Jingyi)](https://github.com/hsfzxjy)
  * @installed matches extension description [PR #196602](https://github.com/microsoft/vscode/pull/196602)
  * Set cursor when clicking "Show Previous/Next Change" [PR #197501](https://github.com/microsoft/vscode/pull/197501)
  * Add inlineSuggest.showToolbar.never [PR #198227](https://github.com/microsoft/vscode/pull/198227)
* [@idootop (Del)](https://github.com/idootop): feat: introducing new hover focus options for editor.action.showHover [PR #196891](https://github.com/microsoft/vscode/pull/196891)
* [@jsoref (Josh Soref)](https://github.com/jsoref): Write out `Cannot` [PR #198377](https://github.com/microsoft/vscode/pull/198377)
* [@jtbandes (Jacob Bandes-Storch)](https://github.com/jtbandes): Update Swift grammar and upstream repository [PR #197470](https://github.com/microsoft/vscode/pull/197470)
* [@marrej (Marcus Revaj)](https://github.com/marrej): # Fix Suggest dropdown/inline completion partial accept (via next token/line) race [PR #197633](https://github.com/microsoft/vscode/pull/197633)
* [@marvinruder (Marvin A. Ruder)](https://github.com/marvinruder): Add Bun as package manager to `npm` extension [PR #198005](https://github.com/microsoft/vscode/pull/198005)
* [@mrgharabaghi (Mohammad Reza Gharabaghi)](https://github.com/mrgharabaghi): Update theme-defaults [PR #197449](https://github.com/microsoft/vscode/pull/197449)
* [@myty (Michael Tyson)](https://github.com/myty): Use Extension Provided Terminal Profile from Context Menu [PR #195108](https://github.com/microsoft/vscode/pull/195108)
* [@n-gist (n-gist)](https://github.com/n-gist): Add pinned tab button (icon) control setting [PR #196896](https://github.com/microsoft/vscode/pull/196896)
* [@nolddor (Jack Nolddor)](https://github.com/nolddor): fix: missing translation for new-empty-windows desktop action [PR #199129](https://github.com/microsoft/vscode/pull/199129)
* [@noritada (Noritada Kobayashi)](https://github.com/noritada): Fix an issue that \xN8 and \xN9 in Rust strings are incorrectly colored [PR #196198](https://github.com/microsoft/vscode/pull/196198)
* [@oxcened (Alen Ajam)](https://github.com/oxcened): fix: do not hide hover on model content change of editor [PR #198100](https://github.com/microsoft/vscode/pull/198100)
* [@PrathamLalwani](https://github.com/PrathamLalwani): added voice chat listening duration feature [PR #197801](https://github.com/microsoft/vscode/pull/197801)
* [@r3m0t (Tomer Chachamu)](https://github.com/r3m0t): [Acc] Keyboard accessible tooltips- Fixes #132344 [PR #197965](https://github.com/microsoft/vscode/pull/197965)
* [@rehmsen (Ole)](https://github.com/rehmsen)
  * Layout when switching from welcome to terminal. [PR #173368](https://github.com/microsoft/vscode/pull/173368)
  * Remove cycle  browserHostService.ts -> web.api.ts. [PR #198221](https://github.com/microsoft/vscode/pull/198221)
  * Make xtermTerminal.test.ts hermetic. [PR #198403](https://github.com/microsoft/vscode/pull/198403)
* [@remcohaszing (Remco Haszing)](https://github.com/remcohaszing): Increase the target of Monaco from es6 to es2018 [PR #192050](https://github.com/microsoft/vscode/pull/192050)
* [@ronakj (Ronak Jain)](https://github.com/ronakj): Fix tsconfig resolution for navigation [PR #192851](https://github.com/microsoft/vscode/pull/192851)
* [@scripthunter7 (David)](https://github.com/scripthunter7): Add TMLanguage aliases to YAML [PR #198300](https://github.com/microsoft/vscode/pull/198300)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke)
  * feature: allow to paste files from the clipboard [PR #195730](https://github.com/microsoft/vscode/pull/195730)
  * fix: memory leak in dropdown action [PR #197769](https://github.com/microsoft/vscode/pull/197769)
  * fix: memory leak in comments controller [PR #198237](https://github.com/microsoft/vscode/pull/198237)
* [@tisilent (xiejialong)](https://github.com/tisilent)
  * Add mousedown,contextmenu events to terminal find [PR #194817](https://github.com/microsoft/vscode/pull/194817)
  * Delete hide assignment in _adoptConfiguration* [PR #197526](https://github.com/microsoft/vscode/pull/197526)
* [@WardenGnaw (Andrew Wang)](https://github.com/WardenGnaw)
  * Show Dynamic Configuration Providers with No Context (file opened) [PR #196768](https://github.com/microsoft/vscode/pull/196768)
  * Add support for running DebugConfigurations with serverReadyAction [PR #197597](https://github.com/microsoft/vscode/pull/197597)
* [@zobo (Damjan Cvetko)](https://github.com/zobo): fix: invalid endCharacter value in built in PHP validation provider [PR #196166](https://github.com/microsoft/vscode/pull/196166)

Contributions to `vscode-css-languageservice`:

* [@dyhagho (Dyhagho Briceño)](https://github.com/dyhagho): [scss] Path resolver to include partial files support [PR #373](https://github.com/microsoft/vscode-css-languageservice/pull/373)

Contributions to `vscode-pull-request-github`:

* [@flpcury (Felipe Cury)](https://github.com/flpcury): Fix deprecation messages for createDraft and setAutoMerge [PR #5429](https://github.com/microsoft/vscode-pull-request-github/pull/5429)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Treat `githubIssues.useBranchForIssues` setting description as markdown (fix #5506) [PR #5508](https://github.com/microsoft/vscode-pull-request-github/pull/5508)
* [@kurowski (Brandt Kurowski)](https://github.com/kurowski): add setting to never offer ignoring default branch pr [PR #5435](https://github.com/microsoft/vscode-pull-request-github/pull/5435)
* [@ThomsonTan (Tom Tan)](https://github.com/ThomsonTan): Iterate the diffs in each active PR in order [PR #5437](https://github.com/microsoft/vscode-pull-request-github/pull/5437)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
