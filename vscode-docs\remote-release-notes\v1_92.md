# July 2024 (version 1.92)

## Dev Containers

### GPG keyboxd support

The GPG forwarding for Dev Containers now supports the use of GPG's new `keyboxd` service for public keys by the local machine, the container, or both.

## SSH

### Local port range configuration

The usable range of local ports is now configurable with the <a href="vscode://settings/remote.SSH.preferredLocalPortRange">`remote.SSH.preferredLocalPortRange`</a> setting.  This might be useful in cases where a range of local ports is reserved on a machine but not yet bound.

Each port is tested sequentially until an available one is found. If no local ports in the range are available, the connection will terminate.

### Improved Windows ARM support

Fixes a bug that prevented connections to remote ARM-based Windows machines.

### Permit PTY allocation

Users with remote connection issues now have the ability to omit the '-T' flag from the generated `ssh` connection command.  This feature is enabled with the <a href="vscode://settings/remote.SSH.permitPtyAllocation" codesetting="true">`remote.SSH.permitPtyAllocation`</a> setting.

This setting is a reported workaround for various issues.  For more information, see [the original issue](https://github.com/microsoft/vscode-remote-release/issues/7558).

### Ignore `curlrc` and `wgetrc` files by default

Execution of `curl` and `wget` within the extension's bootstrapping script now ignores the default configuration files on the remote, providing greater consistency and reliability.

If you require the previous behavior (for example, for proxy configuration), enable the <a href="vscode://settings/remote.SSH.useCurlAndWgetConfigurationFiles" codesetting="true">`remote.SSH.useCurlAndWgetConfigurationFiles`</a> setting.
