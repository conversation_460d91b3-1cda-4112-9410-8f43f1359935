---
Order: 
TOCTitle: April 2019
PageTitle: Visual Studio Code April 2019
MetaDescription: See what is new in the Visual Studio Code April 2019 Release (1.34)
MetaSocialImage: 1_34/release-highlights.png
Date: 2019-05-15
DownloadVersion: 1.34.0
---
# April 2019 (version 1.34)

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

Welcome to the April 2019 release of Visual Studio Code. During April, we were busy with the Preview release of the **Remote Development** extensions. These extensions let you work with VS Code over SSH on a remote machine or VM, in Windows Subsystem for Linux (WSL), or inside a Docker container. You can read the [Remote Development with Visual Studio Code](https://code.visualstudio.com/blogs/2019/05/02/remote-development) blog post to learn more.

There were still several updates in this version that we hope you will like as well as many community contributions.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

## Editor

### Stable CodeLens

VS Code now optimistically caches CodeLens locations and restores them immediately after switching editors. This fixes an issue where CodeLens lines would shift slightly when switching between editors.

![Stable CodeLens](images/1_34/stable-code-lens.gif)

## Languages

### Lower rank for CSS property values with vendor prefix

CSS property values prefixed with dash `'-'` (such as `-moz-grid` or `-ms-grid`), now come after `grid` in auto completion.

## Debugging

### debug.showSubSessionsInToolBar

We have introduced the setting `debug.showSubSessionsInToolBar` which controls whether the debug sub-sessions are shown in the debug tool bar. When this setting is false, the stop command on a sub-session will also stop the parent session. This setting default is false.

## Tasks

### Terminate all tasks

The **Tasks: Terminate Task** command has a new option to terminate all tasks if there are multiple tasks running. If this is an action you do often, you can create a keyboard shortcut for the command with the `terminateAll` argument.

```json
{
  "key": "ctrl+k t",
  "command": "workbench.action.tasks.terminate",
  "args": "terminateAll"
}
```

### Automatically show Problems panel

The new `revealProblems` task property allows you to automatically show the Problems panel. The property values are `always`, `never`, and `onProblem`.

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "type": "npm",
      "script": "watch",
      "problemMatcher": "$tsc-watch",
      "isBackground": true,
      "presentation": {
        "reveal": "always",
        "revealProblems": "onProblem"
      }
    }
  ]
}
```

## Contributions to extensions

### Preview: Remote Development

❗ **Note:** The **[Remote Development extensions](https://aka.ms/vscode-remote/download)** require **[Visual Studio Code Insiders](https://code.visualstudio.com/insiders)**.

**Visual Studio Code Remote Development** allows you to use a container, remote machine, or the [Windows Subsystem for Linux (WSL)](https://learn.microsoft.com/windows/wsl) as a full-featured development environment.

The extensions in the [Remote Development extension pack](https://aka.ms/vscode-remote/download/extension) run in the context of the remote workspace while VS Code feels like it does when you run locally.

### Vetur

The [Vetur](https://github.com/vuejs/vetur) extension now offers semantic diagnostics, hover information, jump to definition, and find references for the JavaScript expression inside Vue's template interpolation region:

![Vetur JavaScript support in interpolations](images/1_34/vetur-interpolation.gif)

You can read more about this feature in the [Vetur documentation](https://vuejs.github.io/vetur/interpolation.html).

Other improvements include reduced memory usage and import path completion. You can learn more about them in the [Vetur changelog](https://github.com/vuejs/vetur/blob/master/CHANGELOG.md).

## Extension authoring

### Multi-extension debugging

In this milestone, we've added support for debugging more than one extension at a time. This is useful if you are developing a set of tightly coupled or interdependent extensions.

Previously this feature was only surfaced via the command line interface of VS Code, now the argument `--extensionDevelopmentPath` can be specified more than once. Typically `--extensionDevelopmentPath` is used in the launch configuration of an extension. When developing more than one extension, we recommend combining the individual extension projects into a multi-folder workspace and create a new launch configuration (stored in the workspace `.code-workspace` file) that uses multiple `--extensionDevelopmentPath` arguments for the individual extensions.

Here is an example workspace `.code-workspace` file with two extensions, `hello1` and `hello2`, and a single launch configuration for both extensions:

```json
{
  "folders": [
    { "path": "hello1" },
    { "path": "hello2" }
  ],
  "launch": {
    "configurations": [
      {
        "type": "extensionHost",
        "request": "launch",
        "name": "Launch Two Extensions",
        "args": [
          "--extensionDevelopmentPath=${workspaceFolder:hello1}",
          "--extensionDevelopmentPath=${workspaceFolder:hello2}",
        ],
        "outFiles": [
          "${workspaceFolder:hello1}/out/**/*.js",
          "${workspaceFolder:hello2}/out/**/*.js"
        ]
      }
    ]
  }
}
```

Please note that in this release, it is not possible to create a `preLaunchTask` that builds both extensions by combining the `preLaunchTask`s of the individual extension projects.

### Command links in notifications

It was always possible to include links in notification messages via the syntax `[link name](http://link)`, but this only worked for links that open in a browser. Now you can also invoke a command from a link with the syntax `[link name](command:<command id>)`. The command with the provided identifier will be triggered when the user clicks the link.

### Links in notifications allow for title

You can now add a title to links in notifications that will be shown when the user hovers over the link. The syntax is `[link name](link "<the title>")`.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project.

Note that you cannot publish an extension that uses a proposed API. We may likely make breaking changes in the next release and we never want to break existing extensions.

### vscode.workspace.workspaceFile

There is a new workspace property returning the location of the workspace file. For example: `file:///Users/<USER>/Development/myProject.code-workspace` or `untitled:1555503116870` for a workspace that is untitled and not yet saved.

Depending on the workspace that is opened, the value will be:

* `undefined` when no workspace or a single folder is opened.
* The path of the workspace file as `Uri`.

If the workspace is untitled, the returned URI will use the `untitled:` scheme.

One use of the workspace file location is to call the `vscode.openFolder` command to open the workspace again after it has been closed:

```typescript
  vscode.commands.executeCommand('vscode.openFolder', uriOfWorkspace);
```

**Note:** It is not recommended using the `workspace.workspaceFile` location to write configuration data directly into the file. You can use `workspace.getConfiguration().update()` which will work both when a single folder is opened as well as an untitled or saved workspace.

### Machine-specific settings

If you have settings that allow users to customize an executable path and if these paths need to be scoped to the machine they are running on, you can now classify such settings as `machine` scoped. Extension authors set the `scope` property when contributing to the `configuration` extension point. Machine-specific settings can only be configured for User settings.

```json
"configuration": {
  "title": "Git",
  "properties": {
   "git.path": {
      "type": [
        "string",
        "null"
      ],
      "markdownDescription": "Path and filename of the git executable.",
      "default": null,
      "scope": "machine"
    }
  }
}
```

## Engineering

### Rewritten filesystem provider for local files

Extensions have been able to provide their own filesystem implementations for custom resources ([read more here](https://code.visualstudio.com/updates/v1_23#_filesystem-providers)). However, VS Code's own implementation for local files was not implemented with the same extension APIs. This resulted in subtle differences when dealing with local file resources compared to resources coming from extensions. Over the last two milestones, we [rewrote our local file system provider](https://github.com/microsoft/vscode/blob/main/src/vs/workbench/services/files/node/diskFileSystemProvider.ts) to use the extension APIs for consistency.

## New documentation

### Python Azure Functions

There is a new [Deploy Python to Azure Functions](https://code.visualstudio.com/docs/python/tutorial-azure-functions) tutorial that describes how to create and deploy Python serverless Azure Functions.

## Miscellaneous

### Language Server Protocol

The [Language Server Protocol](https://microsoft.github.io/language-server-protocol) has proposed support for the following new features:

* Selection ranges: to compute selection ranges for an array of positions. Sent from client to server.
* Call hierarchy: to compute a call hierarchy for a given symbol. Sent from client to server.
* Progress: to initiate progress reporting from the server. Sent from server to client.

The new features are available in the next versions of the `vscode-languageclient` and `vscode-languageserver` npm modules.

## Notable fixes

* [48259](https://github.com/microsoft/vscode/issues/48259): Explorer Respects Trash Capability from FileSystemProvider
* [68276](https://github.com/microsoft/vscode/issues/68276): Call Stack "Paused On Breakpoint" UI not visible for long thread names
* [69603](https://github.com/microsoft/vscode/issues/69603): Terminal window crashes in Mac OS
* [72110](https://github.com/microsoft/vscode/issues/72110): Debug Console render newlines unnecessary
* [71737](https://github.com/microsoft/vscode/issues/71737): Scrolling in debug window is behaving strange
* [71588](https://github.com/microsoft/vscode/issues/71588): Error revealing files in the explorer
* [70492](https://github.com/microsoft/vscode/issues/70492): ‘Report issue’ button on ‘Running extensions’ causes tens of duplicate issues

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to our issue tracking. Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [Muhammed Emin TİFTİKÇİ (@jaqra)](https://github.com/jaqra)
* [Carl Morris (@msftrncs)](https://github.com/msftrncs)

Contributions to `vscode`:

* [@alpalla](https://github.com/alpalla): Fixes #27041 [PR #72015](https://github.com/microsoft/vscode/pull/72015)
* [@BaluErtl](https://github.com/BaluErtl): Incorrect wording of Outline drawer default message [PR #73214](https://github.com/microsoft/vscode/pull/73214)
* [@cmchumak](https://github.com/cmchumak): Improved messaging when running a task with no workspace [PR #71476](https://github.com/microsoft/vscode/pull/71476)
* [Connor Shea (@connorshea)](https://github.com/connorshea): Display extensionId as the description in the formatter dropdown [PR #71910](https://github.com/microsoft/vscode/pull/71910)
* [Alexandr Fadeev (@fadeevab)](https://github.com/fadeevab): Makefile test case about the bug fix with "@,-,+" colorizing in the recipe [PR #72265](https://github.com/microsoft/vscode/pull/72265)
* [Nikita Shilnikov (@flash-gordon)](https://github.com/flash-gordon): Fix typo in localization key [PR #72718](https://github.com/microsoft/vscode/pull/72718)
* [Hung-Wei Hung (@hwhung0111)](https://github.com/hwhung0111): Fix typo in code-cli.bat [PR #73091](https://github.com/microsoft/vscode/pull/73091)
* [Ian Sanders (@iansan5653)](https://github.com/iansan5653): Add support for 8- and 24-bit ANSI escape color codes in the debug console [PR #70935](https://github.com/microsoft/vscode/pull/70935)
* [Muhammed Emin TİFTİKÇİ (@jaqra)](https://github.com/jaqra): Added title case transformation command called 'Transform to Title Case'. [PR #70990](https://github.com/microsoft/vscode/pull/70990)
* [Jimi (Dimitris) Charalampidis (@JimiC)](https://github.com/JimiC): [themes] Fix explorer arrows notification on reload [PR #71855](https://github.com/microsoft/vscode/pull/71855)
* [Jon Bockhorst (@jmbockhorst)](https://github.com/jmbockhorst)
  * Fixed command-line args with leading zeros [PR #71581](https://github.com/microsoft/vscode/pull/71581)
  * Fixed explorer modified sort order not working in some cases [PR #72501](https://github.com/microsoft/vscode/pull/72501)
* [Johannes Wikman (@jwikman)](https://github.com/jwikman): FindInFiles converted to command and accepting arguments [PR #71626](https://github.com/microsoft/vscode/pull/71626)
* [Jiaxun Wei (@LeuisKen)](https://github.com/LeuisKen): fix #72187 [PR #72194](https://github.com/microsoft/vscode/pull/72194)
* [Luís Ferreira (@ljmf00)](https://github.com/ljmf00): theme-monokai: fix tabs indentation [PR #72269](https://github.com/microsoft/vscode/pull/72269)
* [paul cheung (@paul-cheung)](https://github.com/paul-cheung): fix typos [PR #72334](https://github.com/microsoft/vscode/pull/72334)
* [Evan Bovie (@phaseOne)](https://github.com/phaseOne): Add `vscode:` URI prefix to knownSchemes in links.ts [PR #71416](https://github.com/microsoft/vscode/pull/71416)
* [Raman (@ramank775)](https://github.com/ramank775): Fix for "Comment box issue #71703" [PR #71880](https://github.com/microsoft/vscode/pull/71880)
* [Samuel Bronson (@SamB)](https://github.com/SamB): json-language-features/CONTRIBUTING.md fixes [PR #73273](https://github.com/microsoft/vscode/pull/73273)
* [@sebastianwitthus](https://github.com/sebastianwitthus): 44146 automatically show problems panel [PR #71387](https://github.com/microsoft/vscode/pull/71387)
* [Simon Siefke (@SimonSiefke)](https://github.com/SimonSiefke): fix typo [PR #71564](https://github.com/microsoft/vscode/pull/71564)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan): fix-71570 Error when restoring a markdown preview for a file that has been deleted [PR #71762](https://github.com/microsoft/vscode/pull/71762)
* [Tony Xia (@tony-xia)](https://github.com/tony-xia)
  * Fix couple typos [PR #71928](https://github.com/microsoft/vscode/pull/71928)
  * Update the function name [PR #71926](https://github.com/microsoft/vscode/pull/71926)
  * Update variable names [PR #72136](https://github.com/microsoft/vscode/pull/72136)
  * Use strict equality in darkenColor() [PR #72211](https://github.com/microsoft/vscode/pull/72211)
  * Fix Typo: langauge to language [PR #72552](https://github.com/microsoft/vscode/pull/72552)
  * Update makeRandomHexString() to remove duplicate character [PR #72553](https://github.com/microsoft/vscode/pull/72553)
  * Update comment [PR #72134](https://github.com/microsoft/vscode/pull/72134)
* [Alexander (@usernamehw)](https://github.com/usernamehw): Add CLI flag to be able to disable smooth scroll in webviews [PR #65422](https://github.com/microsoft/vscode/pull/65422)
* [Vitaliy Mazurenko (@vitaliymaz)](https://github.com/vitaliymaz)
  * re-rendering Markdown preview when updated extra configurations provided by extension [PR #71442](https://github.com/microsoft/vscode/pull/71442)
  * fix link navigation to extended config file in tsconfig [PR #71944](https://github.com/microsoft/vscode/pull/71944)
* [Waleed Ashraf (@WaleedAshraf)](https://github.com/WaleedAshraf): ignore trailing white-spaces in LINE_COLON_PATTERN [PR #71985](https://github.com/microsoft/vscode/pull/71985)
* [William Whittle (@whittlec)](https://github.com/whittlec): Add 'qsh' as a valid shellscript firstline [PR #73231](https://github.com/microsoft/vscode/pull/73231)
* [Keyi Xie (@xiekeyi98)](https://github.com/xiekeyi98): Fix misspell in build/ [PR #71977](https://github.com/microsoft/vscode/pull/71977)
* [XTY (@xtydev)](https://github.com/xtydev): Resolve regression in WSL launch script [PR #72779](https://github.com/microsoft/vscode/pull/72779)

Contributions to `vscode-css-languageservice`:

* [Tony Xia (@tony-xia)](https://github.com/tony-xia): Fixed a minor typo [PR #151](https://github.com/microsoft/vscode-css-languageservice/pull/151)

Contributions to `vscode-html-languageservice`:

* [Liam Newman (@bitwiseman)](https://github.com/bitwiseman): Update js-beautify to 1.9.1 [PR #58](https://github.com/microsoft/vscode-html-languageservice/pull/58)
* [Christian Oliff (@coliff)](https://github.com/coliff): Fix attribute typo [PR #60](https://github.com/microsoft/vscode-html-languageservice/pull/60)

Contributions to `node-jsonc-parser`:

* [Tony Xia (@tony-xia)](https://github.com/tony-xia): JavaScipt -> JavaScript [PR #18](https://github.com/microsoft/node-jsonc-parser/pull/18)

Contributions to `language-server-protocol`:

* [Danny Tuppeny (@DanTup)](https://github.com/DanTup): Add optional reason for why the client failed to apply an edit  [PR #637](https://github.com/microsoft/language-server-protocol/pull/637)
* [Ben Mewburn (@bmewburn)](https://github.com/bmewburn): correct id in lsif textDocument/definition example [PR #725](https://github.com/microsoft/language-server-protocol/pull/725)

Contributions to `vscode-languageserver-node`:

* [Igor Matuszewski (@Xanewok)](https://github.com/Xanewok): Add proposed window/progress extension [PR #261](https://github.com/microsoft/vscode-languageserver-node/pull/261)
* [Aleksey Kladov (@matklad)](https://github.com/matklad): adjust selectionRange API [PR #474](https://github.com/microsoft/vscode-languageserver-node/pull/474)
* [Ben Mewburn (@bmewburn)](https://github.com/bmewburn): onImplementation and onTypeDefinition can return DefinitionLink[] [PR #479](https://github.com/microsoft/vscode-languageserver-node/pull/479)
* [Remy Suen (@rcjsuen)](https://github.com/rcjsuen): Fix a small typo [PR #482](https://github.com/microsoft/vscode-languageserver-node/pull/482)

Contributions to `vscode-eslint`:

* [Mike Martin (@mcmar)](https://github.com/mcmar): Clarify `changeProcessCWD` documentation  [PR #657](https://github.com/microsoft/vscode-eslint/pull/657)
* [Ilias Tsangaris (@ilias-t)](https://github.com/ilias-t): [PR #661](https://github.com/microsoft/vscode-eslint/pull/661)
* [Charles Stover (@CharlesStover)](https://github.com/CharlesStover): Autocomplete for eslint.codeAction.disableRuleComment should insert separateLine instead of newLine [PR #674](https://github.com/microsoft/vscode-eslint/pull/674)

Contributions to `vscode-lsif-extension`:

* [Chris Wendt (@chrismwendt)](https://github.com/chrismwendt): Fix filesystem lookup for top level files [PR #9](https://github.com/microsoft/vscode-lsif-extension/pull/9)

Contributions to `vscode-textmate`:

* [Carl Morris (@msftrncs)](https://github.com/msftrncs)
  * Allow empty or missing begin [PR #86](https://github.com/microsoft/vscode-textmate/pull/86)
  * capture if pushed rules have advanced and reflect when popped [PR #85](https://github.com/microsoft/vscode-textmate/pull/85)

Contributions to `vscode-recipes`:

* [AJ Alves (@zerokol)](https://github.com/zerokol): Debug one specific case in a rspec file [PR #207](https://github.com/microsoft/vscode-recipes/pull/207)

Contributions to `vscode-vsce`:

* [Jimi (Dimitris) Charalampidis (@JimiC)](https://github.com/JimiC): Fix compile task [PR #347](https://github.com/microsoft/vscode-vsce/pull/347)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code.

We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Bosnian:** Ismar Bašanović.
* **Czech:** Daniel Padrta, David Jareš, Jan Hajek, Jiří Hofman.
* **Danish:** Allan Kimmer Jensen, Thomas Larsen.
* **Dutch:** Maxim Van Damme, Sven Klaasen.
* **English (United Kingdom):** Martin Littlecott, Tobias Collier, William Wood, Giorgi Jambazishvili.
* **French:** Antoine Griffard, Thierry DEMAN-BARCELÒ, Mohamed Sahbi, Steven Dugois, Michael VAUDIN.
* **German:** Christof Opresnik.
* **Hindi:** Abhirav Kushwaha, Pramit Das.
* **Chinese Simplified:** paul cheung, 斌 项, Fan Su, Justin Liu, Horie Yuan, 王文杰, 赵畅畅, anson zhang, Wang Debang, Pluwen, Yiting Zhu, Joel Yang, Michael Zhang, 擎 钟, 涛 徐, 少民 谈, 伟 全, panda small, Array Zhang, LI ZHAO.
* **Chinese Traditional:** 謝政廷, 煾 雪.
* **Hungarian:** Levente Hallai Seiler.
* **Indonesian:** Laurensius Dede Suhardiman.
* **Italian:** Alessandro Alpi, Luigi Bruno, Emanuele Meazzo, Marco Dal Pino.
* **Japanese:** EbXpJ6bp -, nh, Kyohei Uchida, Yoshihisa Ozaki, Seiji Momoto, Hasefumi, 迪人 熊本, Koichi Makino, Kyohei Moriyama, 美穂 山本, Aya Tokura.
* **Korean:** 형섭 이, Youngjae Kim, siin lee, Keunyop Lee.
* **Latvian:** Kaspars Bergs.
* **Lithuanian:** Augustas Grikšas.
* **Polish:** Wojciech Maj, Marek Biedrzycki, Igor 05, Marcin Weksznejder, Szymon Seliga, Paweł Modrzejewski, ‎Michał Stojke, Artur Zdanowski.
* **Portuguese (Brazil):** Alessandro Trovato, Judson Santiago, Roberto Fonseca, Marcelo Fernandes, Lucas Miranda, Ray Carneiro, Loiane Groner, Daniel Luna, Lucas Santos, Saymon Damásio.
* **Portuguese(Portugal):** Ana Rebelo, Pedro Teixeira, João Carvalho, Tiago Antunes.
* **Romanian:** Alexandru Staicu.
* **Russian:** Andrey Veselov, Дмитрий Кирьянов, nata kazakova, Валерий Батурин.
* **Spanish:** Andy Gonzalez, Carlos Mendible, José María Aguilar, Alvaro Enrique Ruano.
* **Tamil:** Boopesh Kumar, Karunakaran Samayan, Merbin J Anselm.
* **Turkish:** mehmetcan gun, Sinan Açar, S. Ferit Arslan.
* **Ukrainian:** Sviatoslav Ivaskiv, George Molchanyuk.
* **Vietnamese:** Khôi Phạm, Spepirus Shouru, Việt Anh Nguyễn.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
