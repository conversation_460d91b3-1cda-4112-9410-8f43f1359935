---
Order: 91
TOCTitle: August 2023
PageTitle: Visual Studio Code August 2023
MetaDescription: Learn what is new in the Visual Studio Code August 2023 Release (1.82)
MetaSocialImage: 1_82/release-highlights.png
Date: 2023-9-7
DownloadVersion: 1.82.3
---
# August 2023 (version 1.82)

**Update 1.82.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2023+Recovery+1%22+is%3Aclosed).

**Update 1.82.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2023+Recovery+2%22+is%3Aclosed).

**Update 1.82.3**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2023+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the August 2023 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[Accessibility improvements](#accessibility)** - Accessible View support for inline completions, updated keybindings.
* **[Built-in port forwarding](#workbench)** - Forward local server ports from within VS Code.
* **[Sticky Scroll updates](#sticky-scroll)** - Scrolls horizontally with editor, display last line in scope, folding UI.
* **[New diff editor features](#diff-editor)** - Detect moved code, dynamically switch inline and side-by-side view.
* **[Command Center displayed by default](#command-center-now-on-by-default)** - Quickly open files or run commands from the title bar.
* **[Copy Notebook output](#notebooks)** - Easily copy cell output as well as generated images.
* **[WebAssembly debugging](#javascript-debugger)** - Decompile WASM code to step through and set breakpoints.
* **[New TypeScript refactorings](#typescript-52)** - Move to File and Inline Variables refactorings.
* **[New Python Formatting topic](#python)** - Learn how to integrate formatters such as autopep8 and Black.
* **[Preview: GitHub Copilot](#github-copilot)** - CreateWorkspace command previews file structure of proposed code.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Focused view in window title

The `window.title` setting now has a `${focusedView}` variable that displays the name of the view in the title bar, if a view is currently focused.

### Accessible View for inline completions

Inline completions, like those coming from the [GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot) extension, for example, can now be inspected in the [Accessible View](https://code.visualstudio.com/docs/configure/accessibility/accessibility#_accessible-view).

### Improved navigation consistency across the workbench

Last iteration, we made the experience when navigating between an input control (for example, search or filter input) and its results consistent across components like the Extensions view, the Keyboard Shortcuts editor, and more using `kb(widgetNavigation.focusNext)` and `kb(widgetNavigation.focusPrevious)`. This has been extended to the Settings editor and GitHub Pull Request comment control.

This also works for navigating between the terminal and the terminal accessible buffer.

### Updated terminal accessible buffer keybindings

Previously, the terminal accessible buffer was opened via `kbstyle(Shift+Tab)`. This conflicted with an existing keyboard shortcut in some shells. As such, we've removed that keybinding in favor of `kb(widgetNavigation.focusNext)` and `kb(workbench.action.terminal.focusAccessibleBuffer)`, to align with the other Accessible Views.

### Actions in the Accessible View

Actions in the Accessible View allow screen reader users to go to `next/previous`, disable accessibility verbosity, and more for a given feature. These actions exist within a convenient toolbar on the view so that the current context can be preserved.

### Go to Symbol in the Accessible View

Accessibility help dialogs and some Accessible Views now have a **Go to Symbol** (`kb(editor.action.accessibleViewGoToSymbol)`) action to allow for swifter navigation through content.

### Focus terminal accessible buffer after run

The terminal now has a `terminal.integrated.focusAfterRun` setting so that users can specify if the terminal's accessible buffer (`accessible-buffer`), the terminal itself (`terminal`), or nothing (`none`) should be focused when **Terminal: Run Selected Text In Active Terminal** is invoked.

## Workbench

### Built-in port forwarding

VS Code now has a built-in port forwarding system. This feature allows you to share locally running services over the internet to other people and devices. To use it, select the **Forward a Port** button in the **Ports** view available in the Panel region (**Ports: Focus on Ports View**).

![Forward a Port button displayed in the Ports view](images/1_82/ports-view.png)

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

Read more about port forwarding in the [Port Forwarding](https://code.visualstudio.com/docs/debugtest/port-forwarding) user guide.

### Command Center now on by default

The Command Center was introduced several months ago as a quick way to both discover and interact with VS Code. You can use it as a launch pad for finding a command in the Command Palette, running a task, and other quick experiences. We've been running an experiment displaying the Command Center in the title bar and have gotten positive feedback, so we felt it was time to enable it by default.

Here is a video using the Command Center, and the back and forward buttons as well.

<video src="images/1_82/commandcenter.mp4" autoplay loop controls muted title="Command Center"></video>

We see immense potential for the Command Center to be the center for finding anything in VS Code, so watch for more improvements in the future!

> **Note**: If you would rather not have the Command Center visible, you can right-click on the title bar and uncheck the Command Center entry in the dropdown to hide it.

### Control how pinned editors close from keyboard or mouse

There is a new setting `workbench.editor.preventPinnedEditorClose` for more control on how pinned tabs respond to keyboard shortcuts or mouse middle-click for closing an editor. By default, a pinned tab will not close from keyboard or mouse interactions (default value `keyboardAndMouse`). You can change this setting accordingly:

* `keyboardAndMouse` - A pinned tab will not close from keyboard shortcut or mouse middle-click (default since `1.82.x`).
* `keyboard` - A pinned tab will not close via keyboard shortcut (default until `1.81.x`).
* `mouse` - A pinned tab will not close via mouse middle-click.
* `never` - A pinned tab will always close from keyboard shortcut or mouse middle-click.

### New and updated themable colors for the Status bar

The Status bar already provides many themable colors for its items. There are now more colors to allow for theming hover foreground and background colors:

* `statusBarItem.errorHoverBackground`
* `statusBarItem.errorHoverForeground`
* `statusBarItem.warningHoverBackground`
* `statusBarItem.warningHoverForeground`
* `statusBarItem.remoteHoverBackground`
* `statusBarItem.remoteHoverForeground`
* `statusBarItem.offlineHoverBackground`
* `statusBarItem.offlineHoverForeground`

The following two color names were updated because the color no longer applies to the entire Status bar, but only to the remote indicator:

* `statusBar.offlineBackground` renamed to `statusBarItem.offlineBackground`
* `statusBar.offlineForeground` renamed to `statusBarItem.offlineForeground`

## Editor

### Sticky Scroll

This iteration there have been several improvements to the Sticky Scroll UI, available at the top of the editor (**View: Toggle Sticky Scroll**).

* Now by default Sticky Scroll is scrolled sideways when the editor horizontal scrollbar is scrolled. This feature can be turned off by disabling `editor.stickyScroll.scrollWithEditor`.
* It is possible to view the last line of a scope by holding the `Shift` key and hovering over a Sticky Scroll line. Clicking on a line while holding `Shift` moves the editor cursor to the last line of the scope.
* Folding icons have been added to the Sticky Scroll gutter. The rendering of these icons follows the setting `editor.showFoldingControls` that controls the rendering of the folding icons in the editor gutter.

<video src="images/1_82/sticky-scroll-improvements.mp4" autoplay loop controls muted title="Sticky Scroll improvements"></video>

### Sort JSON on save

It is now possible to sort JSON or JSONC (JSON with comments) files on save. Use the setting `json.sortOnSave.enable` to enable this feature.

### Code Actions and Quick Fix navigation via keyboard

You can now quickly navigate through the Quick Fix, Code Actions, or Source Control menus (they use the "Action" control) by typing any keyword or letter corresponding with available menu options. The filter utilizes fuzzy matching and searches are not limited to the first letter or prefix but includes the entire label text as well.

![Example of searching "Surround" or "Read" in the Refactor menu](images/1_82/action-control-fuzzy-search.gif)

## Diff Editor

In this release, we enabled the new diff editor by default. We also improved some of the new diff editor features and fixed many bugs.

### Moved code detection

This iteration we polished the moved code detection feature. It can be enabled with `"diffEditor.experimental.showMoves": true` or in the diff editor context menu. When enabled, code blocks that are moved from one location to a different location in the same file are detected and arrows are drawn to indicate where the code blocks moved to.

Code moves are also detected when they are slightly modified. The **Compare** button can be used to compare the block before and after the move.

![Moved code detection demo](images/1_82/diffEditor-movedCodeDetection.gif)

### Collapsed unchanged code headers

Use `"diffEditor.hideUnchangedRegions.enabled": true` or select the map icon in the editor context menu to enable collapsing unchanged code blocks.

With this release, there are now breadcrumbs for collapsed code blocks to indicate which symbols are collapsed. Clicking on a breadcrumb item reveals the selected item:

![Collapsed code headers](images/1_82/diffEditor-collapsedCodeHeaders.gif)

### Dynamic layout

If the width of the diff editor is too small, the editor automatically switches to inline view. If the editor is wide enough again, the previous layout is restored. Set `"diffEditor.useInlineViewWhenSpaceIsLimited": false` to disable this behavior.

### Button toggle states

We updated the toggle style of buttons in the diff editor to be more visible.

Old toggle style (untoggled and toggled) with dim untoggled buttons:

![Old, untoggled dim buttons](images/1_82/diffEditor-oldToolbarDisabled.png), ![Old, toggled](images/1_82/diffEditor-oldToolbarEnabled.png)

New toggle style (untoggled and toggled) with shaded toggled background:

![New, untoggled visible buttons](images/1_82/diffEditor-newToolbarDisabled.png), ![New, toggled with shaded background](images/1_82/diffEditor-newToolbarEnabled.png)

## Terminal

### Control how terminals restore on startup

The new setting `terminal.integrated.hideOnStartup` controls whether a terminal is automatically created when the application starts up. The following options are available:

* `never` (default): Never hide the terminal view on startup.
* `whenEmpty`: Only hide the terminal when there are no persistent sessions restored.
* `always`: Always hide the terminal, even when there are persistent sessions restored.

### Disable bracketed paste mode

[Bracketed paste mode](https://code.visualstudio.com/docs/terminal/basics#_copy-paste) is a feature in the terminal that wraps pasted text in special sequences so the shell can use that information. Shells that turn on this feature are meant to properly support this but there may be reasons it falls over at which point you might see text like `[201~` unexpectedly when pasting. This feature can now be disabled explicitly, which disables the feature even if shells request it.

### Terminal focus after run setting

The terminal now has a `terminal.integrated.focusAfterRun` setting so that users can specify if the terminal should be focused when **Terminal: Run Selected Text In Active Terminal** is invoked. The other options are to focus the terminal's accessible buffer (`accessible-buffer`) or leave nothing focused (`none`).

### Resizable Find

The terminal's Find control can now be resized using the sash on the left similar to the editor:

![Hover the left border of the Find control to see the resize handle](images/1_82/terminal-find-resize.png)

The sash is highlighted when hovering over or dragging it to resize the Find control.

### Faster rendering when GPU acceleration is disabled

The performance of the "DOM renderer", which is used when GPU acceleration is disabled, has been significantly improved thanks to [a rewrite of the component](https://github.com/xtermjs/xterm.js/pull/4605). The rewrite focused on reducing the number of DOM elements used and the savings scale much better the larger the terminal is.

When testing a typical render call on a terminal with 117 columns and 36 rows, it took ~10 ms before and ~2 ms after. Increasing the terminal size to 300x100 on the test machine recorded a render taking ~25-35 ms before and ~4-5 ms after.

### Better selection rendering

When GPU acceleration is off, selection rendering is now the same as the webgl renderer and all backgrounds are changed into the theme's selection background color to ensure good contrast and consistency.

![The background when selected will now be the same as the rest of the selection](images/1_82/terminal-dom-bg.png)

### Respect half minimum contrast ratio for dimmed text

The [minimum contrast ratio feature](https://code.visualstudio.com/docs/terminal/appearance#_minimum-contrast-ratio) allows the terminal to take more control over the foreground colors in the terminal to ensure they appear at a particular contrast ratio. One problem with this feature in the past was that dimmed text (`CSI 2 m`) would also respect the contrast ratio, meaning it could appear just as prominent as regular text. PowerShell's auto complete ghost text is an example where this didn't play nicely.

Dimmed text will now have **half** the contrast requirements. While this means that the text may no longer meet the minimum contrast ratio, it's now obviously different from regular text, which is more important.

![Now dimmed text should be visually different for typical minimum contrast ratios](images/1_82/terminal-mcr-dimmed.png)

### Configure the cursor appearance when unfocused

The look of the cursor in the terminal when it is not focused can now be configured with `terminal.integrated.cursorStyleInactive`. This supports all styles of the existing `terminal.integrated.cursorStyle`, plus `outline` (default) and `none`.

![The new inactive cursor styles are underline, block, line, outline and none](images/1_82/terminal-cursor-styles.png)

### Improved Terminal: Open Detected Link behavior

The **Open Detected Link** command (`kb(workbench.action.terminal.openDetectedLink)`) behavior has been changed to maintain high performance while making it much easier to search the whole terminal buffer for links. Previously, it would provide links for just the viewport and a little above with a cumbersome **Show more links** button at the end to search the rest of the buffer:

![Previously the last entry must have been selected to search everything](images/1_82/terminal-links-before.png)

Now links in the viewport are presented immediately, so the Quick Pick can show as soon as possible:

![Now initial results are restricted to the initial viewport](images/1_82/terminal-links-after.png)

As soon as the Quick Pick is displayed, links are detected for the rest of the terminal buffer in the background. When typing to filter, VS Code waits for all results and includes them in the filtered results:

![Results from the entire buffer are included in the search when filtering begins](images/1_82/terminal-links-after-filter.png)

Notice that `CodeQL.yml` was not included until a filter was typed as it was outside the viewport.

### New link formats

The following GNU-style link formats are now detected in terminals:

* `sourcefile:lineno.column`
* `sourcefile:line1-column1.column2`
* `sourcefile:line1.column1-line2.column2`

## Debug

### JavaScript Debugger

#### WebAssembly debugging

The JavaScript debugger will now automatically decompile WebAssembly modules to the WebAssembly Text Format, and allow you to step through and set breakpoints in the decompiled modules.

![Breakpoint hit during a WebAssembly debug session](images/1_82/js-debug-wat.png)

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

#### Source map loading improvements

We made many improvements to the way source maps are loaded in this release:

* Source maps in some common cases, like in applications compiled with the `tsc` command line, are loaded 3-5x faster.
* Hot module reloading from the [Vite](https://vitejs.dev) dev server is now supported.
* Source maps can now be automatically loaded from authenticated endpoints.

## Testing

### Improved status area

The 'status area' below the filter box in the Testing view is now more concise, and also provides a clickable action to rerun the most recently run tests. Holding `kbstyle(Alt)` while clicking on the rerun button will debug those tests instead.

![Testing view status area displaying test result count, test timing, and rerun button](images/1_82/testing-status-area.png)

### Support for link detection in test output

Link detection now runs in the terminal where Test Output is shown. File names, paths, and URIs are now clickable.

### Improved experience for test-correlated output

Test extensions can correlate console output with specific tests or locations. Previously, each output created in this way would appear as its own item in the Test Results view, and open in a text editor when selected.

Now, they are shown in a proper terminal, and navigating to an output message opens a terminal of that test's output with the message selected.

## Notebooks

### Copy cell output

The cell output menu now includes an option to copy the output to the system clipboard. The context menu can also be used for image output by right-clicking the image and selecting the **Copy Output** command.

<video src="images/1_82/copy-cell-output.mp4" autoplay loop controls muted title="Copy cell output"></video>

_Theme: [Bearded Theme feat. Gold D Raynh](https://marketplace.visualstudio.com/items?itemName=BeardedBear.beardedtheme) (preview on [vscode.dev](https://vscode.dev/editor/theme/BeardedBear.beardedtheme/Bearded%20Theme%20feat.%20Gold%20D%20Raynh))_

## Languages

### TypeScript 5.2

VS Code now ships with TypeScript 5.2.2. This major update brings new TypeScript language features, better performance, and many important improvements and bug fixes. You can read about TypeScript 5.2 on the [TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-5-2).

### Move to file refactoring

The **Move to file** refactoring for JavaScript and TypeScript lets you move a class, function, or constant into an existing file. This will also automatically update all references to the symbol and update imports as needed:

<video src="images/1_82/ts-move-to-file.mp4" autoplay loop controls muted title="Moving a symbol between TypeScript files"></video>

When you select **Move to file**, VS Code shows you a list of all files in the current TypeScript or JavaScript project. You can start typings to quickly filter to the file you want.

Alternatively, you can use **Select existing file...** to select a file using the normal file picker or **Enter new file path...** to specify a new file that should be created.

### Inline variable refactoring

The **Inline variable** refactoring for JavaScript and TypeScript replaces all occurrences of a variable with its constant value.

<video src="images/1_82/ts-inline.mp4" autoplay loop controls muted title="Inlining a constant variable"></video>

This refactoring is often most useful when rewriting existing code. For example, when a variable ends up being declared and then immediately returned, you can use inline variable to remove the extra declaration and return the value directly:

```ts
function add(a, b) {
  const result = a + b;
  return result
}
```

After running `inline variable` on `result`:

```ts
function add(a, b) {
  return a + b;
}
```

### Clickable parameter hints

You can now click on parameter hints to quickly jump to the parameter declaration. After turning on inlay hints using:

```json
"editor.inlayHints.enabled": "on",
"typescript.inlayHints.parameterNames.enabled": "all",
"javascript.inlayHints.parameterNames.enabled": "all"
```

hold down `Ctrl/Cmd` and click on the parameter name to jump to that parameter's declaration:

<video src="images/1_82/ts-click-param-hint.mp4" autoplay loop controls muted title="Ctrl/Cmd clicking on a parameter inlay hint to jump to its declaration"></video>

We plan to enable **Go to Definition** for other JavaScript and TypeScript inlay hints in upcoming releases.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* Better Remote - Tunnels connection reliability.
* New **Install Docker in WSL** command.
* Prebuild Dev Containers instructional guide.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_82.md).

## Contributions to extensions

### GitHub Copilot

#### Create workspaces from the Chat view

You can now use `/createWorkspace` to create workspaces from a natural language description in the stable build of the [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension.

<video src="images/1_82/create-workspace-chat.mp4" autoplay loop controls muted title="Create workspace example from the Chat view"></video>

We've also made the following improvements:

1. Proposed workspaces are now rendered as file trees in the chat response.
2. You can click into files to open a readonly preview in the editor.
3. If Copilot's initial proposal wasn't quite right, you can ask follow-up questions to help Copilot iterate and improve.

#### Start coding in untitled editor with inline chat

There is now a hint for how to start an inline chat session via `kb(inlineChat.start)` when you open an untitled text editor. Screen reader users can hear this hint and choose to disable it with the `accessibility.verbosity.untitledHint` setting.

<video src="images/1_82/untitled-hint.mp4" autoplay loop controls muted title="Inline chat in untitled text editor"></video>

#### Improvements to Quick Chat

We introduced Quick Chat a couple of months ago and have been iterating quickly to provide an experience that feels made for VS Code.

Highlights this iteration:

* More compact UX.
* Quick Chat now stays open when focus is lost.
* Sash and "scroll-to-grow" behavior to resize window.
* **Open Quick Chat** now in the Command Center.

![The Quick Chat window with a question and answer displayed](images/1_82/quickchat-with-answer.png)

Overall, the UX is more compact to align with other "Quick Open" experiences like the Command Palette (less padding everywhere, user and Copilot icons are smaller, buttons are inline instead of being in a title bar).

By default, when you ask a question, the Quick Chat resizes to focus on that question and answer to minimize screen real estate. We also think it helps focus on those quick questions that you might want to ask Copilot. The history of your conversation is still available and you can scroll up to go back in time.

Because of this dynamic height, we also wanted to provide a way to resize the window if you need more or less space. There are two options to adjust the window size. The first is what we call "scroll-to-grow." If your Quick Chat window is small from a small answer, but you'd like to see a previous long answer, as you scroll up, the Quick Chat grows to a max height.

<video src="images/1_82/quickchatscrolltogrow.mp4" autoplay loop controls muted title="Quick Chat 'Scroll-to-Grow'"></video>

The height of the Quick Chat can be reset back to showing only the last question and answer by doing one of the following:

* Closing the Quick Chat and waiting 30 seconds.
* Asking another question or running `/clear` (keep in mind, `/clear` clears your chat history for good).
* Double-clicking on the bottom sash.

Speaking of the sash... we also have a sash at the bottom of the Quick Chat that can be used for manually adjusting the height of the Quick Chat. When you use the sash, you're opting out of the default dynamic behavior and are saying "I want this height to be fixed here until reset."

<video src="images/1_82/sashquickchat.mp4" autoplay loop controls muted title="Quick Chat sash"></video>

> **Note**: If you want to go back to the dynamic behavior, double-click the sash or type `/clear` in the chat (`/clear` clears your chat history).

Last but not least, you can launch Quick Chat from the Command Center with the **Open Quick Chat** option.

![Open Quick Chat command in the Command Center](images/1_82/quickchat-commandcenter.png)

#### Explain terminal selection

Copilot now has the ability to explain the current terminal selection by selecting **Copilot: Explain This** in the terminal's context menu (`Right-click` or `Shift` + `Right-click` depending on the platform).

![The terminal context menu's first entry is Copilot: Explain This](images/1_82/copilot-terminal-selection-menu.png)

The **Copilot: Explain This** command will bring up the Chat view, where Copilot will return a detailed explanation.

![Copilot goes into detail in the explanation, for example it explains that a git push uses LFS, what delta compression is](images/1_82/copilot-terminal-selection-chat.png)

#### Natural language search for settings

The Settings editor now allows natural language search with [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat).

The natural language search results are currently placed below keyword-matching results and ordered by descending similarity score, but we plan to fix the ordering next month so that the search results continue to be arranged by their table of contents group.

Natural language search currently relies on Copilot embeddings. Consequently, natural language search results will not show up in the Settings editor for searches made before the embeddings become available, that is a few seconds after the GitHub Copilot Chat extension activates.

![Searching "make text bigger" in the Settings editor shows editor.fontSize as a result](images/1_82/se-natural-language-search.png)

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### Python

#### New Python Formatting article

There is a new dedicated topic on [Python Formatting](https://code.visualstudio.com/docs/python/formatting), where you'll learn how to set a default formatter such as [autopep8](https://marketplace.visualstudio.com/items?itemName=ms-python.autopep8) or [Black](https://marketplace.visualstudio.com/items?itemName=ms-python.black-formatter) and customize it through various settings.

#### Terminal activation using environment variables

The [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) extension now has a new activation mechanism that activates the selected environment in the terminal without running any explicit activation commands. This month, we are beginning to roll this out as an experiment, making it the default experience. With the new approach, we use environment variables to activate terminals, which is done implicitly on terminal launch and can thus be quicker, especially for conda environments. You can add the following **User** setting: `"python.experiments.optInto": ["pythonTerminalEnvVarActivation"]` to try it out.

If you have any comments or suggestions regarding this experiment, feel free to share them in [vscode-python issue #11039](https://github.com/microsoft/vscode-python/issues/11039).

#### Recreate or use existing .venv environment

When working within a workspace that already contains a `.venv` folder, the **Python: Create Environment** command has been updated with options to either recreate or use the existing environment. If you decide to recreate the environment, your current `.venv` is deleted, allowing you to recreate a new environment named `.venv`. You can then customize this new environment by following the **Python: Create Environment** flow to select your preferred interpreter and specify any dependency files for installation. In the case the environment cannot be deleted, for example, due to it being active, you are prompted to delete the environment manually.

Alternatively, if you choose to use the existing environment, the environment is selected for your workspace.

## Preview Features

### Quick Access text search

We are experimenting with showing workspace search results in a Quick Access menu. To try this, run **Search: Quick Text Search (Experimental)**. This command sets up the Quick Open to accept search queries. Type some text to see matches from different workspace files.

<video src="images/1_82/quick-search-demo.mp4" autoplay loop controls muted title="Quick Search demo"></video>

### Dim unfocused editors and terminals

There's a new experimental feature to dim editors and terminals that are not currently focused. The goal of this feature is to make it much clearer where text will go compared to the typical blinking cursor.

![When dim unfocused is enabled in a 2x2 grid editor group layout, only one will be fully opaque](images/1_82/dim-unfocused.png)

The image above shows the dim unfocused feature enabled with opacity set to 0.5 so that it is clearer that the editor for `terminalService.ts` has focus.

This can be enabled using `accessibility.dimUnfocused.enabled` and the amount of dimming is controlled with `accessibility.dimUnfocused.opacity`.

The feature only covers editors and terminals currently but the plan is to expand this to [allow a user to configure what views they want to dim](https://github.com/microsoft/vscode/issues/191616) themselves.

## Extension authoring

### Support for batch range formatting

The `DocumentRangeFormattingEditProvider` API now supports batch formatting. This means that an extension can optionally signal to the editor that it supports being called with multiple ranges at once. This helps reduce the number of calls to the formatting provider and thus improves performance.

To opt in to batch formatting, providers must implement a new optional function: [provideDocumentRangesFormattingEdits](https://github.com/microsoft/vscode/blob/e34a26097d763548bf220da77b74922765c12fa2/src/vscode-dts/vscode.d.ts#L4172).

### EnvironmentVariableCollection's scoped to a workspace folder

The `EnvironmentVariableCollection` API now supports creating a new collection that is scoped to a particular workspace folder and will apply in addition to the "global" collection.

```ts
// Get a scoped collection for the first workspace folder
const scoped = context.environmentVariableCollection.getScoped({
  workspaceFolder: workspace.workspaceFolders[0]
})
scoped.replace('SCOPED', '1');
// Only terminals created in the first workspace folder will have SCOPED=1 set
```

The [Python extension](https://marketplace.visualstudio.com/items?itemName=ms-python.python) uses this mechanism to set up different virtual environments depending on which folder a terminal belongs to in a multi-root workspace.

### Configure when a EnvironmentVariableMutator is applied

The `EnvironmentVariableCollection` API now has the ability to apply changes inside the shell integration script that will run after shell initialization scripts. This will only work when shell integration is enabled so the change can be applied both at shell creation and inside shell integration if it's critical:

```ts
context.environmentVariableCollection.prepend('PATH', '/my/custom/path', {
  applyAtProcessCreation: true,
  applyAtShellIntegration: true
});
```

This feature is useful when the variable in question could be mutated by a shell initialization script.

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [@vscode/dts](https://github.com/microsoft/vscode-dts) and run `npx @vscode/dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### A contextValue on TestMessage's and contribution points

You can provide a `contextValue` on `TestMessage`s to be shown when users take actions on those messages. Additionally, two new menu contributions points are available, `testing/message/context` and `testing/message/content`. The former is displayed on the message in the Test Results tree view, and the latter is displayed over the message in the editor.

Read more about `contextValue` in [issue #190277](https://github.com/microsoft/vscode/issues/190277).

### Terminal context menu contributions

Two new menus are being proposed that allow extensions to integrate their own context menu actions into the terminal:

* `terminal/context` - The terminal context menu
* `terminal/title/context` - The terminal tabs context menu

### Listen to terminal command execution

An early proposal for the long requested ability for extensions to listen to the terminal command execution API is available for testing. This API is implemented using [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration) and will only fire on terminals that have it enabled and working.

```ts
export interface TerminalExecutedCommand {
  /**
   * The {@link Terminal} the command was executed in.
   */
  terminal: Terminal;
  /**
   * The full command line that was executed, including both the command and the arguments.
   */
  commandLine: string | undefined;
  /**
   * The current working directory that was reported by the shell. This will be a {@link Uri}
   * if the string reported by the shell can reliably be mapped to the connected machine.
   */
  cwd: Uri | string | undefined;
  /**
   * The exit code reported by the shell.
   */
  exitCode: number | undefined;
  /**
   * The output of the command when it has finished executing. This is the plain text shown in
   * the terminal buffer and does not include raw escape sequences. Depending on the shell
   * setup, this may include the command line as part of the output.
   */
  output: string | undefined;
}

export namespace window {
  /**
   * An event that is emitted when a terminal with shell integration activated has completed
   * executing a command.
   *
   * Note that this event will not fire if the executed command exits the shell, listen to
   * {@link onDidCloseTerminal} to handle that case.
   */
  export const onDidExecuteTerminalCommand: Event<TerminalExecutedCommand>;
}
```

The shape of this API is not final but the basic idea will remain the same.

Here's an example usage that listens for any successful `git push` command and triggers a refresh in the extension:

```ts
const disposables = [];
disposables.push(window.onDidExecuteTerminalCommand(command => {
  if (command.commandLine.startsWith('git push') && command.exitCode === 0) {
    refreshState();
  }
}));
function refreshState() { /* ... */ }
```

### Terminal selection access

A simple proposed API to access the terminal selection:

```ts
export interface Terminal {
  /**
   * The selected text of the terminal or undefined if there is no selection.
   */
  readonly selection: string | undefined;
}
```

This API will likely include a corresponding change event before it's finalized and the API name may change to make it clearer that it's a simple string and not `Range`-based like `TextEditor.selection`.

### Terminal Quick Fix progress

The terminal Quick Fix proposal can now trigger a regular VS Code command, as opposed to a terminal command. This change necessitated also changing the terminal command interface name:

```ts
export interface TerminalQuickFixProvider {
  /**
   * Provides terminal quick fixes
   * @param commandMatchResult The command match result for which to provide quick fixes
   * @param token A cancellation token indicating the result is no longer needed
   * @return Terminal quick fix(es) if any
   */
  provideTerminalQuickFixes(commandMatchResult: TerminalCommandMatchResult, token: CancellationToken): ProviderResult<SingleOrMany<TerminalQuickFixExecuteTerminalCommand | TerminalQuickFixOpener | Command>>;
}
export class TerminalQuickFixExecuteTerminalCommand {
  /**
   * The terminal command to run
   */
  terminalCommand: string;
  constructor(terminalCommand: string);
}
```

## Engineering

### Electron 25 update

In this milestone, we are promoting the Electron 25 update to our stable users. This update comes with Chromium `114.0.5735.289` and Node.js `18.15.0`. We want to thank everyone who self-hosted on Insiders builds and provided early feedback.

#### Update highlights for Node.js

Node.js bundled in our desktop application and standalone executable bundled with our [servers](https://code.visualstudio.com/docs/remote/vscode-server) are updated from v16 -> v18. Given that this is a major version bump, there are a couple of behavior and compatibility changes:

* DNS [result orders](https://github.com/nodejs/node/commit/1b2749ecbe) from the OS are no longer sorted. We have added `--dns-result-order=ipv4first` to our extension host in both local and server scenarios to avoid breaking extensions that might not have yet adopted this change. Moving forward, we recommend that extensions use the [autoSelectFamily](https://nodejs.org/dist/latest-v18.x/docs/api/all.html#all_net_socketconnectoptions-connectlistener) option in `socket.connect` API to accommodate the result order changes.

* Prebuilt binaries from the [official Node.js repo](https://nodejs.org) for Linux are now compatible with Linux distributions based on glibc 2.28 or later. This would mean dropping support from our servers for Ubuntu 18, CentOS 7, RHEL 7, etc. We are now shipping a custom build of Node.js for our Linux servers to maintain glibc 2.17 or later compatibility. This support will change in future updates when we are no longer capable of building newer Node.js versions on CentOS 7 images, so we advice our server users to update their OS versions if they are affected by this change.

#### Update highlights for Chromium

* After [Mesa](https://mesa3d.org) version updates, Chromium shader compilation can be broken leading to artifacts in application UI. The issue is tracked in [issue #190437](https://github.com/microsoft/vscode/issues/190437), which also contains the link to the Chromium bug report. You can identify this issue by running with `--verbose` and looking for the following line `ERROR:shared_context_state.cc(81)] Skia shader compilation error` in the logs. If you are affected by this issue, the current workaround is to delete the GPU cache located at `~/.config/Code/GPUCache`.
* When Chromium uses the [SwiftShader](https://swiftshader.googlesource.com/SwiftShader) backend for webgl, it seems to have regressed performance on both Windows and Linux for our integrated terminal. As a workaround, we detect the affected users based on the `GL_RENDERER` string and switch to the DOM backend for the terminal. Additional details can be found in [issue #190195](https://github.com/microsoft/vscode/issues/190195), which also contains link to the Chromium bug report.

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@starball5 (starball)](https://github.com/starball5)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)
* [@rperez030 (Roberto Perez)](https://github.com/rperez030)

### Pull requests

Contributions to `vscode`:

* [@0o001 (Mustafa Ateş Uzun)](https://github.com/0o001): fix: localize string typo [PR #191046](https://github.com/microsoft/vscode/pull/191046)
* [@alphacoma18 (Alpha Romer Coma)](https://github.com/alphacoma18): Fix supported markdown-lint violations in markdown files [PR #190750](https://github.com/microsoft/vscode/pull/190750)
* [@andyleejordan (Andy Jordan)](https://github.com/andyleejordan): Guard `$IsWindows` [PR #190192](https://github.com/microsoft/vscode/pull/190192)
* [@bandantonio (Antonio)](https://github.com/bandantonio): fix: add missing pricing parameter to manifest schema [PR #190293](https://github.com/microsoft/vscode/pull/190293)
* [@ChaseKnowlden (Chase Knowlden)](https://github.com/ChaseKnowlden):Append the focused view in the window title  [PR #190216](https://github.com/microsoft/vscode/pull/190216)
* [@davedash (Dave Dash)](https://github.com/davedash): Fish integration: Use -- in string to signify beginning of positional… [PR #189994](https://github.com/microsoft/vscode/pull/189994)
* [@demccormack (Daniel McCormack)](https://github.com/demccormack): Fix zsh and bash shell integration when using set -u [PR #185425](https://github.com/microsoft/vscode/pull/185425)
* [@floge07](https://github.com/floge07): fix: allow underscore as a valid char in tunnel remote address [PR #190904](https://github.com/microsoft/vscode/pull/190904)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Fix recently-broken cleanup of obsolete (superseded) extension folders [PR #189335](https://github.com/microsoft/vscode/pull/189335)
* [@greenie (Joe Green)](https://github.com/greenie): Fix scroll to top button colour consistency [PR #191034](https://github.com/microsoft/vscode/pull/191034)
* [@hrkw00 (Ryosuke Hirakawa)](https://github.com/hrkw00): Fix comment typo in standaloneLanguages.ts [PR #189449](https://github.com/microsoft/vscode/pull/189449)
* [@hsfzxjy (hsfzxjy)](https://github.com/hsfzxjy)
  * Add context key notebookEditorCursorAtLineBoundary [PR #187679](https://github.com/microsoft/vscode/pull/187679)
  * Always respect non-empty selection in getMultiSelectedEditorContexts [PR #187704](https://github.com/microsoft/vscode/pull/187704)
  * Ensure the concurrency safety of TestResultsViewContent.reveal [PR #189756](https://github.com/microsoft/vscode/pull/189756)
  * Make CommentThread Ctrl+Arrow navigible [PR #189913](https://github.com/microsoft/vscode/pull/189913)
  * Fix overflow of setting list [PR #190721](https://github.com/microsoft/vscode/pull/190721)
  * Fix nested list style in Markdown preview [PR #190936](https://github.com/microsoft/vscode/pull/190936)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fix continuation for configured default shell not used when reconnecting to Codespace [PR #181832](https://github.com/microsoft/vscode/pull/181832)
  * Fix free port quick fix does not work on Windows [PR #190900](https://github.com/microsoft/vscode/pull/190900)
* [@kfrederix (Karel Frederix)](https://github.com/kfrederix): wrap handler for resize observer in requestAnimationFrame() [PR #183325](https://github.com/microsoft/vscode/pull/183325)
* [@kon72 (Kon)](https://github.com/kon72): Add commands for collapsing/showing all unchanged regions [PR #190451](https://github.com/microsoft/vscode/pull/190451)
* [@lucasMarioza](https://github.com/lucasMarioza): Ensure style element is added to shadowDOM components on colorizeElement [PR #190505](https://github.com/microsoft/vscode/pull/190505)
* [@neeleshb (Neelesh Bodas)](https://github.com/neeleshb)
  * Adding empty alt text to decorative images for extensions [PR #190607](https://github.com/microsoft/vscode/pull/190607)
  * Remove incorrect role from the title bar. [PR #190608](https://github.com/microsoft/vscode/pull/190608)
* [@rehmsen (Ole)](https://github.com/rehmsen): Increase shortcut consistency of web with electron. [PR #191061](https://github.com/microsoft/vscode/pull/191061)
* [@SevenOutman (Doma)](https://github.com/SevenOutman): Add "Peek Call Hierarchy" and "Peek Type Hierarchy" actions to Command Palette [PR #189607](https://github.com/microsoft/vscode/pull/189607)
* [@songlinn](https://github.com/songlinn): fix: prevent history show prev/next in composing event [PR #184014](https://github.com/microsoft/vscode/pull/184014)
* [@timar07 (Timothy)](https://github.com/timar07): fix: xterm quick fix appears in wrong spot #169162 [PR #188693](https://github.com/microsoft/vscode/pull/188693)
* [@Timmmm (Tim Hutt)](https://github.com/Timmmm)
  * Update dev container node version [PR #190346](https://github.com/microsoft/vscode/pull/190346)
  * Support . as a row:column separator in terminal link detector [PR #190351](https://github.com/microsoft/vscode/pull/190351)
* [@tisilent (YAYA 刘玉婷)](https://github.com/tisilent)
  * fix:Terminal find widget adjustment [PR #179398](https://github.com/microsoft/vscode/pull/179398)
  * Find with selected values when reveal TerminalFind [PR #190466](https://github.com/microsoft/vscode/pull/190466)
* [@troy351](https://github.com/troy351): listWidget: remove redundant logic [PR #191054](https://github.com/microsoft/vscode/pull/191054)
* [@weartist (Hans)](https://github.com/weartist)
  * Fix #185343 [PR #185421](https://github.com/microsoft/vscode/pull/185421)
  * Add setting to prevent closing the pinned tab when using middle click (fix #115734) [PR #188592](https://github.com/microsoft/vscode/pull/188592)
  * Fix: #188760 [PR #189259](https://github.com/microsoft/vscode/pull/189259)
  * Fix: #188751 [PR #189616](https://github.com/microsoft/vscode/pull/189616)
  * support click on "hidden lines" text to unfold #186406 [PR #189657](https://github.com/microsoft/vscode/pull/189657)
  * immediately search after enter pressed in files to include/exclude te… [PR #190473](https://github.com/microsoft/vscode/pull/190473)
  * add custom hover for quick open title bar buttons [PR #191416](https://github.com/microsoft/vscode/pull/191416)
* [@zardoy (Vitaly)](https://github.com/zardoy): typescript Better paths matching for move to existing file quickpick [PR #181231](https://github.com/microsoft/vscode/pull/181231)

Contributions to `vscode-css-languageservice`:

* [@bentorkington](https://github.com/bentorkington): fix reference to deprecated method [PR #359](https://github.com/microsoft/vscode-css-languageservice/pull/359)

Contributions to `vscode-hexeditor`:

* [@lorsanta](https://github.com/lorsanta): Add fp16 and bf16 support in data inspector [PR #451](https://github.com/microsoft/vscode-hexeditor/pull/451)

Contributions to `vscode-js-debug`:

* [@sunilsurana (Sunil Surana)](https://github.com/sunilsurana): Speed up sourcemap lookups by checking existence of .map files [PR #1780](https://github.com/microsoft/vscode-js-debug/pull/1780)

Contributions to `vscode-pull-request-github`:

* [@tobbbe (Tobbe)](https://github.com/tobbbe): Sanitize slashes from title [PR #5149](https://github.com/microsoft/vscode-pull-request-github/pull/5149)

Contributions to `vscode-vsce`:

* [@bandantonio (Antonio)](https://github.com/bandantonio)
  * feat: add user-friendly info to --help [PR #884](https://github.com/microsoft/vscode-vsce/pull/884)
  * feat: add support for packaging and publishing without license file [PR #887](https://github.com/microsoft/vscode-vsce/pull/887)
  * rephrase error message for mismatched pricing values in manifest [PR #890](https://github.com/microsoft/vscode-vsce/pull/890)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
