---
Order: 57
TOCTitle: VS Code at Build
PageTitle: Visual Studio Code at Microsoft Build 2020
MetaDescription: Visual Studio Code at Microsoft Build 2020
Date: 2020-05-14
Author: <PERSON>ocialImage: /assets/blogs/2020/05/14/vscode-build-social.png
---
# Visual Studio Code at Build 2020

May 14, 2020 by <PERSON>, [@ItalyPaleAle](https://twitter.com/ItalyPaleAle)

The [Microsoft Build 2020](https://mybuild.microsoft.com) conference is starting next Tuesday, May 19, and will be running for 48 continuous hours. For the first time, Build is a fully digital event, open to everyone for free.

[![Microsoft Build 2020](build-2020.png)](https://mybuild.microsoft.com)

Our team has been hard at work creating new experiences for all developers using Visual Studio Code. At Build, we'll demo new features we've been working on in VS Code (hint: Settings Sync!) as well as Notebooks, Remote Development, and a bunch of cool tips and tricks. You will also be able to check out what we've been working on with partner teams (Visual Studio Codespaces, Visual Studio Live Share) and together with our friends at GitHub.

In our sessions, you can learn how Visual Studio Code enables teams to remain productive and collaborate even when distributed.

## Connect with live sessions

There are a number of great live sessions at Build, too many list here, but we think the following offerings will be interesting to developers using Visual Studio Code. All of these sessions are going to be live-streamed on the [Microsoft Build](https://mybuild.microsoft.com/) website, and are repeated three times across three time zones. And, each session includes a live Q&A at the end.

*All times shown below are in Pacific Time.*

**Remote development with Visual Studio Code**

* Chris Dias ([@ChrisDias](https://twitter.com/ChrisDias)) and Aaron Powell ([@slace](https://twitter.com/slace))
* [Tuesday May 19 at 4:15pm](https://mybuild.microsoft.com/sessions/44eb0651-9449-4a50-b344-638ec520e042)
* [Wednesday May 20 at 7:30am](https://mybuild.microsoft.com/sessions/5bf61d0b-f4af-4b2e-bcb4-56829ada4e5b)
* [Thursday May 21 at 12:45am](https://mybuild.microsoft.com/sessions/98051ab8-b26e-4582-9215-5d21f2b18afd)

**How to be super productive with Node.js and Visual Studio Code**

* Brian Holt ([@holtbt](https://twitter.com/holtbt)) and Aaron Powell ([@slace](https://twitter.com/slace))
* [Tuesday May 19 at 7:30pm](https://mybuild.microsoft.com/sessions/28de040a-934e-4890-9168-58501e070653)
* [Wednesday May 20 at 8:15am](https://mybuild.microsoft.com/sessions/a908c4f8-210f-49b4-a7fe-671b4e3890f9)
* [Thursday May 21 at 1:30am](https://mybuild.microsoft.com/sessions/0f743b92-55fd-4c51-8ac5-bc30b56e07e1)

**Build Python apps in Azure faster with Visual Studio Code**

* Nicolas Garfinkel ([@NicolasGarfink1](https://twitter.com/NicolasGarfink1))
* [Tuesday May 19 at 9pm](https://mybuild.microsoft.com/sessions/4b7d35d7-7af8-442d-953a-abcc12627d24)
* [Wednesday May 20 at 12:30pm](https://mybuild.microsoft.com/sessions/314e756d-b145-4d84-90c1-1aea9235df72)
* [Thursday May 21 at 3am](https://mybuild.microsoft.com/sessions/b7a2db58-78fb-49b8-9372-66cd63f2f26f)

**How to be productive with your team while remote**

* Jonathan Carter ([@LostInTangent](https://twitter.com/LostInTangent))
* [Tuesday May 19 at 12:30pm](https://mybuild.microsoft.com/sessions/a0d2222c-cbd4-42ad-9471-88f91dc639f7)
* [Wednesday May 20 at 3:30am](https://mybuild.microsoft.com/sessions/a0e69c1b-96e9-452a-9b3f-91665f4a51cf)
* [Wednesday May 20 at 6pm](https://mybuild.microsoft.com/sessions/ef29e80e-19e3-4a51-a32c-75dc8e58820b)

Additionally, the VS Code team will be online for an **Ask the Team** session on [Tuesday, May 19 at 11:45am PDT](https://mybuild.microsoft.com/sessions/be31cf74-1b32-4ac5-9673-333bc6018b18), where you can come and ask us anything, just like coming to the booth on the expo hall floor, only virtually.

## Learn with on-demand videos

In addition to the live sessions, you'll have the opportunity to learn more about VS Code with 6 videos that will be available on-demand on the [Channel 9](https://channel9.msdn.com/Events/Build/2020) website.

* [**Visual Studio Code tips and tricks**](https://aka.ms/Build2020AppDev-VSCodeTips)
  * Matt Bierner ([@mattbierner](https://hachyderm.io/@mattbierner))
* [**What every Visual Studio Code user should know about GitHub**](https://aka.ms/Build2020AppDev-VSCodeAndGitHub)
  * Rachel Macfarlane ([@RMacfarlane](https://github.com/RMacfarlane)), Alex Ross ([@alexr00](https://github.com/alexr00))
* [**What's New in TypeScript**](https://aka.ms/Build2020AppDev-TypeScript)
  * Daniel Rosenwasser ([@drosenwasser](https://twitter.com/drosenwasser))
* [**Python development with Visual Studio Code**](https://aka.ms/Build2020AppDev-Python)
  * Luciana Abud ([@luumelo14](https://twitter.com/luumelo14))
* [**Supercharge your data science workflow with Python and Visual Studio Code**](https://aka.ms/Build2020AppDev-DataScience)
  * Sid Unnithan ([@SidUnnithan](https://twitter.com/SidUnnithan)), Jeffrey Mew ([@jeffrey_mew](https://twitter.com/jeffrey_mew))
* [**Build containerized microservices faster and ship with confidence using VS Code and GitHub**](https://aka.ms/Build2020AppDev-InnerLoops)
  * Nick Greenfield ([@greenie-msft](https://github.com/greenie-msft))

## The History of VS Code

And you won't want to miss Erich Gamma telling the story of Visual Studio Code.

* [**The History of Visual Studio Code**](https://mybuild.microsoft.com/sessions/6b571733-8198-48da-b870-ef804dcfea93?source=sessions)

## Follow us for more

The sessions above just scratch the surface of what our team (and our teammates at Microsoft and GitHub) have been working on and will showcase at Build. Check out the [Microsoft Build](https://mybuild.microsoft.com/) website for the complete agenda, including keynotes, all other developer sessions, and more opportunities to engage with us.

We hope to connect with you (virtually) at Build, and look forward to you joining us! See you next week.

Happy Coding!

Alessandro Segala, VS Code Product Manager [@ItalyPaleAle](https://twitter.com/ItalyPaleAle)
