---
ContentId: f71d4b81-8361-48ee-82c0-48f69ec0d72e
PageTitle: Get Started with Visual Studio Code
DateApproved: 12/11/2024
MetaDescription: Get Started with Visual Studio Code
---
# Learn to code with Visual Studio Code

Learning to code is intimidating, so set yourself up for success with a tool built for you. Visual Studio Code is a free coding editor that helps you start coding quickly.
Use it to code in any programming language, without switching editors. Visual Studio Code has support for many languages, including Python, Java, C++, JavaScript, and more. Ready to get started? Check out these [introductory videos](https://www.youtube.com/watch?v=Uvf2FVS1F8k&list=PLj6YeMhvp2S4oEV_bT1Uk3oXfpvGW6Xqy) or check out our [coding packs for Java and .NET](/learn/educators/installers.md).

## Why VS Code?

<center><img src="/assets/learn/get-started/overview/vscode2.png" alt="VS Code" aria-hidden="true" class="thumb"/></center>

### Collaborate and code remotely

Work together remotely with your teachers or classmates using the free [Live Share extension](https://marketplace.visualstudio.com/items?itemName=MS-vsliveshare.vsliveshare). Edit and debug your code in real-time, and use the chat and call features to ask questions or discuss ideas together. Whether you're working on a group assignment or teaching a lesson, you can invite multiple people to join your  session and code together. Check out this [tutorial on how start using Live Share](/learn/collaboration/live-share.md).

### Code to learn

New to coding? Visual Studio Code highlights keywords in your code in different colors to help you easily identify coding patterns and learn faster. You can also take advantage of features like [IntelliSense](/docs/editing/intellisense.md#intellisense-features) and [Peek Definition](/docs/editing/editingevolved.md#peek), which help you understand how functions can be used, and how they relate to one another.

### Fix errors as you code

As you code, Visual Studio Code gives you suggestions to complete lines of code and quick fixes for common mistakes. You can also use the debugger in VS Code to step through each line of code and understand what is happening. Check out guides on how to use the debugger if you're coding in [Python](/docs/python/debugging.md), [Java](/docs/java/java-debugging.md#configure), and [JavaScript/TypeScript/Node.js](/docs/debugtest/debugging.md#start-debugging).

### Make it yours with custom themes and colors

You can change the look and feel of VS Code by picking your favorite fonts and icons and choosing from hundreds of color themes. Check out this [video on personalizing VS Code](https://youtu.be/HOShAQzOy4Q).

### Compare changes in your code

Use the built-in source control to save your work over time so you don't lose progress. See a graphical side-by-side view to compare versions of your code from different points in time. Check out this quick [video on how to get a side-by-side "diff"](https://youtu.be/aVmGwnCjRto).

### Code inside Notebooks

If you want to try a project in data science or data visualization, you can use Jupyter notebooks inside VS Code. Run your code step-by-step, and visualize and interact with your data, variables, graphs, and plots. Check out this [tutorial on how to work with Jupyter Notebooks inside VS Code](/docs/datascience/jupyter-notebooks.md).
