---
Order:
TOCTitle: February 2016 Release
PageTitle: Visual Studio February 2016 Release
MetaDescription: Announcing the Visual Studio Code February 2016 Release
Date: 2016-03-07
ShortDescription: Announcing the February 2016 Release of VS Code
Author: <PERSON>
---

# February 2016 Release

March 7, 2016 by The VS Code Team, [@code](https://twitter.com/code)

Today we are releasing the February 2016 build of Visual Studio Code. This release brings many improvements to your development experience, including:

* **JavaScript**: [Sal<PERSON>](https://github.com/microsoft/TypeScript/issues/4789) is now the default JavaScript/TypeScript language service, bringing support for React and React Native to VS Code.
* **Code Folding**: The most requested feature in [UserVoice](https://visualstudio.uservoice.com/forums/293070-visual-studio-code/suggestions/7752321-add-code-folding-support) is now on by default.
* **Extensions**: VS Code can make recommendations for extensions based on the files you've opened in the tool (Press `kb(workbench.action.showCommands)`, search for "recommend").
* **Accessibility**: Screen reader support in the Editor is now on by default.

And much, much more. Please see our [Release Notes](https://go.microsoft.com/fwlink/?LinkID=533483) for more information.

If you have automatic updates turned on (OS X and Windows) then you'll get prompted soon. Otherwise, [download VS Code today](https://code.visualstudio.com)!

Happy Coding!!

The VS Code Team
