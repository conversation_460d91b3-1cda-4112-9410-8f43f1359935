---
Order:
TOCTitle: October 2020
PageTitle: Visual Studio Code October 2020
MetaDescription: Learn what is new in the Visual Studio Code October 2020 Release (1.51)
MetaSocialImage: 1_51/release-highlights.png
Date: 2020-11-6
DownloadVersion: 1.51.1
---
# October 2020 (version 1.51)

**Update 1.51.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2020+Recovery%22+is%3Aclosed+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the October 2020 release of Visual Studio Code. As announced in the [October iteration plan](https://github.com/microsoft/vscode/issues/108473), we focused on housekeeping GitHub issues and pull requests as documented in our issue grooming guide.

We also worked with our partners at GitHub on GitHub Codespaces, which ended up being more involved than originally anticipated. To that end, we'll continue working on housekeeping for part of the November iteration.

During this housekeeping milestone, we also addressed several feature requests and community [pull requests](#thank-you). Read on to learn about new features and settings.

## Workbench

### More prominent pinned tabs

Pinned tabs will now always show their pin icon, even while inactive, to make them easier to identify. If an editor is both pinned and contains unsaved changes, the icon reflects both states.

![Inactive pinned tabs showing pin icons](images/1_51/pinned-tabs.png)

### Extension trees use custom hover

Instead of using the native tooltip in extension tree views, we now use a custom hover that is consistent cross-platform and fits better with the overall UX.

![Custom tree hover](images/1_51/custom-tree-hover.gif)

### Install an extension without synchronizing

You can now install an extension without synchronizing it while settings sync is enabled.

![Install extension without syncing](images/1_51/extension-install-donot-sync.png)

*Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme)*

### Install an extension from Explorer

VS Code now supports installing an extension VSIX file from the Explorer by right-clicking on a VSIX file and choosing the **Install Extension VSIX** context menu item.

### Input blur command

A new internal `workbench.action.blur` command is now available, which removes focus from any focusable input. You can assign a keyboard shortcut for this command in the **Keyboard Shortcuts Preferences**.

## Integrated Terminal

### Local Echo

Making modifications in the terminal traditionally requires information to be sent to the terminal process, processed, and returned to VS Code in order to be affected. This can be slow when working on a poor or distant connection to an [SSH server](https://code.visualstudio.com/docs/remote/ssh) or [Codespace](https://github.com/features/codespaces).

This release adds a "local echo" mode to the terminal, which attempts to predict modifications and cursor movements made locally and show them in the UI without requiring a round trip to the server. By default, predicted characters show as "dimmed":

![Gif showing typing with 250ms latency where characters entered immediately are dimmed](images/1_51/local-echo.gif)

There are two settings you can use to configure this:

- `terminal.integrated.localEchoLatencyThreshold` configures the detected delay threshold, in milliseconds, at which local echo will activate. This can be set to `0` to turn on the feature all the time, or `-1` to disable it. Defaults to `30`.
- `terminal.integrated.localEchoStyle` configures the style or color of local character, defaults to `dim`.

## IntelliSense

### Resizable suggestions

This milestone, we've made several improvements to the suggestions UI. First and foremost, it can now be resized. Drag the sides or corners to resize the control.

![Resizable Suggestions control](images/1_51/suggest-drag.gif)

*Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme), Font: FiraCode*

The size of the suggestions list will be saved and restored across sessions. The size of the details pane is only saved per session, since that size tends to be more variable. Also, the `editor.suggest.maxVisibleSuggestions` setting has become obsolete.

### Status bar for suggestions

The suggestions control can now also show its own status bar at the bottom of the window. Enable it using the `editor.suggest.showStatusBar` setting. It makes toggling details simpler, and shows if a completion supports inserting, replacing, or both.

![Suggestions Status Bar](images/1_51/suggest-status.png)

*Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme), Font: FiraCode*

In the sample above, selecting "Insert" results in `Math.floorceil` and selecting "Replace" results in `Math.floor`.

The new `editor.suggest.insertMode` setting allows you to configure whether you prefer insert or replace. When a suggestion supports both, your preference will be the default.

### Move cursor to select suggestions

Last but not least, you can now move the cursor while suggestions are showing. For instance, you can trigger suggestions at the end of a word, move left to see more suggestions, and then use replace to overwrite the word.

![Moving cursor while suggestions are showing](images/1_51/suggest-left.gif)

*Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme)*

## Emmet

Custom snippets in Emmet are back. Additionally, snippets now automatically refresh upon saving the snippets file or updating the `emmet.extensionsPath` setting.

![Emmet custom snippets working again](images/1_51/emmet-custom-snippets.gif)

## Source Control

### Source Control input box saves commit message history

This addresses a [feature request](https://github.com/microsoft/vscode/issues/26952) to navigate SCM commit history. Press `kb(scm.viewPreviousCommit)` and `kb(scm.viewNextCommit)` to display the prior and next commits, respectively. To move directly to the first and last position of the input box, press `kbstyle(Alt)` in conjunction with the corresponding arrow key.

![After typing a message in the SCM input box, then staging and committing changes, pressing the up arrow reveals the message that was just committed](images/1_51/scm-commit-history-demo.gif)

### Git: Tag commands in submenu

Tag related Git commands have been added to the **...** Git menu.

![Git tags submenu](images/1_51/git-tags.png)

### Git: Rebase command

A new **Git: Rebase branch...** command has been added which lets you rebase a branch using the UI.

### Git: Recursive clone command

With the **Git: Clone (Recursive)** command, you can now recursively clone Git repositories, including its nested Git submodules.

### Timeline: Emoji shortcode rendering

We now render emoji shortcodes, such as `:smile:`, in the Timeline View.

![Timeline view with emoji](images/1_51/timeline.png)

## Languages

### Markdown smart select

Expand and shrink selection in Markdown documents using the following new commands:

* Expand: `kb(editor.action.smartSelect.expand)`
* Shrink: `kb(editor.action.smartSelect.shrink)`

Selection applies to the following, and follows a traditional hierarchical pattern:

* Headers
* Lists
* Block quotes
* Fenced code blocks
* Html code blocks
* Paragraphs

![Smart select within a Markdown document expands from a block element, to the block element containing it, to the rest of the content under a header, to the header itself](images/1_51/markdown-smart-select-demo.gif)

### Empty brace formatting option for JavaScript and TypeScript

The new `javascript.format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces` and `typescript.format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces` formatting configuration option controls if spaces are inserted between empty braces. The default value for these settings is true. For example, for the JavaScript:

```java
class Foo {
    doFoo() { }
}
```

Setting `"javascript.format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces": false` and formatting the code results in:

```java
class Foo {
    doFoo() {} // Notice that the space has been removed
}
```

## Browser support

### Download folders (Edge, Chrome)

Leveraging the new [File System Access](https://wicg.github.io/file-system-access/) API, VS Code running in a browser can now offer a download action for folders from the File Explorer to download all files and folders to disk.

![Download folder](images/1_51/download-folder.gif)

**Note:** This requires a recent version of Microsoft Edge or Google Chrome.

### Open Workspace notification

If you open a folder that contains `.code-workspace` files at the top level, you'll now see a notification asking you to open it. This was always the case in VS Code for desktop, and will now work in the browser too.

![Open workspace notification](images/1_51/open-workspace.png)

### Prevent accidental close

A new setting `window.confirmBeforeClose` was added to show a confirmation dialog before closing or leaving the workbench.

Possible values are:

* `keyboardOnly` The confirmation will only be shown when you use a keybinding to close (for example, `kb(workbench.action.closeWindow)`). (default)
* `always`: The confirmation dialog will always be shown, even if you close from a mouse gesture.
* `never`: The confirmation will never be shown.

![Close confirmation dialog](images/1_51/web-confirmation.png)

**Note**: This setting may not cover all cases. Browsers may still decide to close a tab or window without confirmation.

## Contributions to extensions

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

To learn about all the new features and updates, you can see the full [changelog for the 0.21.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0210) release of the extension.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.51 include:

* Ability to persist/reconnect to terminal sessions.
* Improved port forwarding experience.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_51.md).

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### Settings sync

Settings sync now synchronizes extensions' global state. Extensions will have to provide the state to sync using the newly introduced [setKeysForSync](#Sync-Global-State) API.

### Remember proxy credentials

We are overhauling the login dialog that shows when a network connection requires authentication with a proxy. A new setting, `window.enableExperimentalProxyLoginDialog: true`, will enable this new experience that we plan to make the default in a future release.

![Proxy Login](images/1_51/proxy-login.png)

*Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme)*

The dialog will appear inside the VS Code window and offer a way to remember the credentials so that you do not have to provide them each time you start VS Code. Credentials will be stored in the OS standard credential store (keychain on macOS, Windows Credential Manager on Windows, and gnome keyring on Linux).

We still only show this dialog once per session, but might revisit this decision in the future. You will see the dialog appear again in case the credentials you selected to be remembered are not valid. Providing them again allows you to change them.

Please enable this option and let us know if something does not work as expected through our [issue tracker](https://github.com/microsoft/vscode/issues).

### TypeScript 4.1 beta support

VS Code supports the [TypeScript 4.1 beta](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/) and nightly builds. The 4.1 update brings some new TypeScript language features, such as support for [recursive conditional types](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/#recursive-conditional-types), as well as tooling improvements. One focus area has been [adding initial support](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/#jsdoc-see-tag) for `@see` tags in JSDoc comments.

To start using the TypeScript 4.1 nightly builds, just install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with TypeScript 4.1.

## Extension authoring

### Updated extension samples

We've updated some of our [extension samples](https://github.com/microsoft/vscode-extension-samples) to include VS Code default styles that are hooked up to our color theme tokens. This means that common elements (text, buttons, inputs) will be themeable and match the default styles in the product. Below are the extensions that include this:

* [custom-editor-sample](https://github.com/microsoft/vscode-extension-samples/tree/main/custom-editor-sample)
* [webview-sample](https://github.com/microsoft/vscode-extension-samples/tree/main/webview-sample)
* [webview-view-sample](https://github.com/microsoft/vscode-extension-samples/tree/main/webview-view-sample)

![Webview style samples](images/1_51/webview-styles.gif)

### Codicon colors in trees

With the finalization of the [`ThemeIcon` color API](https://github.com/microsoft/vscode/blob/5dbb54cab77218f8f722c49fb10dfe44fd909298/src/vs/vscode.d.ts#L821-L826), extension authors can use theme colors on codicons in custom tree views.

![Tree view with icon colors](images/1_51/tree-icon-color.png)

### Sync Global State

Extensions can now sync their global state by providing the keys, whose values should be synchronized when Settings Sync is enabled, using the newly introduced `setKeysForSync` API in `globalState` memento.

```ts
/**
 * Set the keys whose values should be synchronized across devices when synchronizing user-data
 * like configuration, extensions, and mementos.
 *
 * Note that this function defines the whole set of keys whose values are synchronized:
 *  - calling it with an empty array stops synchronization for this memento
 *  - calling it with a non-empty array replaces all keys whose values are synchronized
 *
 * For any given set of keys this function needs to be called only once but there is no harm in
 * repeatedly calling it.
 *
 * @param keys The set of keys whose values are synced.
 */
setKeysForSync(keys: string[]): void;
```

### Comment thread reply button visibility

[Comment](https://code.visualstudio.com/api/references/vscode-api#comments) extensions can now control the visibility of the reply button in a comment thread with a new property, `CommentThread#canReply`. When it's set to `false`, users will not see the reply button or comment box in the comment thread.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Folding range providers change event

Folding range providers can signal to the editor that folding ranges need to be updated using the `onDidChangeFoldingRanges` event.

For more details and to provide feedback, please use [issue #108929](https://github.com/microsoft/vscode/issues/108929).

### Password APIs

As part of continued work on [Authentication Providers](https://github.com/microsoft/vscode/issues/88309), we have introduced API for storing and retrieving sensitive information. Internally, this acts as a wrapper of the [keytar](https://github.com/atom/node-keytar) library that VS Code uses for storing secrets.

```ts
	/**
	 * Retrieve a password that was stored with key. Returns undefined if there
	 * is no password matching that key.
	 * @param key The key the password was stored under.
	 */
	export function getPassword(key: string): Thenable<string | undefined>;

	/**
	 * Store a password under a given key.
	 * @param key The key to store the password under
	 * @param value The password
	 */
	export function setPassword(key: string, value: string): Thenable<void>;

	/**
	 * Remove a password from storage.
	 * @param key The key the password was stored under.
	 */
	export function deletePassword(key: string): Thenable<void>;

	/**
	 * Fires when a password is set or deleted.
	 */
	export const onDidChangePassword: Event<void>
```

## Engineering

### Making VS Code Trusted Types compliant

We have continued the effort to make VS Code "Trusted Types" compliant. The goal is to prevent DOM-based cross site scripting vulnerabilities. You can learn more about trusted types at the web.dev [Trusted Types site](https://web.dev/trusted-types) and follow our progress in [issue #103699](https://github.com/microsoft/vscode/issues/103699).

## Documentation

### Getting started

We are focusing on ways to make getting started with the editor easier. We've created a new ["Learn to Code" landing page](https://code.visualstudio.com/learntocode) on our website with coding packs and new content geared towards folks who are new to coding. We've also created new student-friendly videos and resources on the site at [code.visualstudio.com/learn](https://code.visualstudio.com/learn).

![Learn to code](images/1_51/learn-to-code.png)

## New commands

| Key                                               | Command                                       | Command ID                                    |
| ------------------------------------------------- | --------------------------------------------- | --------------------------------------------- |
| `kb(workbench.action.focusLeftGroupWithoutWrap)`  | Focus Left Editor Group (do not wrap around)  | `workbench.action.focusLeftGroupWithoutWrap`  |
| `kb(workbench.action.focusRightGroupWithoutWrap)` | Focus Right Editor Group (do not wrap around) | `workbench.action.focusRightGroupWithoutWrap` |
| `kb(workbench.action.focusAboveGroupWithoutWrap)` | Focus Above Editor Group (do not wrap around) | `workbench.action.focusAboveGroupWithoutWrap` |
| `kb(workbench.action.focusBelowGroupWithoutWrap)` | Focus Below Editor Group (do not wrap around) | `workbench.action.focusBelowGroupWithoutWrap` |
| `kb(workbench.action.focusActivityBar)`           | Focus Activity Bar                            | `workbench.action.focusActivityBar`           |

## Notable fixes

* [43819](https://github.com/microsoft/vscode/issues/43819): Windows: when deleting a folder with files that are in use, then there is no error message shown
* [71315](https://github.com/microsoft/vscode/issues/71315): Should maintain row focus after deleting a file
* [96522](https://github.com/microsoft/vscode/issues/96522): User input variables not working for launch configuration in multi-root workspace
* [100255](https://github.com/microsoft/vscode/issues/100255): Moving a file should load the model directly with the contents it had before
* [108578](https://github.com/microsoft/vscode/issues/108578): Extensions with a onDebugDynamicConfigurations are eagerly activated at startup in v1.50
* [109088](https://github.com/microsoft/vscode/issues/109088): Send `vscode.workspace.onWillCreateFiles/onDidCreateFiles` events for folders
* [109226](https://github.com/microsoft/vscode/issues/109226): Debug hover moves while expanding/collapsing

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

Contributions to `vscode`:

* [Justin Steven (@justinsteven)](https://github.com/justinsteven): (Fixed in 1.49.3) Fix for CVE-2020-16881 can be bypassed [PR #107952](https://github.com/microsoft/vscode/pull/107952)
* [Ashkan (@a5hk)](https://github.com/a5hk): closes #97890 [PR #108779](https://github.com/microsoft/vscode/pull/108779)
* [Daniel Huth (@Agreon)](https://github.com/Agreon): Add Non-Wrapping EditorGroup-focus actions [PR #108071](https://github.com/microsoft/vscode/pull/108071)
* [Andrey Sinitsyn (@asn007)](https://github.com/asn007): fix(git): fatal when adding, reverting files or cleaning repo on win32 [#108690] [PR #108691](https://github.com/microsoft/vscode/pull/108691)
* [Jordan Bayles (@baylesj)](https://github.com/baylesj): Add Git: Clone Recursively option [PR #109133](https://github.com/microsoft/vscode/pull/109133)
* [Takanori Oishi (@bicstone)](https://github.com/bicstone)
  * UI items are incorrect order when applying the language pack [PR #109433](https://github.com/microsoft/vscode/pull/109433)
  * Git: Add tags to '...' menu [PR #109282](https://github.com/microsoft/vscode/pull/109282)
* [Borja Zarco (@bzarco)](https://github.com/bzarco): Fix launch configuration input variable resolution. [PR #97440](https://github.com/microsoft/vscode/pull/97440)
* [Dhairya Nadapara (@dhairyanadapara)](https://github.com/dhairyanadapara): added preserve case and excluse setting in FindInFile interface [PR #107910](https://github.com/microsoft/vscode/pull/107910)
* [Evan Krause (@evangrayk)](https://github.com/evangrayk): Don't focus editor when un-expanded comment is hidden [PR #97101](https://github.com/microsoft/vscode/pull/97101)
* [Fons van der Plas (@fonsp)](https://github.com/fonsp): message "Would you like to stage all chages?" [PR #109272](https://github.com/microsoft/vscode/pull/109272)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray): fix #108673 Put FileSystemProvider error message into settings.json create-failure notification [PR #108694](https://github.com/microsoft/vscode/pull/108694)
* [matvii (@hodovani)](https://github.com/hodovani): Replace `map` with `forEach` [PR #109217](https://github.com/microsoft/vscode/pull/109217)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fixes middle-clicking on a notification sometimes pastes the contents of the clipboard [PR #109349](https://github.com/microsoft/vscode/pull/109349)
  * Fixes cannot disable file auto save when configuration target is other than user [PR #109278](https://github.com/microsoft/vscode/pull/109278)
  * Read file contents as stream in ChangeEncodingAction [PR #108052](https://github.com/microsoft/vscode/pull/108052)
  * Fixes outline view element overflow [PR #108038](https://github.com/microsoft/vscode/pull/108038)
* [Kenny Smith (@kjs3)](https://github.com/kjs3): Trash/delete keybinding for forward delete on MacOS [PR #108863](https://github.com/microsoft/vscode/pull/108863)
* [Li Xueli (@mixj93)](https://github.com/mixj93): fix: Remove extra whitespace in the untitled editor label [PR #108039](https://github.com/microsoft/vscode/pull/108039)
* [@Nafana](https://github.com/Nafana): Markdown reference links starting with ^ should not be clickable [PR #108015](https://github.com/microsoft/vscode/pull/108015)
* [Pierre Papin (@pi-r-p)](https://github.com/pi-r-p): fix download issues in webviews [PR #108603](https://github.com/microsoft/vscode/pull/108603)
* [Rakib Fiha (@RakibFiha)](https://github.com/RakibFiha): Changed shebang same as code.sh [PR #109372](https://github.com/microsoft/vscode/pull/109372)
* [Ryan Clarke (@ryanclarke)](https://github.com/ryanclarke): Add new property to IConfigurationPropertySchema [PR #108120](https://github.com/microsoft/vscode/pull/108120)
* [Sebastian Andil (@selrond)](https://github.com/selrond): Fix `No Nerify` typo inside git package [PR #108329](https://github.com/microsoft/vscode/pull/108329)
* [Simon Siefke (@SimonSiefke)](https://github.com/SimonSiefke): fix typo: eventLister -> eventListener [PR #108066](https://github.com/microsoft/vscode/pull/108066)
* [Tomer Stav (@tomerstav)](https://github.com/tomerstav)
  * Rebase current branch onto another branch [PR #108913](https://github.com/microsoft/vscode/pull/108913)
  * Add optional parameter to showOptions called newEditorGroup [PR #107555](https://github.com/microsoft/vscode/pull/107555)
    * Note: This ended up not being merged, but we appreciate the work nonetheless.
* [Tony Xia (@tony-xia)](https://github.com/tony-xia): Persisten -> Persistent [PR #108389](https://github.com/microsoft/vscode/pull/108389)
* [@turara](https://github.com/turara): Add keybinding shortcut for "Preserve case" replace option [PR #107597](https://github.com/microsoft/vscode/pull/107597)
* [@vivekmthr](https://github.com/vivekmthr): CodeLens activated on mouse up #107736 [PR #108323](https://github.com/microsoft/vscode/pull/108323)

Contributions to `vscode-json-languageservice`:

* [Albert Nigmatzianov (@bogem)](https://github.com/bogem): Improve README [PR #78](https://github.com/microsoft/vscode-json-languageservice/pull/78)

Contributions to `vscode-html-languageservice`:

* [Jaime Oliveira (@IxquitilisSaid)](https://github.com/IxquitilisSaid): Update beautify-html wrap_attributes documentation [PR #92](https://github.com/microsoft/vscode-html-languageservice/pull/92)
* [Nicholas Steven Darmawan (@steve1998)](https://github.com/steve1998): Implement Hover for HTML Entities [PR #89](https://github.com/microsoft/vscode-html-languageservice/pull/89)

Contributions to `vscode-css-languageservice`:

* [@ShPelles](https://github.com/ShPelles): [scss] correctly identify an element with id (div#id) [PR #222](https://github.com/microsoft/vscode-css-languageservice/pull/222)

Contributions to `vscode-eslint`:

* [Brandon Mills (@btmills)](https://github.com/btmills): Add markdown to eslint.probe default docs [PR #925](https://github.com/microsoft/vscode-eslint/pull/925)
* [Brad Zacher (@bradzacher)](https://github.com/bradzacher): support remote development by indicating this is a workspace-only extension [PR #1084](https://github.com/microsoft/vscode-eslint/pull/1084)
* [Clément Tessier (@ctessier)](https://github.com/ctessier): fix typos in README.md [PR #1105](https://github.com/microsoft/vscode-eslint/pull/1105)

Contributions to `language-server-protocol`:

* [@KamasamaK](https://github.com/KamasamaK)
  * Add missing capabilities for callHierarchy [PR #1105](https://github.com/microsoft/language-server-protocol/pull/1105)
  * Add some missing items to change log [PR #1106](https://github.com/microsoft/language-server-protocol/pull/1106)
* [Remy Suen (@rcjsuen)](https://github.com/rcjsuen): Correct the slash to textDocument/semanticTokens [PR #1111](https://github.com/microsoft/language-server-protocol/pull/1111)
* [Andreas Matthias (@AndreasMatthias)](https://github.com/AndreasMatthias): Escape dollar signs. [PR #1124](https://github.com/microsoft/language-server-protocol/pull/1124)

Contributions to `vscode-emmet-helper`:

* [Yasar Siddiqui (@yasarsid)](https://github.com/yasarsid): For Emmet expansion of d: should expand to "display: block" instead of "display: grid" [PR #35](https://github.com/microsoft/vscode-emmet-helper/pull/35)

Contributions to `lsif-node`:

* [Noah Santschi-Cooney (@Strum355)](https://github.com/Strum355): lsif-util: Fix #70 by replacing \\" with \" in vertex extra info sections [PR #112](https://github.com/microsoft/lsif-node/pull/112)

Contributions to `debug-adapter-protocol`:

* [Jonah Graham (@jonahgraham)](https://github.com/jonahgraham): Bug #122: requestId for ProgressStartEvent is an integer [PR #123](https://github.com/microsoft/debug-adapter-protocol/pull/123)
* [Mathias Fußenegger (@mfussenegger)](https://github.com/mfussenegger): Add nvim-dap to tools page [PR #140](https://github.com/microsoft/debug-adapter-protocol/pull/140)
* [Suzy Mueller (@suzmue)](https://github.com/suzmue): Update Go debug extension info in adapters.md [PR #142](https://github.com/microsoft/debug-adapter-protocol/pull/142)
* [Ethan Reesor (@firelizzard18)](https://github.com/firelizzard18): Add byebug-dap and ruby-dap [PR #144](https://github.com/microsoft/debug-adapter-protocol/pull/144)

Contributions to `vscode-vsce`:

* [James George (@jamesgeorge007)](https://github.com/jamesgeorge007): fix: handle unknown args [PR #503](https://github.com/microsoft/vscode-vsce/pull/503)

Contributions to `vscode-js-debug`:

* [hp8wvvvgnj6asjm7](https://github.com/hp8wvvvgnj6asjm7): Debugger does not work when running Node.js on an unsupported operating system [PR #791](https://github.com/microsoft/vscode-js-debug/pull/791)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>

<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
