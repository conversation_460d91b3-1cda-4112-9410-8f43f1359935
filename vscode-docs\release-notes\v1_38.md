---
Order:
TOCTitle: August 2019
PageTitle: Visual Studio Code August 2019
MetaDescription: See what is new in the Visual Studio Code August 2019 Release (1.38)
MetaSocialImage: 1_38/release-highlights.png
Date: 2019-09-04
DownloadVersion: 1.38.1
---
# August 2019 (version 1.38)

**Update 1.38.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+Recovery+2019%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the August 2019 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Preserve case for global search and replace](#preserve-case-in-search-and-replace)** - Keep letter casing across multi-file search/replace.
* **[Settings editor string array validation](#validation-for-string-array-settings)** - Checks min, max, enum values, and glob patterns.
* **[Adjust cursor surrounding lines](#cursor-surrounding-lines-scrolloff)** - Keep your cursor centered in the editor.
* **[Copy and revert in the diff editor](#copy-and-revert-in-the-inline-diff-editor)** - Easily copy or restore deleted content.
* **[Go to Line supports negative line numbers](#go-to-line-supports-negative-line-numbers)** - Quickly jump to the end of a file.
* **[MDN Reference link for HTML and CSS](#mdn-reference-for-html-and-css)** - Links to MDN documentation directly from IntelliSense.
* **[Add missing await Quick Fix](#add-missing-await-quick-fix)** - Find overlooked awaits in asynchronous code.
* **[Debugging data breakpoints](#breaking-when-value-changes-data-breakpoints)** - Debugger breaks when tracked values change.
* **[VS Code icon repository](#vs-code-icon-repository)** - Official product icons available for extension authors.
* **[Alpine Linux distro support](#remote-development-preview)** - Both for the WSL and Dev Containers extensions.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to see new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Workbench

### Preserve case in Search and Replace

In the 1.37 release, we added a **Preserve Case** option to the editor's Find/Replace control. The same option is now available as the **AB** icon in global search and replace.

There is also a new case preservation mode for hyphen-separated words.

![Preserve Case button in search](images/1_38/search-preserve-case.png)

### Updated cancel search icon

When running long searches, we've updated the icon used to cancel a search to better represent the action.

![Cancel search icon](images/1_38/cancel-search-icon.png)

### Validation for string array settings

The settings editor now displays errors for array-of-string items if the current value does not satisfy the specified `minItems`, `maxItems`, `item.enum`, or `item.pattern` schema.

![String of array error validation](images/1_38/string-of-array-error-validation.png)

### Link protection for outgoing links

VS Code now shows a prompt before opening outgoing links.

![Link protection prompt](images/1_38/link-protection.gif)

You can open the link directly or trust specific domains to bypass this prompt. With the **Configure Trusted Domains** button or command, you can enable/disable link protection for specific domains or all outgoing links.

### Explorer improvements

**Top-level resource creation**

We have improved the process of creating top-level files and folders in the Explorer. There are now three ways to create a file or folder in the root of the File Explorer:

* Scrolling beyond the last element in order to invoke the context menu on the empty space.
* Pressing `kbstyle(Escape)` clears both focus and selection in the Explorer thus making the title area actions execute on the Explorer root.
* Using a context menu on the scroll bar.

**Open file and preserve focus**

It is now possible to open a file in preview from the Explorer while keeping focus in the Explorer. The new command is `filesExplorer.openFilePreserveFocus` and by default, it can be triggered via the `kbstyle(Space)` key.

**Explorer file naming**

There is a new setting, `explorer.incrementalNaming`, to control duplicate file naming, which can have the values `simple` or `smart`.

* `simple` - Appends the word "copy" at the end of the duplicated name potentially followed by a number. This is the current VS Code stable behavior.
* `smart` - Adds a number at the end of the duplicated name. If a number is already part of the name, increment that number.

### Maximize editor toggle command

A new command **Toggle Editor Group Sizes** (`workbench.action.toggleEditorWidths`) will toggle between maximizing the active editor group and evening out the editor group widths.

### Grid layout for the workbench

The layout engine of the workbench has been rewritten to use the same grid widget as the editor area itself. This work has been in progress for some time and Insiders builds have had this enabled by default for the past couple iterations. The setting is `workbench.useExperimentalGridLayout` and will now be enabled by default.

**Toggling the editor area / Maximizing the panel**

The new engine provides more flexibility for the workbench layout in the future. For now, this manifests itself with the ability to hide the editor area. There is a new command available with the grid layout, **Toggle Editor Area** that will hide the editor area and allow the panel (for example Output or Debug console) to fill the layout. The **Toggle Maximized Panel** command also has the same effect. This means that a maximized panel is now truly maximized without the tiny gap previously left of the editor.

Below you can see the maximized Integrated Terminal:

![Maximized panel](images/1_38/maximized-panel.png)

The editor will automatically reappear if you try to open a file from anywhere such as the panel or sidebar.

### Hide individual macOS Touch Bar entries

A new setting `keyboard.touchbar.ignored` can selectively remove VS Code commands from the macOS Touch Bar. VS Code adds the following commands by default:

* `workbench.action.navigateBack`
* `workbench.action.navigateForward`
* `workbench.action.debug.start`
* `workbench.action.debug.run`
* `workbench.action.debug.continue`
* `workbench.action.debug.pause`
* `workbench.action.debug.stepOver`
* `workbench.action.debug.stepInto`
* `workbench.action.debug.stepOut`
* `workbench.action.debug.restart`
* `workbench.action.debug.stop`

### New editor group context keys

There are two new when clause contexts for conditionally binding keyboard shortcuts:

* `activeEditorGroupIndex` - A number starting from `1` reflecting the position of an editor group in the editor grid. The group with index `1` will be the first in the top-left corner.
* `activeEditorGroupLast` - Will be `true` for the last editor group in the editor grid.

**Note:** See [when clause contexts reference](https://code.visualstudio.com/api/references/when-clause-contexts) for a full list of when clause contexts.

### Accessibility improvements

We continue to fix [accessibility issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+label%3Aaccessibility+milestone%3A%22August+2019%22+is%3Aclosed) in this milestone. Highlights are:

* Word navigation now follows what NVDA and VoiceOver expect. Words are properly announced when navigating using word navigation in the editor.
* The Windows Magnifier Tool can now follow the cursor in the editor.

### Screencast mode

There have been several improvements to the Screencast mode (**Developer: Toggle Screencast Mode**):

* There's now a limit on the length of the screencast keybinding label.
* The keybinding label vertical position can be adjusted via the `screencastMode.verticalOffset` setting.
* The keybinding label can be configured to render only actual keyboard shortcuts via the `screencastMode.onlyKeyboardShortcuts` setting.
* The keyboard shortcuts render style was improved.

## Editor

### Cursor surrounding lines (scrollOff)

You can now customize the number of visible lines to display around the cursor when moving the cursor towards the beginning or end of a file by setting `editor.cursorSurroundingLines`. In the Vim editor, this feature is called `scrollOff`.

![Cursor surrounding lines](images/1_38/cursor-surrounding-lines.gif)

### Multi-line search in Find

The editor Find control now supports multiple line text search and replace. By pressing `kbstyle(Ctrl+Enter)`, you can insert new lines into the input box.

![Multi-line search in the editor](images/1_38/multiple-line-support.gif)

### Copy and revert in the inline diff editor

There are new actions for copying or reverting deleted content when using the inline diff editor. Hover on deleted content in the inline diff editor and you can now:

* Copy the whole deleted content
* Copy a specific line
* Revert the change

![Diff actions](images/1_38/diff-actions.gif)

### Go to Line supports negative line numbers

You can now type in negative line numbers to the **Go to Line** picker to navigate from the end of the file. For example, typing `-1` will reveal the last line of the file.

### Global search minimap decorations

The minimap (code overview) now shows search results for searches both within a file and global search.

## Integrated Terminal

### Improved fallback behavior when using variables in terminal.integrated.cwd

When using variables in the `cwd` setting such as:

```json
"terminal.integrated.cwd": "${fileDirname}"
```

instead of throwing an error when a variable cannot be resolved, the terminal will now log an error to the console and fallback to the workspace directory.

### Automation shell setting

In addition to setting the shell for the Integrated Terminal, you can now specify a shell for automation (such as Tasks). This is particularly useful if you use tmux as your shell since that doesn't work with all automation.

```json
"terminal.integrated.shell.osx": "/Users/<USER>/bin/tmux_script"
"terminal.integrated.automationShell.osx": "bash",
```

## Languages

### MDN Reference for HTML and CSS

VS Code now displays a URL pointing to the relevant MDN Reference in completion and hover of HTML & CSS entities:

![HTML & CSS MDN Reference](images/1_38/html-css-mdn-reference.gif)

We thank the MDN documentation team for their effort in curating [mdn-data](https://github.com/mdn/data) / [mdn-browser-compat-data](https://github.com/mdn/browser-compat-data) and making MDN resources easily accessible by VS Code.

### Improved Less support

VS Code now supports many new Less.js features including root functions, map lookups and anonymous mixins.

### Deprecation mark for nonstandard and obsolete CSS properties

The CSS language server adopts the [Deprecation Tag](https://code.visualstudio.com/updates/v1_38#_deprecation-tags-for-symbols-and-completions) experimental API and shows a deprecation mark for CSS properties marked as `nonstandard` or `obsolete` in auto completion.

![CSS deprecated properties](images/1_38/css-deprecated-properties.png)

### TypeScript 3.6

VS Code now ships with TypeScript 3.6.2. This major update brings some TypeScript language improvements—including [stricter generators](https://devblogs.microsoft.com/typescript/announcing-typescript-3-6/#stricter-generators) and [support for `import.meta`](https://devblogs.microsoft.com/typescript/announcing-typescript-3-6/#import-meta-support-in-systemjs), as well as some new tooling features for both JavaScript and TypeScript. As always, this release also includes a number of important bug fixes.

You can read more about the TypeScript 3.6 features on the [TS 3.6 blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-3-6/).

### Add missing await Quick Fix

Is your code getting a little ahead of itself? The new **Add 'await'** Quick Fix helps you `await` values that you may have overlooked in asynchronous code:

![Detect missing calls to await in JavaScript using @ts-check and Quick Fix them](images/1_38/js-await-quickfix.gif)

This Quick Fix is available in TypeScript and JavaScript source code that has [type checking enabled](https://code.visualstudio.com/Docs/languages/javascript#_type-checking).

### Semicolon aware editing for JavaScript and TypeScript

When you add an import or apply a refactoring in JavaScript or Typescript source code, VS Code now tries to infer whether or not to  include semicolons from existing code in the file:

![No semicolons](images/1_38/js-no-semicolon.gif)

You can find the details of how this semicolon detection works on the [pull request that added this feature](https://github.com/microsoft/TypeScript/pull/31801). Some Quick Fixes and refactorings as still not semicolon aware, but we will be working to address this in future updates.

### JSDoc comments no longer merge for IntelliSense

Previously, our JavaScript and TypeScript IntelliSense would combine multiple JSDoc comments if they appeared before a definition. In the example below, notice how the documentation for the `User` type is merged with that of the `getUser` function:

![Older TS versions merged multiple JSDoc comments](images/1_38/js-jsdoc-merge-before.png)

With TypeScript 3.6, our IntelliSense now only uses the immediately preceding JSDoc comment for documentation and typing information:

![TS 3.6 only uses the immediately preceding JSDoc comment](images/1_38/js-jsdoc-merge-after.png)

## Source Control

### Git: Branch name on commit input

The current Git branch name now appears in the commit input box, to avoid committing on the wrong branch:

![Git branch name in input box](images/1_38/branch-name.png)

### Git: Sort branch list alphabetically

There's a new `git.branchSortOrder` setting to change the order of branches when checking out to a branch with the **Git: Checkout to...** command.

### Git: Support pull cancellation

When enabling the `git.supportCancellation` setting, you'll have the opportunity to cancel ongoing **Git Pull** requests, which is useful when pulling from slow remotes.

## Debugging

### Breaking when value changes (Data Breakpoints)

From the Variables view, it is now possible to create data breakpoints that will get hit when the value of the underlying variable changes. Just like other breakpoints, data breakpoints can be disabled/enabled and removed in the Breakpoints view.

![Data breakpoints](images/1_38/data-breakpoints.png)

Please note that data breakpoints require specific support by an underlying runtime or debugger, and we expect only a few debug extensions like C++ and C# (but not Node.js) to opt into this feature in the future. For this release, only our example debugger [Mock Debug](https://marketplace.visualstudio.com/items?itemName=andreweinand.mock-debug) "mocks" data breakpoints.

### Call Stack view improvements

We have done some improvements to the Call Stack view, most notably:

* When a debug session has just one thread, we will always hide the thread and show the call stack directly.
* Clicking on the threads and debug sessions in the Call Stack view no longer expands / collapses them. Only an explicit click on the twistie will do the expansion. The reason for this change is that it was tedious to change the focused session without doing some unwanted expansion. The same behavior can be seen in our Outline view.
* Previously when there was a new debug session, the Debug view would get focus. Now the Debug view gets focus only if a session actually breaks.
* Debug session elements only display a twistie when there are actually threads for that session.

### New shell setting for launching a debug target

When launching a debug target in the Integrated Terminal, VS Code now respects the new "shell for automation" setting (`terminal.integrated.automationShell...`). This setting is useful if you use a specific default shell for the Integrated Terminal (for example, tmux) that does not work with automation, when launching a debug target.

```json
"terminal.integrated.shell.osx": "/Users/<USER>/bin/tmux_script"
"terminal.integrated.automationShell.osx": "bash",
```

## Contributions to extensions

### Helping webview extensions add a Content Security Policy

We've identified a number of extensions that create [Webviews](https://code.visualstudio.com/api/extension-guides/webview) that don't have a [Content Security Policy](https://developer.mozilla.org/docs/Web/HTTP/CSP). While this does not present an immediate concern, all webview should have a content security policy as a good security best practice. This iteration, we've [started to open issues](https://github.com/microsoft/vscode/issues/79340) against these extensions to make them aware of the recommendation.

If you are interested in making some of the extensions you use everyday a bit more secure, take a look at [VS Code issue #79340](https://github.com/microsoft/vscode/issues/79340) and submit PRs to help them out.

### GitHub Pull Requests

This milestone we continued working on improvements to the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, including bug fixes and features like **Delete branch and remote after merging a pull request**. See our [August Milestone plan](https://github.com/microsoft/vscode-pull-request-github/milestone/5?closed=1) for more details.

### Remote Development (Preview)

Work has continued on the [Remote Development](https://aka.ms/vscode-remote/download/extension) extensions, which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

To help get you started with the Remote Development extensions, there are three introductory tutorials:

* [Dev Containers](https://code.visualstudio.com/docs/devcontainers/tutorial) - Run Visual Studio Code in a Docker Container.
* [Remote via SSH](https://code.visualstudio.com/docs/remote/ssh-tutorial) - Connect to remote and virtual machines with Visual Studio Code via SSH.
* [Working in WSL](https://code.visualstudio.com/docs/remote/wsl-tutorial) - Run Visual Studio Code in Windows Subsystem for Linux.

You can also read a recent blog post describing [Tips and Tricks for Linux development with WSL and Visual Studio Code](https://devblogs.microsoft.com/commandline/tips-and-tricks-for-linux-development-with-wsl-and-visual-studio-code/).

Feature highlights in 1.38 include:

* VS Code Stable preview support for Alpine Linux Containers, Alpine WSL distributions, and ARMv7l / AArch32 SSH hosts.
* VS Code Insiders experimental support for ARMv8l / AArch64 SSH hosts.
* Improvements to Dev Containers including a new container explorer!

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_38.md).

## Extension authoring

### VS Code icon repository

We've published a repository of all of the [VS Code icons](https://github.com/microsoft/vscode-icons) for use by extension authors. There are dark/light versions of each icon, and we also linked to our [Figma design file](https://www.figma.com/file/7wJdjbq2rKZffZqPdz6OjN/VS-Code-Icons-%E2%80%94-Public?node-id=0%3A1).

![VS Code icons](images/1_38/vscode-icons-repo.png)

### Webview.asWebviewUri and Webview.cspSource

There are two new properties on webviews:

* `Webview.asWebviewUri` - Convert a URI for the local file system to one that can be used inside webviews.

  For desktop VS Code, this will convert `file:` URIs into `vscode-resource:` URIs.

* `Webview.cspSource` - The content security policy source for webview resources.

  For desktop VS Code, this would be the string `vscode-resource:`.

```ts
const panel = vscode.window.createWebviewPanel(CatCodingPanel.viewType, 'Cat Coding', vscode.ViewColumn.One, {
    // Restrict the webview to only loading local content from our extension's `media` directory.
    localResourceRoots: [vscode.Uri.file(path.join(extensionPath, 'media'))]
});

const imagePath = vscode.Uri.file(path.join(extensionPath, 'media'));

panel.html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${panel.webview.cspSource} https:;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cat Coding</title>
</head>
<body>
    <img src="${panel.webview.asWebviewUri(imagePath)}/cat.gif" width="300" />
</body>
</html>`
```

### Warning when creating webview without a Content Security Policy

While developing an extension that uses the [Webview API](https://code.visualstudio.com/api/extension-guides/webview), we now log a warning when you create a webview that does not set a [Content Security Policy](https://developer.mozilla.org/docs/Web/HTTP/CSP).

![Debug warning for a webview that lacks a CSP](images/1_38/webview-csp-warning.png)

All webviews (even very simple ones) should set a content security policy. This helps limit the potential impact of content injections and is generally a good measure for defense in depth. We've documented how to add a content security policy to VS Code webviews in the [Webview extension guide](https://code.visualstudio.com/api/extension-guides/webview#content-security-policy).

### Machine-specific overridable settings

You can now define a machine specific setting that can be overridable at workspace and folder level using the scope `machine-overridable`.

```json
"configuration": {
  "title": "My Extension Settings",
  "properties": {
   "myextension.libPath": {
      "type": [
        "string",
        "null"
      ],
      "markdownDescription": "Specify the path to the library.",
      "default": null,
      "scope": "machine-overridable"
    }
  }
}
```

### Multi-select in custom tree view

Trees contributed through `createTreeView` can now add the `canSelectMany` option to the `TreeViewOptions<T>`. This enables multi-select in the contributed tree view and causes commands that are run on tree elements to receive all the selected tree elements as an array in the second command argument.

### markdown.api.render

The new `markdown.api.render` command from VS Code's built-in Markdown extension takes a `string` of Markdown or a `vscode.TextDocument` and returns the rendered Markdown as HTML:

```ts
import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
  vscode.commands.executeCommand('markdown.api.render', '# Hello Markdown').then(result => {
    console.log(`rendered markdown: ${result}`);
  });
}
```

Logs:

```html
rendered markdown: <h1 id="hello-markdown" data-line="0" class="code-line">Hello Markdown</h1>
```

### Custom Data marked as Stable

The custom data format introduced as experimental feature in the [January 2019 1.31 release](https://code.visualstudio.com/updates/v1_31#_html-and-css-custom-data-support) is now marked as Stable.

* `html.experimental.customData` - This setting is deprecated and superseded by `html.customData`.
* `css.experimental.customData` - This setting is deprecated and superseded by `css.customData`.
* `contributes.html.experimental.customData` - This Contribution Point is deprecated and superseded by `contributes.html.customData`.
* `contributes.css.experimental.customData` - This Contribution Point is deprecated and superseded by `contributes.css.customData`.

You can learn more about how to use the custom data format to enhance VS Code's HTML/CSS language support in the documentation and samples at [microsoft/vscode-custom-data](https://github.com/microsoft/vscode-custom-data).

### Deprecated workspace.rootPath

When we added support for multi-root workspaces, we deprecated the API `workspace.rootPath` in favor of `workspace.workspaceFolders` and `workspace.getWorkspaceFolder`. We have noticed that many extensions still use this API, even though it won't work properly in multi-root workspaces. If you own an extension that uses this API, please update it as we may want to get rid of this API in the future. You can find more details about moving away from `rootPath` on the [Adopting-Multi-Root-Workspace-APIs wiki page](https://github.com/microsoft/vscode/wiki/Adopting-Multi-Root-Workspace-APIs#eliminating-rootpath).

## Debug Adapter Protocol

### Improvements for completion proposals

A debug adapter can now use the new capability `completionTriggerCharacters` for announcing the characters that a frontend UI should use to trigger the completion proposals UI in a REPL or debug console. If none is specified, the frontend UI should use the '.' character to trigger the proposal UI.

In addition, we've added an optional `sortText` attribute to the `CompletionItem` type. With this attribute, a debug adapter can control how the frontend UI sorts the completion proposals returned by the adapter. If the attribute is missing, the frontend may sort items based on the `label` attribute.

### Fixed a type issue in DAP's JSON schema

In the [Debug Adapter Protocol JSON schema](https://github.com/microsoft/debug-adapter-protocol/blob/gh-pages/debugAdapterProtocol.json), we've changed the use of the type specifier `number` to `integer` in those places where a float type makes no sense, for example for IDs.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project.

Note that you cannot publish an extension that uses a proposed API. We may likely make breaking changes in the next release and we never want to break existing extensions.

### Deprecation tags for symbols and completions

The API for completions and document/workspace symbols now supports marking items as deprecated. Completions and symbols have a new, optional property `tags` that is a set of `CompletionItemTag`s or `SymbolTag`s. Today, only deprecation is supported but there are plans to add more tags, for example tags for access modifiers and other modifiers.

The snippet below shows a minimal completion item provider that renders an item that's marked as deprecated.

```ts
vscode.languages.registerCompletionItemProvider('plaintext', {
  provideCompletionItems() {
    const item = new vscode.CompletionItem("deprecated_completion");
    item.tags = [vscode.CompletionItemTag.Deprecated];
    return [item];
  }
});
```

When showing deprecated completions or symbols a strikeout is rendered across the names, items are slightly dimmed, and highlights aren't shown:

![Deprecated items in smart completions](images/1_38/suggest-deprecated.png)

### Pseudoterminal.onDidClose now accepts a number

The proposed `Pseudoterminal.onDidClose` has changed from an `Event<void>` to an `Event<void | number>`, allowing extension authors to indicate that the terminal or `CustomExecution2` task failed.

### Deprecated proposed terminal APIs removed

The deprecated `TerminalOptions.runInBackground` and `createTerminalRenderer` APIs have been removed. If you need to migrate off of these, you should use `TerminalOptions.hideFromUser` (stable) and `ExtensionTerminalOptions` (proposed) respectively.

## New Commands

We now expose commands for navigating through search results in the Find control from the Editor, Integrated Terminal, Extension Details view, and Webviews.

Key|Command|Command ID
---|-------|----------
`kb(editor.action.nextMatchFindAction)`|Find Next Result in Editor|`editor.action.nextMatchFindAction`
`kb(editor.action.previousMatchFindAction)`|Find Previous Result in Editor|`editor.action.previousMatchFindAction`
`kb(workbench.action.terminal.findNext)`|Find Next Result in Integrated Terminal|`workbench.action.terminal.findNext`
`kb(workbench.action.terminal.findPrevious)`|Find Previous Result in Integrated Terminal|`workbench.action.terminal.findPrevious`
`kb(editor.action.extensioneditor.findNext)`|Find Next Result in Extension Details View|`editor.action.extensioneditor.findNext`
`kb(editor.action.extensioneditor.findPrevious)`|Find Previous Result in Extension Details View|`editor.action.extensioneditor.findPrevious`
`kb(editor.action.webvieweditor.findNext)`|Find Next Result in Webview|`editor.action.webvieweditor.findNext`
`kb(editor.action.webvieweditor.findPrevious)`|Find Previous Result in Webview|`editor.action.webvieweditor.findPrevious`

## Notable fixes

* [26012](https://github.com/microsoft/vscode/issues/26012): Automatic insertion of parens doesn't interact nicely with completions, results in duplicated characters
* [47292](https://github.com/microsoft/vscode/issues/47292): Diagnostics created by tasks aren't accessible to extensions
* [73884](https://github.com/microsoft/vscode/issues/73884): Linux: opening files from FTP connection show up empty
* [75054](https://github.com/microsoft/vscode/issues/75054): macOS simple fullscreen is working again
* [77293](https://github.com/microsoft/vscode/issues/77293): Respect the CancelationToken in provideDebugConfigurations and resolveDebugConfigurations to cancel debugging
* [77735](https://github.com/microsoft/vscode/issues/77735): workspace.applyEdit drops the TextEdit if there is a RenameFile later
* [77747](https://github.com/microsoft/vscode/issues/77747): file name with `$(xx)` displayed incorrectly in folder view
* [77990](https://github.com/microsoft/vscode/issues/77990): Starting with a TS/JS file is significantly file slower than opening other files
* [77996](https://github.com/microsoft/vscode/issues/77996): File > Save As moves cursor to beginning of file
* [78147](https://github.com/microsoft/vscode/issues/78147): Configure task action should reveal the newly added task and position the cursor there
* [78179](https://github.com/microsoft/vscode/issues/78179): Invoking 'tasks.fetchTask()' with a type filter fetches all tasks
* [79478](https://github.com/microsoft/vscode/issues/79478): Highlight custom html tags in markdown files
* [79704](https://github.com/microsoft/vscode/issues/79704): Support `@example <caption>` in JSDoc
* [79763](https://github.com/microsoft/vscode/issues/79763): Editors: can close all editors, even if dirty
* [79798](https://github.com/microsoft/vscode/issues/79798): Editors: group not activated with workbench.editor.revealIfOpen: true
* [78046](https://github.com/microsoft/vscode/issues/78046): SCM: Support staging selected changes for *new* files too, thanks to [Darrien Singleton (@OneThatWalks)](https://github.com/OneThatWalks) from [PR #78562](https://github.com/microsoft/vscode/pull/78562)
* [79625](https://github.com/microsoft/vscode/issues/79625): Provide code completion for the "preLaunchTask" structure

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to `vscode`:

* [Andy Hinkle (@ahinkle)](https://github.com/ahinkle): Check for updates only once at launch [PR #72169](https://github.com/microsoft/vscode/pull/72169)
* [Aidan Dang (@AidanGG)](https://github.com/AidanGG): Fix trivial zsh completion typo [PR #79420](https://github.com/microsoft/vscode/pull/79420)
* [Anton Kosyakov (@akosyakov)](https://github.com/akosyakov): [monaco] expose missing language providers [PR #78955](https://github.com/microsoft/vscode/pull/78955)
* [Andrew Casey (@amcasey)](https://github.com/amcasey): Strip a path list out of an error message [PR #78991](https://github.com/microsoft/vscode/pull/78991)
* [Aminadav Glickshtein (@Aminadav)](https://github.com/Aminadav): Fix typo in tasks jsonSchema [PR #79803](https://github.com/microsoft/vscode/pull/79803)
* [Russell Kennington (@arussellk)](https://github.com/arussellk): Fix #72587 suggestWidget no scroll on first open [PR #73625](https://github.com/microsoft/vscode/pull/73625)
* [Arash Arbabi (@ATheCoder)](https://github.com/ATheCoder): This fixes #78170 [PR #78327](https://github.com/microsoft/vscode/pull/78327)
* [Patrick Burke (@burknator)](https://github.com/burknator): Show head label in placeholder of commit message [PR #75295](https://github.com/microsoft/vscode/pull/75295)
* [Chris May (@chrismay)](https://github.com/chrismay): #56286 Add a configuration option to show/hide icons in the breadcrumbs view [PR #78879](https://github.com/microsoft/vscode/pull/78879)
* [Connor Peet (@connor4312)](https://github.com/connor4312): feat(markdown): add render command (fixes #75612) [PR #77151](https://github.com/microsoft/vscode/pull/77151)
* [Christopher Strack (@csk-ableton)](https://github.com/csk-ableton): Normalize paths when matching problems [PR #77875](https://github.com/microsoft/vscode/pull/77875)
* [Dan McCarthy (@Daniel-McCarthy)](https://github.com/Daniel-McCarthy): Updated git branch tooltip to display full branch name instead of "checkout..." [PR #72275](https://github.com/microsoft/vscode/pull/72275)
* [DiamondYuan (@DiamondYuan)](https://github.com/DiamondYuan)
  * storage - refactor: GlobalStorageDatabaseChannel should dependents in IStorageMainService [PR #79387](https://github.com/microsoft/vscode/pull/79387)
  * [wip]feat: support list.focusParent command [PR #77848](https://github.com/microsoft/vscode/pull/77848)
  * docs: fix typo restablished -> reestablished [PR #79129](https://github.com/microsoft/vscode/pull/79129)
* [Carson McManus (@dyc3)](https://github.com/dyc3): Fix #68849 "Screencast: Some keys do not get special treatment" [PR #69904](https://github.com/microsoft/vscode/pull/69904)
* [Phil Marshall (@flurmbo)](https://github.com/flurmbo)
  * Hide screencast overflow [PR #68048](https://github.com/microsoft/vscode/pull/68048)
  * Git prompt save only on staged [PR #67953](https://github.com/microsoft/vscode/pull/67953)
* [Gabriel DeBacker (@GabeDeBacker)](https://github.com/GabeDeBacker): Fix issue with CustomExecutions not working through tasks.executeTask [PR #79132](https://github.com/microsoft/vscode/pull/79132)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
  * Fix #79047 - Wasted stat call on workspace root  [PR #79052](https://github.com/microsoft/vscode/pull/79052)
  * Fix #79240 - Duplicated '(read-only)' suffix on titlebar name [PR #79241](https://github.com/microsoft/vscode/pull/79241)
* [Hung-Wei Hung (@hwhung0111)](https://github.com/hwhung0111): Feat. #77878 - trim git clone [PR #78504](https://github.com/microsoft/vscode/pull/78504)
* [Andrew Liu (@hypercubestart)](https://github.com/hypercubestart)
  * fix #78465 Markdown Preview scroll remains same after clicking on some other… [PR #79416](https://github.com/microsoft/vscode/pull/79416)
  * fix for 79704 - support for @example <caption> [PR #79846](https://github.com/microsoft/vscode/pull/79846)
* [Martin Jähn (@infmja)](https://github.com/infmja): Fix middle mouse button opening broken release notes link in browser [PR #76487](https://github.com/microsoft/vscode/pull/76487)
* [Yunlei Liu (@ipmsteven)](https://github.com/ipmsteven): Add only tracked files can be auto staged flag [Fix #70091] [PR #70539](https://github.com/microsoft/vscode/pull/70539)
* [Itamar (@itamark)](https://github.com/itamark): cancel hide if still hovered - solves #77490 [PR #78377](https://github.com/microsoft/vscode/pull/78377)
* [Jarnin Fang (@JarninFang)](https://github.com/JarninFang): Issue 78480: Clear Filter command link includes dot [PR #78982](https://github.com/microsoft/vscode/pull/78982)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Make centerEditorLayout do not call layout [PR #77743](https://github.com/microsoft/vscode/pull/77743)
  * Ignore double click on twistie [PR #78922](https://github.com/microsoft/vscode/pull/78922)
  * Don't auto expand if it's root in indexTreeModel [PR #79542](https://github.com/microsoft/vscode/pull/79542)
  * Force refresh on setInput in settingEditor [PR #78933](https://github.com/microsoft/vscode/pull/78933)
* [Jon Bockhorst (@jmbockhorst)](https://github.com/jmbockhorst): Use DOM scrollbar in SCM commit message box [PR #72171](https://github.com/microsoft/vscode/pull/72171)
* [João Ricardo Reis (@joaorreis13)](https://github.com/joaorreis13): Git: Support sync cancellation #59047 Issue Fix [PR #65212](https://github.com/microsoft/vscode/pull/65212)
* [Josh Leeb-du Toit (@joshleeb)](https://github.com/joshleeb): Add option to hide git sync button in status bar [PR #70536](https://github.com/microsoft/vscode/pull/70536)
* [Kamran Ayub (@kamranayub)](https://github.com/kamranayub): Strikeout deprecated CompletionItems [PR #78092](https://github.com/microsoft/vscode/pull/78092)
* [Leonardo Rochael Almeida (@leorochael)](https://github.com/leorochael): Toggle for disabling autofocus on debugger stop [PR #77213](https://github.com/microsoft/vscode/pull/77213)
* [Jiaxun Wei (@LeuisKen)](https://github.com/LeuisKen): fix: keep the two "Copy Path" behavior consistent [PR #79294](https://github.com/microsoft/vscode/pull/79294)
* [Marcus Noble (@MarcusNoble)](https://github.com/MarcusNoble): Add setting to make touchbar controls optional [PR #70174](https://github.com/microsoft/vscode/pull/70174)
* [@marmikc](https://github.com/marmikc): Fix Microsoft #71258 - Cannot undo empty commits [PR #72699](https://github.com/microsoft/vscode/pull/72699)
* [Matthew Kwiecien (@mattkwiecien)](https://github.com/mattkwiecien): Allowing a user to change screencast mode overlay position and height/width from settings. [PR #69314](https://github.com/microsoft/vscode/pull/69314)
* [@mayaswrath](https://github.com/mayaswrath): Issues/77879 [PR #79065](https://github.com/microsoft/vscode/pull/79065)
* [Max Belsky (@mbelsky)](https://github.com/mbelsky)
  * Use const instead of let [PR #79770](https://github.com/microsoft/vscode/pull/79770)
  * Polish breadcrumbs icons spacing [PR #79160](https://github.com/microsoft/vscode/pull/79160)
* [Mickael Istria (@mickaelistria)](https://github.com/mickaelistria): Issue #79599 - CSS LS fails if InitializationOptions.dataPaths not set [PR #79600](https://github.com/microsoft/vscode/pull/79600)
* [Mikhail Zaretski (@mIkhail-zaretsky)](https://github.com/mIkhail-zaretsky): 'suggestSmartCommit' configuration setting was introduced [PR #63743](https://github.com/microsoft/vscode/pull/63743)
* [@mkenigs](https://github.com/mkenigs)
  * Add setting to only display control characters [PR #69296](https://github.com/microsoft/vscode/pull/69296)
  * set screencast to display 15 characters [PR #67625](https://github.com/microsoft/vscode/pull/67625)
* [N.Z (@neesonqk)](https://github.com/neesonqk): Fixes #69240 [PR #69506](https://github.com/microsoft/vscode/pull/69506)
* [Micah Smith (@Olovan)](https://github.com/Olovan): Fix #72640 [PR #75213](https://github.com/microsoft/vscode/pull/75213)
* [Darrien Singleton (@OneThatWalks)](https://github.com/OneThatWalks)
  * Support staging for new files [PR #78562](https://github.com/microsoft/vscode/pull/78562)
  * Git clone fix to use basename [PR #61112](https://github.com/microsoft/vscode/pull/61112)
* [@qadram](https://github.com/qadram): #10027 confirmSave should check the goal of running an extension is a… [PR #78033](https://github.com/microsoft/vscode/pull/78033)
* [Mariana Costa (@Quendrique)](https://github.com/Quendrique): Git sync now publishes branch upon confirmation (#64183 issue fix) [PR #65247](https://github.com/microsoft/vscode/pull/65247)
* [Will Bender (@Ragnoroct)](https://github.com/Ragnoroct): Ignore empty string arg [PR #61957](https://github.com/microsoft/vscode/pull/61957)
* [Rich Evans (@rcbevans)](https://github.com/rcbevans)
  * Try resolve git branches beginning with '@' before replacing with symbolic full path [PR #66097](https://github.com/microsoft/vscode/pull/66097)
  * Git discard changes fails for large changesets with "ENAMETOOLONG" Fix for #65693 [PR #66095](https://github.com/microsoft/vscode/pull/66095)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan)
  * Fix-79525 New pattern to Preserve Case ( hyphen separated variables) [PR #79528](https://github.com/microsoft/vscode/pull/79528)
  * Fix-78397 Implement case preservation in search as well [PR #79111](https://github.com/microsoft/vscode/pull/79111)
  * Fix #64077 - Allow to sort branches alphabetically  [PR #78695](https://github.com/microsoft/vscode/pull/78695)
  * Fix-78915 Webview not handing CustomEvent [PR #79523](https://github.com/microsoft/vscode/pull/79523)
* [Jonathan Mannancheril (@SneakyFish5)](https://github.com/SneakyFish5)
  * Add CURRENT_SECONDS_UNIX snippet variable [PR #79099](https://github.com/microsoft/vscode/pull/79099)
  * Allow goToLine to work backwards [PR #79348](https://github.com/microsoft/vscode/pull/79348)
* [Tony Xia (@tony-xia)](https://github.com/tony-xia): Fixed minor typos [PR #78042](https://github.com/microsoft/vscode/pull/78042)
* [Salvador Cabrera Lozano (@txava)](https://github.com/txava): Fix for #78465: Markdown Preview scroll remains same after clicking on some othe… [PR #79450](https://github.com/microsoft/vscode/pull/79450)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
  * [Screencast] Add border for keys [PR #67641](https://github.com/microsoft/vscode/pull/67641)
  * [Screencast] Smooth showing/hiding of marker [PR #67823](https://github.com/microsoft/vscode/pull/67823)
* [Utkarsh Gupta (@UtkarshGupta-CS)](https://github.com/UtkarshGupta-CS): fix: event's jsdoc typo [PR #79071](https://github.com/microsoft/vscode/pull/79071)
* [Dan Wood (@valtism)](https://github.com/valtism): Clean user input for git clone extension [PR #65906](https://github.com/microsoft/vscode/pull/65906)
* [Vitaliy Mazurenko (@vitaliymaz)](https://github.com/vitaliymaz): fix commit template message whitespace removing #71312 [PR #71710](https://github.com/microsoft/vscode/pull/71710)
* [Yisrael Veller (@YisraelV)](https://github.com/YisraelV)
  * Make toggle-explain a SuggestCommand  [PR #78351](https://github.com/microsoft/vscode/pull/78351)
  * if there is no documentation don't show border [PR #62584](https://github.com/microsoft/vscode/pull/62584)
  * Reuse never show again logic - work in progress [PR #73968](https://github.com/microsoft/vscode/pull/73968)
* [Yuya Tanaka (@ypresto)](https://github.com/ypresto): Let executeCodeActionProvider pass-through Selection object [PR #77999](https://github.com/microsoft/vscode/pull/77999)

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [kanlukasz (@kanlukasz)](https://github.com/kanlukasz)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues.

Contributions to `vscode-css-languageservice`:

* [Benny Powers (@bennypowers)](https://github.com/bennypowers): Update mdn-data dependency [PR #170](https://github.com/microsoft/vscode-css-languageservice/pull/170)
* [Ben Scott (@BPScott)](https://github.com/BPScott): Improve scss partial uri building [PR #159](https://github.com/microsoft/vscode-css-languageservice/pull/159)
* [Brett Jurgens (@brettjurgens)](https://github.com/brettjurgens): Enable strict compiler setting [PR #167](https://github.com/microsoft/vscode-css-languageservice/pull/167)
* [Matthew Dean (@matthew-dean)](https://github.com/matthew-dean): Support for Less root functions, lookups, anonymous mixins [PR #135](https://github.com/microsoft/vscode-css-languageservice/pull/135)

Contributions to `vscode-html-languageservice`:

* [Tony Xia (@tony-xia)](https://github.com/tony-xia): Use 'const' rather than 'var' [PR #66](https://github.com/microsoft/vscode-html-languageservice/pull/66)

Contributions to `vscode-eslint`:

* [@salvofid](https://github.com/salvofid): fix https://github.com/microsoft/vscode-eslint/issues/682 [PR #698](https://github.com/microsoft/vscode-eslint/pull/698)

Contributions to `language-server-protocol`:

* [Nurbol Alpysbayev (@anurbol)](https://github.com/anurbol): Typo in contributing.md [PR #798](https://github.com/microsoft/language-server-protocol/pull/798)

Contributions to `debug-adapter-protocol`:

* [KamasamaK (@KamasamaK)](https://github.com/KamasamaK): Fix spelling [PR #70](https://github.com/microsoft/debug-adapter-protocol/pull/70)

Contributions to `vscode-loader`:

* [Samuel Bronson (@SamB)](https://github.com/SamB): Normalize line endings [PR #24](https://github.com/microsoft/vscode-loader/pull/24)

Contributions to `vscode-recipes`:

* [Jamie Haywood (@jamiehaywood)](https://github.com/jamiehaywood): Updating the README to reflect the code. [PR #224](https://github.com/microsoft/vscode-recipes/pull/224)
* [Michał Wojciechowski (@odyniec)](https://github.com/odyniec): Fix typos in debugging-Ruby-on-Rails/README [PR #223](https://github.com/microsoft/vscode-recipes/pull/223)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Bosnian:** Ismar Bašanović, Ernad Husremovic.
* **Bulgarian:** Hristiqn Hristov, Иван Иванов, Любомир Василев, Gheorghi Penkov.
* **Czech:** Tadeáš Cvrček, Daniel Padrta, David Jareš, Jan Hajek, Jakub Stibůrek, Michal Franc, Jan Kos, Radim Hampl, Jiří Hofman, Samuel Tulach, Jan Brudný.
* **Danish:** René Pape, Lasse Stilvang, Allan Kimmer Jensen, Lars Vange Jørgensen, Alexander Matzen, Martin Liversage, Johan Fagerberg, Thomas Larsen, Anders Lund, Anton Ariens.
* **Dutch:** Leroy Witteveen, Laurens Kwanten, Pieterjan De Clippel, Maxim Janssens, Luc Sieben, Hans Zoons, Jos Verlinde, Eric Algera, Damien van Gageldonk, Maxim Van Damme, Sven Klaasen, Tom Meulemans, Sujith Quintelier.
* **English (United Kingdom):** Martin Littlecott, Alexander Ogilvie, Tobias Collier, William Wood, Cloud Sky, Fabio Zuin, Mohit Nain, sonali Dixit, Dan Jacobs, Giorgi Jambazishvili, Sulkhan Ninidze, alshyab wa3ed, YASHU MITTAL, Tejas kale, Mohammad Idrees, Chris Dias.
* **Finnish:** Petri Niinimäki, Kiti Suupohja, Lasse Leppänen, Sebastian de Mel, Riku Riikonen, Valtteri Vatanen.
* **French:** Griffard, Thierry DEMAN-BARCELÒ, Corwin D'Ambre, Mohamed Sahbi, Rodolphe NOEL, Maxime Coquerel, Steven Dugois, Cédric M., Michael VAUDIN, Bastien Boussouf, Alicia lic, francois-joseph du fou, franto, DJ Dakta.
* **German:** Julian Pritzi, Joscha Mathis, Jan Widmer, Jakob von der Haar, Frank Lindecke, Dejan Dinic, Florian Erbs, Patrick Burke, Mario Koschischek, Florian Berger, Christof Opresnik, Hans Meiser, Carsten Kneip, Ettore Atalan, Thorsten Hans, Meghana Garise, Sebastian Seidl.
* **Greek:** Δημήτρης Παπαϊωάννου, Sotiris Koukios-Panopoulos, Jim Spentzos, Θοδωρής Τσιρπάνης, Stratos Kourtzanidis, Charalampos Fanoulis, John D, Stavros Papadakis, Vassilis Vouvonikos.
* **Hebrew:** חיים לבוב, Shalom Craimer, Matan Amos, Eyal Ellenbogen, Snir Broshi, Kyle Orin.
* **Hindi:** Satish Yadav, Amit Gusain, Sanyam Jain, Abhirav Kushwaha, Ashok Kumar Rathore, nirav adatiya, Rajeev Desai, shaswat rungta, Kumar Vaibhav, Pramit Das, Jagjeet Singh, Pratishek PII, Kiren Paul, Chethana S, Piyush Sonagara, KRISHNA TANDON, Kishan K, Dhanvi Kapila.
* **Hungarian:** Levente Borbély, Péter Nagy, Alex Kiss, Levente Hallai Seiler, Dániel Tar, Richard Borcsik, Boldi Kemény.
* **Chinese Simplified:** Tingting Yi, 斌 项, paul cheung, 建东 薛, Yiting Zhu, feiyun0112 feiyun0112, Justin Liu, Yizhi Gu, Joel Yang, 刘瑞恒 刘瑞恒, Edi Wang, Junzhe Liu, Dave Young, 张锐, Fan Su, 昊宇 周, Pluwen, Tony Xia, Yu Zhang, XIANG ZUO, 少民 谈, 彦佐 刘, Zhiqiang Li, 志乐 龚, 福永 叶, G.Y. Z, Hao Hu, meng shao, Horie Yuan, Xiangrui Kong, 王文杰, Liam Kennedy, 赵畅畅, Charles Lee, 松阳 樊, anson zhang, Young Bige, Shi Liu, Wang Debang, Jarvis Jiang, Lin ai, Jessica Zhang, Licheng Ren, , 驰 雷, sun qing, xiong Fu, Lynne Dong, zhao yu, 吉姆 舒, 红东 黄, 剑秋 陶, 游 尤, Hanlin Yang, bh wu, Bravo Yeung, Michael Zhang, Hanyu ZHANG, Y F, WangCG duoduobear, 越 郑, ziqiang sun, 正元 刘, Jiang LI, 擎 钟, peng wei, 涛 徐, 逍遥 许, 瑜 周, WL, 伟 全, rsy iridescent, Simon Chan, 海龙 黄, 仁松 陈, Jieting Xu, panda small, 翼 张, Chen Yang, Wang Weixuan, Ludi Fang, 舜杰 杨, 建 周, cuibty wong, 立飞 李, 雨齐 刘, 涛 罗, 九鼎 谭, LI ZHAO.
* **Chinese Traditional:** 謝政廷, Yi-Jyun Pan, Winnie Lin, LikKee 沥祺 Richie, Martin Lau, salagadoola _, 牧村 蔡, Jeremy, 小克, 煾 雪.
* **Indonesian:** Jakka Prihatna, Septian Adi, Arif Fahmi, Laurensius Dede Suhardiman, Bakhtiar Amaludin, Heston Sinuraya, Riwut Libinuko, _Hendra Widjaja_, Rachmat Wahidi, Franky So, Bervianto Leo Pratama, Eriawan Kusumawardhono, Rifani Arsyad, Afnizar Nur Ghifari, Pradipta Hendri, Christian Elbrianno, Azhe Kun.
* **Italian:** Alessandro Alpi, Bruni Luca, Luigi Bruno, Andrea Dottor, Riccardo Cappello, Moreno Bruschi, Aldo Donetti, andrea falco, Emanuele Meazzo, Michael Longo, Marco Dal Pino.
* **Japanese:** EbXpJ6bp -, nh, Kyohei Uchida, Takayuki Fuwa, Yoshihisa Ozaki, Hasefumi, TENMYO Masakazu, Michihito Kumamoto, Koichi Makino, Aya Tokura, Seiji Momoto, Yosuke Sano, Makoto Sakaguchi, Kyohei Moriyama, 裕子 知念, Rie Moriguchi, Fujio Kojima, 美穂 山本, taniokae, 太郎 西岡, Kazuya Ujihara, Hiroomi Kurosawa, Yasuaki Matsuda.
* **Korean:** Hongju, 우현 조, Jong Heun Shin, 형섭 이, Kyunghee Ko, Youngjae Kim, siin lee, Jae Yong Kum, Hoyeon Han, Kevin Lee, SeungJin Jeong, Hong Kwon.
* **Latvian:** Kaspars Bergs, Andris Vilde.
* **Lithuanian:** Andrius Svylas, Augustas Grikšas, Tautvydas Derzinskas, Karolis Kundrotas, Martynas J..
* **Norwegian:** Andreas Rødland, Cookius Monsterius, Dag H. Baardsen, Ole Kristian Losvik, Stephan Eriksen, Agnethe Seim Olsen.
* **Polish:** Warchlak, Wojciech Maj, Marcin Weksznejder, Rafał Całka, Tomasz Świstak, Tomasz Wiśniewski, Marek Biedrzycki, Szymon Seliga, Michal Szulc, Jakub Żmidziński, Bartek PL, Igor 05, Rafał Wolak, Tomasz Chojnacki, Piotr Grędowski, Czech Kaczmarek, Artur Pelczar, Marcin Floryan, Paweł Modrzejewski, Jakub Jedryszek, Michał Stojke, Artur Zdanowski.
* **Portuguese (Brazil):** Marcondes Alexandre, Alessandro Trovato, Marcelo Fernandes, Roberto Fonseca, Albert Tanure, Judson Santiago, Junior Galvão - MVP, Ray Carneiro, Lucas Miranda, Rodrigo Crespi, Thiago Dupin Ugeda, Renan Miguel, Weslei A. de T. Marinho, Rafael Lima Teixeira, Eduardo Moura, Gerardo Magela Machado da Silva, Bruno Talanski, Bruno Sonnino, Loiane Groner, Marcos Albuquerque, Jucinei Pereira dos Santos, Emmanuel Gomes Brandão, Fábio Corrêa, Flávio Albuquerque Camilo, Pablo Garcia, Alessandro Fragnani, Andrei Bosco, Daniel Luna, José Rafael de Santana, Douglas Ivatiuk Martim, Giuliano Reginatto, Marcos Dias, Alan William, Lucas Nunes, Gabriel Schade, Lucas Santos, arthurdenner ., Mauricio Lima, Igor Felix, Gabriel Barbosa, Fabio Lux, Guilherme Pais, Victor Cavalcante, Letticia Nicoli, Becky Marques, Fernando Val, Milton Camara, Saymon Damásio, Felipe Scuissiatto, Rodrigo Vieira, Djonathas Cardoso, André Gama, Gustavo Bezerra, Cynthia Zanoni, Marcelo Guerra, Jhonathan Soares, Rafael Laranja, Swellington Soares.
* **Portuguese(Portugal):** Ana Rebelo, Nuno Carapito, Pedro Daniel, Pedro Teixeira, João Carvalho, Diogo Barros, José Rodrigues, Sandro Pereira, Vitor Barbosa, Tiago Antunes, Daniel Correia.
* **Romanian:** Mihai Marinescu, Stefan Gabos, Dragos Marinescu, Alexandru Staicu.
* **Russian:** Andrey Veselov, Дмитрий Кирьянов, netf0rger, michael v, Иван Лещенко, nata kazakova, Анатолий Калужин, Валерий Батурин, Эдуард Тихонов.
* **Serbian:** Milos Zivkovic, Radovan Skendzic.
* **Spanish:** Andy Gonzalez, Ricardo Estrada Rdez, Carlos Mendible, Alvaro Enrique Ruano, Engel Aguilar, José María Aguilar, David Fernández Aldana, Ricardo Rubio, Thierry DEMAN, Gabriel Perez, julian3xl, Adolfo Jayme, Ing. Sergio Uziel Tovar Lemus, Mario Mendieta, Jorge Serrano Pérez.
* **Swedish:** Johan Spånberg, Notetur Nomen.
* **Tamil:** Kondasamy Jayaraman, Merbin J Anselm, Jeyanthinath Muthuram, Mani M, Boopesh Kumar, Vignesh Rajendran, Jaganathan B, Nithun Harikrishnan, Purusothaman Ramanujam, கருணாகரன் சமயன், Krishna Pravin, Jeffin R P, Sakthi Raj, Vetri ., Ranjith kumar.
* **Turkish:** mehmetcan Gün, Meryem Aytek, Fıratcan Sucu, Anıl Mısırlıoğlu, Mehmet Yönügül, Ahmetcan Aksu, Ömer Sert, Sinan Açar, Misir Jafarov, Umut Can Alparslan, Yakup Ad, Hüseyin Fahri Uzun, Murat Pala, S. Ferit Arslan, Mesut Pişkin, Okan Çetin, Bruh Moment, Muhammed Emin TİFTİKÇİ, Burak Göksel.
* **Ukrainian:** Dmytro Kutianskyi, Yaroslav, Max Harasym, Arthur Murauskas, Sviatoslav Ivaskiv, George Molchanyuk, Did Kokos, Alexander Varchenko, Вадим Шашков, Евгений Коростылёв.
* **Vietnamese:** Khôi Phạm, Van-Tien Hoang, Việt Anh Nguyễn, Belikhun, Spepirus Shouru, Vuong Bui, Chủ Tất, Poon Nguyễn.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
