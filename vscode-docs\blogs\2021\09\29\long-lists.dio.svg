<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="924px" height="455px" viewBox="-0.5 -0.5 924 455" content="&lt;mxfile scale=&quot;1.2&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;dw6CfWT_FkV9XQvZtEv5&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 911.15 246.7 L 911.2 273.6 L 911.21 292.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 911.22 298.66 L 907.01 290.26 L 911.21 292.36 L 915.41 290.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 883.55 246.7 L 883.6 273.6 L 883.61 292.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 883.62 298.66 L 879.41 290.26 L 883.61 292.36 L 887.81 290.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 47.33 251.5 L 47.3 278.4 L 47.37 297.16" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 47.39 303.46 L 43.16 295.07 L 47.37 297.16 L 51.56 295.04 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 645.6 48 L 645.6 12 L 609.6 12 L 609.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 609.6 82.66 L 605.4 74.26 L 609.6 76.36 L 613.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 789.6 48 L 789.6 12 L 753.6 12 L 753.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 753.6 82.66 L 749.4 74.26 L 753.6 76.36 L 757.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 789.6 48 L 789.6 12 L 897.6 12 L 897.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 897.6 82.66 L 893.4 74.26 L 897.6 76.36 L 901.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 789.6 48 L 789.6 12 L 825.6 12 L 825.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 825.6 82.66 L 821.4 74.26 L 825.6 76.36 L 829.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 255.6 48 L 255.6 12 L 33.6 12 L 33.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 33.6 82.66 L 29.4 74.26 L 33.6 76.36 L 37.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 465.6 48 L 465.6 12 L 321.6 12 L 321.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 321.6 82.66 L 317.4 74.26 L 321.6 76.36 L 325.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 465.6 48 L 465.6 12 L 393.6 12 L 393.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 393.6 82.66 L 389.4 74.26 L 393.6 76.36 L 397.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 465.6 48 L 465.6 12 L 465.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 465.6 82.66 L 461.4 74.26 L 465.6 76.36 L 469.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 237.6 48 L 177.6 48 L 177.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 177.6 82.66 L 173.4 74.26 L 177.6 76.36 L 181.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 237.6 48 L 105.6 48 L 105.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 105.6 82.66 L 101.4 74.26 L 105.6 76.36 L 109.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 237.6 48 L 249.6 48 L 249.6 76.36" fill="none" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 249.6 82.66 L 245.4 74.26 L 249.6 76.36 L 253.8 74.26 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="9.6" y="0" width="912" height="48" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 758px; height: 1px; padding-top: 20px; margin-left: 9px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List, Length 26
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="388" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List, Length 26
                </text>
            </switch>
        </g>
        <path d="M 19.73 251.5 L 19.7 278.4 L 19.77 297.16" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 19.79 303.46 L 15.56 295.07 L 19.77 297.16 L 23.96 295.04 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="9.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 28 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: -41px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="28" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="81.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 88 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 19px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="88" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="153.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 148 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 79px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="148" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="225.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 208 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 139px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="208" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="297.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 268 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 199px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="268" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="369.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 328 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 259px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="328" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="441.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 388 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 319px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="388" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="162" y="292.8" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 259px; margin-left: 150px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                ...
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="150" y="265" fill="#000000" font-family="Helvetica" font-size="21px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
        <path d="M 868.8 342 L 890.4 342" fill="none" stroke="#000000" stroke-width="2.4" stroke-miterlimit="10" transform="rotate(90,879.6,342)" pointer-events="all"/>
        <rect x="837.6" y="357.6" width="84" height="24" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 308px; margin-left: 733px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Position 24
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="733" y="312" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Position 24
                </text>
            </switch>
        </g>
        <path d="M 441.36 -157.91 L 435.36 -157.91 Q 429.36 -157.91 429.36 -145.91 L 429.36 259.22 Q 429.36 271.22 423.36 271.22 L 420.36 271.22 Q 417.36 271.22 423.36 271.22 L 426.36 271.22 Q 429.36 271.22 429.36 283.22 L 429.36 688.36 Q 429.36 700.36 435.36 700.36 L 441.36 700.36" fill="none" stroke="#000000" stroke-width="2.4" stroke-miterlimit="10" transform="translate(429.36,0)scale(-1,1)translate(-429.36,0)rotate(-90,429.36,271.22)" pointer-events="all"/>
        <rect x="279.6" y="286.2" width="204" height="60" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 264px; margin-left: 318px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                All these nodes also
                                <br style="font-size: 13px"/>
                                have to be looked at to
                                <br style="font-size: 13px"/>
                                find the node at position 24
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="318" y="267" fill="#000000" font-family="Helvetica" font-size="13px" text-anchor="middle">
                    All these nodes also...
                </text>
            </switch>
        </g>
        <rect x="9.6" y="304.8" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 264px; margin-left: 9px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="17" y="268" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="37.2" y="304.8" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 264px; margin-left: 32px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="268" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="873.6" y="300" width="20.4" height="25.2" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 261px; margin-left: 729px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="736" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="901.2" y="300" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 261px; margin-left: 752px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="760" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 561.6 252 L 524.4 252 L 524.4 291.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 524.4 298.06 L 520.2 289.66 L 524.4 291.76 L 528.6 289.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 609.6 252 L 609.6 276 L 609.6 291.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 609.6 298.06 L 605.4 289.66 L 609.6 291.76 L 613.8 289.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 657.6 252 L 696 252 L 696 276 L 695.6 291.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 695.43 298.06 L 691.45 289.55 L 695.6 291.76 L 699.85 289.77 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="513.6" y="84" width="192" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 140px; margin-left: 429px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                                <br/>
                                Length 6
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="508" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair...
                </text>
            </switch>
        </g>
        <rect x="729.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 627.9999999999998 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 559px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="628" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="801.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 687.9999999999998 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 619px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="688" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="873.6" y="84" width="48" height="168" fill="#dae8fc" stroke="#6c8ebf" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 747.9999999999998 140)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 140px; margin-left: 679px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="748" y="144" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length 2
                </text>
            </switch>
        </g>
        <rect x="513.6" y="299.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 260px; margin-left: 429px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="437" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="685.2" y="299.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 260px; margin-left: 572px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="580" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 579.6 324.6 L 573.6 324.6 L 573.6 333.16" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 573.6 339.46 L 569.4 331.06 L 573.6 333.16 L 577.8 331.06 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 639.6 324.6 L 645.6 324.6 L 645.6 333.16" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 645.6 339.46 L 641.4 331.06 L 645.6 333.16 L 649.8 331.06 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="549.6" y="299.4" width="120" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 459px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List, Length 4
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="508" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List, Length 4
                </text>
            </switch>
        </g>
        <rect x="81.6" y="304.8" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 264px; margin-left: 69px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="77" y="268" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="109.2" y="304.8" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 264px; margin-left: 92px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="268" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="549.6" y="340.8" width="48" height="67.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 478 311.9999999999998)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 54px; height: 1px; padding-top: 312px; margin-left: 451px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Pair Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="478" y="316" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pair Leng...
                </text>
            </switch>
        </g>
        <rect x="621.6" y="340.8" width="48" height="67.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 538 311.9999999999998)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 54px; height: 1px; padding-top: 312px; margin-left: 511px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Pair Length 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="538" y="316" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pair Leng...
                </text>
            </switch>
        </g>
        <path d="M 561.6 408 L 559.8 408 L 559.8 420.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 559.8 427.06 L 555.6 418.66 L 559.8 420.76 L 564 418.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 585.6 408 L 587.4 408 L 587.4 420.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 587.4 427.06 L 583.2 418.66 L 587.4 420.76 L 591.6 418.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="549.6" y="428.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 368px; margin-left: 459px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="467" y="371" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="577.2" y="428.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 368px; margin-left: 482px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="371" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 633.6 408 L 631.8 408 L 631.8 420.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 631.8 427.06 L 627.6 418.66 L 631.8 420.76 L 636 418.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 657.6 408 L 659.4 408 L 659.4 420.76" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 659.4 427.06 L 655.2 418.66 L 659.4 420.76 L 663.6 418.66 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="621.6" y="428.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 368px; margin-left: 519px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="527" y="371" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="649.2" y="428.4" width="20.4" height="25.2" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 15px; height: 1px; padding-top: 368px; margin-left: 542px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="550" y="371" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 119.32 252 L 119.3 278.9 L 119.36 297.66" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 119.38 303.96 L 115.16 295.58 L 119.36 297.66 L 123.56 295.55 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 91.72 252 L 91.7 278.9 L 91.76 297.66" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 91.78 303.96 L 87.56 295.58 L 91.76 297.66 L 95.96 295.55 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="807.6" y="289.2" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 256px; margin-left: 688px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                ...
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="688" y="262" fill="#000000" font-family="Helvetica" font-size="21px" text-anchor="middle">
                    ...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>