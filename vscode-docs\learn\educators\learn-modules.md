---
Order: 3
Area: educators
TOCTitle: Microsoft Learn
ContentId: d7c14f6c-d8ca-4516-9147-2deb86a29701
PageTitle: Visual Studio Code Learn modules
DateApproved: 10/22/2020
MetaDescription: Visual Studio Code Microsoft Learn modules
---
# Learn modules

If you're looking for resources for your students to get started with Visual Studio Code, check out this curated list of Learn modules. These modules are designed to walk you through step-by-step to set up your work environment or finish a small project, all while familiarizing you with the VS Code interface.

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/modules/python-install-vscode/"><h2 class="title faux-h3">Set up your Python beginner development environment</h2></a>
    </div>
    <p class="description">Get started with learning Python by installing and configuring the tools you'll need to build real applications.</p>
    <a href="https://learn.microsoft.com/training/modules/python-install-vscode" title="Python module">
        <img src="/assets/learn/educators/learn-modules/learn-python-vscode.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>
<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/modules/use-git-from-vs-code/"><h2 class="title faux-h3">Use Git version-control tools</h2></a>
    </div>
     <p class="description">Utilize the tight integration of Visual Studio Code with the Git source-control management system.</p>
    <a href="https://learn.microsoft.com/training/modules/use-git-from-vs-code/" title="Git module">
        <img src="/assets/learn/educators/learn-modules/learn-git.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/modules/basic-python-nasa/"><h2 class="title faux-h3">Write basic Python in Notebooks</h2></a>
    </div>
    <p class="description">Learn the basics of Python.</p>
    <a href="https://learn.microsoft.com/training/modules/basic-python-nasa/" title="Python notebook module">
        <img src="/assets/learn/educators/learn-modules/learn-python-notebooks.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/modules/build-node-cosmos-app-vscode/"><h2 class="title faux-h3">Build a Node.js app for Azure Cosmos DB</h2></a>
    </div>
    <p class="description">Build a database app to store and query data in Azure Cosmos DB by using Visual Studio Code and Node.js.</p>
    <a href="https://learn.microsoft.com/training/modules/build-node-cosmos-app-vscode/" title="Azure Cosmos DB module">
        <img src="/assets/learn/educators/learn-modules/learn-node-app.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>
