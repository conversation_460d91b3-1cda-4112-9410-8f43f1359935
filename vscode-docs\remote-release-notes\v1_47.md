# June 2020 (version 1.47)

## Visual Studio Code Remote Core

## SSH

## Remote server can listen on socket instead of port

If you are connecting to a multi-user remote, you may not want the VS Code Remote Server to open a port that's accessible to any user on the machine. We've added the setting `remote.SSH.remoteServerListenOnSocket` which tells the server on a Linux or macOS host to switch to using Unix sockets that are locked down to a particular user instead. This socket is then forwarded over SSH instead of the port.

## Containers (version 0.128.0)

### Guidance to open a repository in a volume

When opening a Git repository folder, the **Reopen in Container** notification now offers to clone and reopen the repository in a Docker volume. Using a Docker volume has better disk performance because it uses a Linux filesystem without any extra layer between the Linux Kernel and the filesystem. (We do not show this guidance on Linux, but the feature is still available using the **Dev Containers: Clone Repository in Container Volume** command.)

![Reopen in Container notification](images/1_47/reopenincontainernotification.png)

The **Reopen in Container** notification is also shown when there is a configuration in our [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers/tree/main/repository-containers) repository matching the current folder's Git repository.

If you choose **Don't Show Again**, you will be prompted to clarify if you don't want to see this notification for the current folder only, or for any folder. If you would like to see the notification again, you can reset it for all folders with the **Dev Containers: Reset Don't Show Reopen Notification** command.

### Configurable repository configuration paths

There is now a setting for adding local lookup paths for configurations to repositories. The folder structure at the lookup paths is `<host>/<owner>/<repository>` (for example, `github.com/angular/angular`). See the [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers/tree/main/repository-containers) repository for examples.

![Add local lookup path for repository configurations](images/1_47/repoconfigpathssetting.png)

[Stuart Leeks (@stuartleeks)](https://github.com/stuartleeks) contributed this setting as a PR.

### Attach configuration by container name

In addition to having configuration files per Docker image, we now also support configuration files per container name. To create one, first attach VS Code to a running container, and then create a configuration for its name with the **Dev Containers: Open Named Container Configuration File** command.

From then on, the existing **Dev Containers: Open Container Configuration File** command will open the name configuration and **Open Attached Container Configuration File...** will show both image and name configurations.

## WSL

With updates to Windows 10 and Docker Desktop Stable in May, WSL 2 in Windows and Docker Desktop is now GA. Check out the blog post [Using Dev Containers in WSL 2](https://code.visualstudio.com/blogs/2020/07/01/containers-wsl) to learn how to develop applications in a Docker container in WSL 2 from VS Code.
