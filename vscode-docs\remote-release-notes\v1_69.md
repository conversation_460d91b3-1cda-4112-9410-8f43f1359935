# June 2022 (version 1.69)

## Development Container specification

(Dev Containers 0.241.x, devcontainer CLI 0.7.x)

### Multi-platform builds

The devcontainer CLI adds a `--platform` option to its `build` command that can be used to build multi-platform images. This should usually be used together with the `--push` option because the local image cache only supports images for the local architecture:

```bash
devcontainer build --workspace-folder <workspace folder> --platform linux/arm64,linux/amd64 --image-name <your Docker ID>/<name> --push
```
