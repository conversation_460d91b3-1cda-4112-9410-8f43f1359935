# September 2022 (version 1.72)

## Extension naming (WSL and Dev Containers)

We've heard your feedback about the naming of the **Remote - WSL** and **Remote - Containers** extensions. We intended for 'Remote' in their names to indicate you develop in a "remote" or "separate" WSL distro or development container, rather than traditional local development. However, this is a different use of "remote" than many people use, and it could lead to confusion (for example, is the dev container on my machine?).

Thus, we've updated naming and in-product commands in these extensions to be clearer, renaming them to [WSL](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl) and [Dev Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers). Links such as the extension identifier in the Marketplace and documentation remain the same, so you shouldn't see breakage in your current use of these two extensions.

![WSL extension in the Marketplace](images/1_72/wsl-extension.png)

![Dev Containers extension in the Marketplace](images/1_72/dev-containers-extension.png)

## Dev Containers

### Dev Container image metadata

A Dev Container image can now include additional configuration (like VS Code extensions and settings) that is applied when the container is created, so a `devcontainer.json` can now look as simple as:

```json
{
    "image": "mcr.microsoft.com/devcontainers/rust:1"
}
```

When building a Dev Container image with the [Dev Container CLI](https://github.com/devcontainers/cli) configuration from the `devcontainer.json` and the configured features are included in the image. This configuration is then applied when the Dev Container is created and run. For additional details, see the proposal in the [Dev Container Specification](https://github.com/devcontainers/spec/blob/main/proposals/image-metadata.md).

## Additional devcontainer.json Properties

We have added support to `devcontainer.json` for `init`, `privileged`, `capAdd`, and `securityOpt`, and when using Docker Compose, we now also support `mounts`, `containerEnv`, and `containerUser`.
