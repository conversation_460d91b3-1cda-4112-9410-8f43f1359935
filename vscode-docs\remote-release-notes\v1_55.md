# March 2021 (version 1.55)

## Visual Studio Code Remote Core

### Consolidation of local and remote terminal process architecture

Last month, terminal processes in local windows started launching in a new isolated process called the pty host. This isolates the important main process from potential native crashes and frees up cycles in its important event loop. In this release, these changes come to remote terminals as well, sharing most of their implementation and bringing the benefits of the pty host to remote.

We also removed the `terminal.integrated.serverSpawn` and `terminal.integrated.flowControl` settings, which are now stable and always on.

### Keybinding changes for some commands in browsers

Due to overlap with existing browser keybindings, we have changed the following commands:

* **Step Over** is now using `Alt + F10` instead of `F10` on all browsers.
* **Step Into** is now using `Alt + F11` instead of `F11` on Windows browsers.

### Detection of ports where the processes is root

Port forwarding autodetection now detects some ports where the process that is listening on a port is running as root. These ports will only be autodetected if the command line for the process contains the port number.

### Double-click to name port

Double-clicking on a port in the Ports view will allow you to name the port.

### Use a regex for portsAttributes

The `remote.portsAttributes` setting can accept a regular expression as one of its properties. This regular expression will be used to match against the command line of ports that are forwarded. For example, if you don't know what port your application listens on, but you know that the command line will contain the string "/server.js" and you want to open a preview of your app when it runs, you could add this setting:

```json
"remote.portsAttributes": {
    ".+/server.js": {
        "onAutoForward": "openPreview"
    }
}
```

### Configure default port detection behavior

The setting `remote.otherPortsAttributes` can be used to configure the behavior of any ports that aren't configured using `remote.portsAttributes`. If for example, you want all other ports that are detected to be silently (for example, no notification or opening) forwarded then you can configure it with `remote.otherPortsAttributes`:

```json
"remote.otherPortsAttributes": {
    "onAutoForward": "silent"
}
```

## Containers (version 0.166.x)

### Clone repository in container volume improvements

* There is a new command, **Dev Containers: Clone GitHub Pull Request in Container Volume**. You could already clone a GitHub pull request in a container volume when using the URL of the pull request with the **Dev Containers: Clone Repository in Container Volume** command. Having a separate command makes the feature more discoverable.

* The command **Dev Containers: Clone Repository in Container Volume** now also understands Azure DevOps and GitLab repository URLs.

### Optional install command for dotfiles repository

If a dotfiles repository has no install command, we now link all top-level dotfiles into the home folder. We consider additional filenames when looking for an install command: `install.sh`, `install`, `bootstrap.sh`, `bootstrap`, `setup.sh` and `setup`.

### Portable "initializeCommand"

When using the array syntax for `"initializeCommand"`, the command element can use a relative path with slashes as delimiters to be portable between Windows, Mac, and Linux. On Mac and Linux, the path will be used as-is. On Windows, the slashes will be replaced with backslashes and an executable will be searched for by appending Windows' executable file extensions.