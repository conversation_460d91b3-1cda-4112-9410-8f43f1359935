<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="611px" height="435px" viewBox="-0.5 -0.5 611 435" content="&lt;mxfile scale=&quot;1.2&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;dw6CfWT_FkV9XQvZtEv5&quot; name=&quot;Page-1&quot;&gt;7VxLc6M4EP41XLdA4nmMnddhdmuqctidIwHF1g5GLqzEdn79CiOZh7CDg2wZL3OYMg1S4PvUre5WSwacLjZPWbic/0lilBjAjDcGvDcAgAFk/+eCbSGwTA8UklmGYy4rBS/4E4kHufQdx2hVe5ASklC8rAsjkqYoojVZmGVkXX/sjST1v7oMZ0gSvERhIkv/xjGdF1LfMUv5M8KzufjLlsnvLELxMBes5mFM1hURfDDgNCOEFr8WmylKcvAELkW7xwN39y+WoZR2auDxJh9h8s6/jr8Z3YrPzch7GqO8hWnAyXqOKXpZhlF+d80IZrI5XSTsymI/30hKH8MFTnJyn1HygSiOQnZjRTPyG01JQrJdt9AHr9B18yY4SYQ8JSnrdyJ/h3hPlFG0qYj4dz0hskA027JH+F1b0MFHmS8wX5eU2Vw0r7LlBXyk8FEy23ddAsl+cCwP4Gq5EowoZoOIX5KMzsmMpGHyUEondaDLZ34QsuTw/oso3XKNCN8pqYOPNpj+kzf/Azj88lfl1v2Gd7272PKLg1ivyHsWiSFveVzRwmyGxHO2XQjzTzvKSYaSkOKPuv60Abxrepdl4bbywJLglK4qPf/MBSXVANapdjmFJVlFjyV1+1frxqbnfIPNXvx53fk7qHH5jYq+mbt/ees0vsutIBNHSbha4agQPuJEvEh9JPYYId51jBDHPPMIER9fsaM/8Cp/A2P6YEwsafycZlQrFpIx+faG3Chqs6mxF7yaRynrbkAt0MAMBrIBDVosqLCq/QyoLwE6ycLoN8ox/RnirB+ic5LhT6YfYfLVGP8+YL7dAljbjGMqASwYHmC+TsAEWwMCbI+GHsBkX/HaAYOmVsDgl4CZz4pBa0wVsYP82D7qfiuA+du+NlQCs33ZcakFYsdqQOx1g1hJNAOc/wPCnk6EXQlhw5tIsLJPoXXswgTPUvY7Yh+OGCqT/IOZ/5/c8RsLHMdFdIlW+DN83XWVg8x9ZtavMzGc+7wvFpCsitjEktDmkXkeVPD4BUjx/pTFAxjlZu0vtJYjkDsHQObXqfEdLKvGVtDCFmxhS43F8SRmhh3dAz6Ga7GbMOK6o3u3oZeW4tgN+HrZPCHW78OmdRVswmaQpJxNOQ7ikfhDv1lKy6TU9Pg9T3Zg986q8iAcwhuzc9Bp04wryVHBM2sGtG/MzrWz6VwHm8G52ZR9cm7nngZo56Twxupo5+AR4rrbuVtbrYF+i2aIlKpmzbCaSSzlmqHZO1dv51rZDK6CTdDMsClnU14I4HbufoB2rrlGcFl/zgY3ZufEAnRNM+CV2LlGPsmzFWuGrdk7V27nWtkEV2LngnOzKWexuZ2T04ADtHOmbOf2VVDq7ZzsGxuePF2M2dQ9XWJtX2RTXZmus2VT7TH3fSJbompPC1typcuoW0fZCnTqluw9j7p1jK19QkELW3LuetSto2w5GnVLFD2MutWVrUCjbjktle6jbh2rnbJ06hYYdes0thyduiUXuo26dYwt2wIa2ZIzDaNuHWXL1qlbYy7jRLZcnfPWmMs4jS3H1KlbYy7jRLaATt0acxknsuXq1K0xl3EiW75G3XLbouOb0C2xHGrJ7JlmEKjaLQl9jR59ACSmLroS3H0hmAGcbSuN8stf1Xtls93VOTc5H2S9tijttS1K+8pLDDpz7X2926nYcyyPievfcwwbtf6ty8ZOixopWTb2ZOe9HVq5kFbpfqcLYd3IxnbetCd8wF5Yi8luAKVIw7NZflvBoBjdmgtp7MbGLd80GwOnZyGNKNQZJ8MLDSyxzVnDZChM2ABK4G6EbKh8e0X3KeNgJfBuUparIQfg7zTmYM+X52Ag1uGqkzBQEvS1hOj9MLyWw7++vZ1EzelfovL39oD9dl3nvn62n73/+kSNczrmnWtnJUBbYO+M8WUPLQkGcppGP4i1njwUDOQ4jV4Q6z2rKPg6lB8+xJc83YhdlseSFmFZebgrfPgP&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="406.8" y="217.2" width="48" height="214.8" fill="none" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <path d="M 157.8 48 L 142.8 48 L 142.8 76.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 142.8 82.66 L 138.6 74.26 L 142.8 76.36 L 147 74.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 451.8 48 L 466.8 48 L 466.8 76.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 466.8 82.66 L 462.6 74.26 L 466.8 76.36 L 471 74.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="10.8" y="0" width="588" height="48" fill="#ffe6cc" stroke="#d79b00" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 488px; height: 1px; padding-top: 20px; margin-left: 10px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List α
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="254" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List α
                </text>
            </switch>
        </g>
        <rect x="10.8" y="252" width="48" height="120" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 29 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: -20px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="29" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="82.8" y="252" width="48" height="120" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 89 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 40px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="89" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="154.8" y="252" width="48" height="120" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 149 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 100px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="149" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="226.8" y="252" width="48" height="120" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 209 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 160px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="209" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="406.8" y="217.2" width="48" height="156" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 359 246)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 246px; margin-left: 295px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair H
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="359" y="250" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair H
                </text>
            </switch>
        </g>
        <rect x="478.8" y="277.2" width="48" height="94.8" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 419 270.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 271px; margin-left: 381px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="274" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="550.8" y="277.2" width="48" height="94.8" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 479 270.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 271px; margin-left: 441px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="479" y="274" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="0" y="397.2" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 346px; margin-left: 15px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="15" y="352" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <path d="M 184.8 216 L 178.8 216 L 178.8 244.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.8 250.66 L 174.6 242.26 L 178.8 244.36 L 183 242.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 244.8 216 L 250.8 216 L 250.8 244.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 250.8 250.66 L 246.6 242.26 L 250.8 244.36 L 255 242.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="154.8" y="168" width="120" height="48" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 160px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List E
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="179" y="164" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List E
                </text>
            </switch>
        </g>
        <path d="M 508.8 253.2 L 508.8 216 L 502.8 216 L 502.8 269.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 502.8 275.86 L 498.6 267.46 L 502.8 269.56 L 507 267.46 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 568.8 253.2 L 568.8 216 L 574.8 216 L 574.8 269.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 574.8 275.86 L 570.6 267.46 L 574.8 269.56 L 579 267.46 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="478.8" y="216" width="120" height="37.2" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 196px; margin-left: 400px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List G
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="449" y="199" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List G
                </text>
            </switch>
        </g>
        <path d="M 40.8 216 L 34.8 216 L 34.8 244.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 34.8 250.66 L 30.6 242.26 L 34.8 244.36 L 39 242.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 100.8 216 L 106.8 216 L 106.8 244.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 106.8 250.66 L 102.6 242.26 L 106.8 244.36 L 111 242.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="10.8" y="168" width="120" height="48" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 160px; margin-left: 10px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List D
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="59" y="164" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List D
                </text>
            </switch>
        </g>
        <path d="M 76.8 132 L 70.8 132 L 70.8 160.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 70.8 166.66 L 66.6 158.26 L 70.8 160.36 L 75 158.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 208.8 132 L 214.8 132 L 214.8 160.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 214.8 166.66 L 210.6 158.26 L 214.8 160.36 L 219 158.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="10.8" y="84" width="264" height="48" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 90px; margin-left: 10px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="119" y="94" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List B
                </text>
            </switch>
        </g>
        <rect x="33.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 43px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="43" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="72" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 75px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="75" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="105.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 103px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="103" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="144" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 135px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="135" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="177.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 163px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="163" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="216" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 195px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="195" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="249.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 223px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="223" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="288" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 255px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="255" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="361.2" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 316px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="316" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="396" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 345px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="345" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="429.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 373px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="373" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="468" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 405px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="405" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="501.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 433px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="433" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="540" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 465px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="465" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="573.6" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 493px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="493" y="351" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="325.2" y="396" width="36" height="36" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 286px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #009900; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="286" y="351" fill="#009900" font-family="Courier New" font-size="21px" text-anchor="middle" font-weight="bold">
                    {
                </text>
            </switch>
        </g>
        <path d="M 448.8 132 L 467.4 132 L 467.4 161.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 467.4 167.86 L 463.2 159.46 L 467.4 161.56 L 471.6 159.46 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="298.8" y="84" width="300" height="48" fill="#ffe6cc" stroke="#d79b00" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 90px; margin-left: 250px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair β
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="374" y="94" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair β
                </text>
            </switch>
        </g>
        <rect x="336" y="217.2" width="48" height="154.8" fill="#ffe6cc" stroke="#d79b00" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 300 245.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 127px; height: 1px; padding-top: 246px; margin-left: 237px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair δ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="300" y="249" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair δ
                </text>
            </switch>
        </g>
        <path d="M 401.7 193.2 L 360 193.2 L 360 209.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 360 215.86 L 355.8 207.46 L 360 209.56 L 364.2 207.46 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 467.4 193.2 L 430.8 193.2 L 430.8 209.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 430.8 215.86 L 426.6 207.46 L 430.8 209.56 L 435 207.46 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 533.1 193.2 L 538.8 193.2 L 538.8 208.36" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 538.8 214.66 L 534.6 206.26 L 538.8 208.36 L 543 206.26 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="336" y="169.2" width="262.8" height="24" fill="#ffe6cc" stroke="#d79b00" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 217px; height: 1px; padding-top: 151px; margin-left: 281px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List γ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="155" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List γ
                </text>
            </switch>
        </g>
        <rect x="478.8" y="216" width="120" height="214.8" fill="none" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <rect x="10.8" y="84" width="264" height="348" fill="none" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <rect x="10.8" y="252" width="48" height="120" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 29 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: -20px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="29" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="82.8" y="252" width="48" height="120" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 89 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 40px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="89" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="154.8" y="252" width="48" height="120" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 149 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 100px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="149" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="226.8" y="252" width="48" height="120" fill="#d5e8d4" stroke="#82b366" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 209 260)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 260px; margin-left: 160px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="209" y="264" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>