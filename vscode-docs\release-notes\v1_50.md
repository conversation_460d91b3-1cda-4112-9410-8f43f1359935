---
Order:
TOCTitle: September 2020
PageTitle: Visual Studio Code September 2020
MetaDescription: Learn what is new in the Visual Studio Code September 2020 Release (1.50)
MetaSocialImage: 1_50/release-highlights.png
Date: 2020-10-8
DownloadVersion: 1.50.1
---
# September 2020 (version 1.50)

**Update 1.50.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22September+2020+Recovery%22+is%3Aclosed+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the September 2020 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Accessible settings editor](#settings-editor)** - Interact with the settings list like any other list in VS Code.
* **[Pinned tabs improvements](#pinned-tabs-improvements)** - Resize pinned tabs, unpin with one click, and more.
* **[Linux ARM builds](#linux-arm-builds)** - VS Code is now available for both Linux ARMv7 and ARM64.
* **[Improved debugging experience](#debugging)** - Improved hover and filtering in debug console.
* **[New JS debugger features](#javascript-debugging)** - Toggle auto attach flows and see performance in real-time.
* **[Panel layout improvements](#panel-layout-improvements)** - New panel maximization settings and bottom panel size.
* **[Webview Views support](#webview-views)** - Build extensions with webview views in the sidebar and panel.
* **[Updated "Create a Dev Container" tutorial](#updated-create-a-dev-container-tutorial)** - Get started with Development Containers.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/events) on Tuesday, October 13 at 8am Pacific (4pm London), to see a demo of what's new in this release and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## macOS: Keychain access moved to a different process

Some changes in this release around how the macOS Keychain is accessed from VS Code will require you to acknowledge the following prompt for keychain access on macOS, even if you've previously allowed keychain access:

![Keychain Access prompt](images/1_50/keychain.png)

Details: VS Code previously accessed the macOS keychain from the window process itself, but now accesses it from the main process. As such, for macOS it prompts for approval like a new application is requesting access, because the process is a different one from before.

## Accessibility

### Settings editor

Back in July, we mentioned in the [1.48 release notes](https://code.visualstudio.com/updates/v1_48#_settings-editor-improvements) that we were investigating ways to improve the accessibility of the settings editor. After a few months of talking to screen reader users, experimenting, prototyping, and gathering feedback from the community, we are shipping the new experience, which we believe is an improvement for all keyboard-heavy users, whether using a screen reader or not.

In this new interaction model, interacting with the settings list works more like interacting with any other list in VS Code. With focus in the search bar, you can press the down arrow to focus a setting row. Then you can use the arrow keys to move focus around the settings list. To edit a setting, you can press enter to set focus on the control in the setting row.

![settings editor as list](images/1_50/settings-editor.gif)

If you would like to read more about the motivation and thinking behind this approach, please see GitHub [issue #108006](https://github.com/microsoft/vscode/issues/108006).

## Workbench

### Pinned tabs improvements

Pinned tabs were introduced in our [May 2020](https://code.visualstudio.com/updates/v1_46#_pin-tabs) release. Since then, we've received valuable feedback on how to improve this experience further, and for this milestone there are a couple of changes worth mentioning.

A new setting `workbench.editor.pinnedTabSizing` allows you to configure how large a pinned tab should appear:

* `normal`: a pinned tab inherits the look of other tabs (new default)
* `shrink`: a pinned tab shrinks to a fixed size showing parts of the editor label
* `compact`: a pinned tab will only show as icon or first letter of the editor label

If you liked how pinned tabs looked prior to this release, make sure to configure `"workbench.editor.pinnedTabSizing": "compact"`.

Pinned tabs also show a new "pinned" icon to allow you to unpin with one click. This icon will also appear in the "Open Editors" view.

![Pinned Tabs](images/1_50/pinned_tabs.gif)
_Theme: [GitHub Light](https://marketplace.visualstudio.com/items?itemName=github.github-vscode-theme)_

Some other improvements include:

* Editors can now be pinned even when tabs are disabled.
* `Cmd+W` (`Ctrl+W`) no longer closes a pinned editor, but selects the next non-pinned editor.
* A new command **workbench.action.closeActivePinnedEditor** can be assigned to close a pinned editor.
* A new `tab.lastPinnedBorder` color can be assigned to draw a border to the right of the last pinned tab.

### Renamed some editor-related context keys

A few new editor context keys have been introduced, deprecating some of the existing ones. If you are using these in your keybinding settings, please update them:

* `groupActiveEditorDirty` was renamed to `activeEditorIsDirty`
* `editorPinned` was renamed to `activeEditorIsNotPreview`
* `editorSticky` was renamed to `activeEditorIsPinned`

### Added new resource (explorer-related) context keys

Two new context keys have been added for resources.

* `resourceDirname` for the folder path of the resource, equivalent to `dirname(uri.fsPath)`
* `resourcePath` for the full path of the resource, equivalent to `uri.fsPath`

### Extensions: Simplified recommendation notifications

In this release, we improved the experience of recommending extensions using notifications by making them smarter and less noisy to users.

* Recommendations notifications are not shown again after you tried them or went through them.
* Only one recommendation notification is shown at any time in VS Code window. You can access already recommended notifications under the notification manager, the `bell` icon in the status bar.

![Extension Recommendations Queue](images/1_50/ext-recommendations-queue.gif)

*Theme: GitHub Sharp Dark*

* Recommendation notifications supporting installations, like WSL, Docker etc., on your system, are made less noisy by showing only one such notification once per VS Code session - a session begins when you open VS Code and ends when you quit.

### Panel layout improvements

**New panel maximization setting**

For those who prefer to work with the panel maximized, you no longer have to maximize the panel each time after toggling it open. For those that don't, the old panel behavior isn't going away.

Instead, a new setting, `panel.opensMaximized`, allows users to adjust to their own preference whether or not the panel always opens maximized when toggled with `Toggle Panel`. The options are `always`, `never`, and the default value `preserve`. The `preserve` option opens the panel maximized if it was maximized before being closed, and opens it non-maximized otherwise.

Additionally, if the panel is hidden, `Toggle Maximized Panel` will now always open the panel maximized.

**Decreased minimum window width when panel is on the bottom**

When the panel is on the bottom rather than on the side, the minimum window width is 400px instead of 600px. This way, developers who want a narrower editor-only or panel-only window can now move the panel to the bottom, optionally hide or maximize it, and then resize the window to the new narrower minimum width.

### Linux ARM builds

VS Code is now available for both Linux ARMv7 and ARM64 architectures. This means you can now officially use VS Code on a Raspberry Pi, Chromebook, and other ARM-based devices!
On these devices, you can also leverage the VS Code extensions for [Remote Development](https://code.visualstudio.com/docs/remote/remote-overview) to get access to more powerful development environments when you need them.

![Linux ARM](images/1_50/linux-arm.png)

**[Download it now](https://code.visualstudio.com/#alt-downloads)**! Also available for [Insiders](https://code.visualstudio.com/insiders/#linux).

## Editor

### Trigger IntelliSense

For years, `Ctrl+Space` has been the predominant keybinding to trigger IntelliSense. However, on macOS and Windows, the same keybinding is used to toggle between keyboard layouts. To minimize confusion, we added another keybinding to trigger IntelliSense: on Windows and Linux it's `Ctrl+I`, and on macOS it's `Cmd+I`.

## Debugging

### Debug hover improvements

* **Language hover now available while debugging**

  While debugging, the debug hover takes precedence over the language hover, making it impossible to see the language hover. Starting with this release, you can switch from the debug hover back to the language hover by holding down the `Alt` modifier key. As long as `Alt` is pressed, the language hover is shown instead of the debug hover. This makes it much easier to read Javadoc while debugging.

* **Auto sizing debug hover**

  In order to minimize the need for scrolling, the debug hover width and height now automatically adapts to the size of the hover contents. This not only helps you the full strings, but you can also drill down into object structures, and the hover grows as needed.

  ![Auto Sizing debug hover](images/1_50/autosize-hover.gif)

### Debug console filter improvements

The Debug Console now supports filtering, making it easier for users to find the output they are looking for, or to hide irrelevant logging output. We've added a badge to make it clear how many items are filtered away.

![Debug console badge](images/1_50/debug-console-badge.png)

### JavaScript debugging

**Consolidated auto attach flows**

Last month, we introduced [new auto attach modes](https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_auto-attach-feature) as an experimental feature. This month, they have left the experimental state. The previous **debug.autoAttach** setting is deprecated in favor of the **debug.javascript.autoAttachFilter** option. The auto attach badge in the status bar now shows the current mode and allows you to toggle between modes.

**Real-time performance view**

The [flame chart extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-js-profile-flame) will now show a real-time view of performance metrics while debugging JavaScript code. CPU and memory usage are shown for Node.js programs and extension debugging. Debugging Chrome or Edge will additionally show metrics for DOM nodes, relayouts, and restyles.

![Debug realtime view](images/1_50/js-debug-realtime.gif)

*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

In addition to the controls in the view, there are a few settings you can use to control it:

* **debug.flameGraph.realtimePollInterval**: how often stats are updated
* **debug.flameGraph.realtimeViewDuration**: how much time should be displayed on the graph
* **debug.flameGraph.realtimeEasing**: whether easing is enabled

**Left-heavy flame chart view**

Also new in the [flame chart extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-js-profile-flame) is a "left heavy" view, toggled by clicking the button in the upper-right corner of the chart.

This view groups call stacks and orders them by time, creating a visual representation of the "top down" table you might have used in other tools. This is especially useful if your profile has lots of thin call stacks (common with things like web servers) which are hard to get a sense of in the chronological view.

Here we have the same profile opened in the traditional chronological view on the right, and the left-heavy view on the left. With the left-heavy view, it's much easier to tell exactly where time is being spent.

![Screenshot of an editor showing the two flame graphs described above](images/1_50/left-heavy-flame.png)
*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

**Step through missing code**

Previously if you tried to debug a file that has a sourcemap, but for which the source was not available, you would get stuck on a "could not load source" error and would be unable to view the compiled code. We now show a prompt allowing you to ignore the sourcemap and step through compiled code in these cases.

![Notification that says "This is a missing file referenced by a sourcemap. Would you like to debug the compiled version instead?"](images/1_50/js-debug-compiled-code-step.png)
*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

## Source Control

### Git: Commit --no-verify commands

Git commit commands with the `--no-verify` flag are now available after enabling the `git.allowNoVerifyCommit` setting.

![git --no-verify in command palette](images/1_50/git-no-verify.png)

### Git: Multiple git.path values

You can now specify a list of locations to look for the git executable via the same `git.path` setting, by using an array of strings.

### Source Control view saves input on window reload

Addresses [this request](https://github.com/microsoft/vscode/issues/41232) to save the Source Control view input on window reload.

![a message is typed in the SCM and the window is reloaded showing the saved message](images/1_50/scm-saves.gif)

## Terminal

### Search the workspace by selecting text in the terminal

The standard shortcut for searching the workspace `kb(workbench.action.terminal.searchWorkspace)` will now use the terminal's selection if it's focused.

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### Settings sync

You can now select which settings sync service to use (insiders or stable) while turning on Settings Sync. **Note:** This option is only available on [Insiders](https://code.visualstudio.com/insiders).

### TypeScript 4.1 beta support

VS Code supports the [TypeScript 4.1 beta](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/) and nightly builds. The 4.1 update brings some new TypeScript language features, such as support for [recursive conditional types](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/#recursive-conditional-types), as well as tooling improvements. One focus area has been [adding initial support](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/#jsdoc-see-tag) for `@see` tags in JSDoc comments.

To start using the TypeScript 4.1 nightly builds, just install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with TypeScript 4.1.

## Contributions to extensions

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

To learn about all the new features and updates, you can see the full [changelog for the 0.21.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0210) release of the extension.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.50 include:

* Improved log access and rendering for containers.
* Better recovery support for cloning repositories in container volumes.
* Ability to install an extension in a remote context (container, SSH, WSL) without having to reload VS Code.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_50.md).

### ESLint

The approval flow to allow the execution of a ESLint library got reworked. The initial experience is now as follows:

* No modal dialog is shown when the ESLint extension tries to load a ESLint library for the first time and an approval is necessary. Instead, the ESLint status bar item changes to ![ESLint status icon](images/1_50/eslint-status.png) indicating that the execution is currently blocked.
* If the active text editor content can be validated using ESLint, a problem at the top of the file is shown in addition.

The execution of the ESLint library can be denied or approved using the following gestures:

* Clicking on the Status Bar icon.
* Using the quick fix for the corresponding ESLint problem.
* Executing the command **ESLint: Manage Library Execution** from the command palette.

All gestures will open the following dialog:

![ESLint Dialog](images/1_50/eslint-dialog.png)

The chosen action is then reflected in the ESLint Status Bar item in the following way:

* `Allow` will prefix the status bar item with a check mark.
* `Allow Everywhere` will prefix the status bar item with a double check mark.
* `Deny` and `Disable` will prefix the status bar item with a blocked sign.

You can manage our decisions using the following commands:

* **ESLint: Manage Library Execution** will reopen above dialog.
* **ESLint: Reset Library Decisions** lets you reset previous decisions you have made.

### Template string converter

The **[Template String Converter](https://marketplace.visualstudio.com/items?itemName=meganrogge.template-string-converter)** extension converts quotes to backticks when a `$` and `{` are entered within a string in JavaScript and TypeScript files.

![typing a dollar sign then open curly brace within a string converts the quotes to backticks](images/1_50/template-string-converter-extension.gif)

### Docs View

The [Docs View](https://marketplace.visualstudio.com/items?itemName=bierner.docs-view) extension uses the new [Webview View API](#webview-views) to show documentation for the symbol at the current cursor position in the sidebar or panel:

![Showing the docs for Array.from in the panel](images/1_50/docs-view.png)

## Extension authoring

### Submenus

The submenu API has been promoted out of proposed state. Extension authors can now define and use submenus across supported menu locations. Check out the [syntax](https://github.com/microsoft/vscode/issues/100172#issuecomment-*********) as well as the [possibilities](https://github.com/microsoft/vscode/pull/102784#issuecomment-*********) that this API enables.

### Source Control Resource State context value

Instances of `SourceControlResourceState` now support an optional `contextValue: string` field, which allows defining a context value to match again `when` clauses in the `scm/resourceState/context` menu. The context key to use is `scmResourceState`. More details on the original [feature request](https://github.com/microsoft/vscode/issues/86180) and respective [pull request](https://github.com/microsoft/vscode/pull/90952).

### Asynchronously resolve code action

For code action providers, we have added an optional function ([`CodeActionProvider#resolveCodeAction`](https://github.com/microsoft/vscode/blob/1fd7ee15d3a2c50547228b08cefc7c13a5a739d2/src/vs/vscode.d.ts#L2237-L2252)) which allows you to defer the computation of the actual edits a code action is going to make. This should help reduce the time it takes to provide code actions.

### References Viewlet API

The references viewlet now [exports an API](https://github.com/microsoft/vscode-references-view/blob/main/src/references-view.d.ts) that allows its content to be defined by other extensions.

### New theme colors

* `tab.lastPinnedBorder`: Border on the right of the last pinned editor to separate from unpinned editors.
* `gitDecoration.stageDeletedResourceForeground`: Foreground color for staged deletions git decorations.
* `gitDecoration.stageModifiedResourceForeground`: Foreground color for staged modifications git decorations.
* `charts.red`, `charts.blue`, `charts.yellow`, `charts.orange`, `charts.green`, `charts.purple`, `charts.foreground`, `charts.lines`: Colors intended to be used by data visualization extensions.

### Webview Views

The [Webview View API](https://github.com/microsoft/vscode/issues/46585) allows extensions to contribute [webview](https://code.visualstudio.com/api/extension-guides/webview) based views to the sidebar or panel. These views can offer specialized presentations of data, more advanced user interfaces, and much more.

![The 'calico colors' webview view sample extension in the side bar](images/1_50/webview-view.png)

To get started with webview views, take a look at the [webview view extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/webview-view-sample).

### Updated Codicons

We've added the following new icons to our [codicon library](https://code.visualstudio.com/api/references/icons-in-labels):

* `export`
* `graph-left`
* `magnet`
* `notebook`
* `redo`
* `check-all`
* `pinned`

![Updated codicons](images/1_50/codicons.png)

### View title description

The `TreeView` now has an optional [`description`](https://github.com/microsoft/vscode/blob/1fd7ee15d3a2c50547228b08cefc7c13a5a739d2/src/vs/vscode.d.ts#L8618-L8622) property which can be used to show less prominent text next to a tree's title.

![TreeView description](images/1_50/treeview-description.png)

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Notebook API

The following new and changed APIs are part of the notebooks API proposal:

* `NotebookCell#index` - A notebook cell belongs to a notebook document and this new property indicates at which position in the notebook document a cell sits. This property changes when a cell is moved.
* `TextDocument#notebook` - A property of a text document, which signals that it is part of a notebook, as a notebook cell document. This property is `undefined` for ordinary documents.
* Strict Editing - Notebook documents can only be modified via declarative edits that are either part of a `WorkspaceEdit` and submitted via `NotebookEditor#edit`. This ensures data consistency between extension hosts and the UI.

### Comments API

We have added a new property `readOnly` to `CommentThread` to allow hiding the reply button in a comment thread.

## Debug Adapter Protocol

### New invalidated event

A new `invalidated` event has been added to the protocol. This event signals that some state in the debug adapter has changed and requires that the client (frontend) needs to re-render the data previously requested.
Debug adapters do not have to emit the event for runtime changes like "stopped" or thread changes because in these cases the client refetches the new state anyway. However, the event can be used to refresh the UI after rendering formatting has changed in the debug adapter. Debug adapters should only send the event if a true `supportsInvalidatedEvent` capability was received from the client in the ‘initialize’ request. Mock debug has an [example](https://github.com/microsoft/vscode-mock-debug/blob/23fd6bf224234f188eff23eba83b086e1ee5917c/src/mockDebug.ts#L553) for how to use the event.

### Proposal for Conditional Exception support

We are planning to support "Conditional Exceptions" in a future version of VS Code. Since this requires support in the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol/) (DAP) we've prepared a [proposal for "Conditional Exception" support](https://github.com/microsoft/debug-adapter-protocol/issues/137#issuecomment-698211532). If you are a debug extension author and interested in this DAP protocol addition, we'd appreciate your feedback.

## Engineering

### Building VS Code with TypeScript 4.1 beta

We now use the TypeScript 4.1 nightly builds to build VS Code. TypeScript 4.1 adds a few new strictness checks, including [more strict types for `resolve` in promises](https://devblogs.microsoft.com/typescript/announcing-typescript-4-1-beta/#resolves-parameters-are-no-longer-optional-in-promises). This improved type checking helped us to identify and fix potential bugs in our code.

## Documentation

### Updated Create a Dev Container tutorial

We've updated the documentation to help you [create a Dev Container](https://code.visualstudio.com/docs/devcontainers/create-dev-container). The tutorial now clearly walks you through the steps to create a reusable Docker container to match your development environment. You'll learn how to:

* Author a `devcontainer.json` to describe how VS Code will create and start your container.
* Add additional software and frameworks through a `Dockerfile`.
* Combine multiple Docker containers through Docker Compose.
* Build and test your custom development container.

## Notable fixes

- [96293](https://github.com/microsoft/vscode/issues/96293): Run menu does not remember dynamic launch configurations
- [100924](https://github.com/microsoft/vscode/issues/100924): Debug & variable inspector break if filename case in editor differs to project explorer
- [106727](https://github.com/microsoft/vscode/issues/106727): If I switch workspace, focus moves to different VS Code instance
- [103185](https://github.com/microsoft/vscode/issues/103185): Dropdown base font-size increased from Electron 9 update
- [101952](https://github.com/microsoft/vscode/issues/101952): Trigger "Save As..." for virtual document editors when saving
- [106668](https://github.com/microsoft/vscode/issues/106668): Bash for Windows terminals wrapping over 80 characters, terminals aren't sized correctly

## Partner extensions

### Microsoft C++ extension

The Microsoft C/C++ extension is now out of preview and version 1.0 has lots of great features:

* Support for Linux on ARM and ARM 64 - complete with IntelliSense and remote build and debug support.
* Code formatting - Use the same formatting settings as Visual Studio IDE, along with [EditorConfig](https://learn.microsoft.com/visualstudio/ide/cpp-editorconfig-properties) properties.

And check out this [video tutorial](https://www.youtube.com/watch?v=w6PnOlmop9Y) for configuring C++ IntelliSense in VS Code.

![Microsoft C++ extension](images/1_50/cpp-extension.png)

### Microsoft Edge Tools extension

The [Microsoft Edge Tools for VS Code](https://marketplace.visualstudio.com/items?itemName=ms-edgedevtools.vscode-edge-devtools) extension is out of preview and provides the Microsoft Edge dev tools directly in VS Code. You will be able to see your site's runtime HTML structure, alter its layout, fix styling issues as well as see your site's network requests.

![Microsoft Edge Tools for VS Code extension](images/1_50/edge-tools-extension.png)

You can read more about this release and the extension's powerful features in this recent [blog post](https://blogs.windows.com/msedgedev/2020/10/01/microsoft-edge-tools-vscode/).

### Azure Cognitive Search extension

Try out the new Microsoft [Azure Cognitive Search](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurecognitivesearch) extension for working with [Azure Cognitive Search](https://learn.microsoft.com/azure/search/search-what-is-azure-search). The extension makes it easy to develop and manage your search service directly from VS Code and includes full IntelliSense and a rich set of snippets.

![Azure Cognitive Search extension](images/1_50/azure-cognitive-search.png)

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

- [@Abhijith-S-D](https://github.com/Abhijith-S-D): Always trim trailing colon from word link provider links [PR #104090](https://github.com/microsoft/vscode/pull/104090)
- [Alan Ren (@alanrenmsft)](https://github.com/alanrenmsft): normalize EOL setting for build folder [PR #106822](https://github.com/microsoft/vscode/pull/106822)
- [Caio (@amazingcaio)](https://github.com/amazingcaio): associate _.c++ & _.h++ files with c++ [PR #104339](https://github.com/microsoft/vscode/pull/104339)
- [Cameron Little (@apexskier)](https://github.com/apexskier): Fix crash in JSON/HTML LSP servers when initializationOptions are not provided [PR #107775](https://github.com/microsoft/vscode/pull/107775)
- [@brajkowski](https://github.com/brajkowski): Run Selected Text action should use the active code editor [PR #104734](https://github.com/microsoft/vscode/pull/104734)
- [@ChaseKnowlden](https://github.com/ChaseKnowlden): Fix capitalization of GitHub org [PR #106808](https://github.com/microsoft/vscode/pull/106808)
- [David Goldman (@DavidGoldman)](https://github.com/DavidGoldman): Terminal: Support linking local file paths containing '+' [PR #105108](https://github.com/microsoft/vscode/pull/105108)
- [Felipe Santos (@felipecrs)](https://github.com/felipecrs): Fix README.md detection in welcome page [PR #107315](https://github.com/microsoft/vscode/pull/107315)
- [HiDeoo (@HiDeoo)](https://github.com/HiDeoo): Fix typos in vscode.d.ts / API documentation [PR #107885](https://github.com/microsoft/vscode/pull/107885)
- [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH): Add numeric values support for terminal.integrated.fontWeight [PR #106072](https://github.com/microsoft/vscode/pull/106072)
- [ItzLevvie (@ItzLevvie)](https://github.com/ItzLevvie): Fix two spelling mistakes [PR #107073](https://github.com/microsoft/vscode/pull/107073)
- [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  - Fixes SnippetString.appendChoice does not escape commas in choices [PR #107348](https://github.com/microsoft/vscode/pull/107348)
  - Fixes settings editor scrolls slowly when `workbench.list.smoothScrolling` enabled [PR #104284](https://github.com/microsoft/vscode/pull/104284)
  - Fixes outline: shift + tab needs to be pressed twice for focus to go to title actions [PR #107339](https://github.com/microsoft/vscode/pull/107339)
  - Proper fix for #105202 [PR #106293](https://github.com/microsoft/vscode/pull/106293)
- [Josh Bolduc (@joshbolduc)](https://github.com/joshbolduc): Detect Jenkinsfile as groovy [PR #106813](https://github.com/microsoft/vscode/pull/106813)
- [Jiaxun Wei (@LeuisKen)](https://github.com/LeuisKen): Fix #106213 [PR #106225](https://github.com/microsoft/vscode/pull/106225)
- [Luqman Aden (@luqmana)](https://github.com/luqmana): Register mimetype for \*.code-workspace [PR #95637](https://github.com/microsoft/vscode/pull/95637)
- [@mbassit](https://github.com/mbassit): Increased height of proxy authentication dialog to prevent cutting confirmation buttons [PR #83900](https://github.com/microsoft/vscode/pull/83900)
- [@MoQ93](https://github.com/MoQ93): [Git] Allow empty tag message [PR #106751](https://github.com/microsoft/vscode/pull/106751)
- [Mosh Feu (@moshfeu)](https://github.com/moshfeu): Add padding to space the x button of "suggest" tooltip [PR #106200](https://github.com/microsoft/vscode/pull/106200)
- [@msftwindowslinux](https://github.com/msftwindowslinux): force .exe, .dll as binary to fix CRLF/LF problems [PR #107862](https://github.com/microsoft/vscode/pull/107862)
- [Michael Wood (@mwood77)](https://github.com/mwood77): Added additional colour options for differing git staging statuses [PR #107318](https://github.com/microsoft/vscode/pull/107318)
- [Nicholas Rayburn (@nrayburn-tech)](https://github.com/nrayburn-tech): Replace 'master' with branch head when publishing to github [PR #105878](https://github.com/microsoft/vscode/pull/105878)
- [Nicolas Vuillamy (@nvuillam)](https://github.com/nvuillam)
  - Add .nf to Groovy file extensions [PR #106282](https://github.com/microsoft/vscode/pull/106282)
- [Pascal Fong Kye (@pfongkye)](https://github.com/pfongkye): Show filtered stats [PR #107067](https://github.com/microsoft/vscode/pull/107067)
- [Rafał Chłodnicki (@rchl)](https://github.com/rchl): [json-language-features] Fix json/schemaAssociations parameters documentation [PR #106061](https://github.com/microsoft/vscode/pull/106061)
- [@tomerstav](https://github.com/tomerstav): Implemented fix for first parameter being a substring of the second p… [PR #106432](https://github.com/microsoft/vscode/pull/106432)
- [Vyacheslav Pukhanov (@vpukhanov)](https://github.com/vpukhanov)
  - Update gitignore decorations when .git/info/exclude file is edited [PR #106270](https://github.com/microsoft/vscode/pull/106270)
  - Adds commands for --no-verify commit variants [PR #106335](https://github.com/microsoft/vscode/pull/106335)
- [WhizSid (@whizsid)](https://github.com/whizsid): Multi git executable paths [PR #85954](https://github.com/microsoft/vscode/pull/85954)
- [Wojciech Buczek (@Wowol)](https://github.com/Wowol): Add cursor change after revert or stage changes [PR #85074](https://github.com/microsoft/vscode/pull/85074)

Contributions to `vscode-references-view`:

- [Babak K. Shandiz (@babakks)](https://github.com/babakks): Extend removeRefCommand to support call hierarchy items [PR #61](https://github.com/microsoft/vscode-references-view/pull/61)

Contributions to `vscode-vsce`:

- [Ron Buckton (@rbuckton)](https://github.com/rbuckton): Adds detection for yarn, and '--no-yarn' to suppress detection [PR #492](https://github.com/microsoft/vscode-vsce/pull/492)

Contributions to `vscode-json-languageservice`:

* [Sebastian Copei (@KosrenDQ)](https://github.com/KosrenDQ): Add severity DocumentSetting [PR #74](https://github.com/microsoft/vscode-json-languageservice/pull/74)

Contributions to `vscode-css-languageservice`:

* [Avi Vahl (@AviVahl)](https://github.com/AviVahl): chore(deps): vscode-nls@5.0.0 [PR #220](https://github.com/microsoft/vscode-css-languageservice/pull/220)

Contributions to `node-jsonc-parser`:

* [Michael Bullington (@mbullington)](https://github.com/mbullington): Optimize parseLiteral for number-heavy JSON files (ala GeoJSON) [PR #34](https://github.com/microsoft/node-jsonc-parser/pull/34)

Contributions to `vscode-js-debug`:

* [niceSprite (@iFwu)](https://github.com/iFwu): Fix nodeVersionHint Option Description mispairing [PR #770](https://github.com/microsoft/vscode-js-debug/pull/770)
* [Dan Čermák (@dcermak)](https://github.com/dcermak): await nyc.report() or no test coverage gets recorded [PR #750](https://github.com/microsoft/vscode-js-debug/pull/750)

Contributions to `vscode-codicons`:

* [Piers Deseilligny (@piersdeseilligny)](https://github.com/piersdeseilligny): Add magnet icon [PR #24](https://github.com/microsoft/vscode-codicons/pull/24)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
