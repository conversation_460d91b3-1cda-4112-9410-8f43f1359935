[{"name": "Setup", "area": "setup", "topics": [["Overview", "/docs/setup/setup-overview"], ["Linux", "/docs/setup/linux"], ["macOS", "/docs/setup/mac"], ["Windows", "/docs/setup/windows"], ["VS Code for the Web", "/docs/setup/vscode-web"], ["Raspberry Pi", "/docs/setup/raspberry-pi"], ["Network", "/docs/setup/network"], ["Additional Components", "/docs/setup/additional-components"], ["Enterprise", "/docs/setup/enterprise"], ["Uninstall", "/docs/setup/uninstall"]]}, {"name": "Get Started", "area": "getstarted", "topics": [["VS Code Tutorial", "/docs/getstarted/getting-started"], ["Copilot Quickstart", "/docs/getstarted/copilot-quickstart"], ["User Interface", "/docs/getstarted/userinterface"], ["Personalize VS Code", "/docs/getstarted/personalize-vscode"], ["Install Extensions", "/docs/getstarted/extensions"], ["Tips and Tricks", "/docs/getstarted/tips-and-tricks"], ["Intro Videos", "/docs/getstarted/introvideos"]]}, {"name": "Configure", "area": "configure", "topics": [["Display Language", "/docs/configure/locales"], ["Layout", "/docs/configure/custom-layout"], ["Keyboard Shortcuts", "/docs/configure/keybindings"], ["Settings", "/docs/configure/settings"], ["Settings Sync", "/docs/configure/settings-sync"], ["", "", {"name": "Extensions", "area": "configure/extensions", "topics": [["Extension Marketplace", "/docs/configure/extensions/extension-marketplace"], ["Extension Runtime Security", "/docs/configure/extensions/extension-runtime-security"]]}], ["Themes", "/docs/configure/themes"], ["Profiles", "/docs/configure/profiles"], ["", "", {"name": "Accessibility", "area": "configure/accessibility", "topics": [["Overview", "/docs/configure/accessibility/accessibility"], ["Voice Interactions", "/docs/configure/accessibility/voice"]]}], ["Command Line Interface", "/docs/configure/command-line"], ["Telemetry", "/docs/configure/telemetry"]]}, {"name": "Edit code", "area": "editing", "topics": [["Basic Editing", "/docs/editing/codebasics"], ["IntelliSense", "/docs/editing/intellisense"], ["Code Navigation", "/docs/editing/editingevolved"], ["Refactoring", "/docs/editing/refactoring"], ["Snippets", "/docs/editing/userdefinedsnippets"], ["", "", {"name": "Workspaces", "area": "editing/workspaces", "topics": [["Overview", "/docs/editing/workspaces/workspaces"], ["Multi-root Workspaces", "/docs/editing/workspaces/multi-root-workspaces"], ["Workspace Trust", "/docs/editing/workspaces/workspace-trust"]]}]]}, {"name": "Build, Debug, Test", "area": "debugtest", "topics": [["Tasks", "/docs/debugtest/tasks"], ["Debugging", "/docs/debugtest/debugging"], ["Debug Configuration", "/docs/debugtest/debugging-configuration"], ["Testing", "/docs/debugtest/testing"], ["Port Forwarding", "/docs/debugtest/port-forwarding"]]}, {"name": "Source Control", "area": "sourcecontrol", "topics": [["Overview", "/docs/sourcecontrol/overview"], ["Introduction to <PERSON><PERSON>", "/docs/sourcecontrol/intro-to-git"], ["Collaborate on GitHub", "/docs/sourcecontrol/github"], ["FAQ", "/docs/sourcecontrol/faq"]]}, {"name": "Terminal", "area": "terminal", "topics": [["Getting Started Tutorial", "/docs/terminal/getting-started"], ["Terminal Basics", "/docs/terminal/basics"], ["Terminal Profiles", "/docs/terminal/profiles"], ["Shell Integration", "/docs/terminal/shell-integration"], ["Appearance", "/docs/terminal/appearance"], ["Advanced", "/docs/terminal/advanced"]]}, {"name": "GitHub Copilot", "area": "copilot", "topics": [["Overview", "/docs/copilot/overview"], ["Setup", "/docs/copilot/setup"], ["Quickstart", "/docs/copilot/getting-started"], ["Code Completions", "/docs/copilot/ai-powered-suggestions"], ["", "", {"name": "Cha<PERSON>", "area": "copilot/chat", "topics": [["Overview", "/docs/copilot/chat/copilot-chat"], ["Chat Tu<PERSON>l", "/docs/copilot/chat/getting-started-chat"], ["Manage Context", "/docs/copilot/chat/copilot-chat-context"], ["Ask Mode", "/docs/copilot/chat/chat-ask-mode"], ["Edit Mode", "/docs/copilot/chat/copilot-edits"], ["Agent Mode", "/docs/copilot/chat/chat-agent-mode"], ["MCP Servers", "/docs/copilot/chat/mcp-servers"], ["Inline Chat", "/docs/copilot/chat/inline-chat"], ["Prompt Engineering", "/docs/copilot/chat/prompt-crafting"]]}], ["Smart Actions", "/docs/copilot/copilot-smart-actions"], ["Personalize Copilot", "/docs/copilot/copilot-customization"], ["Language Models", "/docs/copilot/language-models"], ["", "", {"name": "Guides", "area": "copilot/guides", "topics": [["Edit notebooks with AI", "/docs/copilot/guides/notebooks-with-ai"], ["Test with AI", "/docs/copilot/guides/test-with-copilot"], ["Debug with AI", "/docs/copilot/guides/debug-with-copilot"]]}], ["Tips and Tricks", "/docs/copilot/copilot-tips-and-tricks"], ["AI Extensibility", "/docs/copilot/copilot-extensibility-overview"], ["FAQ", "/docs/copilot/faq"], ["", "", {"name": "Reference", "area": "copilot/reference", "topics": [["<PERSON><PERSON>lot Cheat Sheet", "/docs/copilot/reference/copilot-vscode-features"], ["Settings Reference", "/docs/copilot/reference/copilot-settings"], ["Workspace Context", "/docs/copilot/reference/workspace-context"]]}]]}, {"name": "Languages", "area": "languages", "topics": [["Overview", "/docs/languages/overview"], ["JavaScript", "/docs/languages/javascript"], ["JSON", "/docs/languages/json"], ["HTML", "/docs/languages/html"], ["<PERSON><PERSON>", "/docs/languages/emmet"], ["CSS, SCSS and Less", "/docs/languages/css"], ["TypeScript", "/docs/languages/typescript"], ["<PERSON><PERSON>", "/docs/languages/markdown"], ["PowerShell", "/docs/languages/powershell"], ["C++", "/docs/languages/cpp"], ["Java", "/docs/languages/java"], ["PHP", "/docs/languages/php"], ["Python", "/docs/languages/python"], ["<PERSON>", "/docs/languages/julia"], ["R", "/docs/languages/r"], ["<PERSON>", "/docs/languages/ruby"], ["Rust", "/docs/languages/rust"], ["Go", "/docs/languages/go"], ["T-SQL", "/docs/languages/tsql"], ["C#", "/docs/languages/csharp"], [".NET", "/docs/languages/dotnet"], ["Polyglot", "/docs/languages/polyglot"], ["Swift", "/docs/languages/swift"]]}, {"name": "Node.js / JavaScript", "area": "nodejs", "topics": [["Working with JavaScript", "/docs/nodejs/working-with-javascript"], ["Node.js Tutorial", "/docs/nodejs/nodejs-tutorial"], ["Node.js Debugging", "/docs/nodejs/nodejs-debugging"], ["Deploy Node.js Apps", "/docs/nodejs/nodejs-deployment"], ["Browser Debugging", "/docs/nodejs/browser-debugging"], ["Angular Tutorial", "/docs/nodejs/angular-tutorial"], ["React Tutorial", "/docs/nodejs/reactjs-tutorial"], ["Vue Tutorial", "/docs/nodejs/vuejs-tutorial"], ["Debugging Recipes", "/docs/nodejs/debugging-recipes"], ["Performance Profiling", "/docs/nodejs/profiling"], ["Extensions", "/docs/nodejs/extensions"]]}, {"name": "TypeScript", "area": "typescript", "topics": [["Tutorial", "/docs/typescript/typescript-tutorial"], ["Compiling", "/docs/typescript/typescript-compiling"], ["Editing", "/docs/typescript/typescript-editing"], ["Refactoring", "/docs/typescript/typescript-refactoring"], ["Debugging", "/docs/typescript/typescript-debugging"]]}, {"name": "Python", "area": "python", "topics": [["Quick Start", "/docs/python/python-quick-start"], ["Tutorial", "/docs/python/python-tutorial"], ["Run Python Code", "/docs/python/run"], ["Editing", "/docs/python/editing"], ["Lin<PERSON>", "/docs/python/linting"], ["Formatting", "/docs/python/formatting"], ["Debugging", "/docs/python/debugging"], ["Environments", "/docs/python/environments"], ["Testing", "/docs/python/testing"], ["Python Interactive", "/docs/python/jupyter-support-py"], ["<PERSON><PERSON><PERSON>", "/docs/python/tutorial-django"], ["FastAPI Tutorial", "/docs/python/tutorial-fastapi"], ["Flask Tutorial", "/docs/python/tutorial-flask"], ["Create Containers", "/docs/python/tutorial-create-containers"], ["Deploy Python Apps", "/docs/python/python-on-azure"], ["Python in the Web", "/docs/python/python-web"], ["Settings Reference", "/docs/python/settings-reference"]]}, {"name": "Java", "area": "java", "topics": [["Getting Started", "/docs/java/java-tutorial"], ["Navigate and Edit", "/docs/java/java-editing"], ["Refactoring", "/docs/java/java-refactoring"], ["Formatting and Linting", "/docs/java/java-linting"], ["Project Management", "/docs/java/java-project"], ["Build Tools", "/docs/java/java-build"], ["Run and Debug", "/docs/java/java-debugging"], ["Testing", "/docs/java/java-testing"], ["Spring Boot", "/docs/java/java-spring-boot"], ["Application Servers", "/docs/java/java-tomcat-jetty"], ["Deploy Java Apps", "/docs/java/java-on-azure"], ["GUI Applications", "/docs/java/java-gui"], ["Extensions", "/docs/java/extensions"], ["FAQ", "/docs/java/java-faq"]]}, {"name": "C++", "area": "cpp", "topics": [["Intro Videos", "/docs/cpp/introvideos-cpp"], ["GCC on Linux", "/docs/cpp/config-linux"], ["GCC on Windows", "/docs/cpp/config-mingw"], ["GCC on Windows Subsystem for Linux", "/docs/cpp/config-wsl"], ["Clang on macOS", "/docs/cpp/config-clang-mac"], ["Microsoft C++ on Windows", "/docs/cpp/config-msvc"], ["Build with CMake", "/docs/cpp/build-with-cmake"], ["CMake Tools on Linux", "/docs/cpp/cmake-linux"], ["C<PERSON>ake Quick Start", "/docs/cpp/cmake-quickstart"], ["Editing and Navigating", "/docs/cpp/cpp-ide"], ["Debugging", "/docs/cpp/cpp-debug"], ["Configure Debugging", "/docs/cpp/launch-json-reference"], ["Refactoring", "/docs/cpp/cpp-refactoring"], ["Settings Reference", "/docs/cpp/customize-cpp-settings"], ["Configure IntelliSense", "/docs/cpp/configure-intellisense"], ["Configure IntelliSense for Cross-Compiling", "/docs/cpp/configure-intellisense-crosscompilation"], ["FAQ", "/docs/cpp/faq-cpp"]]}, {"name": "C#", "area": "csharp", "topics": [["Intro Videos", "/docs/csharp/introvideos-csharp"], ["Get Started", "/docs/csharp/get-started"], ["Navigate and Edit", "/docs/csharp/navigate-edit"], ["IntelliCode", "/docs/csharp/intellicode"], ["Refactoring", "/docs/csharp/refactoring"], ["Formatting and Linting", "/docs/csharp/formatting-linting"], ["Project Management", "/docs/csharp/project-management"], ["Build Tools", "/docs/csharp/build-tools"], ["Package Management", "/docs/csharp/package-management"], ["Run and Debug", "/docs/csharp/debugging"], ["Testing", "/docs/csharp/testing"], ["FAQ", "/docs/csharp/cs-dev-kit-faq"]]}, {"name": "Container <PERSON><PERSON>", "area": "containers", "topics": [["Overview", "/docs/containers/overview"], ["Node.js", "/docs/containers/quickstart-node"], ["Python", "/docs/containers/quickstart-python"], ["ASP.NET Core", "/docs/containers/quickstart-aspnet-core"], ["Debug", "/docs/containers/debug-common"], ["<PERSON><PERSON>", "/docs/containers/docker-compose"], ["Registries", "/docs/containers/quickstart-container-registries"], ["Deploy to Azure", "/docs/containers/app-service"], ["Choose a Dev Environment", "/docs/containers/choosing-dev-environment"], ["Customize", "/docs/containers/reference"], ["Develop with Kubernetes", "/docs/containers/bridge-to-kubernetes"], ["Tips and Tricks", "/docs/containers/troubleshooting"]]}, {"name": "Data Science", "area": "datascience", "topics": [["Overview", "/docs/datascience/overview"], ["Jupyter Notebooks", "/docs/datascience/jupyter-notebooks"], ["Data Science Tutorial", "/docs/datascience/data-science-tutorial"], ["Python Interactive", "/docs/datascience/python-interactive"], ["Data Wrangler Quick Start", "/docs/datascience/data-wrangler-quick-start"], ["Data Wrangler", "/docs/datascience/data-wrangler"], ["PyTorch Support", "/docs/datascience/pytorch-support"], ["Azure Machine Learning", "/docs/datascience/azure-machine-learning"], ["Manage <PERSON><PERSON><PERSON>", "/docs/datascience/jupyter-kernel-management"], ["Jupyter Notebooks on the Web", "/docs/datascience/notebooks-web"]]}, {"name": "Intelligent Apps", "area": "intelligentapps", "topics": [["AI Toolkit Overview", "/docs/intelligentapps/overview"], ["Models", "/docs/intelligentapps/models"], ["Playground", "/docs/intelligentapps/playground"], ["Agent Builder", "/docs/intelligentapps/agentbuilder"], ["Bulk Run", "/docs/intelligentapps/bulkrun"], ["Evaluation", "/docs/intelligentapps/evaluation"], ["Fine-tune", "/docs/intelligentapps/finetune"], ["Model Conversion", "/docs/intelligentapps/modelconversion"], ["FAQ", "/docs/intelligentapps/faq"], ["", "", {"name": "Reference", "area": "intelligentapps/reference", "topics": [["File Structure", "/docs/intelligentapps/reference/FileStructure"], ["Manual Model Conversion", "/docs/intelligentapps/reference/ManualModelConversion"], ["Manual Model Conversion On GPU", "/docs/intelligentapps/reference/ManualConversionOnGPU"], ["Setup Environment Without AI Toolkit", "/docs/intelligentapps/reference/SetupWithoutAITK"], ["Template Project", "/docs/intelligentapps/reference/TemplateProject"]]}]]}, {"name": "Azure", "area": "azure", "topics": [["Overview", "/docs/azure/overview"], ["Getting Started", "/docs/azure/gettingstarted"], ["Resources View", "/docs/azure/resourcesextension"], ["Deployment", "/docs/azure/deployment"], ["VS Code for the Web - Azure", "/docs/azure/vscodeforweb"], ["Containers", "/docs/azure/containers"], ["Azure Kubernetes Service", "/docs/azure/aksextensions"], ["Kubernetes", "/docs/azure/kubernetes"], ["MongoDB", "/docs/azure/mongodb"], ["Remote Debugging for Node.js", "/docs/azure/remote-debugging"]]}, {"name": "Remote", "area": "remote", "topics": [["Overview", "/docs/remote/remote-overview"], ["SSH", "/docs/remote/ssh"], ["Dev Containers", "/docs/remote/dev-containers"], ["Windows Subsystem for Linux", "/docs/remote/wsl"], ["GitHub Codespaces", "/docs/remote/codespaces"], ["VS Code Server", "/docs/remote/vscode-server"], ["Tunnels", "/docs/remote/tunnels"], ["SSH Tutorial", "/docs/remote/ssh-tutorial"], ["WSL Tutorial", "/docs/remote/wsl-tutorial"], ["Tips and Tricks", "/docs/remote/troubleshooting"], ["FAQ", "/docs/remote/faq"]]}, {"name": "Dev Containers", "area": "devcontainers", "topics": [["Overview", "/docs/devcontainers/containers"], ["Tutorial", "/docs/devcontainers/tutorial"], ["Attach to Container", "/docs/devcontainers/attach-container"], ["Create Dev Con<PERSON>er", "/docs/devcontainers/create-dev-container"], ["Advanced Containers", "/docs/devcontainers/containers-advanced"], ["devcontainer.json", "/docs/devcontainers/devcontainerjson-reference"], ["Dev Container CLI", "/docs/devcontainers/devcontainer-cli"], ["Tips and Tricks", "/docs/devcontainers/tips-and-tricks"], ["FAQ", "/docs/devcontainers/faq"]]}, {"name": "Reference", "area": "reference", "topics": [["Default Keyboard Shortcuts", "/docs/reference/default-keybindings"], ["<PERSON><PERSON><PERSON>", "/docs/reference/default-settings"], ["Substitution Variables", "/docs/reference/variables-reference"], ["Tasks <PERSON><PERSON>a", "/docs/reference/tasks-appendix"]]}]