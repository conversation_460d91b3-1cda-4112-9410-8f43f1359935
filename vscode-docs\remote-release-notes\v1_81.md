# July 2023 (version 1.81)

## Tunnels

### Install Tunnels as a service from VS Code

Using the **Remote Tunnels: Turn on Remote Tunnel Access** command, users now have the option to either run the tunnel in the current VS Code instance, or install it as a service on their system. Service-mode is supported on Windows, macOS, and systemd-based Linux systems.

When installed as a service, the **Turn off Remote Tunnel Access** command will uninstall the service.

## Dev Containers

### Simplified @devcontainers/cli installation

You no longer need a build toolchain to install the [@devcontainers/cli](https://github.com/devcontainers/cli) package. A simple `npm install -g @devcontainers/cli` will do the trick. The [Dev Container CLI](https://code.visualstudio.com/docs/devcontainers/devcontainer-cli) lets you to build and manage development containers, and is a companion to the [Development Containers Specification](https://containers.dev).

### Record Feature dependencies in lockfile

[Feature](https://code.visualstudio.com/docs/devcontainers/containers#_dev-container-features) dependencies are now also recorded in the `devcontainer-lock.json`. You can enable the use of lockfiles with the `dev.containers.experimentalLockfile` user setting:

![Lockfile user setting](images/v1_81/lockfile-setting.png)
