# August 2024 (version 1.93)

## Dev Containers

### Optional paths for Templates

During publishing, [Dev Container Template](https://containers.dev/templates) authors can now indicate optional files or folders within the Template. This uses a new property on the Template metadata called ['optionalPaths'](https://github.com/devcontainers/spec/blob/e2d850e48292b19b8beb3575b7e538a7bfdad981/docs/specs/devcontainer-templates.md#the-optionalpaths-property). End users of the Template can decide whether or not to apply those files to their workspace.

For example, a Template author might want to let a user choose to include/exclude starter code or platform-specific files.

The Dev Containers extension prompts users to select which files or directories should be included when applying the Template.

![Optional path prompt from Dev Containers extension](images/1_93/optionalPaths.png)

## SSH

### **Feedback Wanted**: Expanded compatibility for remote OSs

Effort has been made this milestone to make it possible to use the `Remote - SSH` extension with historically unsupported remote machines.

Changes include removing the hard requirement for `bash` and supporting the BusyBox toolkit of programs.

This effort is experimental and subject to change.  We encourage users to try connecting to previously unsupported servers and [share their experience](https://github.com/microsoft/vscode-remote-release/issues/new).

> **Note**: The <a href="vscode://settings/remote.SSH.useExecServer" codesetting="true">`remote.SSH.useExecServer`</a> setting must be enabled and [VS Code Server dependencies like `libstdc`](https://code.visualstudio.com/docs/remote/linux#_remote-host-container-wsl-linux-prerequisites) are still required on the remote machine.
