// Place your settings in this file to overwrite default and user settings.
{
  "git.branchProtection": [
    "main"
  ],
  "git.branchProtectionPrompt": "alwaysCommitToNewBranch",
  "editor.wordWrap": "on",
    "files.eol": "\n",
    "files.trimTrailingWhitespace": true,
    "files.associations": {
        "**/toc.json": "jsonc"
    },
    "markdown.validate.enabled": true,
    "markdown.validate.ignoredLinks": [
        "/Download",
        "/insiders"
    ],
    "markdown.copyFiles.destination": {
        // /release-notes/v123.md -> /release-notes/images/123/img.png
        "/release-notes/**/*": "/release-notes/images/${documentBaseName/v(.*)/$1/}/",

        // Put into 'images' directory next to file
        "/api/**/*": "images/${documentBaseName}/"
    },
    "editor.codeActionsOnSave": {
        "source.organizeLinkDefinitions": "explicit"
    },
    "markdown.editor.filePaste.videoSnippet": "<video src=\"${src}\" title=\"${title}\" autoplay loop controls muted></video>",

    "doc-assistant.milestone": "April 2025",

    "github.copilot.chat.codeGeneration.useInstructionFiles": false,
}