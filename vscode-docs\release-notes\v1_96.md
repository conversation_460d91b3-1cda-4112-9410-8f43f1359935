---
Order: 105
TOCTitle: November 2024
PageTitle: Visual Studio Code November 2024
MetaDescription: Learn what is new in the Visual Studio Code November 2024 Release (1.96)
MetaSocialImage: 1_96/release-highlights.png
Date: 2024-12-11
DownloadVersion: 1.96.4
---
# November 2024 (version 1.96)

**Update 1.96.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2024+Recovery+1%22+is%3Aclosed) and enables the [GitHub Copilot Free plan](#github-copilot-free-plan).

**Update 1.96.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2024+Recovery+2%22+is%3Aclosed).

**Update 1.96.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2024+Recovery+3%22+is%3Aclosed).

**Update 1.96.4**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22November+2024+Recovery+4%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the November 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [GitHub Copilot Free](#github-copilot-free-plan) - Use Copilot for free with the GitHub Copilot Free plan
* [Overtype mode](#overtype-mode) - Switch between overwrite or insert mode in the editor
* [Add imports on paste](#paste-with-imports-for-javascript-and-typescript) - Automatically add missing TS/JS imports when pasting code
* [Test coverage](#attributable-coverage) - Quickly filter which code is covered by a specific test
* [Move views](#move-views-between-primary-and-secondary-side-bar) - Easily move views between the Primary and Secondary Side Bar
* [Terminal ligatures](#ligature-support) - Use ligatures in the terminal
* [Extension allow list](#configure-allowed-extensions) - Configure which extensions can be installed in your organization
* [Debug with Copilot](#debugging-with-copilot) - Use `copilot-debug` terminal command to start a debugging session
* [Chat context](#add-context) - Add symbols and folders as context Chat and Edits
* [Move from chat to Copilot Edits](#move-chat-session-to-copilot-edits) - Switch to Copilot Edits to apply code suggestions from Chat

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## GitHub Copilot

### GitHub Copilot Free plan

We're excited to announce an all new **free tier for GitHub Copilot**. Sign up for the [GitHub Copilot Free plan](https://github.com/github-copilot/signup), and all you need is a GitHub account. You are entitled to a number of completions and chat interactions per month, which reset each month.

You can sign up directly from within VS Code. Follow the steps in the [Copilot Setup guide](https://code.visualstudio.com/docs/copilot/setup).

![Chat view shows the Copilot message and a button that enables you to sign in to use Copilot.](images/1_96/copilot-chat-view-new-user.png)

Learn more about the [Copilot Free plan details and conditions](https://docs.github.com/en/copilot/about-github-copilot/subscription-plans-for-github-copilot).

### Copilot Edits

Last milestone, we introduced [Copilot Edits](https://code.visualstudio.com/docs/copilot/copilot-edits) (currently in preview), which allows you to quickly edit multiple files at once using natural language. Since then, we've continued to iterate on the experience. You can try out Copilot Edits by opening the Copilot menu in the Command Center, and then selecting Open Copilot Edits, or by triggering `kb(workbench.action.chat.openEditSession)`.

#### Progress and editor controls

Copilot Edits can make multiple changes across different files. You can now more clearly see its progress as edits stream in. And with the editor overlay controls, you can easily cycle through all changes and accept or discard them.

<video src="images/1_96/chat-edits.mp4" title="Copilot Edits changing a file" autoplay loop controls muted></video>

#### Move chat session to Copilot Edits

You might use the Chat view to explore some ideas for making changes to your code. Instead of applying individual code blocks, you can now move the chat session to Copilot Edits to apply all code suggestions from the session.

![Edit with Copilot showing for a chat exchange.](images/1_96/chat-move.png)

#### Working set suggested files

In Copilot Edits, the working set determines the files that Copilot Edits can suggest changes for. To help you add relevant files to the working set, for a Git repo, Copilot Edits can now suggest additional files based on the files you've already added. For example, Copilot Edits will suggest files that are often changed together with the files you've already added.

Copilot shows suggested files alongside the **Add Files** button in the working set. You can also select **Add Files** and then select **Related Files** to choose from a list of suggested files.

<video src="images/1_96/working-set-suggested-files.mp4" title="Add suggested files to Copilot Edits working set." autoplay loop controls muted></video>

#### Restore Edit sessions after restart

Edit sessions are now fully restored after restarting VS Code. This includes the working set, acceptance state, as well as the file state of all past edit steps.

#### Add to working set from Explorer, Search, and editor

You can add files to your Copilot Edits working set with the new **Add File to Copilot Edits** context menu action for search results in the Search view and for files in the Explorer view. Additionally, you can also attach a text selection to Copilot Edits from the editor context menu.

![Add a file from the explorer view to Copilot Edits](images/1_96/add-file-to-edits.png)

### Debugging with Copilot

Configuring debugging can be tricky, especially when you're working with a new project or language. This milestone, we're introducing a new `copilot-debug` terminal command to help you debug your programs using VS Code. You can use it by prefixing the command that you would normally run with `copilot-debug`. For example, if you normally run your program using the command `python foo.py`, you can now run `copilot-debug python foo.py` to start a debugging session.

<video src="images/1_96/copilot-debug.mp4" title="Use the copilot-debug command to debug a Go program." autoplay loop controls muted></video>

After your program exits, you are given options to rerun your program or to view, save, or regenerate the VS Code [launch configuration](https://code.visualstudio.com/docs/editor//debugging#launch-configurations) that was used to debug your program.

![The terminal shows options to rerun, regenerate, save, or the launch config after a debugging session.](./images/1_96/copilot-debug.png)
_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

#### Tasks Support

Copilot's debugging features, including `copilot-debug` and the `/startDebugging` intent, now generate `preLaunchTask`s as needed for code that needs a compilation step before debugging. This is often the case for compiled languages, such as Rust and C++.

### Add Context

We’ve added new ways to include symbols and folders as context in Copilot Chat and Copilot Edits, making it easier to reference relevant information during your workflow.

#### Symbols

Symbols can now easily be added to Copilot Chat and Copilot Edits by dragging and dropping them from the Outline View or Breadcrumbs into the Chat view.

<video src="images/1_96/context_symbols_dnd.mp4" title="Dragging and dropping symbols from the outline view and editor breadcrumbs into copilot chat" autoplay loop controls muted></video>

We’ve also introduced symbol completion in the chat input. By typing `#` followed by the symbol name, you’ll see suggestions for symbols from files you've recently worked on.

<video src="images/1_96/context_symbols_completion.mp4" title="After typing # completions show for files and symbols and further typing enables to filter down the completion items" autoplay loop controls muted></video>

To reference symbols across your entire project, you can use `#sym` to open a global symbols picker.

<video src="images/1_96/context_symbols_sym.mp4" title="Writing #sym allows to see the completion item #sym to open a global symbol picker" autoplay loop controls muted></video>

#### Folders

Folders can now be added as context by dragging them from the Explorer, Breadcrumbs, or other views into Copilot Chat.

<video src="images/1_96/context_folder_chat.mp4" title="Dragging and dropping the @types folder into copilot chat and asking how to implement a share provider" autoplay loop controls muted></video>

When a folder is dragged into Copilot Edits, all files within the folder are included in the working set.

<video src="images/1_96/context_folder_edits.mp4" title="Dragging and dropping a folder into copilot edits adds all files in the folder to copilot edits" autoplay loop controls muted></video>

### Copilot usage graph

VS Code extensions can use the VS Code API to [build on the capabilities of Copilot](https://code.visualstudio.com/docs/copilot/copilot-extensibility-overview). You can now see a graph of an extension's Copilot usage in the Runtime Status view. This graph shows the number of chat requests that were made by the extension over the last 30 days.

![Copilot usage graph in the Runtime Status view](images/1_96/copilot-usage-chart.png)

### Custom instructions for commit message generation

Copilot can help you generate commit messages based on the changes you've made. This milestone, we added support for custom instructions when generating a commit message. For example, if your commit messages need to follow a specific format, you can describe this in the custom instructions.

You can use the `setting(github.copilot.chat.commitMessageGeneration.instructions)` setting to either specify the custom instructions or specify a file from your workspace that contains the custom instructions. These instructions are appended to the prompt that is used to generate the commit message. Get more information on how to [use custom instructions](https://code.visualstudio.com/docs/copilot/copilot-customization).

### Inline Chat

This milestone, we have further improved the user experience of Inline Chat: we made the progress reporting more subtle, while streaming in changes squiggles are disabled, and detected commands are rendered more nicely.

Also, we have continued to improve our pseudo-code detection and now show a hint that you can continue with Inline Chat when a line is mostly natural language. This functionality lets you type pseudo code in the editor, which is then used as a prompt for Inline Chat. You can also trigger this flow by pressing `kb(inlineChat.startWithCurrentLine)`.

![Inline Chat hint for a line that is dominated by natural language.](images/1_96/inline-chat-nl-hint.png)

Additionally, there is a new, experimental, setting to make an Inline Chat hint appear on empty lines. This setting can be enabled via `setting(inlineChat.lineEmptyHint)`. By default, this setting is disabled.

### Terminal Chat

Terminal Inline Chat has a fresh coat of paint that brings the look and feel much closer to editor Inline Chat:

![Terminal inline chat looks a lot like editor chat now](images/1_96/copilot-terminal-chat.png)

Here are some other improvements of note that were made:

* The layout and positioning of the widget is improved and generally behaves better
* There's a model picker
* The buttons on the bottom are now more consistent

### Performance improvements for `@workspace`

When you use [`@workspace`](https://code.visualstudio.com/docs/copilot/workspace-context) to ask Copilot about your currently opened workspace, we first need to narrow the workspace down into a set of relevant code snippets that we can hand off to Copilot as context. If your workspaces is backed by a GitHub repo, we can find these relevant snippets quickly by using Github code search. However, as the code search index tracks the main branch of your repository, we couldn't rely on it for local changes or when on a branch.

This milestone, we've worked bring the speed benefits of Github search to branches and pull requests. This means that we now search both the remote index based on your repo's main branch, along with searching any locally changed files. We then merge these results together, giving Copilot a fast and up to date set of snippets to work with. You can read more about [Github code search and how to enable it](https://docs.github.com/en/enterprise-cloud@latest/copilot/github-copilot-enterprise/copilot-chat-in-github/using-github-copilot-chat-in-githubcom#asking-a-question-about-a-specific-repository-file-or-symbol).

## Accessibility

### Code Action accessibility signals

Some code actions can take a long time to complete, for example a quick fix that calls an external service to generate image alt text. It might not be obvious when they were triggered or when they're fully applied. Therefore, we added accessibility signals to indicate that a code action was triggered or applied.

You can enable these signals with the `setting(accessibility.signals.codeActionTriggered)` and `setting(accessibility.signals.codeActionApplied)` settings.

### Automatic focus management in the REPL

We introduced a new setting to improve accessibility when working in the REPL. With `setting(accessibility.replEditor.autoFocusReplExecution)`, you can now specify whether focus remains unchanged (`none`), moves to the input box (`input`), or shifts to the most recently executed cell (`lastExecution`) whenever code is executed. By default, the focus moves to the input box.

## Workbench

### Improved extension search results

When you search for extensions using free-form text in the Extensions view, installed extensions now appear at the top of the search results. This makes it easier to find and manage your installed extensions when searching through the Marketplace.

![Installed extensions shown at top of search results.](images/1_96/extension-search-order.png)

### Download extensions from the Extensions view

You can now download extensions directly from VS Code by using the download action in the context menu of an extension in the Extensions view. This can be useful if you want to download an extension without installing it.

![Context menu option to download an extension from the Extensions view.](images/1_96/extensions-download.png)

### Extension disk space

You can now see the memory usage of an extension on disk in the Extensions editor. This can help you understand how much disk space an extension is using.

![Extension memory usage on disk shown in the Extensions view.](images/1_96/extension-memory-usage-on-disk.png)

### Find in Explorer improvements

In the September release, we introduced the ability to find files in the Explorer across the entire project, a capability that was previously unavailable. However, this update temporarily removed highlight mode and limited certain actions.

In this release, we’re bringing back highlight mode. This feature allows you to easily locate files and folders across your workspace, with matching results highlighted for better visibility. Additionally, we’ve introduced a new visual indicator on collapsed folders, showing if matches are hidden within them.

<video src="images/1_96/explorer_find_highlight.mp4" title="Find in the Explorer highlights all matching results and adds a badge to folders indicating the number of matches inside." autoplay loop controls muted></video>

The filter toggle remains available, enabling you to focus only on files and folders that match your query by hiding non-matching items. We also reenabled all context menu actions we had to disable in a previous release.

<video src="images/1_96/explorer_find_filter.mp4" title="Find in the Explorer with filter mode enabled shows only the files and folders that match the search term." autoplay loop controls muted></video>

We’ve also improved the user experience when using the find control. When scrolled to the top of the file explorer, additional space is created at the top, ensuring the control doesn’t obstruct your search results.

![The find control is rendered above the first file or folder in the explorer when scrolled to the top.](images/1_96/explorer_find_empty_space.png)

### Move views between Primary and Secondary Side Bar

You could already [move a view container](https://code.visualstudio.com/docs/configure/custom-layout#_drag-and-drop-views-and-panels) to another location by using drag and drop or by using the **Move View** command. You can now directly use the Move To context menu action on a view container to move it between the Primary Side Bar, Secondary Side Bar, or Panel area.

<video src="images/1_96/move-views.mp4" title="Move view containers" autoplay loop controls muted></video>

### Hide navigation controls in the title area

Some people prefer to keep the title area as clean as possible. We added a new setting `setting(workbench.navigationControl.enabled)` that enables you to hide the back/forward buttons in the title area.

You can also access this setting by right-clicking in the title area, and selecting **Navigation Controls**.

![Navigation Controls context menu when right-clicking the VS Code title area.](images/1_96/nav-controls.png)

## Editor

### Configure paste and drop behavior

When you drag and drop or copy and paste a file into a text editor, VS Code provides multiple ways to insert it into that file. By default, VS Code tries to insert the file's workspace relative path. Now you can use the drop/paste control to switch how the resource is inserted. Extensions can also provide customized edits, such as in Markdown, which [provides edits that insert Markdown links](https://code.visualstudio.com/updates/v1_67#_markdown-drop-into-editor-to-create-link).

With the new `setting(editor.pasteAs.preferences)` and `setting(editor.dropIntoEditor.preferences)` settings, you can now specify a preference for which edit type will be used by default. For example, if you'd like copy/paste to always insert the absolute path of pasted files, just set:

```json
"editor.pasteAs.preferences": [
    "uri.path.absolute"
]
```

These settings are ordered lists of edit kinds. The first matching edit of a preferred kind is applied by default. You can still use the drop/paste control to change to a different type of edit after the default edit is applied.

These new settings play nicely with our [new copy and paste with imports support in JavaScript and TypeScript](#paste-with-imports-for-javascript-and-typescript). This feature automatically adds imports when copy and pasting code across JavaScript or TypeScript files. To avoid disrupting your workflows, by default, we decided that paste just inserts plain text and `paste with imports` is offered as an option in the paste control. However, if you'd like VS Code to always try to paste with imports, just set:

```json
"editor.pasteAs.preferences": [
    "text.updateImports"
]
```

Now, VS Code automatically tries to paste with imports when possible, falling back to pasting plain text if no paste with imports edit is available. Right now, this only works for JavaScript and TypeScript, but we hope additional languages will adopt support over time.

Finally, you can now also specify a preferred paste style when setting up a `editor.action.pasteAs` keybinding. The keybinding below will always try pasting and updating imports:

```json
{
    "key": "ctrl+shift+v",
    "command": "editor.action.pasteAs",
    "args": {
        "preferences": [
            "text.updateImports"
        ]
    }
}
```

### Persist editor find history

The Find control now can persist the search history across sessions and restores it across VS Code restarts. The search history is stored per workspace and can be disabled via the `setting(editor.find.history)` setting.

<video src="images/1_96/editor-find-history.mp4" title="Demo of the persistence of editor find history" autoplay loop controls muted></video>

### Overtype mode

Did you know that VS Code didn't support overwriting text in the editor, unless you installed the Vim keymap? On popular request, we now added overtype mode to overwrite text in the editor instead of inserting it when typing. A useful scenario for this is when editing Markdown tables, where you want to keep the table cell boundaries nicely aligned.

This mode can be toggled with the command **View: Toggle Overtype/Insert Mode** or by using the `kbstyle(Insert)` key on your keyboard. When you're in overtype mode, the Status Bar shows an `OVR` indicator.

<video src="images/1_96/overtype.mp4" title="Overtype mode" autoplay loop controls muted></video>

It is possible to change the cursor style while in overtype mode by using the setting `setting(editor.overtypeCursorStyle)`. In addition, there is a setting `setting(editor.overtypeOnPaste)`, which determines whether pasting in overtype mode should overwrite or insert. The default behavior is to insert pasted text.

## Source Control

### Git blame information (Experimental)

This milestone, we have added experimental support for displaying blame information using editor decorations and a Status Bar item. You can enable this functionality by using the `setting(git.blame.editorDecoration.enabled:true)` and `setting(git.blame.statusBarItem.enabled:true)` settings. You can hover over the blame information to see more commit details.

<video src="images/1_96/git-blame.mp4" title="Show git blame information in editor and Status Bar." autoplay loop controls muted></video>

You can customize the format of the message that is shown in the editor and in the Status Bar with the `setting(git.blame.editorDecoration.template)` and `setting(git.blame.statusBarItem.template)` settings. You can use variables for the most common information. For example, the following template shows the subject of the commit, the author's name, and the author's date relative to now:

```json
{
  "git.blame.editorDecoration.template": "${subject}, ${authorName} (${authorDateAgo})"
}
```

If you would like to adjust the color of the editor decoration, use the `git.blame.editorDecorationForeground` theme color.

Give this experimental feature a try and let us know what you think.

### Source Control Graph title actions

Based on user feedback, we have brought back the Pull, and Push actions to the Source Control Graph view title bar. These actions are enabled if the current history item reference is shown in the Source Control Graph.

If you do not want to use these actions, or any other actions from the Source Control Graph view title bar, you can right-click on the title bar and hide them.

![Source Control Graph title actions and context menu to hide specific items.](images/1_96/source-control-graph-title-actions.png)

## Notebooks

### Selection highlight across cells

Selection highlighting is now supported within notebooks, allowing for textual selection based highlights across multiple cells. This is controlled with the preexisting setting `setting(editor.selectionHighlight)`.

<video src="images/1_96/notebook-selection-highlight.mp4" title="Notebook selection highlighting demo" autoplay loop controls muted></video>

### Multi Cursor: Select All Occurrences of Find Match

Notebooks now support the keyboard shortcut for **Select All Occurrences of Find Match**. This can be found with the command id `notebook.selectAllFindMatches` and can be used by default with the keystroke `kb(notebook.selectAllFindMatches)`.

<video src="images/1_96/notebook-select-all-occurrences.mp4" title="Notebook select all occurrences multicursor demo" autoplay loop controls muted></video>

### Run Cells in Section for Markdown

Notebooks now have the **Run Cells in Section** action exposed to the cell toolbar of Markdown cells. If the Markdown cell has a header, all cells contained within the section and children sections are executed. If there is no header, this executes all cells in the surrounding section, if possible.

<video src="images/1_96/notebook-md-toolbar-run-in-section.mp4" title="Notebook run in section from markdown cell toolbar demo" autoplay loop controls muted></video>

### Cell execution time verbosity

The execution time information within the cell status bar now has an option for increased verbosity. This can be turned on with the setting `setting(notebook.cellExecutionTimeVerbosity)` and is able to display the execution timestamp in addition to the duration.

![Verbose cell execution time within cell status bar.](images/1_96/notebook-verbose-execution-time.png)

## Terminal

### Ligature support

Ligatures are now supported in the terminal, regardless of whether [GPU acceleration](https://code.visualstudio.com/docs/terminal/appearance#_gpu-acceleration) is being used. This feature can be turned on with the setting `setting(terminal.integrated.fontLigatures)`:

![Fonts that support ligatures like ->, ==>, and so on will now visually look like single characters](images/1_96/terminal-ligatures.png)

In order to use this feature, make sure you also use a font that supports ligatures `setting(terminal.integrated.fontFamily)`.

### New variables for customizing terminal tabs

What text appears in terminal tabs is determines by the `terminal.integrated.tabs.title` and `terminal.integrated.tabs.description` settings which allow the use of a collection of variables. We now support the following new variables:

- `${shellType}` - The detected type of shell that is being used in the terminal. This is similar the default value, but it will not change to `git` for example when running a git command.
- `${shellCommand}` - The command that is being run in the terminal. This requires [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration).

  ![alt text](images/1_96/terminal_shellCommand.png)
- `${shellPromptInput}` - The command that is being run in the terminal or the current detected prompt input. This requires [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration).

  ![Typing "echo hello" in the terminal will show "echo hello|" in the tab when configured](images/1_96/terminal_shellPromptInput.png)

### Run recent command now shows the history source file

The [run recent command](https://code.visualstudio.com/docs/terminal/shell-integration#_run-recent-command) shell integration feature now includes full size headers for the source of the command, including the history file where relevant and a convenient button to open it.

![alt text](images/1_96/terminal-run-recent-command.png)

The default keybinding for this command is `Ctrl+Alt+R`.

### New supported link format

Links with the format `/path/to/file.ext, <line>` should now be detected as links in the terminal.

## Testing

### Attributable coverage

This milestone, we finalized an API that enables extensions to provide coverage on a per-test basis, so you can see exactly what code any given test executed. When attributable coverage is available, a filter button is available in the Test Coverage view, in editor actions, in the Test Coverage toolbar when toggled on (via the **Test: Test Coverage Toolbar** command), or simply by using the **Test: Filter Coverage by Test** command.

<video src="images/1_96/per-test-coverage.mp4" title="Demo of the per-test coverage feature." autoplay loop controls muted></video>

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

### Reworked inline failure messages

We reworked test failure messages to be both more eye-catching and less obtrusive. This is particularly useful for busy scenarios, such as in diffs from SCM or Copilot Edits. Selecting the failure message still opens a peek control to show the complete details of the failure.

![Image of new test error messages in the editor.](./images/1_96/test-errors.png)

### Improvements to the continuous run UI

Previously, the global state of continuous test runs, togglable via the "eye" icon in the Test Explorer view, would toggle on or off continuous running with the default set of run profiles.

We reworked the continuous run UI to include a drop-down menu to turn continuous run on or off individually per-profile. Selecting the indicator toggles the last used set of run profiles on or off.

## Languages

### TypeScript 5.7

Our JavaScript and TypeScript support now uses TypeScript 5.7. This major update includes a number of language and tooling improvements, along with important bug fixes and performance optimizations.

You can read all about the TypeScript 5.7 release on the [TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-5-7/). We've also included a few tooling highlights in the following sections.

### Paste with imports for JavaScript and TypeScript

Tired of having to add imports after moving code between files? Try the Paste with imports feature for TypeScript 5.7+. Now whenever you copy and paste code between JavaScript or TypeScript, VS Code can add imports for the pasted code.

<video src="images/1_96/jsts-update-imports-paste.mp4" title="Imports are automatically updated when pasting code between files in JS and TS." autoplay loop controls muted></video>

Notice how not only imports are added, even a new export was added for a local variable that was used in the pasted code!

While we think this feature is a huge time saver, we also are sensitive to disrupting your existing workflow. That's why, by default, we've kept it so copy and paste always inserts just the pasted text. If a `paste with imports` edit is available, you then see the paste control, which lets you select the `paste with imports` edit.

![Paste control that shows options to insert plain text or paste with imports.](images/1_96/jsts-paste-widget.png)

If you prefer always pasting with imports, you can use the new [`editor.pasteAs.preferences` setting](#configure-paste-and-drop-behavior):

```json
"editor.pasteAs.preferences": [
    "text.updateImports"
]
```

This will always try pasting with imports if an edit is available.

You can also setup a keybinding to paste with imports if available:

```json
{
    "key": "ctrl+shift+v",
    "command": "editor.action.pasteAs",
    "args": {
        "preferences": [
            "text.updateImports"
        ]
    }
}
```

If you prefer, you can even do the reverse and make paste with imports the default and add a keybinding to paste as plain text:

```json
"editor.pasteAs.preferences": [
    "text.updateImports"
]
```

```json
{
    "key": "ctrl+shift+v",
    "command": "editor.action.pasteAs",
    "args": {
        "preferences": [
            "text.plain"
        ]
    }
}
```

Finally, if you want to fully disable `paste with imports`, you can use `setting(typescript.updateImportsOnPaste.enabled)` and `setting(javascript.updateImportsOnPaste.enabled)`.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* `remote-ssh` Copilot chat participant
* Enhanced session logging

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_96.md).

## Enterprise support

### Configure allowed extensions

You can now control which extensions can be installed in VS Code using the `setting(extensions.allowed)` setting.  This setting allows you to specify allowed or blocked extensions by publisher, specific extensions and versions. If an extension or version is blocked, it will be disabled if already installed. You can specify the following types of extension selectors:

* Allow or block all extensions from a publisher
* Allow or block specific extensions
* Allow specific extension versions
* Allow specific extension versions and platforms
* Allow only stable versions of an extension
* Allow only stable extension versions from a publisher

The following JSON snippet shows examples of the different setting values:

```json
"extensions.allowed": {
    // Allow all extensions from the 'microsoft' publisher. If the key does not have a '.', it means it is a publisher ID.
    "microsoft": true,

    // Allow all extensions from the 'github' publisher
    "github": true,

    // Allow prettier extension
    "esbenp.prettier-vscode": true,

    // Do not allow docker extension
    "ms-azuretools.vscode-docker": false,

    // Allow only version 3.0.0 of the eslint extension
    "dbaeumer.vscode-eslint": ["3.0.0"],

    // Allow multiple versions of the figma extension
    "figma.figma-vscode-extension": ["3.0.0", "4.2.3", "4.1.2"],

    // Allow version 5.0.0 of the rust extension on Windows and macOS
    "rust-lang.rust-analyzer": ["5.0.0@win32-x64", "5.0.0@darwin-x64"],

    // Allow only stable versions of the GitHub Pull Requests extension
    "github.vscode-pull-request-github": "stable",

    // Allow only stable versions from redhat publisher
    "redhat": "stable"
}
```

Specify publishers by their publisher ID. If a key does not have a period (`.`), it is considered a publisher ID. If a key has a period, it is considered an extension ID. The use of wildcards is currently not supported.

You can use `microsoft` as the publisher ID to refer to all extensions published by Microsoft, even though they might have different publisher IDs.

Version ranges are not supported. If you want to allow multiple versions of an extension, you must specify each version individually. To further restrict versions by platform, use the `@` symbol to specify the platform. For example, `"rust-lang.rust-analyzer": ["5.0.0@win32-x64", "5.0.0@darwin-x64"]`.  For more details, refer to the [enterprise documentation](https://code.visualstudio.com/docs/setup/enterprise#configure-allowed-extensions).

Administrators can also configure this setting via group policy on Windows. For more information, see the [Group Policy on Windows](https://code.visualstudio.com/docs/setup/enterprise#group-policy-on-windows) section in the enterprise documentation.

## Set up VS Code with preinstalled extensions

You can set up VS Code with a set of preinstalled extensions (*bootstrap*). This functionality is useful in cases where you prepare a machine image, virtual machine, or cloud workstation where VS Code is preinstalled and specific extensions are immediately available for users.

> **Note**: Support for preinstalling extensions is currently only available on Windows.

Follow these steps to bootstrap extensions:

1. Create a folder `bootstrap\extensions` in the VS Code installation directory.

1. Download the [VSIX files](https://code.visualstudio.com/docs/configure/extensions/extension-marketplace#_can-i-download-an-extension-directly-from-the-marketplace) for the extensions that you want to preinstall and place them in the `bootstrap\extensions` folder.

1. When a user launches VS Code for the first time, all extensions in the `bootstrap\extensions` folder are installed silently in the background.

Users can still uninstall extensions that were preinstalled. Restarting VS Code after uninstalling an extension will not reinstall the extension.

## Contributions to extensions

### Python

#### Python Environments extension

In this release we are introducing the Python Environments extension, now available in preview on the Marketplace.

This extension simplifies Python environment management, offering a UI to create, delete, and manage environments, along with package management for installing and uninstalling packages.

Designed to integrate seamlessly with your preferred environment managers via various APIs, it supports Global Python interpreters, venv, and Conda by default. Developers can build extensions to add support for their favorite Python environment managers and integrate with our extension UI, enhancing functionality and user experience.

You can download the Python Environments in the Marketplace, and use it with the pre-release version of the Python extension.

#### Python testing enhancements

* The `--rootdir` argument for pytest is now dynamically adjusted based on the presence of a `python.testing.cwd` setting in your workspace.
* Restarting a test debugging session now reruns only the specified tests.
* Coverage support updated to handle `NoSource` exceptions.
* `pytest-describe` plugin is supported with test detection and execution in the UI.
* Testing Rewrite now leverages FIFO instead of UDS for interprocess communication allowing users to harness pytest plugins like `pytest_socket` in their own testing design.
* **Rewrite Nearing Default Status:** This release addresses [the final known issue](https://github.com/microsoft/vscode-python/issues/23279) in the testing rewrite, and unless further issues arrive, the rewrite experiment will be turned off and the rewrite set to default in early 2025.

#### Python REPL enhancements

* Leave focus on editor after smart-send to Native REPL
* Improved handling after reload for Native REPL
* Fix indentation error issues with Python 3.13 in VS Code terminal

#### Pylance "full" language server mode

The `python.analysis.languageServerMode` setting now also supports `full` mode, enabling you to take advantage of the complete range of Pylance's functionality and the most comprehensive IntelliSense experience. It's worth noting that this comes at the cost of lower performance, as it can cause Pylance to be resource-intensive, particularly in large codebases.

The `python.analysis.languageServerMode` setting now changes the default values of the following settings, depending on whether it's set to `light`, `default` or `full`:

| Setting                                                | light    | default | full |
|--------------------------------------------------------|----------|---------|------|
| python.analysis.exclude                                | \["**"\] | []      | []   |
| python.analysis.useLibraryCodeForTypes                 | false    | true    | true |
| python.analysis.enablePytestSupport                    | false    | true    | true |
| python.analysis.indexing                               | false    | true    | true |
| python.analysis.autoImportCompletions                  | false    | false   | true |
| python.analysis.showOnlyDirectDependenciesInAutoImport | false    | false   | true |
| python.analysis.packageIndexDepths                     | ```[ { "name": "sklearn", "depth": 2 }, { "name": "matplotlib", "depth": 2 }, { "name": "scipy", "depth": 2 }, { "name": "django", "depth": 2 }, { "name": "flask", "depth": 2 }, { "name": "fastapi", "depth": 2 } ]``` | ```[ { "name": "sklearn", "depth": 2 }, { "name": "matplotlib", "depth": 2 }, { "name": "scipy", "depth": 2 }, { "name": "django", "depth": 2 }, { "name": "flask", "depth": 2 }, { "name": "fastapi", "depth": 2 } ]``` | ```{ "name": "", "depth": 4,  "includeAllSymbols": true }``` |
| python.analysis.regenerateStdLibIndices                | false    | false   | true |
| python.analysis.userFileIndexingLimit                  | 2000     | 2000    | -1   |
| python.analysis.includeAliasesFromUserFiles            | false    | false   | true |
| python.analysis.functionReturnTypes                    | false    | false   | true |
| python.analysis.pytestParameters                       | false    | false   | true |
| python.analysis.supportRestructuredText                | false    | false   | true |
| python.analysis.supportDocstringTemplate               | false    | false   | true |

### TypeScript

#### TypeScript expandable hover (Experimental)

This milestone, we made it possible to view expanded/contracted information from the TS server. The extension uses the Expandable Hover API to show `+` and `-` markers in the editor hover to display more or less information.

<video src="images/1_96/expandable-hover.mp4" title="TypeScript Expandable Hover" autoplay loop controls muted></video>

The experimental setting can be enabled using `setting(typescript.experimental.expandableHover)`. For this setting to work, you must be on TypeScript version 5.8 or above. You can change the TypeScript version by using the `TypeScript: Select TypeScript Version...` command.

### Microsoft Account now uses MSAL (with WAM support on Windows)

In order to ensure a strong security baseline for Microsoft authentication, we've adopted the [Microsoft Authentication Library](https://github.com/AzureAD/microsoft-authentication-library-for-js) in the Microsoft Account extension.

One of the stand out features of this work is WAM (Web Account Manager... also known as [Broker](https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/brokering.md)) integration. Put simply, rather than going to the browser for Microsoft authentication flows, we now talk to the OS directly, which is the recommended way of acquiring a Microsoft authentication session. Additionally, it's faster since we're able to leverage the accounts that you're already logged into on the OS.

![An authentication popup that the OS shows over VS Code.](images/1_96/showingBrokerOSDialog.png)

Let us know if you see any issues with this new flow. If you do see a major issue and need to revert back to the old Microsoft authentication behavior, you can do so with `setting(microsoft-authentication.implementation)` (setting it to `classic`, and restarting VS Code) but do keep in mind that this setting won't be around for much longer. So, open an issue if you are having trouble with the MSAL flow.

## Extension Authoring

### @vscode/chat-extension-utils

We've had our [chat](https://code.visualstudio.com/api/references/vscode-api#chat) and [language model](https://code.visualstudio.com/api/references/vscode-api#lm) extension APIs available for several months to let extension authors integrate with GitHub Copilot. But we've found that working with LLMs and building high-quality chat extensions is inherently complex, especially if you want to make use of tool calling.

We've published an npm package, `@vscode/chat-extension-utils`, that aims to make it as easy as possible to get a chat participant up and running. It takes over several things that you would otherwise have to do yourself, so that your chat participant can be implemented in a just a few lines of code. The package also contains a collection of useful, high-quality elements to use with [@vscode/prompt-tsx](https://github.com/microsoft/vscode-prompt-tsx).

You can view the full documentation in the [`chat-extension-utils` repository](https://github.com/microsoft/vscode-chat-extension-utils/blob/main/README.md) and see it in action in the [sample chat extension](https://github.com/microsoft/vscode-extension-samples/tree/main/chat-sample). Our new [LanguageModelTool API docs](https://code.visualstudio.com/api/extension-guides/tools) also describe how to use it.

### Attributable Coverage API

The test coverage APIs now enable extensions to provide coverage information on a per-test basis. To implement this API, populate the the `includesTests?: TestItem[]` property on the `FileCoverage` to indicate which tests executed code in that file, and implement `TestRunProfile.loadDetailedCoverageForTest` to provide statement and declaration coverage.

See the [Attributable Coverage section above](#attributable-coverage) for an example of what this looks like for users.

### Contributing to a JavaScript Debug Terminal

The JavaScript debugger now has a mechanism for other extensions to participate in the creation of JavaScript Debug Terminals. This enables frameworks, or runtimes aside from Node.js, to enable debugging in the same familiar place. Refer to the [JavaScript Debugger documentation](https://github.com/microsoft/vscode-js-debug/blob/main/EXTENSION_AUTHORS.md#extension-api) for more information.

### Proxy support for Node.js `fetch` API

The global `fetch` function now comes with proxy support enabled (`setting(http.fetchAdditionalSupport)`). This is similar to the `https` module, which already had proxy support.

## Preview Features

### Paste code to attach chat context

Previously, you could already attach files as context to Copilot Chat. For more fine-grained control over the context, you can now paste a code fragment to attach it as context for chat. This adds the necessary file information and corresponding line numbers. You can only paste code coming from files in the current workspace.

To try this out, copy some code and paste it in Inline Chat, Quick Chat, or the Chat view. Select the paste control that shows up and select `Pasted Code Attachment.` Alternatively, you can set the `setting(editor.pasteAs.preferences)` setting:

```json
"editor.pasteAs.preferences": [
    "chat.attach.text"
]
```

![Attaching code as context in Copilot Chat using the paste control.](images/1_96/paste-code-context.gif)

### Terminal completions for more shells

We added experimental support for terminal completions in `pwsh` in prior iterations. This release, we have started working on expanding this to other shells. Specifically targeting `bash` and `zsh` for now, but since this new approach is powered by an extension host API, we plan on having general support for most shells.

You can try out the current work in progress by setting `setting(terminal.integrated.suggest.enabled)` and
`setting(terminal.integrated.suggest.enableExtensionCompletions)`. Currently only `cd`, `code`, and `code-insiders` arguments are supported.

![The command `code` is typed on the terminal, which shows suggestions. Then `--` is typed and options are provided, `--locale` is selected. Completions are requested with ctrl+space and all locales are shown. `e` is typed and the list is filtered to `en` and `es`.](images/1_96/terminal-completions.gif)

## Proposed APIs

### Proposed Value Selection API on Quick Pick

For `InputBox` you have been able to set the "value selection", which enables you to programmatically select part or all of the input. This milestone, we added a proposed API for value selection in a QuickPick.

Here's an example of what that might look like:

```ts
const qp = vscode.window.createQuickPick();
qp.value = '12345678';
qp.valueSelection = [4, 6];
qp.items = [
	{ label: '12345678', description: 'desc 1' },
	{ label: '12345678', description: 'desc 2' },
	{ label: '12345678', description: 'desc 3' },
];
qp.show();
```

![A couple of characters are selected in the quick pick's input box.](images/1_96/valueSelectionQuickPick.png)

Try out the [valueSelectionInQuickPick proposal](https://github.com/microsoft/vscode/blob/008340a55c6391f9b333010951455ee71b338787/src/vscode-dts/vscode.proposed.valueSelectionInQuickPick.d.ts) and let us know what you think [in this GitHub issue](https://github.com/microsoft/vscode/issues/233274)!

### Proposed Native Window Handle API

This milestone, we added a new proposed API to retrieve the native window handle of the focused window. The native window handle is an OS concept that essentially provides a pointer to a particular window. This is useful if you are interacting with native code and need to, for example, render a native dialog on top of a window.

```ts
declare module 'vscode' {

	export namespace window {
		/**
		 * Retrieves the native window handle of the current active window.
		 * This will be updated when the active window changes.
		 */
		export const nativeHandle: Uint8Array | undefined;
	}
}
```

This was added specifically for Microsoft Authentication's [adoption of MSAL](https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/brokering.md#window-parenting), so that we could pass the native handle down to the OS so it could render an auth dialog overtop VS Code.

If you have a use case or feedback for the [nativeWindowHandle proposal](https://github.com/microsoft/vscode/blob/008340a55c6391f9b333010951455ee71b338787/src/vscode-dts/vscode.proposed.nativeWindowHandle.d.ts), let us know what you think [in this GitHub issue](https://github.com/microsoft/vscode/issues/233106)!

## Engineering

### Optimized extension updates with vscode-unpkg service

To reduce the load on the Marketplace infrastructure, VS Code now uses the newly added endpoint from the `vscode-unpkg` service to check for extension updates. The service implements server-side caching with a 10-minute TTL, which significantly reduces the number direct requests to the Marketplace. The optimization is controlled via the `setting(extensions.gallery.useUnpkgResourceApi)` setting (enabled by default).

If you notice issues with extension updates, you can disable this functionality with `setting(extensions.gallery.useUnpkgResourceApi:false)`, and revert back to direct Marketplace version checks.

### Ground work for GPU acceleration in the editor

We are excited to announce that we have started work on enabling GPU acceleration in the editor, [similar to the terminal](https://code.visualstudio.com/docs/terminal/appearance#_gpu-acceleration). The goals of this effort are to improve the overall coding experience primarily by reducing input latency and improving scrolling performance.

This is still early and not ready to test out, but we wanted to share some details about the progress that has been made:

* The GPU renderer is using WebGPU behind the scenes.
* We're focusing currently on feature parity and correctness over performance.
* There's a fallback mechanism when GPU acceleration is enabled that allows lines to "fallback" to DOM rendering when it's not fully supported. This means that we can self-host early on and currently incompatible lines will show using the DOM approach instead. Some examples of lines that currently fallback: lines over 200 characters, lines with certain Monaco decorations (e.g. fading unused variables), lines that wrap, and so on.
* Monaco's inline decorations which allow styling the actual elements containing the characters posed a big challenge for this feature as they are styled using CSS. The approach we're using to support most inline decorations without breaking or changing API is to detect the CSS attached to these decorations and then support a subset of common CSS properties, falling back if not all styles are supported.

Here's a screenshot of the feature in action. Note the yellow line in the gutter tells us what lines are using fallback rendering. This particular case uses fallback rendering due to the `dontShow` parameter having an inline decoration as it's unused:

![GPU rendering looks mostly the same as DOM rendering currently, a yellow line appears for lines rendered via the DOM](images/1_96/editor-gpu.png)

The issue tracking this work is [#221145](https://github.com/microsoft/vscode/issues/221145) which has frequent updates and more details on progress as it's made.

### EOL warning for macOS 10.15

VS Code desktop will be updating to [Electron 33](https://github.com/microsoft/vscode/issues/232993) in the next couple of milestones. With the Electron 33 update, VS Code desktop will no longer run on **macOS Catalina**. In this milestone, we have added deprecation notices for the users on this affected platform to prepare them for migration. If you are a user of the aforementioned OS version, please take a look at our [FAQ](https://code.visualstudio.com/docs/supporting/faq#_can-i-run-vs-code-on-old-macos-versions) for additional information.

## Notable fixes

* [233915](https://github.com/microsoft/vscode/issues/233915) Share an extension with others by using the **Copy Link** action in the context menu of an extension in the Extensions view.
* [231542](https://github.com/microsoft/vscode/issues/231542) Frequently unable to save file or file data gets erased with error EBUSY
* [233304](https://github.com/microsoft/vscode/issues/233304) `onDidChangeCheckboxState` broken in 1.95
* [232263](https://github.com/microsoft/vscode/issues/232263) Optimize tree view such that cross process calls are batched
* [156723](https://github.com/microsoft/vscode/issues/156723) Drag and drop support fixed when running with wayland

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@sahin52 (Sahin Kasap)](https://github.com/sahin52)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart): Add support for a border between sidebar and panel titles and views [PR #157318](https://github.com/microsoft/vscode/pull/157318)
* [@aravind-n (Aravind Nidadavolu)](https://github.com/aravind-n): Fix fish shell integration execution order [PR #226589](https://github.com/microsoft/vscode/pull/226589)
* [@BABA983 (BABA)](https://github.com/BABA983): Correct ShellIntegrationDecorationsEnabled in markdownDescription [PR #233387](https://github.com/microsoft/vscode/pull/233387)
* [@BenLocal (benshi)](https://github.com/BenLocal): Cli serve_web sets the path prefix to /<quality>-<commit>/, commit value parsing error [PR #233986](https://github.com/microsoft/vscode/pull/233986)
* [@BlackHole1 (Kevin Cui)](https://github.com/BlackHole1): fix: cannot open vscode when use vscode-win32-x64 in Windows [PR #233285](https://github.com/microsoft/vscode/pull/233285)
* [@BugGambit (Fredrik Anfinsen)](https://github.com/BugGambit): Add support for links 'foo, <line>' [PR #231775](https://github.com/microsoft/vscode/pull/231775)
* [@cachandlerdev](https://github.com/cachandlerdev): Copy extension link [PR #234210](https://github.com/microsoft/vscode/pull/234210)
* [@CrafterKolyan (Nikolai Korolev)](https://github.com/CrafterKolyan): Add interface for adding value selection in QuickPick for extension API [PR #233275](https://github.com/microsoft/vscode/pull/233275)
* [@davidmartos96 (David Martos)](https://github.com/davidmartos96): Fix PATH prepending when using Fish [PR #232291](https://github.com/microsoft/vscode/pull/232291)
* [@dibarbet (David Barbet)](https://github.com/dibarbet): Do not mark interpolation tokens as strings in C# [PR #232772](https://github.com/microsoft/vscode/pull/232772)
* [@duncpro (Duncan)](https://github.com/duncpro): fix: clickability of create new file/folder button [PR #232130](https://github.com/microsoft/vscode/pull/232130)
* [@elias-pap (Elias Papavasileiou)](https://github.com/elias-pap): feat: add icon for Vite [PR #234620](https://github.com/microsoft/vscode/pull/234620)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Add `workbench.view.showQuietly` settings object to stop extensions revealing hidden Output view (fix #105270) [PR #205225](https://github.com/microsoft/vscode/pull/205225)
  * Fix `Go to Current History Item` breakage (fix #235063) [PR #235067](https://github.com/microsoft/vscode/pull/235067)
  * Enable `Go to Current History Item` correctly after reference picker change (fix #235132) [PR #235134](https://github.com/microsoft/vscode/pull/235134)
* [@iisaduan (Isabel Duan)](https://github.com/iisaduan): fix typescript organizeImports settings [PR #232676](https://github.com/microsoft/vscode/pull/232676)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fixes old extensionHost process is not killed immediately after reloading vscode web tab in the browser [PR #234944](https://github.com/microsoft/vscode/pull/234944)
* [@Kannav02 (Kannav Sethi)](https://github.com/Kannav02): Change "Organize Imports" command label to "Optimize Imports" [PR #232869](https://github.com/microsoft/vscode/pull/232869)
* [@LionelJouin (Lionel Jouin)](https://github.com/LionelJouin): Fix: go grammar update (#232142) [PR #232335](https://github.com/microsoft/vscode/pull/232335)
* [@LitoMore (LitoMore)](https://github.com/LitoMore): Remove Microsoft-related logos [PR #215758](https://github.com/microsoft/vscode/pull/215758)
* [@Logicer16 (Logicer)](https://github.com/Logicer16): Fix grammar in activeOnStart description [PR #197536](https://github.com/microsoft/vscode/pull/197536)
* [@RedCMD (RedCMD)](https://github.com/RedCMD): Add `.winget` file extension to YAML [PR #232218](https://github.com/microsoft/vscode/pull/232218)
* [@ribru17 (Riley Bruins)](https://github.com/ribru17): Render JSDoc examples as typescript code [PR #234143](https://github.com/microsoft/vscode/pull/234143)
* [@sandersn (Nathan Shively-Sanders)](https://github.com/sandersn): Revert register copilotRelated with copilot [PR #233729](https://github.com/microsoft/vscode/pull/233729)
* [@nickdiego (Nick Yamane)](https://github.com/nickdiego): Fix for drag and drop support when using wayland [Chromium CL](https://chromium-review.googlesource.com/c/chromium/src/+/6000277)

Contributions to `vscode-emmet-helper`:

* [@onlurking (Diogo Felix)](https://github.com/onlurking): Add missing HTML tags to emmet [PR #90](https://github.com/microsoft/vscode-emmet-helper/pull/90)

Contributions to `vscode-eslint`:

* [@MariaSolOs (Maria José Solano)](https://github.com/MariaSolOs): Update contributing instructions [PR #1947](https://github.com/microsoft/vscode-eslint/pull/1947)

Contributions to `vscode-extension-samples`:

* [@olguzzar (Olivia Guzzardo)](https://github.com/olguzzar): Update Chat tutorial to use request.model [PR #1125](https://github.com/microsoft/vscode-extension-samples/pull/1125)
* [@phil294 (Philip Waritschlager)](https://github.com/phil294): webview-codicons: Move codicons dependency from devDependencies into dependencies [PR #1005](https://github.com/microsoft/vscode-extension-samples/pull/1005)
* [@witsaint (gaodingqiang)](https://github.com/witsaint): fix: `lsp-embedded-language-service` cleaninterval args type [PR #1126](https://github.com/microsoft/vscode-extension-samples/pull/1126)

Contributions to `vscode-extension-telemetry`:

* [@kmagiera (Krzysztof Magiera)](https://github.com/kmagiera): Propagate session ID metadata [PR #215](https://github.com/microsoft/vscode-extension-telemetry/pull/215)

Contributions to `vscode-hexeditor`:

* [@Antecer (Antecer)](https://github.com/Antecer): We need a WYSIWYG copy method [PR #540](https://github.com/microsoft/vscode-hexeditor/pull/540)
* [@Hexa3333 (Alp Yılmaz)](https://github.com/Hexa3333): Fix: DisplayContextSelection read violation (#547) [PR #548](https://github.com/microsoft/vscode-hexeditor/pull/548)
* [@jogo-](https://github.com/jogo-): Update CHANGELOG.md [PR #549](https://github.com/microsoft/vscode-hexeditor/pull/549)
* [@tomilho (Tomás Silva)](https://github.com/tomilho): fix: ctrl+f not working with caps lock active [PR #555](https://github.com/microsoft/vscode-hexeditor/pull/555)

Contributions to `vscode-json-languageservice`:

* [@jeremyfiel (Jeremy Fiel)](https://github.com/jeremyfiel): fix: typo in `then` description [PR #251](https://github.com/microsoft/vscode-json-languageservice/pull/251)
* [@Legend-Master (Tony)](https://github.com/Legend-Master): Fix slow large oneof validation [PR #247](https://github.com/microsoft/vscode-json-languageservice/pull/247)
* [@sumimakito (Makito)](https://github.com/sumimakito): feat(completion): support detail from schema [PR #243](https://github.com/microsoft/vscode-json-languageservice/pull/243)

Contributions to `vscode-jupyter`:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Add `connor4312.esbuild-problem-matchers` recommendation [PR #16195](https://github.com/microsoft/vscode-jupyter/pull/16195)
* [@pwang347 (Paul)](https://github.com/pwang347): Add public API event for kernel post-initialization [PR #16214](https://github.com/microsoft/vscode-jupyter/pull/16214)

Contributions to `vscode-mypy`:

* [@hamirmahal (Hamir Mahal)](https://github.com/hamirmahal): fix: address dev-dependency issues reported by `npm audit` [PR #327](https://github.com/microsoft/vscode-mypy/pull/327)
* [@taesungh (Taesung Hwang)](https://github.com/taesungh): Use global settings for `ignorePatterns` default [PR #325](https://github.com/microsoft/vscode-mypy/pull/325)

Contributions to `vscode-python-debugger`:

* [@rchiodo (Rich Chiodo)](https://github.com/rchiodo)
  * Update debugpy_info for v1.8.8 [PR #500](https://github.com/microsoft/vscode-python-debugger/pull/500)
  * Update debugpy version to 1.8.9 [PR #505](https://github.com/microsoft/vscode-python-debugger/pull/505)

Contributions to `vscode-python-tools-extension-template`:

* [@oliversen (Oliver Olsen)](https://github.com/oliversen)
  * Exclude `.dist-info` directories from extension package [PR #215](https://github.com/microsoft/vscode-python-tools-extension-template/pull/215)
  * Fix glob pattern for `.pyc` files in `.vscodeignore` [PR #216](https://github.com/microsoft/vscode-python-tools-extension-template/pull/216)
  * Remove duplicate code from `noxfile.py` [PR #217](https://github.com/microsoft/vscode-python-tools-extension-template/pull/217)

Contributions to `vscode-test-web`:

* [@Cecil0o0 (hj)](https://github.com/Cecil0o0): VS Code main has moved back to npm, we could catch it [PR #148](https://github.com/microsoft/vscode-test-web/pull/148)

Contributions to `inno-updater`:

* [@BlackHole1 (Kevin Cui)](https://github.com/BlackHole1): fix: dialog is show when silent is true [PR #29](https://github.com/microsoft/inno-updater/pull/29)

Contributions to `language-server-protocol`:

* [@EwanDubashinski (Ivan Dubashinskii)](https://github.com/EwanDubashinski): Added link to the PL/SQL language server [PR #2057](https://github.com/microsoft/language-server-protocol/pull/2057)
* [@gquerret (Gilles Querret)](https://github.com/gquerret): Add OpenEdge ABL in language servers list [PR #2056](https://github.com/microsoft/language-server-protocol/pull/2056)
* [@orbitalquark](https://github.com/orbitalquark): Added link to client implementation for Textadept. [PR #2058](https://github.com/microsoft/language-server-protocol/pull/2058)


<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
