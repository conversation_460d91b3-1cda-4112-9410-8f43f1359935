---
Order:
TOCTitle: August 2022
PageTitle: Visual Studio Code August 2022
MetaDescription: Learn what is new in the Visual Studio Code August 2022 Release (1.71)
MetaSocialImage: 1_71/release-highlights.png
Date: 2022-9-1
DownloadVersion: 1.71.2
---
# August 2022 (version 1.71)

**Update 1.71.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2022+Recovery+1%22+is%3Aclosed).

**Update 1.71.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2022+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the August 2022 release of Visual Studio Code. August is when many of the engineers on VS Code take their vacations, but there are still many updates in this version that we hope you'll like:

* **[Merge editor improvements](#merge-editor-improvements)** - Easier transition between text and merge editors.
* **[Expanded codecs support](#ffmpeg-codecs-support)** - To help display embedded audio and video in notebooks and webviews.
* **[File rename selection](#explorer-rename-selection-improvements)** - Pressing F2 selects filename, whole name, or file extension.
* **[New Code Action UI](#new-code-action-control)** - Quickly find the Code Action you're looking for.
* **[Terminal updates](#terminal)** - Shell integration for fish and Git Bash, new smooth scrolling.
* **[Jupyter notebook image pasting](#jupyter)** - Paste and preview image files in notebook Markdown cells.
* **[TypeScript livestreams](#typescript-livestreams)** - Watch TS "Crash Course" or "Tips and Tricks" on YouTube.
* **[Live Preview extension](#live-preview)** - Live Preview now supports multi-root web projects.
* **[Markdown Language Server blog post](#markdown-language-server)** - Learn how Markdown support moved to a Language Server.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Workbench

### Merge editor improvements

#### Opening the merge editor from files with conflicts

A file that has a conflict will now automatically show an **Open in Merge Editor** button to transition between the text and merge editors.

<video src="images/1_71/mergeHint.mp4" autoplay loop controls muted title="Conflicting files shows a hint to open in the merge editor"></video>

#### Merge editor file not modified on open

VS Code no longer modifies the result file when opening it in the merge editor (conflicting regions were replaced with base). Instead, the conflict markers remain in the file but are hidden in the result view. The checkboxes can be used to replace the conflict markers with either side, a combination of both sides or, by unchecking them, with base.

#### Restoring old conflict decorators

The old inline conflict decorators are no longer disabled, so the merge editor and inline experience can be used together. When the merge editor opens, you can get the previous experience with a single click in the editor toolbar:

<video src="images/1_71/merge-editor-open-file-old-decorators.mp4" autoplay loop controls muted title="Use Open File in the merge editor to see old decorators"></video>

By clicking the same icon next to the file name in the Source Control view, you can skip the merge editor entirely.

However, you can switch between the new and old experience at any time and even use them in parallel:

<video src="images/1_71/merge-editor-side-by-side.mp4" autoplay loop controls muted title="The new and old merge experiences can be used at the same time"></video>

#### Checkbox improvements

We now make sure that the checkboxes of a conflict are always visible, even when the conflict is larger than the viewport. Colors are used to emphasize the checkboxes for unhandled conflicts.

#### Diffing algorithm improvements

We started exploring alternative diffing algorithms to improve the quality of conflicts. A new experimental diffing algorithm can be enabled with the following setting:

```json
"mergeEditor.diffAlgorithm": "experimental",
```

This new algorithm is not optimized for performance yet, but improves the merge editor experience for many edge-cases.

#### Going forward

In this release, we focused on the most important UX issues of the merge editor and fixed [many bugs](https://github.com/microsoft/vscode/issues?q=is%3Aissue+label%3Amerge-editor+milestone%3A%22August+2022%22+is%3Aclosed). If you still find yourself confused by the new merge editor, we would love to hear from you! Please create a [new issue in our repo](https://github.com/microsoft/vscode/issues/new), share a screenshot and your merge editor state (using the **Copy Merge Editor State as JSON** command). We really appreciate all the feedback we've gotten so far, and more feedback will help us make the merge editor experience great for everyone.

Our future work will focus on a [separate base view](https://github.com/microsoft/vscode/issues/155277), [conflict minification](https://github.com/microsoft/vscode/issues/157496), diffing algorithm refinement, and [other improvements](https://github.com/microsoft/vscode/issues?q=is%3Aopen+label%3Amerge-editor+milestone%3A%22September+2022%22).

### FFmpeg codecs support

The [FFmpeg](https://ffmpeg.org) shared library that ships as part of the VS Code previously had only support for the `FLAC` codec. With this release, the library has been updated to support the following list of codecs and containers. This will allow more audio and video files to be played from notebooks or by extensions embedding audio and video into webviews.

* Vorbis
* Flac
* H.264
* VP8
* WAV
* MP3
* Ogg

<video src="images/1_71/codec.mp4" autoplay loop controls muted title="A notebook playing an mp4 video file"></video>

_Theme: [fairyfloss](https://marketplace.visualstudio.com/items?itemName=nopjmp.fairyfloss) (preview on [vscode.dev](https://vscode.dev/editor/theme/nopjmp.fairyfloss))_

### Explorer rename selection improvements

After initiating a rename action on a file, pressing the `F2` key will cycle through the file name, entire selection, and file extension to allow for more flexible keyboard-only interaction.

<video src="images/1_71/renameToggle.mp4" autoplay loop controls muted title="A file in the Explorer being renamed demonstrating that subsequent presses of the F2 key toggle selecting various pieces of the file name"></video>

### Rounded buttons

We've update all of our buttons to have a slight rounded corner to better match our design aesthetic.

![An example of a button with rounded corners](images/1_71/rounded-buttons.png)

_Theme: [Material Theme Palenight High Contrast](https://marketplace.visualstudio.com/items?itemName=Equinusocio.vsc-material-theme)_

### Window Controls Overlay on Windows

In release 1.68, VS Code adopted the [Window Controls Overlay](https://wicg.github.io/window-controls-overlay) feature support from Electron behind an experimental flag and was off by default. We have resolved the remaining issues around this feature affecting VS Code, allowing us to enable it by default for all users on Windows.

The Windows Controls Overlay feature lets VS Code users on Windows 11 select [Snap Layouts](https://learn.microsoft.com/windows/apps/desktop/modernize/apply-snap-layout-menu) to place VS Code on their desktop.

![Windows Snap Layouts control in the right of the VS Code title bar](images/1_71/windows-snap-layout.png)

### Tree view Expand Mode

The setting **Workbench > Tree: Expand Mode** (`workbench.tree.expandMode`), which controls whether tree nodes expand on single-click or double-click, will now be respected by tree views that are contributed by extensions.

## Editor

### Sticky Scroll

Sticky scroll shows the current scope at the top of the view port. The feature is no longer experimental and can be enabled through the settings with `editor.stickyScroll.enabled`. Further improvements are:

* It is possible to set the maximum number of lines to display with the **Editor > Sticky Scroll: Max Line Count** (`editor.stickyScroll.maxLineCount`) setting. The default maximum is 5 lines.
* You can use `Ctrl/Cmd + Click` in the sticky scroll, to navigate to a definition.

<video src="images/1_71/sticky-scroll-ctrlclick.mp4" autoplay loop controls muted title="Sticky scroll Ctrl/Cmd click to go to a symbol definition"></video>

### New Code Action control

We've completely overhauled the Code Action control. Instead of a simple menu of [Code Actions](https://code.visualstudio.com/docs/editing/refactoring#_code-actions-quick-fixes-and-refactorings), there is now a custom control that makes it easier to find the Code Action you want:

![The new Code Action control showing Quick Fixes and refactorings](images/1_71/code-action-widget.png)

The new control also lets VS Code surface additional information. For example, you can now hover over disabled Code Actions to learn why they are disabled:

![Hovering over a disabled Code Action to understand why it is disabled](images/1_71/code-action-disabled-hover.png)

You can also customize the keyboard shortcuts used to navigate through the Code Action list by modifying the [keybindings](https://code.visualstudio.com/docs/getstarted/keybindings) for the following commands:

* `selectNextCodeAction`
* `selectPrevCodeAction`
* `acceptSelectedCodeAction`

Going forward, the new code action UI provides a great foundation for further UX improvements. Be sure to let us know if you have any suggestions!

### Configure suggest matching

There is a new setting to configure IntelliSense filtering. By default, the first character must match a word start, for example `c` must match `console` or `WebContext` but won't match `description`. The `editor.suggest.matchOnWordStartOnly` setting changes this behavior. When disabled, filtering takes any match into account - this leads to more results but filtering still happens by match quality.

In the short video below, you can see that when `matchOnWordStartOnly` is disabled, suggestions for 'dir' include more items, such as 'mkdir' and 'makedirs'.

<video src="images/1_71/suggestMatchWordStart.mp4" autoplay loop controls muted title="IntelliSense demo with and without match on word start"></video>

## Source Control

### Commit action button improvements

Users can globally control the secondary action (for example, `pull`, `sync`) that is being executed following a successful commit operation using the `git.postCommitCommand` setting. This milestone we have added a new setting `git.rememberPostCommitCommand` that can be enabled to remember the last executed secondary action per repository.

## Terminal

### Shell integration improvements

The following improvements to shell integration were made this release:

* Fish shell integration is available as an experimental manual install only. See the [Shell Integration documentation](https://code.visualstudio.com/docs/terminal/shell-integration#_manual-installation) for how to install it.
* Git bash for Windows shell integration is available as an experimental manual install only. See the [section on manual installation](https://code.visualstudio.com/docs/terminal/shell-integration#_manual-installation) for details.
* Support for common alternative current working directory sequences: `OSC 6 ; scheme://<cwd> ST`, `OSC 1337 ; CurrentDir=<cwd> ST`, `OSC 9 ; 9 ; <cwd> ST`
* Better handling of [various shell integration edge cases](https://github.com/microsoft/vscode/issues?q=is%3Aissue+assignee%3ATyriar+milestone%3A%22August+2022%22+is%3Aclosed+label%3Aterminal-shell-integration+label%3Abug).

### Smooth scrolling

The terminal now supports smooth scrolling, which will animate scrolling for a short period of time to help you see your location after scrolling, similar to the editor and lists. To enable smooth scrolling set:

```json
"terminal.integrated.smoothScrolling": true
```

### Underline styles and colors

Underline styles and colors are now supported using the escape sequences [originally pioneered by the kitty terminal](https://sw.kovidgoyal.net/kitty/underlines/). For supporting programs, these new underlines should light up automatically, provided they send these sequences to the VS Code terminal.

![The terminal can now display straight, double, curly, dotted, and dashed underlines in any color](images/1_71/terminal-underlines.png)

Included in this change is improved underline rendering when GPU acceleration is enabled, which avoids glyphs with long descenders and the underline overlapping:

![An example showing that characters with descenders such as 'g' and 'p' no longer overlap with underlines](images/1_71/terminal-descenders.png)

Underline styles and colors are currently not supported on Windows due to [an outstanding issue in ConPTY](https://github.com/microsoft/terminal/issues/7228).

### Rendering improvements

Several improvements were made to terminal rendering:

* A [longstanding bug](https://github.com/microsoft/vscode/issues/85154) that sometimes caused blurriness when the workbench was zoomed in or out has been fixed!

  ![Text is now more crisp as canvas is no longer stretched, which caused anti-aliasing to look poorly](images/1_71/terminal-blurry.png)

* When [minimum contrast ratio](https://code.visualstudio.com/docs/terminal/appearance#_minimum-contrast-ratio) is enabled and the text luminance needs to be flipped to ensure the ratio is met, the text's hue will now be retained.

  ![Bold yellow text on red in some themes will now display as dark yellow instead dark grey](images/1_71/terminal-mcr-flip.png)

* The new theme key `terminal.inactiveSelectionBackground` is available to show a different selection background color whether the terminal is focused or not to better align with the editor.

  ![Most themes now dim the selection background color when not focused](images/1_71/terminal-inactive.png)

* Custom Powerline glyph rendering has improved edge clipping. This is most noticeable on the semi-circle characters, which should now be a smooth curve.

  ![Powerline glyphs are no longer cut off on the edges](images/1_71/terminal-powerline-clip.png)

## Tasks

### Reconnection on window reload

Watch tasks now get reconnected to on window reload, enabling uninterrupted work when VS Code is updated or an extension's state changes. Task reconnection is enabled by default, but can be disabled with the `task.reconnection` setting.

## Debugging

### suppressMultipleSessionWarning option in launch.json

When you try to start debugging with a launch configuration, but there is already an active debug session for that configuration, VS Code will show a warning dialog before starting the second instance. This is easy to do by accident and is usually not what you meant to do. However, if running multiple instances of the same configuration is part of your workflow, you can now disable this warning by adding `"suppressMultipleSessionWarning": true` to your `launch.json` configuration file.

## Comments

### Filtering

The Comments view has a new filter where you can filter by comment text and by resolved/unresolved state.

<video src="images/1_71/comments-filtering.mp4" autoplay loop controls muted title="Comments filtering"></video>

### Editor decoration

The Comments editor gutter decoration now uses codicons and has new styling.

<video src="images/1_71/comments-editor-decoration.mp4" autoplay loop controls muted title="Comments editor gutter decoration"></video>

## Languages

### TypeScript 4.8

VS Code now ships with TypeScript 4.8.2. This major update brings language improvements for type checking and inference. On the tooling side, you also should see some nice performance improvements and bug fixes!

Check out the [TypeScript 4.8 announcement](https://devblogs.microsoft.com/typescript/announcing-typescript-4-8/) for more about this update.

### TypeScript livestreams

And if you missed the VS Code [livestreams](https://code.visualstudio.com/livestream) on TypeScript, have a look at two recent sessions with [Matt Pocock](https://twitter.com/mattpocockuk).

* [TypeScript Crash Course](https://www.youtube.com/watch?v=p6dO9u0M7MQ)
* [TypeScript Tips and Tricks](https://www.youtube.com/watch?v=hBk4nV7q6-w)

![VS Code livestreams page](images/1_71/livestream.png)

## Contributions to extensions

### Jupyter

#### Notebook image pasting

The [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension now lets users paste screenshots or image files into Markdown cells inside their notebooks. Currently only supported for the `image/png` mime type. To use the feature, add/enable the following settings:

```json
"ipynb.experimental.pasteImages.enabled": true
"editor.experimental.pasteActions.enabled": true
```

<video src="images/1_71/screenshot_paste.mp4" autoplay loop controls muted title="Pasting an image file into a Markdown cell in a Jupyter notebook."></video>

#### Improved IntelliSense for Jupyter Notebooks with Pylance

IntelliSense support for Jupyter Notebooks with Python kernels is now much better when using [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance), thanks to the [updates in the Language Server Protocol](https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#whatIsNew) to include notebook support.

Not only you will get improved auto completion options when writing Python code in notebook files, but you will also be able to use refactoring features such as Extract Variable, Extract Method, and Rename Symbol, as well as auto imports and semantic highlighting.

<video src="images/1_71/pylance-notebooks-lsp.mp4" autoplay loop controls muted title="Writing Python code showing auto import, auto complete suggestions, extract method, and a rename symbol functionality."></video>

_Theme: [Horizon Extended](https://marketplace.visualstudio.com/items?itemName=LanceWilhelm.horizon-extended) (preview on [vscode.dev](https://vscode.dev/editor/theme/LanceWilhelm.horizon-extended))_

### Live Preview

The [Live Preview](https://marketplace.visualstudio.com/items?itemName=ms-vscode.live-server) extension now officially  supports [multi-root workspaces](https://code.visualstudio.com/docs/editor/multi-root-workspaces)! Although users could have technically used Live Preview in multi-root workspaces before, there is now a cleaner implementation supporting it.

The Live Preview extension now starts a new server for each root in your multi-root workspace; therefore, links that are relative to the root of your project (ones that start with a `/`) will work properly when previewing multi-root workspaces.

<video src="images/1_71/live-preview-multi-root-demo.mp4" autoplay loop controls muted title="Live Preview multi-root demo"></video>

_Theme: [GitHub Dark Dimmed](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Dark%20Dimmed))_

You can view the servers that were opened by the extension by hovering over Live Preview's Status bar indicator (located on the bottom-right corner). In the image below, a server was started for each workspace (on ports 3000 and 3002), and there was another server spawned for a file that was not from any open workspace (on port 3004).

![Live Preview multi-root ports displayed from the Status bar indicator](images/1_71/live-preview-server-status.png)

Install the [Live Preview](http://aka.ms/live-preview) extension and try it out today! 📡✨🔎

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Highlights of this release include:

* Notifications for pull requests.
* Review comments can be resolved/unresolved directly from the pull request overview.

Check out the [changelog for the 0.50.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0500) release of the extension to see the other highlights.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_71.md).

## Preview features

### Markdown link update on file move and rename

Tired of accidentally breaking links or images in your Markdown when you move or rename files? Try the new `markdown.experimental.updateLinksOnFileMove.enabled` setting!

With this new experimental setting enabled, VS Code will automatically update links and images in Markdown when files are moved or renamed in the VS Code Explorer:

<video src="images/1_71/md-link-update.mp4" autoplay loop controls muted title="Markdown file links are updated on move and rename"></video>

This new behavior can be enabled using the `markdown.experimental.updateLinksOnFileMove.enabled` setting. You can control the types of files affected using `markdown.experimental.updateLinksOnFileMove.externalFileGlobs`.

Give this new feature a try and be sure to share feedback.

### Settings Profiles

We have been working the last couple of months to support **Settings Profiles** in VS Code, which is one of the most popular asks from the community. This feature is available for preview via the `workbench.experimental.settingsProfiles.enabled` setting. Try it out and give us your feedback by creating issues in the [vscode repository](https://github.com/microsoft/vscode/issues) or commenting in [issue #116740](https://github.com/microsoft/vscode/issues/116740).

#### Command-line interface

In this milestone, we added command-line interface (CLI) support for Settings Profiles. You can now pass the name of the profile using the `--profile` argument and open a folder or a workspace using that profile. The command line below opens the `web-sample` folder with the "Web Development" profile:

`code ~/projects/web-sample --profile "Web Development"`

If the profile specified does not exist, a new empty profile with the given name is created.

#### Temporary Settings Profile

There is also now support for a **Temporary Settings Profile** that can be created and associated to a folder or workspace temporarily. A temporary profile is automatically deleted once it is not associated with any folder or workspace.

In the short video below, when the user opens a folder and selects the **Settings Profiles: Create Temporary Settings Profile** command, a new 'Temp 1' profile is created and is visible in the profiles list. When the folder is closed, the 'Temp 1' profile is deleted.

<video src="images/1_71/settings-profiles-temp.mp4" autoplay loop controls muted title="Temporary Settings Profile demo"></video>

You can also create a temporary Settings Profile and associate it to a folder or a workspace from the CLI using the `--profile-temp` switch. The command line below opens the **try-out-sample** folder with a temporary profile that gets deleted after closing the folder:

`code ~/projects/try-out-sample --profile-temp`

### Bring your changes with you when moving across development environments

When you are browsing a GitHub or Azure Repos repository such as [https://vscode.dev/github/microsoft/vscode](https://vscode.dev/github/microsoft/vscode), you can use the [**Continue Working On**](https://code.visualstudio.com/docs/sourcecontrol/github#_continue-working-on) command to select a different development environment to use with your repository.

Previously, if you had pending edits in your virtual workspace, you would need to push them to GitHub or Azure Repos to view them elsewhere. This milestone, we have added **Edit Sessions** integration to the **Continue Working On** feature, so that your uncommitted changes automatically travel with you to your target development environment, such as a GitHub codespace.

In the video below, the user's changes to a TypeScript file that were made when using VS Code for the Web are applied when they create and switch to working in a new GitHub codespace.

<video src="images/1_71/continue-on-codespaces.mp4" autoplay loop controls muted title="Continue On in GitHub Codespaces"></video>

To try this out, set `"workbench.experimental.editSessions.enabled": true` in your settings, enable [Settings Sync](https://code.visualstudio.com/docs/configure/settings-sync), and run the **Edit Sessions: Sign In** command in VS Code for the Web or desktop.

When you are using VS Code for the Web, your uncommitted changes will then travel with you when you use **Continue Working On** in:

* A new cloud hosted environment in GitHub Codespaces
* A new local clone of your GitHub repository
* A local VS Code instance with the same virtual GitHub repository

When you are using desktop VS Code with the [Remote Repositories](https://marketplace.visualstudio.com/items?itemName=ms-vscode.remote-repositories) extension, your uncommitted changes will travel with you when you use **Continue Working On** in:

* A cloud hosted environment in GitHub Codespaces (available via the [GitHub Codespaces](https://marketplace.visualstudio.com/items?itemName=GitHub.codespaces) extension)
* A new local clone of your GitHub repository
* A new clone of your GitHub repository in a container volume (available via the [Dev Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension)
* [https://vscode.dev](https://vscode.dev)
* An existing local folder containing a clone of your repository

## Extension Authoring

### TerminalExitStatus.reason

Extension authors now have better insight into why a terminal exited via the new `TerminalExitReason` API.

```ts
export enum TerminalExitReason {
  Unknown = 0,
  Shutdown = 1,
  Process = 2,
  User = 3,
  Extension = 4,
}
```

### Enum settings enumItemLabels documentation

Enum settings in the Settings editor support the `enumItemLabels` key, which allows the extension author to specify the values to be shown in the dropdowns corresponding to the `enum` values.

![Example of enumItemLabels with a mock setting. The select box displays the label value, and the dropdown options display both the label value and the enum value, though the enum values are rendered less prominently.](images/1_71/enum-item-labels.png)

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### New activeWebviewPanelId context key

The new `activeWebviewPanelId` [context key](https://code.visualstudio.com/api/references/when-clause-contexts) tracks the `viewType` of the currently focused webview panel. You can use it in when clauses to enable commands or menu items when your webview is focused.

```json
"when": "activeWebviewPanelId == 'markdown.preview'"
```

### TypeScript server plugins on web

[TypeScript language service plugins](https://github.com/microsoft/TypeScript/wiki/Writing-a-Language-Service-Plugin) are now supported on web. This applies to plugins that are contributed by extensions using the `typescriptServerPlugins` contribution point.

To get started, you must first tell TypeScript that your plugin is web enabled by adding a `browser` entrypoint in your `package.json` in addition to `main`:

```json
"main": "out/main.js",
"browser": "out/main.browser.js"
```

The `browser` entrypoint must be a JavaScript module that exports a default function that activates the plugin:

```ts
export default function init(modules: { typescript: typeof import("typescript/lib/tsserverlibrary") }) {
  ...
};
```

Keep in mind that on web, your plugin will be running in a normal web worker environment, so you can **not** use Node.js APIs such as `fs`.

### Disabled tree items

Tree items with commands that have their enablement clause resolving to false will be displayed as disabled.

### Markdown Language Server

The Markdown support in VS Code has been reimplemented to be a full-featured [Language Server](https://microsoft.github.io/language-server-protocol). You can read about [Matt Bierner's](https://hachyderm.io/@mattbierner) journey in his [Markdown Language Server blog post](https://code.visualstudio.com/blogs/2022/08/16/markdown-language-server), where he describes adding programming language features such as **Find All References**, **Rename Symbol**, and broken link detection for Markdown files.

### Upcoming change to context of 'view/title' menu

In August, we tried passing the focus and selection of a view's tree for commands contributed to the `view/title` menu of a tree view. In this release, we reverted the change as it caused problems for some extensions. We want to bring the change back and release it in September. If this update will break your extension or if you think this behavior is undesirable, please leave a comment in [issue #42903](https://github.com/microsoft/vscode/issues/42903).

## Debug Adapter Protocol

### Proposal for a 'startDebugging' request

Today VS Code supports multiple concurrent debug sessions but the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol) (DAP) covers only a single session. That means that creating new debug sessions programmatically is not part of DAP and can only be done outside of DAP or the debug adapter, typically in the debug extension that contains the debug adapter. A consequence of this is that multi-session functionality, such as debugging child-processes, is not easily available for non-VS Code DAP clients that only reuse the debug adapter and not the VS Code specific debug extension.

To improve this situation, we are planning to add a new reverse request to DAP to create a new debug session from inside the debug adapter. After some rounds of discussions, we've prepared a [proposal](https://github.com/microsoft/debug-adapter-protocol/issues/79#issuecomment-1230242253) for a `startDebugging` reverse request. If you are a debug extension or DAP client author and are interested in this DAP protocol addition, we'd appreciate your feedback.

## Engineering

### Electron 19 update

In this milestone, we finished the exploration to bundle Electron 19 into VS Code desktop and we want to thank everyone involved with self-hosting on Insiders. This update comes with Chromium `102.0.5005.167` and Node.js `16.14.2`.

### Windows 7 support has ended

Microsoft [ended support for Windows 7](https://learn.microsoft.com/lifecycle/products/windows-7) in January 2020. However, Electron continued to support Windows 7 by patching the `libuv` library. With the Electron 19 update, the `libuv` patch [no longer works](https://github.com/electron/electron/issues/35219) and we recommend users update to a newer Windows version in order to use the VS Code desktop version. VS Code will no longer provide product updates and security fixes on Windows 7. You can learn more about upgrading Windows at [support.microsoft.com](https://support.microsoft.com/windows/windows-7-support-ended-on-january-14-2020-b75d4580-2cc7-895a-2c9c-1466d9a53962).

## Notable fixes

* [115768](https://github.com/microsoft/vscode/issues/115768) Windows: different zoom levels on different monitors fail to restore windows at correct location
* [133444](https://github.com/microsoft/vscode/issues/133444) Dragging settings tab to another editor group clears search query
* [140305](https://github.com/microsoft/vscode/issues/140305) Main window extended across multiple screens does not restore position when re-opening
* [146683](https://github.com/microsoft/vscode/issues/146683) Window layout style bug on open
* [148492](https://github.com/microsoft/vscode/issues/148492) Use platform agnostic paths for workspace file
* [154963](https://github.com/microsoft/vscode/issues/154963) Fixed codicon support in extension walkthroughs
* [155341](https://github.com/microsoft/vscode/issues/155341) Piping into VS Code fails if data writes delayed
* [156075](https://github.com/microsoft/vscode/issues/156075) \[Emmet\] per-language "Trigger expansion on tab"
* [156385](https://github.com/microsoft/vscode/issues/156385) Run recent command shows resolved aliases - `ls` appends `--color=auto` to executed command

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [RedCMD (@RedCMD)](https://github.com/RedCMD)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@babakks (Babak K. Shandiz)](https://github.com/babakks)
  * 🎁 Add support for `fish` shell history [PR #156058](https://github.com/microsoft/vscode/pull/156058)
  * 🎁 Support other terminals CWD escape sequence [PR #157783](https://github.com/microsoft/vscode/pull/157783)
* [@Balastrong (Leonardo Montini)](https://github.com/Balastrong): Added cursor pointer in monaco select box for consistency [PR #152976](https://github.com/microsoft/vscode/pull/152976)
* [@ChaseKnowlden](https://github.com/ChaseKnowlden)
  * Add offline_access to list of default scopes [PR #157453](https://github.com/microsoft/vscode/pull/157453)
  * Fix a typo in secret state [PR #157559](https://github.com/microsoft/vscode/pull/157559)
* [@CsCherrYY (Shi Chen)](https://github.com/CsCherrYY): Support switching to/from custom views in reference-view API [PR #152008](https://github.com/microsoft/vscode/pull/152008)
* [@DingWeizhe (DingWeizhe)](https://github.com/DingWeizhe): fix sticky scroll start line number [PR #157466](https://github.com/microsoft/vscode/pull/157466)
* [@dirondin (Mikhail Po)](https://github.com/dirondin): Fix #147912 (multipleSessionWarning debug option) [PR #147914](https://github.com/microsoft/vscode/pull/147914)
* [@emeric-martineau (Emeric MARTINEAU)](https://github.com/emeric-martineau): Fix Simplify bash PROMPT_COMMAND handling commit [PR #157631](https://github.com/microsoft/vscode/pull/157631)
* [@etriebe (Eric Triebe)](https://github.com/etriebe): Add an option to expose the allowMidWordMatch as a setting [PR #152292](https://github.com/microsoft/vscode/pull/152292)
* [@Evpok (Evpok)](https://github.com/Evpok): Enable Wayland build for snaps [PR #156551](https://github.com/microsoft/vscode/pull/156551)
* [@FantasqueX (FantasqueX)](https://github.com/FantasqueX)
  * Replace the deprecated canceled with Cancellation Error in ipc.ts [PR #156965](https://github.com/microsoft/vscode/pull/156965)
  * Fix typos in files.ts [PR #157280](https://github.com/microsoft/vscode/pull/157280)
* [@ferdnyc (Frank Dana)](https://github.com/ferdnyc): RPM packaging: Use standard macros [PR #153247](https://github.com/microsoft/vscode/pull/153247)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Correct initial window size on Windows secondary display with different scaling factor (#146499) [PR #155589](https://github.com/microsoft/vscode/pull/155589)
  * Show Issue Reporter window in taskbar (#130497) [PR #156141](https://github.com/microsoft/vscode/pull/156141)
  * Don't link to deprecated `terminal.integrated.automationShell.*` settings (#156481) [PR #156486](https://github.com/microsoft/vscode/pull/156486)
  * Provide valid markdown-specific default for `editor.quickSuggestions` setting (#156686) [PR #156689](https://github.com/microsoft/vscode/pull/156689)
  * Add close button to SCM editor validation message (#143036) [PR #158131](https://github.com/microsoft/vscode/pull/158131)
* [@juihanamshet1 (Jui Hanamshet)](https://github.com/juihanamshet1): If the brackets are removed, reduce the range. If the brackets are added, increase the range. [PR #156313](https://github.com/microsoft/vscode/pull/156313)
* [@kidonng (Kid)](https://github.com/kidonng): Fix fish integration script when commandline is empty [PR #157778](https://github.com/microsoft/vscode/pull/157778)
* [@MaddyDev (Maddy)](https://github.com/MaddyDev): check lowercase value on validExtensions [PR #158319](https://github.com/microsoft/vscode/pull/158319)
* [@MonadChains (MonadChains)](https://github.com/MonadChains): Fix selection when reach boundaries of the terminal [PR #156071](https://github.com/microsoft/vscode/pull/156071)
* [@pingren (Ping)](https://github.com/pingren)
  * Fix isStandalone when PWA entering fullscreen [PR #156424](https://github.com/microsoft/vscode/pull/156424)
  * Add env for terminalProcess getCwd Unicode path on macOS [PR #157377](https://github.com/microsoft/vscode/pull/157377)
* [@r3m0t (Tomer Chachamu)](https://github.com/r3m0t): Inherit more settings during extension development [PR #151872](https://github.com/microsoft/vscode/pull/151872)
* [@Semesse (Semesse)](https://github.com/Semesse)
  * Skip collapsed state check for nested children when dropping files in explorer [PR #156759](https://github.com/microsoft/vscode/pull/156759)
  * Fix exthost language features test [PR #158782](https://github.com/microsoft/vscode/pull/158782)
* [@ssigwart (Stephen Sigwart)](https://github.com/ssigwart): Fix search editor title not updating [PR #156011](https://github.com/microsoft/vscode/pull/156011)
* [@SvanT (Svante Boberg)](https://github.com/SvanT): Cleanup disposed terminals [PR #156326](https://github.com/microsoft/vscode/pull/156326)
* [@zgracem (Amy Grace)](https://github.com/zgracem)
  * add shell integration script for fish [PR #157291](https://github.com/microsoft/vscode/pull/157291)
  * improve fish shell integration in vi mode [PR #158127](https://github.com/microsoft/vscode/pull/158127)
* [@zhuowei](https://github.com/zhuowei): simpleFileDialog: ask user if we should create directory if it doesn't exist when saving [PR #152536](https://github.com/microsoft/vscode/pull/152536)

Contributions to `vscode-extension-samples`:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): lsp-embedded-request-forwarding: don't encode uri used in map (fix #682) [PR #683](https://github.com/microsoft/vscode-extension-samples/pull/683)

Contributions to `devcontainers/cli`:

* [@leopoldsedev (Christian Leopoldseder)](https://github.com/leopoldsedev): Implement optional default values in localEnv/containerEnv expansions [PR #51](https://github.com/devcontainers/cli/pull/51)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
