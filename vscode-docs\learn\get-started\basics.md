---
Order: 1
Area: getstarted
TOCTitle: Setup
ContentId: d8ebd3c0-ff4a-4e27-aba2-3acb9e17fff4
PageTitle:  Set up Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: Download and learn the basics of Visual Studio Code.
---
# Set up Visual Studio Code

In this video, we walk you through setting up Visual Studio Code and give an overview of the basic features.

<iframe src="https://www.youtube-nocookie.com/embed/ITxcbrfEcIY" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="Getting Started with Visual Studio Code"></iframe>

Next video: [Extensions for Visual Studio Code](/learn/get-started/extensions.md)

## Outline

* Download and install VS Code.
* Create a new file.
* See an overview of the user interface.
* Install support for your favorite programming language.
* Change your keyboard shortcuts and easily migrate from other editors using keymap extensions.
* Customize your editor with themes.
* Explore VS Code features in the **Interactive Editor Playground**.

## Next video

* [Extensions for Visual Studio Code](/learn/get-started/extensions.md) - Add features to VS Code through extensions.

## Learn more

* [User Interface](/docs/getstarted/userinterface.md) - View the documentation for VS Code.
* [Setup overview](/docs/setup/setup-overview.md) - Documentation for getting up and running with VS Code, including platform-specific setup.
