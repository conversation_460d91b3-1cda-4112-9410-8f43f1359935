---
Order:
TOCTitle: September 2019
PageTitle: Visual Studio Code September 2019
MetaDescription: See what is new in the Visual Studio Code September 2019 Release (1.39)
MetaSocialImage: 1_39/release-highlights.png
Date: 2019-10-09
DownloadVersion: 1.39.2
---
# September 2019 (version 1.39)

**Update 1.39.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22September+2019+Recovery%22+is%3Aclosed), including a fix for a security vulnerability.

**Update 1.39.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22September+2019+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the September 2019 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Text selections displayed in minimap](#minimap-editor-selection-decorations)** - See selection regions in the minimap overview.
* **[Toggle region folding keyboard shortcut](#toggle-folding)** - Quickly expand and collapse regions with Toggle Fold.
* **[Source Control tree view](#updated-source-control-view)** - Display pending changes in either a list or new tree view.
* **[Open terminal in custom working directory](#open-new-terminals-with-custom-working-directories)** - Add keyboard shortcuts for specific folders.
* **[HTML ARIA attribute reference links](#html-aria-attributes-reference)** - Links to ARIA documentation directly from IntelliSense.
* **[CSS property completions include semicolons](#css-property-completion-with-semicolon)** - Semicolons added as you enter CSS properties.
* **[CSS color variables preview](#css-color-preview-for-variable-completion)** - Color variable completions display color swatch.
* **[Improved column breakpoint UI](#improved-ui-for-column-breakpoints)** - View possible inline breakpoints directly in your source code.
* **[Inline debug actions in CALL STACK view](#inline-debug-actions-in-call-stack-view)** - Stay in context with debug actions on hover.
* **[Remote Explorer updates](#remote-development-preview)** - Explorer now displays WSL distros and repository containers.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to see new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Workbench

### Updated Source Control view

The Source Control view has been updated to use the latest tree widget. You can now toggle between a list and a tree view with the **Toggle View Mode** button on the Source Control title bar.

![Source Control toggle view mode button](images/1_39/scm-toggle-view-mode.png)

You can change the default view using the `scm.defaultViewMode` setting, which takes the values `list` or `tree`.

You also now benefit from the automatic keyboard navigation and filtering of the updated tree widget by simply starting to type in the view.

![Updated SCM view](images/1_39/scm.gif)

When in tree view mode, which displays folders, the Git extension now contributes commands to folders, for example **Stage Changes**, which will be applied to all files within a folder.

### Improved Trusted Domains setting

We simplified the management of trusted domains for the [outgoing link protection](https://code.visualstudio.com/updates/v1_38#_link-protection-for-outgoing-links) feature introduced in the 1.38 release.

You can now use the command **Manage Trusted Domains** to easily add, remove, or modify trusted domains as a JSON text file.

![Improved trusted domains management](images/1_39/improved-trusted-domains-management.gif)

### Selectable completion details

Auto completion details can now be selected for copy and paste.

![Selectable completion details](images/1_39/selectable-completion-details.gif)

### Updated Japanese UI font on Windows

On Windows, we have switched the Japanese UI typeface from `Meiryo` to `Yu Gothic UI` and `Meiryo UI`.

The `UI` typefaces save horizontal space by using narrower Japanese kana characters. You can use the Japanese UI by installing the [Japanese Language Pack](https://marketplace.visualstudio.com/items?itemName=MS-CEINTL.vscode-language-pack-ja) and, with the **Configure Display Language** command, setting the display language identifier to `ja`.

In the image below, you can see how the narrower `UI` typeface looks in the Insiders (green icon) menu bar.

![Japanese typeface](images/1_39/japanese-typeface.png)

### Improved monospace typeface display in suggestions and hovers

Previously, the inline `<code>` segments in the suggestion and hover widgets didn't specify `font-family`. On Windows / macOS, these segments were rendered with Chrome's default choice for `font-family: monospace`.

In this iteration, we aligned them with the system sans-serif typefaces that are used throughout the VS Code UI.

* Windows: `Courier New` -> `Consolas` (To align with `Segoe UI`)
* macOS: `Courier` -> `SF Mono`, `Monaco`, `Menlo` (To align with `SF Text` and `Helvetica Neue`)
* Linux: No change. Inline code segments still rendered in `Liberation Mono`, which is Chrome's default for `font-family: monospace` on Linux.

Below you can see that `SF Mono` gives a more consistent appearance to the inline code blocks.

![Suggest and Hover widgets monospace typeface](images/1_39/suggest-hover-widgets-monospace-typeface.png)

### Validation for uniqueItems in the Settings editor

[Validation for string array settings](https://code.visualstudio.com/updates/v1_38#_validation-for-string-array-settings), which was introduced in the 1.38 release, now supports `uniqueItems`.

If using `"uniqueItems": true`, duplicate entries in the Settings editor will show a warning.

### Code block highlighting in extension READMEs

Code blocks in extension READMEs, which are displayed in the Extensions view Details pane, are now highlighted in VS Code:

![A block of JSON being highlighted](images/1_39/extension-readme-highlighting.png)

The highlighting uses the current [color theme](https://code.visualstudio.com/docs/getstarted/themes).

## Editor

### Toggle folding

You can now expand and collapse a folding region with the **Toggle Fold** (`kb(editor.toggleFold)`) command.

### Minimap editor selection decorations

The editor selection is now highlighted within the minimap:

![Minimap displays the editor selection](images/1_39/minimap-selection.png)

You can change the color of these decorations by modifying the `minimap.selectionHighlight` theme color.

### Touch events for the minimap

The minimap slider can now be dragged with touch.

### New option for multi cursor pasting

In the past, when pasting multi-line text from the clipboard, VS Code would check if the clipboard text line count matched the cursor count, and if it did, it would "distribute" or "spread" each line to a cursor. You can now control this behavior with the `editor.multiCursorPaste` setting, which can have the values:

* `spread` - Each cursor pastes a line of text (default).
* `full` - Each cursor pastes the full clipboard text.

## Integrated Terminal

### Open new terminals with custom working directories

There is a new command that allows the creation of terminals with a custom current working directory (cwd):

```json
{
  "key": "cmd+shift+h",
  "command": "workbench.action.terminal.newWithCwd",
  "args": {
    "cwd": "${fileDirname}"
  }
}
```

You can create your own keyboard shortcuts to open new terminals in any number of handy working directories. The `cwd` value can either be a normal path or a [variable](https://code.visualstudio.com/docs/editor/variables-reference).

### Better locale detection

The `terminal.integrated.setLocaleVariables` setting has been replaced with the new `terminal.integrated.detectLocale` setting. The default value is `auto`, which will set the `$LANG` variable in your terminal session to the detected language UTF-8 value but only when it has not been explicitly configured by the user. This better default should lead to fewer issues related to language and encoding in the terminal.

## Languages

### HTML ARIA attributes reference

Auto completion and hover information for HTML ARIA attributes now include a reference to the corresponding [WAI-ARIA](https://www.w3.org/TR/wai-aria-1.1/) documentation.

![HTML ARIA reference](images/1_39/html-aria-reference.png)

### CSS property completion with semicolon

CSS property completion now inserts a semicolon at the end of a line.

You can use this feature with `kbstyle(Ctrl+Enter)` (Windows / Linux) or `kbstyle(Cmd+Enter)` (macOS) to create a new line without pushing the ending `;` to the new line.

You can control this feature with the `[css|scss|less].completion.completePropertyWithSemicolon` settings.

![CSS property completion semicolon](images/1_39/css-property-completion-semicolon.gif)

### CSS color preview for variable completion

When completing CSS variables, if the original variable is a color string, VS Code now shows the completion item with its color:

![CSS variable completion with color swatch](images/1_39/css-color-swatch-variable.png)

### markdown.links.openLocation

The new `markdown.links.openLocation` setting controls where links within Markdown files are opened.

`markdown.links.openLocation` has two values:

* `currentGroup` - Opens links in the current editor group (default).
* `beside` - Open links to the side of the current Markdown editor.

`"markdown.links.openLocation": "beside"` can be useful when working on documentation.

## Debugging

### Improved UI for column breakpoints

While debugging, VS Code now shows all the breakpoint candidate locations inline. This makes it easier for the user to place a breakpoint on a more accurate position. To not clutter the UI, candidate breakpoints are only shown if there is more than one possible location on the line.

You can interact directly with inline breakpoints; clicking to enable and disable them and invoking their context menu for more actions.

![Inline breakpoints](images/1_39/breakpoints.gif)

Note that this feature requires support by the underlying runtime or debugger, and we expect only a few debug extensions will use this feature in the future. For this release, **Node Debug** and the **Debugger for Chrome** support this. As an example for extension authors, our [Mock Debug](https://marketplace.visualstudio.com/items?itemName=andreweinand.mock-debug) sample "mocks" support for this.

### Inline debug actions in CALL STACK view

When there are sessions or threads shown in the CALL STACK view, we now show debug actions inline on hover. This will make it easier to control the debug flow when debugging multiple sessions or threads. You no longer need to first set focus on a session or thread in order to perform an action on it.

If a session has only one thread, all actions are shown on the session since the thread is not displayed. Otherwise all actions are shown in their context (session actions on sessions and thread actions on threads).

![Inline debug actions from the Call Stack view](images/1_39/callstack.gif)

### Improved CALL STACK view behavior

We improved the functionality of the CALL STACK view:

* Clicking on the debug session or the thread no longer expands them. You have to click on the chevron (**>**) UI element directly. This prevents accidentally expanding a session or thread when you just want to move focus.
* VS Code no longer automatically focuses new debug sessions if they did not break.

### Improved link detection in Debug Console

VS Code now detects links in expressions in the Debug Console, not only in string output. The link detection algorithm was also improved.

### PreLaunchTask can now refer to default build

With the introduction of a new variable `${defaultBuildTask}`, it is now possible to have a generic `preLaunchTask` that always refers to the default build task:

```json
{
    "name": "Hello World",
    "type": "node",
    "request": "launch",
    "preLaunchTask": "${defaultBuildTask}"
}
```

`${defaultBuildTask}` resolves to the name of the task that is run when the command **Tasks: Run Build Task** is executed. Similar to using the **Run Build Task** command, if there are multiple default build tasks, or no default build task, then a Quick Pick is shown to select the build task.

### Save choice when preLaunchTask errors

You can now remember your choice when there is a `preLaunchTask` error with the **Remember my choice in user settings** checkbox. Your choice is stored in user settings with `debug.onTaskErrors`, which can have three values; `prompt` (default), `debugAnyway`, and `showErrors`.

![PreLaunchTask error choice dialog](images/1_39/prelaunch-choice.png)

## Contributions to extensions

### Remote Development (Preview)

Work has continued on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.39 include:

* Remote Explorer now supports the WSL extension and lets you easily connect to installed Linux distros.
* New **Clone Repository in Container Volume** command so you can work on source code repositories in isolation.
* Experimental support for SSH connection sharing and using SSH to connect to Windows remote machines.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_39.md).

You can also read two recent blog posts describing:

* [Remote SSH Tips and Tricks](https://code.visualstudio.com/blogs/2019/10/03/remote-ssh-tips-and-tricks)
* [WSL 2 with Visual Studio Code](https://code.visualstudio.com/blogs/2019/09/03/wsl2).

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### TypeScript 3.7-beta support

Although VS Code will not bundle TypeScript 3.7 until its official release, we have already been working to support all the [exciting new TypeScript 3.7-beta features](https://devblogs.microsoft.com/typescript/announcing-typescript-3-7-beta/).

These include:

* Syntax highlighting of optional chaining and nullish coalescing in JavaScript and TypeScript files.
* Completion support for optional chaining.
* Control over semicolons with the new `javascript.format.semicolons` and `typescript.format.semicolons` settings.

You can easily try all these new TypeScript 3.7 features today by installing the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with the TypeScript 3.7 beta!

## Extension authoring

### Extension terminal API

The extension terminal API has been finalized, allowing an extension to react to terminal input and fully control its output. The following creates an extension terminal and prints 'Hello world' in red on startup:

```ts
const writeEmitter = new vscode.EventEmitter<string>();
const pty: vscode.Pseudoterminal = {
  onDidWrite: writeEmitter.event,
  open: () => writeEmitter.fire('\x1b[31mHello world\x1b[0m'),
  close: () => {}
};
vscode.window.createTerminal({ name: 'My terminal', pty });
```

See the [API reference](https://code.visualstudio.com/api/references/vscode-api) and the [extension-terminal-sample](https://github.com/microsoft/vscode-extension-samples/tree/main/extension-terminal-sample) for more details.

### Deprecation tags for symbols and completions

The symbol tag API that allows completion items and symbols to be marked as deprecated has been finalized. Learn more about it in the [1.38 release notes](https://code.visualstudio.com/updates/v1_38#_deprecation-tags-for-symbols-and-completions).

Here you can see that the [Java extension](https://marketplace.visualstudio.com/items?itemName=redhat.java) is using `SymbolTag.Deprecated` to show source code that references deprecated types or members with a strike-through line.

![Java deprecated method](images/1_39/java-deprecated-method.png)

### Debug Session name is no longer readonly

It is possible to change the name of the `DebugSession`. The debug session's name is initially taken from the `DebugConfiguration`. Any changes will be properly reflected in the UI.

### Warning if webviews don't use Webview.asWebviewUri for local resources

While developing an extension that uses the [Webview API](https://code.visualstudio.com/api/extension-guides/webview), we now log a warning when you create a webview that loads local resources using `vscode-resource:` URIs directly instead of using the new [Webview.asWebviewUri API](https://code.visualstudio.com/updates/v1_38#_webviewaswebviewuri-and-webviewcspsource).

![Debug warning for a webview that uses vscode-resources](images/1_39/webview-resource-warning.png)

Although old `vscode-resource:` URIs will continue to work, we want all webview extensions to migrate to the `Webview.asWebviewUri` API as the new API:

* Handles some tricky edge cases around loading local resources from network drives.
* Makes it possible to have a webview that can load a mix of local and remote content.
* Will work more reliably for web versions of VS Code.

### TreeView message API

The API to set a message in a tree view has been finalized. You can see an example of how to use the message API in the [tree view extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/tree-view-sample).

### Updated Octicons style

We've updated our [Octicons](https://code.visualstudio.com/api/references/icons-in-labels) to match our [new icon outline style](https://code.visualstudio.com/updates/v1_37#_new-product-icons) so that all of our iconography is consistent.

![Updated Octicons](images/1_39/updated-octicons.png)

### SCM folder menu

The updated SCM view now supports rendering changes as a tree. As an SCM provider, you can contribute commands to a folder's menu using the `scm/resourceFolder/context` menu id.

## Language Server Protocol

The specification of the [3.15.0](https://microsoft.github.io/language-server-protocol/specifications/specification-3-15) version of the [Language Server Protocol](https://microsoft.github.io/language-server-protocol/overview) is available. The version is not final yet so feedback is still welcome as an issue or a pull request in the [Language Server Protocol repository](https://github.com/microsoft/language-server-protocol). Streaming and progress reporting support has been added to the next version of the language server protocol. Implementations for the node server and the VS Code client are available as `vscode-languageserver@6.0.0-next.1` and `vscode-languageclient@6.0.0-next.1` respectively.

## Debug Adapter Protocol

### Cancellation support

A new `cancel` request has been added to the Debug Adapter Protocol that can give a hint to the debug adapter that the frontend client is no longer interested in the result produced for a specific request issued earlier.

This request has a hint characteristic: a debug adapter can only be expected to make a 'best effort' in honoring this request but there are no guarantees. A frontend client may only call this request if the capability `supportsCancelRequest` is defined and has the value of `true`.

The request that was canceled still needs to send a response back. This can either be a normal result or an error response. Returning partial results from a canceled request is possible but note that a frontend client has no generic way for detecting that a response is partial or not.

In the September release, VS Code supports cancellation for the `stacktrace`, `scopes`, and `variables` requests (used in the **CALL STACK** and **VARIABLES** views) and the `completions` requests (used in the Debug Console).

Note that as of September, only the [Mock Debug](https://marketplace.visualstudio.com/items?itemName=andreweinand.mock-debug) extension implements cancellation.

### Finding possible breakpoints in a source range

The new `breakpointLocations` request can be used by a DAP client to find all possible breakpoint locations in a given source range. This can be used in the UI in order to improve the discoverability of "inline" ("column") breakpoints.

A client may only call the `breakpointLocations` request if the `supportsBreakpointLocationsRequest` capability is defined and has the value of `true`.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. We may likely make breaking changes in the next release and we never want to break existing extensions.

### vscode.env.uiKind

A new proposed API `vscode.env.uiKind` was added that allows an extension to figure out from what kind of UI the extension is being used from. In preparation for supporting running VS Code in a browser, the possible values are `UIKind.Desktop` and `UIKind.Web`.

### vscode.env.asExternalUri

The proposed `vscode.env.asExternalUri` API allows an extension to resolve an **external** URI - such as a `http:` or `https:` URI - from where the extension is running to a URI for the same resource on the client machine. This is a companion to the `vscode.env.openExternal` API, except instead of opening the resolved URI using an external program, it returns the result to extensions.

```ts
import * as vscode from 'vscode';
import * as http from 'http';

const PORT = 3000;

export function activate(context: vscode.ExtensionContext) {
  startLocalServer(PORT);

  context.subscriptions.push(vscode.commands.registerCommand('example.command', async () => {
      const resolved = vscode.env.asExternalUri(vscode.Uri.parse(`http://localhost:${PORT}`));
      vscode.window.showInformationMessage(`Resolved to: ${resolved} on client and copied to clipboard`);
      vscode.env.clipboard.writeText(resolved.toString());
  }));
}

function startLocalServer(port: number) {
  const server = http.createServer((req, res) => {
    res.end('Hello world!');
  });
  server.on('clientError', (err, socket) => {
    socket.end('HTTP/1.1 400 Bad Request\r\n\r\n');
  });
  server.listen(port);
  vscode.window.showInformationMessage(`Started local server on port: ${port}`);
}
```

`vscode.env.asExternalUri` currently only supports `http:` and `https:` URIs. It is a no-op if the extension is running on the client machine. However, if the extension is running remotely, `vscode.env.asExternalUri` automatically establishes a port forwarding tunnel from the local machine to `target` on the remote and returns a local URI to the tunnel.

### Readonly webview editors for resources

You can find VS Code extensions for previewing everything from shaders to Excel files to 3D models. A few bold extensions have even built complex editor-style experiences for visually editing binary or XML files. While it's great that all this is already possible using VS Code's existing extension API, building a preview or custom editor currently requires cobbling together a bunch of APIs in non-intuitive ways. It also leaves a lot up to extensions themselves, often resulting in user experiences that are not consistent with the rest of VS Code. The [custom editor API](https://github.com/microsoft/vscode/issues/82115) proposal aims to improve this.

The custom editor API will provide a framework for extensions to create fully customizable read/write editors that are used in place of VS Code's standard text editor for specific resources. A XAML custom editor, for example, could show a WYSIWYG style editor for your `.xaml` files. Our end goal is to give extensions the most flexibility possible while keeping VS Code fast, lean, and consistent.

This iteration, we are sharing the first pieces of the custom editor API proposal: readonly, webview-based custom editors. You can find an overview of the custom editor proposal [here](https://github.com/microsoft/vscode/issues/82115). Custom editors are already being used to power VS Code's built-in image preview.

The current proposal is only a first step and will likely be heavily revised. We will continue to iterate on the custom editor API over the coming months.

### TreeView title

The tree view title has always been taken from the extension's `package.json` file. Now, a tree view title is initially taken from `package.json` but can be changed later. Changes to the `title` property will be properly reflected in the UI in the title of the view.

### startDebugging consoleMode option to merge Debug Consoles

In order to support hierarchical debug sessions better, we've added an optional argument `consoleMode` to the `vscode.debug.startDebugging` API. This argument controls if VS Code should merge the Debug Console of the parent and child debug sessions. For example, this is particularly useful for cluster debugging, where there are many child sessions and it is much cleaner if all the debug sessions go to the same Debug Console.

### Removed deprecated Terminal.onDidWriteData API

The `Terminal.onDidWriteData` proposed API has been removed in favor of the global event `window.onDidWriteTerminalData`.

## Engineering

### Website refresh

We updated the [Visual Studio Code website](https://code.visualstudio.com) icons and colors to better match our brand. This is reflected throughout the website and [documentation](https://code.visualstudio.com/docs) pages.

![Website colors updated](images/1_39/website-refresh.png)

### JavaScript minification via terser

We changed the minifier used for minification of JavaScript for the product build to be [terser](https://github.com/terser/terser). Previously we had been using an older version of `uglify-es`. The terser developers have been very helpful supporting us during the adoption.

### Filewatcher update (Chokidar)

During this milestone, we updated our file watcher for macOS and Linux to version `3.x`, which brings improved performance, reduced memory consumption, and a smaller package size. You can learn more from the [Chokidar repo](https://github.com/paulmillr/chokidar) and [Chokidar 3 blog post](https://paulmillr.com/posts/chokidar-3-save-32tb-of-traffic/).

### Integration tests run against the real build

Our product builds run a large integration test suite as part of the build. Previously, these tests were executing against VS Code running out of sources. Now, these tests use the built version of VS Code to verify that the build is ready to be released.

### Building VS Code using TypeScript 3.6

VS Code core and the built-in extensions are now built using [TypeScript 3.6](https://devblogs.microsoft.com/typescript/announcing-typescript-3-6/).

### Image preview moved to a built-in extension

VS Code's image preview functionality has been moved out of the core codebase and into a built-in **Image Preview** extension. The functionality of image previews should be almost exactly the same as before.

![The built-in Image Preview extension](images/1_39/image-preview-extension.png)

Extracting image preview into a built-in extension makes it easier to fix bugs and add features, and will eventually allow extensions to override VS Code's built-in image preview more easily.

This change was enabled by the [custom editor API](#readonly-webview-editors-for-resources) work we completed this iteration.

## New documentation

### Node.js deployment tutorials

The [Node.js deployment tutorials](https://code.visualstudio.com/docs/azure/deployment) have moved to the [Azure JavaScript Developer Center](https://learn.microsoft.com/azure/developer/javascript) on [learn.microsoft.com](https://learn.microsoft.com).

There you can find tutorials for using Visual Studio Code to:

* [Deploy Azure Functions](https://learn.microsoft.com/azure/developer/javascript/tutorial/azure-function-cosmos-db-mongo-api)
* [Deploy to Azure App Service](https://learn.microsoft.com/azure/app-service/tutorial-nodejs-mongodb-app)
* [Deploy Docker containers](https://learn.microsoft.com/azure/developer/javascript/tutorial/tutorial-vscode-docker-node/tutorial-vscode-docker-node-01)
* [Create a static website](https://learn.microsoft.com/azure/static-web-apps/getting-started)

## Notable fixes

* [78984](https://github.com/microsoft/vscode/issues/78984): Touch doesn't work in Suggest widget
* [79196](https://github.com/microsoft/vscode/issues/79196): debug console shows REPL results and console.log output in wrong order
* [80003](https://github.com/microsoft/vscode/issues/80003): Regular breakpoints don't have a title tooltip
* [55106](https://github.com/microsoft/vscode/issues/55106): Breakpoints appear as verified if verified by at least one session
* [80435](https://github.com/microsoft/vscode/issues/80435): Please let me hide the empty folder section in the explorer sidebar
* [80464](https://github.com/microsoft/vscode/issues/80464): Failure message from SetVariable request is not surfaced
* [80402](https://github.com/microsoft/vscode/issues/80402): debug UI doesn't cleanup spinners on debug session end
* [82047](https://github.com/microsoft/vscode/issues/82047): New title variable to indicate the remote name

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues.

Contributions to `vscode`:

* [Andrew Branch (@andrewbranch)](https://github.com/andrewbranch): [typescript-language-features] Add formatter option for semicolons [PR #80828](https://github.com/microsoft/vscode/pull/80828)
* [Arash Arbabi (@ATheCoder)](https://github.com/ATheCoder): fix: #81077 [PR #81156](https://github.com/microsoft/vscode/pull/81156)
* [Christian Oliff (@coliff)](https://github.com/coliff): HTTPS link to ux.stackexchange.com [PR #80255](https://github.com/microsoft/vscode/pull/80255)
* [David Reis (@davidreis97)](https://github.com/davidreis97): Add support for touch events on the suggest widget (Fixes #78984) [PR #81442](https://github.com/microsoft/vscode/pull/81442)
* [Dmitry Gozman (@dgozman)](https://github.com/dgozman)
  * Fire onDidChangeReplElements directly from ReplModel [PR #81392](https://github.com/microsoft/vscode/pull/81392)
  * Improve LinkDetector [PR #81336](https://github.com/microsoft/vscode/pull/81336)
  * Separate REPL evaluation from it's result; fixes #79196 [PR #80422](https://github.com/microsoft/vscode/pull/80422)
  * fixes #80014 [PR #80071](https://github.com/microsoft/vscode/pull/80071)
  * Linkify variable values in repl; #79198 [PR #80502](https://github.com/microsoft/vscode/pull/80502)
  * Make DebugSession.name writable; fixes #79583 [PR #80122](https://github.com/microsoft/vscode/pull/80122)
  * Allow debug session to share repl with its parent; fixes #62419 [PR #80673](https://github.com/microsoft/vscode/pull/80673)
  * Make DebugSession.loadSource work for non-cached sources, see #79228 [PR #80669](https://github.com/microsoft/vscode/pull/80669)
* [@ElasticPencil](https://github.com/ElasticPencil): A readOnly editor doesn't honor readonly in the textArea [PR #81394](https://github.com/microsoft/vscode/pull/81394)
* [@harujii](https://github.com/harujii): Add Yu Gothic UI to font-family jp [PR #79735](https://github.com/microsoft/vscode/pull/79735)
* [Xueping (@Heromyth)](https://github.com/Heromyth): Add .c++ externsion support for CPP [PR #80187](https://github.com/microsoft/vscode/pull/80187)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
  * Add missing spaces to view mode switcher tooltip [PR #81318](https://github.com/microsoft/vscode/pull/81318)
  * Update localize JSDoc example to match signature [PR #81319](https://github.com/microsoft/vscode/pull/81319)
* [Jason Ginchereau (@jasongin)](https://github.com/jasongin): Refactor smoke UI automation into separate package [PR #80293](https://github.com/microsoft/vscode/pull/80293)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fix image preview should zoom into center of image [PR #81885](https://github.com/microsoft/vscode/pull/81885)
  * Fix character escaping in snippet transformation [PR #81297](https://github.com/microsoft/vscode/pull/81297)
  * Reset SearchView tree focus when there is a single result [PR #80239](https://github.com/microsoft/vscode/pull/80239)
  * Respect inputOption.activeBackground in preserve case checkbox [PR #81283](https://github.com/microsoft/vscode/pull/81283)
  * Fix focus next/prev search result command inconsistent behavior [PR #80241](https://github.com/microsoft/vscode/pull/80241)
  * Fix explorer item creation and disposal [PR #79383](https://github.com/microsoft/vscode/pull/79383)
  * Add terminal.newHere command [PR #79863](https://github.com/microsoft/vscode/pull/79863)
* [@KapitanOczywisty](https://github.com/KapitanOczywisty): Fix PHP in HTML style tags [PR #80657](https://github.com/microsoft/vscode/pull/80657)
* [Kimmo Lehto (@kke)](https://github.com/kke): Fix terminalProcess typo "exectuableVerification" [PR #81165](https://github.com/microsoft/vscode/pull/81165)
* [Lars Hvam (@larshp)](https://github.com/larshp): editor, modes.ts: documentation, fix broken link [PR #81606](https://github.com/microsoft/vscode/pull/81606)
* [Jiaxun Wei (@LeuisKen)](https://github.com/LeuisKen): chore: update tsc target [PR #80050](https://github.com/microsoft/vscode/pull/80050)
* [@navrkald](https://github.com/navrkald): Skip WLS check if env var DONT_PROMPT_WSL_INSTALL is set. [PR #80529](https://github.com/microsoft/vscode/pull/80529)
* [@pi1024e](https://github.com/pi1024e): Typo fix: disposables [PR #80154](https://github.com/microsoft/vscode/pull/80154)
* [Ron Buckton (@rbuckton)](https://github.com/rbuckton): Fix to allow for optional chain token in completions [PR #81402](https://github.com/microsoft/vscode/pull/81402)
* [Lionell Pack (@Rophuine)](https://github.com/Rophuine): Add touch events to minimap [PR #81097](https://github.com/microsoft/vscode/pull/81097)
* [Sergio Schvezov (@sergiusens)](https://github.com/sergiusens)
  * snap: add assets verbatim [PR #80585](https://github.com/microsoft/vscode/pull/80585)
  * buildSnapPackage: use the default snapcraft target [PR #80217](https://github.com/microsoft/vscode/pull/80217)
* [Fuchen Shi (@shifuchen98)](https://github.com/shifuchen98): Fix typo in batch replacement pop-up [PR #81572](https://github.com/microsoft/vscode/pull/81572)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan)
  * Fix 81779 Preserve Case Hyphen and Underscore replacement issues  [PR #81781](https://github.com/microsoft/vscode/pull/81781)
  * Fix-79658 New pattern to Preserve Case ( underscore separated variables) [PR #79660](https://github.com/microsoft/vscode/pull/79660)
  * Fix-80080 Show more detailed error message for "Regex parse error" in search [PR #80495](https://github.com/microsoft/vscode/pull/80495)
* [Konstantin Solomatov (@solomatov)](https://github.com/solomatov)
  * Shorter expand/shrink selection shortcuts on mac [PR #80491](https://github.com/microsoft/vscode/pull/80491)
  * remove docIndex in markdown language extension [PR #80933](https://github.com/microsoft/vscode/pull/80933)
  * Fix activation of linters on unopened files caused by markdown plugin [PR #80506](https://github.com/microsoft/vscode/pull/80506)
  * Replace Disposable[] with DisposableStore [PR #80661](https://github.com/microsoft/vscode/pull/80661)
  * Replace Disposable[] with DisposableStore [PR #80447](https://github.com/microsoft/vscode/pull/80447)
* [Salvador Cabrera Lozano (@txava)](https://github.com/txava): Fix for #26659. [PR #80227](https://github.com/microsoft/vscode/pull/80227)
* [Alexander (@usernamehw)](https://github.com/usernamehw): Update enablement of status bar item in constructor [PR #80517](https://github.com/microsoft/vscode/pull/80517)
* [Dipen Ved (@vedipen)](https://github.com/vedipen): Save all and commit fix [PR #81609](https://github.com/microsoft/vscode/pull/81609)
* [Yuya Tanaka (@ypresto)](https://github.com/ypresto): Fix Cmd+F key in problems view robbed by find in editor [PR #80068](https://github.com/microsoft/vscode/pull/80068)

Contributions to `vscode-eslint`:

* [Rafał Chłodnicki (@rchl)](https://github.com/rchl): Fix None position values in publishDiagnostics message [PR #753](https://github.com/microsoft/vscode-eslint/pull/753)
* [Wil Lee (@kourge)](https://github.com/kourge): Add `typescriptreact` to the README's TypeScript section [PR #764](https://github.com/microsoft/vscode-eslint/pull/764)

Contributions to `vscode-languageserver-node`:

* [Jason Dent (@Jason3S)](https://github.com/Jason3S): Add isPreferred to the CodeAction protocol. [PR #489](https://github.com/microsoft/vscode-languageserver-node/pull/489)
* [Thomas Mäder (@tsmaeder)](https://github.com/tsmaeder): [PR #512](https://github.com/microsoft/vscode-languageserver-node/pull/512)
* [Adam Yarris (@RubbaBoy)](https://github.com/RubbaBoy): Fixed dead links in README  [PR #834](https://github.com/microsoft/language-server-protocol/pull/834)

Contributions to `language-server-protocol`:

* [Krzysztof Cieślak (@Krzysztof-Cieslak)](https://github.com/Krzysztof-Cieslak): Add DiagnosticTag to documentation  [PR #645](https://github.com/microsoft/language-server-protocol/pull/645)
* [Remy Suen (@rcjsuen)](https://github.com/rcjsuen): Fix #375 Add more formatting options [PR #735](https://github.com/microsoft/language-server-protocol/pull/735)
* [Danny Tuppeny (@DanTup)](https://github.com/DanTup): Remove "non-empty string" comment for workspace/symbol [PR #770](https://github.com/microsoft/language-server-protocol/pull/770)
* [Mickael Istria (@mickaelistria)](https://github.com/mickaelistria): Issue #782 - InitializationParams.clientName [PR #783](https://github.com/microsoft/language-server-protocol/pull/783)
* [Roberto Aloi (@robertoaloi)](https://github.com/robertoaloi): Add Erlang and Elixir to the list of supported programming languages [PR #812](https://github.com/microsoft/language-server-protocol/pull/812)
* [@KamasamaK](https://github.com/KamasamaK): Added SignatureHelpContext [PR #805](https://github.com/microsoft/language-server-protocol/pull/805)
* [Eyal Kalderon (@ebkalderon)](https://github.com/ebkalderon): Fix typos in gh-pages [PR #820](https://github.com/microsoft/language-server-protocol/pull/820)

Contributions to `debug-adapter-protocol`:

* [Pahan Sarathchandra (@pahans)](https://github.com/pahans): Add Ballerina debug adapter [PR #77](https://github.com/microsoft/debug-adapter-protocol/pull/77)

Contributions to `vscode-debugadapter-node`:

* [Damjan Cvetko (@zobo)](https://github.com/zobo): Fixing wrong signature of terminateThreadsRequest [PR #214](https://github.com/microsoft/vscode-debugadapter-node/pull/214/)
* [Holger Benl (@hbenl)](https://github.com/hbenl): add data breakpoint requests to testSupport [PR #219](https://github.com/microsoft/vscode-debugadapter-node/pull/219)

Contributions to `vscode-css-languageservice`:

* [Simon Siefke (@SimonSiefke)](https://github.com/SimonSiefke): fix typo [PR #179](https://github.com/microsoft/vscode-css-languageservice/pull/179)

Contributions to `vscode-vsce`:

* [Sumesh Majhi ⚡ (@MajhiRockzZ)](https://github.com/MajhiRockzZ): Update README.md [PR #387](https://github.com/microsoft/vscode-vsce/pull/387)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Danish:** Mark Drastrup, Lasse Stilvang.
* **Dutch:** Niels ter Haar, Laurens Kwanten.
* **English (United Kingdom):** Martin Littlecott, s.benson, sonali Dixit.
* **Finnish:** Kiti Suupohja, Tommi Finnilä.
* **French:** Thierry DEMAN-BARCELÒ, Maxime Coquerel, Antoine Griffard, Rodolphe NOEL, DJ Dakta.
* **German:** Christof Opresnik.
* **Greek:** Θοδωρής Τσιρπάνης.
* **Hebrew:** Yonatan Bachar, Snir Broshi.
* **Hindi:** mtar05, निरव आडतिया, Amit Gusain, Degant Puri.
* **Chinese Simplified:** Justin Liu, Yizhi Gu, paul cheung, yungkei fan, 斌 项, Yiting Zhu, 一斤瓜子, Tianzhi Zeng, cool yang, Peng Zeng, Y!an, Joel Yang, 楠 姜, 建 周.
* **Chinese Traditional:** Winnie Lin, 蔡牧村, 謝政廷, Yi-Jyun Pan, Poy Chang.
* **Indonesian:** Eriawan Kusumawardhono, Riwut Libinuko, William Surya Permana, Septian Adi, Laurensius Dede Suhardiman.
* **Italian:** Luigi Bruno, Alessandro Alpi, Claudio Mezzasalma, Marco Dal Pino.
* **Japanese:** Michihito Kumamoto, Hiroyuki Mori, Aya Tokura, 井上 圭司, Yuzo Konishi, Yoshihisa Ozaki, Kogesaka, Yuta Ojima, TENMYO Masakazu, Koichi Makino, Takayuki Fuwa.
* **Korean:** Hongju, Sungjin Jeong, Seikwang Chung.
* **Latvian:** Andris Vilde.
* **Lithuanian:** Renatas Laužadis.
* **Polish:** Kacper Łakomski.
* **Portuguese (Brazil):** Marcelo Fernandes, Marcondes Alexandre, Felipe Oliveira, Alan Bueno.
* **Portuguese(Portugal):** Pedro G..
* **Romanian:** Mihail-Gabriel Alexe.
* **Russian:** konpl.
* **Spanish:** José María Aguilar, Abdón Rodríguez P., Ricardo Estrada Rdez, Anix Cormak, Carlos Mendible.
* **Tamil:** Rajeshkumar Ramasamy.
* **Turkish:** Safa Selim.
* **Ukrainian:** Oleksandr Krasnokutskyi, Arthur Murauskas.
* **Vietnamese:** Vương, Van-Tien Hoang, Hai Nguyen, Tuan Duong.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
