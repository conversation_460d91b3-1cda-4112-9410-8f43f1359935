---
ContentId: 12bf713e-5f20-46ac-81bb-8e05565aba3a
DateApproved: 05/08/2025
MetaDescription: How to deploy Python applications to Azure with Visual Studio Code
MetaSocialImage: images/tutorial/python-social.png
---
# Deploy Python Web Apps

The [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack) extensions for Visual Studio Code make it easy to deploy Python applications (including containers) to [Azure App Service](https://azure.microsoft.com/services/app-service) and to deploy serverless code to [Azure Functions](https://azure.microsoft.com/services/functions).

![Azure Tools extension](images/azure/azure-tools.png)

## Deployment tutorials

The following tutorials on the [Python Azure Developer's Center](https://learn.microsoft.com/azure/developer/python) walk you though the details.

Tutorial | Description | Related Tools
--- | --- | ---
[Deploy Python web app to Azure App Service](https://learn.microsoft.com/azure/app-service/quickstart-python) | Deploy a simple web app | [Django](https://www.djangoproject.com/) <br> [Flask](https://flask.palletsprojects.com/) <br> [Azure CLI](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azurecli) <br> [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice) <br> [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack)
[Deploy Python web app with database to Azure App Service](https://learn.microsoft.com/azure/app-service/tutorial-python-postgresql-app) | Deploy a web app with PostgreSQL database | [Django](https://www.djangoproject.com/) <br> [Flask](https://flask.palletsprojects.com/) <br> [PostgreSQL](https://www.postgresql.org/download/) <br> [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice) <br> [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack)
[Deploy Python containers to Azure App Service](https://learn.microsoft.com/azure/developer/python/tutorial-deploy-containers-01) | Deploy a container |  [Container Tools](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-containers) <br> [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice) <br> [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack)
[Deploy Python to Azure Functions](https://learn.microsoft.com/azure/azure-functions/create-first-function-vs-code-python) | Deploy serverless code with Azure Functions | [Azure Functions Core Tools](https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local#install-the-azure-functions-core-tools) <br> [Azure Functions](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurefunctions) <br> [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack)
