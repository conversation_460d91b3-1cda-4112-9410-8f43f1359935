---
Order: 106
TOCTitle: January 2025
PageTitle: Visual Studio Code January 2025
MetaDescription: Learn what is new in the Visual Studio Code January 2025 Release (1.97)
MetaSocialImage: 1_97/release-highlights.png
Date: 2025-02-06
DownloadVersion: 1.97.2
---
# January 2025 (version 1.97)

**Update 1.97.1**: The update addresses these security [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22January+2025+Recovery+1%22+).

**Update 1.97.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22January+2025+Recovery+2%22+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the January 2025 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [Next Edit Suggestions (preview)](#copilot-next-edit-suggestions-preview) - Copilot predicts the next edit you are likely to make.
* [Reposition Command Palette](#move-the-command-palette-and-quick-inputs) - Drag the Command Palette and Quick Inputs to a new position.
* [Auto accept edits](#improved-editor-controls) - Automatically accept edits from Copilot after a configurable timeout.
* [Extension publisher trust](#trusting-extension-publishers) - Keep your environment secure with extension publisher trust.
* [Compound logs](#compound-logs) - Combine multiple logs into a single, aggregated log view.
* [Filter output logs](#output-panel-filtering) - Filter the contents of the Output panel.
* [Git blame information](#git-blame-information) - Rich git blame information and open on GitHub.
* [Search values in debug variables](#filter-and-search-on-values) - Filter and search for specific values in debug variables.
* [Notebook inline values](#inline-values-upon-cell-execution) - View inline values for code cell variables in notebooks.
* [Python no-config debug](#no-config-debug) - Quickly debug a Python script or module without setup.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## GitHub Copilot

### Copilot Next Edit Suggestions (Preview)

**Setting**: `setting(github.copilot.nextEditSuggestions.enabled)`

GitHub Copilot code completions are great at autocomplete, but since most coding activity is editing existing code, it's a natural evolution of completions to also help with edits. So, we're excited to release a new preview feature, **Copilot Next Edit Suggestions** (Copilot NES).

Based on the edits you're making, Copilot NES both predicts the location of the next edit you'll want to make and what that edit should be. NES suggests future changes relevant to your current work, and you can simply `kbstyle(Tab)` to quickly navigate and accept suggestions.

Notice in the following example that changing a variable triggers an edit suggestion further down the file. Just use the `kbstyle(Tab)` key to navigate and accept the suggestion. The gutter indicator will guide you to your next edit suggestion.

![Video showing Copilot NES suggesting code edits at another location. The gutter shows an arrow indicating the relative position of the edit.](./images/1_97/nes-arrow-directions.gif)

Enable Copilot NES via the VS Code setting `setting(github.copilot.nextEditSuggestions.enabled)`.

Based on the size and type of edit, the rendering of the suggestion might change dynamically from side-by-side to below the current line. Configure the `setting(editor.inlineSuggest.edits.renderSideBySide:never)` setting to always render suggestions below the current line.

Copilot NES is rapidly evolving, and we can't wait to get your feedback via issues in [our repo](https://github.com/microsoft/vscode-copilot-release). You can read our full [Copilot NES docs](https://aka.ms/gh-copilot-nes-docs) for more information and scenarios as we expand the NES experience.

> **Note**: If you are a Copilot Business or Enterprise user, an administrator of your organization [must opt in](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/managing-policies-for-copilot-in-your-organization#enabling-copilot-features-in-your-organization) to the use of Copilot "Editor Preview Features," in addition to you setting `setting(github.copilot.nextEditSuggestions.enabled)` in your editor.

### Copilot Edits

#### Copilot Edits general availability

In our VS Code October release, we announced the preview of Copilot Edits. Today, we're now announcing the general availability of Copilot Edits! Copilot Edits is optimized for code editing and enables you to make code changes across multiple files in your workspace, directly from chat.

#### Improved editor controls

Edits can now be accepted and discarded individually, giving you more control. Also new is that the editor controls for edits remain visible when switching to the side-by-side view. This is useful for understanding larger changes.

![Screenshot that shows how to accept an individual change from Copilot Edits in the editor.](images/1_97/edits-accept-hunk.png)
_Theme: [GitHub Light Colorblind (Beta)](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Light%20Colorblind%20(Beta)))_

Lastly, we have added a new setting for automatically accepting edit suggestions after a configurable timeout. The setting for that is `setting(chat.editing.autoAcceptDelay)`, which specifies the number of seconds after which changes are accepted. The countdown stops when you interact with the accept button or when you start to review changes. This should be familiar to anyone who binge-watches on the weekends.

<video src="images/1_97/edits-auto-accept.mp4" title="Video showing a gradient on the Accept button for Copilot Edits, indicating the auto-accept progress." autoplay loop controls muted></video>
_Theme: [GitHub Light Colorblind (Beta)](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Light%20Colorblind%20(Beta)))_

### Apply in editor

In Copilot Chat, any code block can be applied to a file in the workspace by using the **Apply to Editor** action in the toolbar of the code block.
We made several improvements to this experience:

- The hover of the action now shows the file the code block was generated for.

  ![Screenshot that shows the Apply Code Block hover text, indicating the target file name.](images/1_97/apply-code-block-hover.png)

- If the code block is for a non-existent file, you are prompted where to create the file. This can be at a file path suggested by Copilot, in an untitled editor, or in the currently active editor.

- When the changes are computed and applied, the same flow and UI as for Copilot Edits are used. You can review, improve, or discard each change individually.

### Temporal context

Temporal context helps when editing or generating code by informing the language model about files that you have recently interacted with. We are experimenting and measuring its effectiveness but it can also be enabled manually,  `setting(github.copilot.chat.editor.temporalContext.enabled)` for Inline Chat and `setting(github.copilot.chat.edits.temporalContext.enabled)` for Copilot Edits.

### Workspace index status UI

When you ask Copilot a question about the code in your project by using `@workspace` or `#codebase`, we use an index to quickly and accurately search your codebase for relevant code snippets to include as context. This index can either be a [remote index managed by GitHub](https://code.visualstudio.com/docs/copilot/workspace-context#_remote-index), [a locally stored index](https://code.visualstudio.com/docs/copilot/workspace-context#_local-index), or [a basic index](https://code.visualstudio.com/docs/copilot/workspace-context#_basic-index) used as a fallback for large projects that can't use a remote index.

This iteration, we've added the new workspace index to the language status indicator in the Status Bar that shows the type of index being used by Copilot and related information, such as the number of files being reindexed. To see this, just select the `{}` icon in the VS Code Status Bar.

![Screenshot that shows the status of the Copilot workspace indexing in the Status Bar.](images/1_97/copilot-workspace-status.png)

Check out the [Copilot workspace docs](https://code.visualstudio.com/docs/copilot/workspace-context#_managing-the-workspace-index) for more info about the types of workspace indexes and how you can switch between them.

### Build a remote workspace index

[Remote workspace indexes](https://code.visualstudio.com/docs/copilot/workspace-context#_remote-index) are managed by GitHub. A remote index can provide high-quality results quickly, even for large projects. They also only have to be built once per GitHub project, instead of once per user.

Given all these advantages, we have added several new ways to upgrade a project to a remote index:

- Run the new **GitHub Copilot: Build Remote Index** command.

- Select the Build Index button in the [workspace index status UI](#workspace-index-status-ui). This only shows up if your project is eligible for remote indexing.

- Select the Build Index button in the first `@workspace` response you see. This only shows up if your project is eligible and also only shows once per workspace.

Keep in mind that only projects with a GitHub remote can currently use a remote index. It may also take some time to build up the remote index, especially if your project is large. Check the [Workspace index status UI](#workspace-index-status-ui) to see if remote indexing has completed.

### Workspace search improvements

We've also continued optimizing code search for `@workspace` and `#codebase`. Highlights include:

- Improved tracking and handling of locally changed files when using a [remote index](https://code.visualstudio.com/docs/copilot/workspace-context#_remote-index).

- Added background updating of changed files in the [local index](https://code.visualstudio.com/docs/copilot/workspace-context#_local-index), so that `@workspace` questions don't have to wait for them to be updated.

- Optimized the [basic index](https://code.visualstudio.com/docs/copilot/workspace-context#_basic-index) for large projects.

### Git changes context variable

When writing queries for Chat or Edits, you can now reference files that were modified in Git source control by using the `#changes` context variable. For example, you could prompt for `summarize the #changes in my workspace`.

![Screenshot of a Copilot chat response, which lists the modified files and changes when prompting for '#changes'.](images/1_97/copilot-chat-git-changes.png)

### Model availability

You now have even more models to choose from when using Copilot. The following models are now available in the model picker in Visual Studio Code and github.com chat:

- **OpenAI’s o3-mini**: OpenAI’s newest reasoning model to your coding workflow, is rolling out gradually and will be available to GitHub Copilot Pro, Business, and Enterprise users. Learn more about the o3-mini model availability in the [GitHub blog post](https://github.blog/changelog/2025-01-31-openai-o3-mini-now-available-in-github-copilot-and-github-models-public-preview/).

- **Gemini 2.0 Flash**: Google’s latest model shows high capabilities for code suggestions, documentation, and explaining code. This model is now available to all GitHub Copilot customers, including Copilot Free. Learn more about the Gemini 2.0 Flash model availability in the [GitHub blog post](https://github.blog/changelog/2025-02-05-google-gemini-2-0-flash-is-now-available-to-all-copilot-users-in-public-preview/).

## Accessibility

### Enhanced accessibility sounds

We've refined several accessibility sounds based on user feedback to improve clarity and distinction. The following sounds have been updated:

- `setting(accessibility.signals.save)`
- `setting(accessibility.signals.lineHasFoldedArea)`
- `setting(accessibility.signals.terminalQuickFix)`
- `setting(accessibility.signals.lineHasInlineSuggestion)`

You can preview these updates by running the command **Help: List Signal Sounds** from the Command Palette.

### Copilot Edits Accessibility Help dialog

Screen reader users can now access guidance on interacting with Copilot Edits by invoking:
`kb(editor.action.accessibilityHelp)` within the input box.

Additionally, when an editor contains pending Copilot edits, this status is now indicated in the editor help dialog. We've also introduced keyboard shortcuts for navigating next `kb(chatEditor.action.navigateNext)` / previous `kb(chatEditor.action.navigatePrevious)`, accepting `kb(chatEditor.action.acceptHunk)`, discarding `kb(chatEditor.action.undoHunk)`, and toggling the diff view `kb(chatEditor.action.diffHunk)`.

### Source Control Accessibility Help dialog

If you invoke the **Show Accessibility Help** command while the Source Control view is focused, it opens the Source Control Accessibility Help dialog to provide important information for screen reader users. The dialog contains the summary of the current source control state and general information about the views and how to navigate them.

### Improved screen reader notifications

When a screen reader is detected, related notifications now include a link to learn more, providing additional context and resources.

### Ignore code blocks in text to speech

Previously, when you used text-to-speech to read out a Copilot response, code blocks were also read out loud. You can ignore code blocks from text-to-speech sessions by using the `setting(accessibility.voice.ignoreCodeBlocks)` setting.

## Workbench

### Move the Command Palette and Quick Inputs

You can now move the Command Palette and other Quick Inputs to a new position, instead of having it fixed at the top of the window.

Features:

* Drag and drop the Command Palette or any other Quick Input with snapping to center and top
* Persisted position across reloads, allowing you to set a new permanent position for Quick Inputs
* Preset positions are available in the Customize Layout picker

<video src="images/1_97/quick-pick-move.mp4" title="Video showing moving the Command Palette around the screen and watching it snap into different places. Then opening Customize Layout to play with the presets: top & center." autoplay loop controls muted></video>

### Trusting extension publishers

When you install an extension from a publisher for the first time you will now see a dialog to help you assess the trustworthiness of the extension publisher. This feature helps ensure that you only install extensions from trusted sources, enhancing the security of your development environment.

![Screenshot that shows the Trust Publisher dialog that is shown when a user installs an extension.](images/1_97/trust-publisher-dialog.png)

If you install an extension pack or an extension with dependencies, trusting the publisher will also implicitly trust the publishers of the extensions that get installed along with it.

When you update to the VS Code 1.97 release, the publishers of currently installed extensions are automatically trusted. You can manage the trusted extension publishers with the **Extensions: Manage Trusted Extensions Publishers** command. This command allows you to reset or revoke trust for publishers you have previously trusted.

![Screenshot that shows the Quick Pick list of trusted publishers, enabling unchecking publishers to make them untrusted.](images/1_97/manage-trusted-publishers.png)

**Note**: When no VS Code window is open and you install an extension from the CLI (`code-insiders --install-extension pub.name`), the extension is installed, but the publisher is not added to the trusted list.

For more information, you can visit the [learn more](https://aka.ms/vscode-extension-security) link.

### Output panel filtering

You can now filter the contents of the Output panel, which can greatly improve managing and analyzing logs, especially when you have to deal with large volumes of log data.

![Screenshot of the Output panel, highlighting the filtering dropdown.](images/1_97/output-view-filtering.png)

- **Filter by Log Level:** Filter logs based on their severity level (for example, Error, Warning, Info). This helps you focus on the most critical issues first.
- **Filter by Category:** Narrow down logs by specific categories, allowing you to isolate logs from particular sources or components. The categories are picked up automatically from the log data.
- **Filter by Text:** Search for specific text within the logs to quickly locate relevant entries.

### Compound logs

Sometimes you find that information is spread across multiple logs, and you need to view them together to get a complete picture. You can now view multiple logs in a single compound log view. Combine this with the new [filtering](#output-panel-filtering) functionality and analyzing logs has just become much better!

To create a custom compound log, use the **Create Compound Log...** action in the overflow menu of the Output panel.

<video src="images/1_97/compound-log.mp4" title="Video showing how to create a compound log that combines the log messages from two other logs." autoplay loop controls muted></video>

You can also open compound logs in an editor or a new VS Code window for flexible monitoring and analysis. This feature improves your ability to diagnose issues by providing a consolidated view of related logs.

> **Note:** Compound log views are currently not persisted across VS Code restarts.

### Export and import logs

You can now export and import logs using the actions in the overflow menu of the Output view. This feature enhances collaboration and log management by making it easy to share and review logs.

Select the corresponding **Export Logs** or **Import Log** action in the overflow menu of the Output panel to export or import logs.

### Settings editor search fixes

This iteration, we fixed a regression where search queries with missing letters were not showing expected results. For example, the Settings editor was not finding the `setting(editor.formatOnPaste)` setting when you searched for "editor formonpast".

We also fixed an issue where the Settings editor would reveal the table of contents during a search even when it was in a narrow editor group.

![Screenshot of a narrow-width Settings editor, with a search for 'edtor cursstyle' that shows the 'editor.cursorStyle' setting and does not reveal the settings table of contents.](images/1_97/settings-editor-search.png)

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### Extension filter enhancements

To help you keep track of extensions that have updates available and to find extensions that were recently updated, you now have two new filter options in the Extensions view: `@outdated` and `@recentlyUpdated`.

![Screenshot of the filtering options in the Extension view, highlighting the 'outdated' and 'recentlyUpdated' options.](images/1_97/extension-filters.png)

### SVG image preview support

The built-in image preview now has basic support for previewing SVG files.

![Screenshot that shows the default preview of an SVG image in VS Code.](images/1_97/image-svg-preview.png)

Check out the [Visual Studio Marketplace](https://marketplace.visualstudio.com/) for more advanced SVG preview extensions.

### Remove a root folder from a workspace from the CLI

Previously, you could already add a root folder to a [multi-root workspace](https://code.visualstudio.com/docs/editing/workspaces/multi-root-workspaces) by using the `--add` [command-line](https://code.visualstudio.com/docs/configure/command-line#_advanced-cli-options) option.

We've now also added the ability to remove a root folder from a multi-root workspace with the new `--remove` command-line option.

```bash
code --remove /path/to/rootfolder
```

## Editor

### Persist find and replace history

Last milestone, we introduced history persistence to the editor find control. In this milestone, we are extending that further to the replace input control, so that you can maintain both separately across multiple sessions. The replace history is stored per workspace and can be disabled via the `setting(editor.find.replaceHistory)` setting.

<video src="images/1_97/replace-history.mp4" title="Video showing the persistence of editor replace history across VS Code reloads." autoplay loop controls muted></video>

### Comments

#### Confirmation when closing an un-submitted comment

A confirmation dialog will show when you `esc` or otherwise close a comment control that has un-submitted comments. You can disable this confirmation with the setting `setting(comments.thread.confirmOnCollapse)`.

#### Quick actions in the comment editor

Quick actions can be used from the comments editor.

![Screenshot that shows Quick actions in the Comments editor.](images/1_97/quick-actions-in-comment.gif)

## Source Control

### Git blame information

This milestone, we continued to polish the feature to display git blame information in the editor and in the Status Bar. We have also improved the information that is shown when you hover over the editor decoration or the Status Bar item.

![Screenshot that shows Git blame information when hovering over the git blame item in the Status Bar.](images/1_97/scm-git-blame.png)

The git blame Status Bar item is now enabled by default, and you can disable it by using the `setting(git.blame.statusBarItem.enabled:false)` setting. Enable the git blame editor decoration with the `setting(git.blame.editorDecoration.enabled:true)` setting.

We have also added commands to easily toggle the git blame information by using the Command Palette or by using keybindings: **Git: Toggle Git Blame Editor Decoration** and **Git: Toggle Git Blame Status Bar Item**.

### GitHub repositories

For repositories that are hosted on GitHub, we have added a new command, **Open on GitHub**, to the timeline context menu and hover, the Source Control Graph context menu and hover, and the git blame editor decoration and Status Bar item hover.

![Screenshot of the Source Control history item hover, highlighting the Open on GitHub link.](images/1_97/scm-graph-hover.png)

GitHub issue and pull request references are rendered as links in the timeline, Source Control Graph, and git blame editor decoration and Status Bar item hovers, so that they can be easily opened in a browser.

Last but not least, we also added the GitHub avatar in the timeline, Source Control Graph, and git blame editor decoration and Status Bar item hovers. Disable the rendering of the GitHub avatar by using the `setting(github.showAvatar:false)` setting.

### Source Control Graph actions

We have expanded the functionality of the Source Control Graph view by adding actions scoped to a history item reference (for example, branch tag) to the context menu. The first actions that we added are **Checkout**, **Delete Branch**, and **Delete Tag** that allow you to easily check out a branch/tag, delete a branch, and delete a tag directly from the Source Control Graph view.

We are planning to add more actions in the upcoming milestones.

## Notebooks

### Inline values upon cell execution

The notebook editor now supports showing inline values after cell execution with the setting `setting(notebook.inlineValues)`. When enabled, after a successful cell execution, inline values are displayed according to the results of a registered `InlineValueProvider`.

If there's no provider, the fallback approach matches values found in the kernel against the cell document via simple regex matching. It's recommended to use a provider from a language extension to ensure more accurate results.

![Screenshot that shows inline values after cell execution in the notebook editor.](images/1_97/nb-inline-values.png)

### Custom font family for Markdown cells

The notebook editor now supports setting a custom font family for rendered Markdown cells. This can be controlled with the `setting(notebook.markup.fontFamily)` setting. When left blank, the default workbench font family is used.

![Screenshot that shows a custom font for rendered Markdown cells.](images/1_97/markdown-cell-font-family.png)

## Terminal

### Ligature support

This feature is now considered stable. Here is a summary of the changes since the last version:

- The enablement setting changed from `terminal.integrated.fontLigatures` to `setting(terminal.integrated.fontLigatures.enabled)`.
- Ligatures are now temporarily disabled while the cursor or the selection is within the ligature.
- Use `setting(terminal.integrated.fontLigatures.featureSettings)` to set ligature sets and variants. This is passed on to the [`font-feature-settings`](https://developer.mozilla.org/en-US/docs/Web/CSS/font-feature-settings) CSS property behind the scenes.
- Use `setting(terminal.integrated.fontLigatures.fallbackLigatures)` to set ligature sequences manually when the library that we use to parse ligatures is not supported.

### Support for ConEmu's progress escape sequences

The `ESC ] 9 ; 4` escape sequence pioneered by ConEmu that reports progress in the terminal is now supported. This is used by some CLI tools such as `winget` to report progress. To view the progress in terminal tabs, add `${progress}` to either `setting(terminal.integrated.tabs.title)` or `setting(terminal.integrated.tabs.description)`. This typically shows as a progress spinner or as a percentage.

<video src="images/1_97/terminal-progress.mp4" title="Video showing a progress indicator in the terminal title while running a 'winget' command." autoplay loop controls muted></video>

### Sticky Scroll for truncated commands

Sticky Scroll in the terminal (`setting(terminal.integrated.stickyScroll.enabled)`) now shows when a command is truncated with an ellipsis at the end:

![Screenshot that shows an ellipsis at the end of a command in Sticky Scroll when it is truncated.](images/1_97/terminal-sticky-scroll-ellipsis.png)

### Configure the behavior when the last terminal closes

The new `setting(terminal.integrated.hideOnLastClosed)` setting allows configuring whether the panel will be closed when the last terminal has been closed. Along with this, the experience when there is no terminal open has been improved.

## Tasks

### Column number variable

The new `${columnNumber}` variable can be used in [`tasks.json`](https://code.visualstudio.com/docs/debugtest/tasks) and [`launch.json`](https://code.visualstudio.com/docs/debugtest/debugging#_launch-configurations) to refer to the column number of the cursor position. See the full list of [variables](https://code.visualstudio.com/docs/editor/variables-reference) in the VS Code documentation.

## Debug

### Filter and search on values

You can now search within a view (`kb(list.find)`) in the Variables and Watch views to filter on variable and expression values, rather than just names.

![Screenshot that shows the search control in the Variables view when debugging.](images/1_97/debug-search-values.png)

### Select in the Debug Console

The Debug Console now supports longer and more reliable content selection, allowing easier copy and paste.

### JavaScript debugger

Scripts can now be _pretty printed_ by using the **Debug: Pretty print for debugging** command from the command palette or editor actions, even when they are not the source the debugger is currently paused in.

## Languages

### TypeScript 5.7.3

This release includes the [TypeScript 5.7.3](https://github.com/microsoft/typescript/issues?q=is%3Aissue%20state%3Aclosed%20%20milestone%3A%22TypeScript%205.7.3%22) recovery release. This minor update fixes a few import bugs and regressions.

### Right click to open images from the Markdown preview

You can now right click on a workspace image in the Markdown preview and select **Open Image** to open it in a new editor.

![Screenshot that shows the context menu option to open an image in the Markdown preview.](images/1_97/md-preview-open-image.png)

This is supported for any images that are part of the current workspace.

### Markdown link validation status item

VS Code's built-in Markdown features support automatically [validating local links to files and images](https://code.visualstudio.com/docs/languages/markdown#_link-validation). This is a great way to catch common mistakes, such as linking to a header that was renamed or to a file that no longer exists on disk.

To help you discover this feature, we've added a new language status item for link validation:

![Screenshot that shows the Markdown link validation language status item.](images/1_97/md-link-status-item.png)

With a Markdown file open, select the `{}` in the Status Bar to view the link validation status. You can also use the status item to quickly toggle link validation off and on.

### New Ruby syntax highlighting grammar

We've moved away from the old, unmaintained, Ruby grammar from `textmate/ruby.tmbundle` and now get our Ruby grammar from `Shopify/ruby-lsp`.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack) allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

- Migration path for connecting to Linux legacy servers
- SSH Chat participant improvements
- SSH configuration improvements
- Default remote extensions for SSH

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_97.md).

## Contributions to extensions

### Microsoft Account

#### Microsoft Account now uses MSAL (with WAM support on Windows)

> NOTE: Last month's rollout of an MSAL-based authentication for Microsoft needed to be rolled back due to a critical bug. This bug has been squashed and we are proceeding with the rollout.

In order to ensure a strong security baseline for Microsoft authentication, we've adopted the [Microsoft Authentication Library](https://github.com/AzureAD/microsoft-authentication-library-for-js) in the Microsoft Account extension.

One of the stand out features of this work is WAM (Web Account Manager... also known as [Broker](https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/brokering.md)) integration. Put simply, rather than going to the browser for Microsoft authentication flows, we now talk to the OS directly, which is the recommended way of acquiring a Microsoft authentication session. Additionally, it's faster since we're able to leverage the accounts that you're already logged into on the OS.

![Screenshot that shows an authentication popup that the OS shows over VS Code.](images/1_97/showingBrokerOSDialog.png)

Let us know if you see any issues with this new flow. If you do see a major issue and need to revert back to the old Microsoft authentication behavior, you can do so with `setting(microsoft-authentication.implementation)` (setting it to `classic`, and restarting VS Code) but do keep in mind that this setting won't be around for much longer. So, open an issue if you are having trouble with the MSAL flow.

### Python

#### Launch native REPL from the terminal

You are now able to launch a VS Code Native REPL from your REPL in the terminal. Setting `setting(python.terminal.shellIntegration.enabled)` to `true` should display a clickable link in the Python REPL in the terminal, allowing you to directly open the VS Code Native REPL from the terminal.

<video src="images/1_97/native-repl-link.mp4" title="Video showing the Native REPL entry point link in terminal REPL." autoplay loop controls muted></video>

#### No config debug

You are now able to debug a Python script or module without setup, right from the terminal as part of the new no-config debug feature! Check out the [wiki page](https://github.com/microsoft/vscode-python-debugger/wiki/No%E2%80%90Config-Debugging) on the feature for all the details and troubleshooting tips.

<video src="images/1_97/no-config-debug.mp4" title="Video showing the no-config debug feature for Python." autoplay loop controls muted></video>

#### Test discovery cancellation

When triggering test discovery from the Test Explorer UI, you can now cancel an ongoing test discovery call. Use the Cancel button, which appears in replacement of the Refresh button during discovery.

![Screenshot that shows the Test Explorer, highlighting the Cancel button to cancel the test discovery.](./images/1_97/test-discovery-cancelation.png)

#### Go to Implementation

[Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) now has support for **Go to Implementation**, which allows you to more quickly navigate to the implementation of a function or method directly from its usage. This is a particularly helpful feature when working with inherited classes.

![Screenshot that shows the Go to Implementation displayed via the context menu](./images/1_97/pylance-go-to-implementation.png)

#### AI Code Action: Generate Symbol (Experimental)

There's a new experimental AI Code Action for generating symbols with Pylance and Copilot. To try it out, you can enable the following setting:

```
"python.analysis.aiCodeActions": {"generateSymbol": true}
```

Then once you define a new symbol, for example, a class or a function, you can select the **Generate Symbol with Copilot** Code Action and let Copilot handle the implementation! If you want, you can then leverage Pylance's **Move Symbol** Code Actions to move it to a different file.

<video src="images/1_97/pylance-create-symbol-with-copilot.mp4" title="Video showing invoking a new class that doesn't exist and then selecting the 'Create Class With Copilot' code action to implement a new class." autoplay loop controls muted></video>

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which enables you to work on, create, and manage pull requests and issues. New features include:

* Global pull request queries, with a variable to specify a time range relative to today (`${today-7d}`).
* `:<emoji-name>:` style emojis are now supported in comments.
* All non-outdated comments will show in the Comments panel when you open the description of an un-checked out pull request.

Review the [changelog for the 0.104.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#01040) release of the extension to learn about the other highlights.

## Preview Features

### Agent mode (Experimental)

We've been working on a new _agent mode_ for Copilot Edits. When in agent mode, Copilot can automatically search your workspace for relevant context, edit files, check them for errors, and run terminal commands (with your permission) to complete a task end-to-end.

![Screenshot that shows agent mode in the Copilot Edits view.](images/1_97/agent-mode.png)

You can switch between the current edit mode that we've had for a few months and agent mode with the dropdown in the Copilot Edits view. To see the dropdown, enable the `setting(chat.agent.enabled)` setting. You can start using agent mode in [VS Code Insiders](https://code.visualstudio.com/insiders/) today. We will gradually be rolling it out to VS Code Stable users. If the setting doesn't show up for you in Stable, then it isn't enabled for you yet.

![Screenshot of the agent mode setting in the Settings editor.](images/1_97/agent-setting.png)

In agent mode, Copilot runs autonomously, but it can only edit files within your current workspace. When it wants to run a terminal command, it shows you the command and waits for you to review it and select Continue before proceeding.

> **Note**: Copilot Edits may use many chat requests in agent mode, so it will periodically pause and ask you whether to continue. You can customize this with `setting(chat.agent.maxRequests)`. This defaults to 15 for Copilot paid users, and 5 for Copilot Free users.

Learn more about [agent mode in Copilot Edits](https://code.visualstudio.com/docs/copilot/copilot-edits#_use-agent-mode-preview) in the VS Code documentation.

### Agentic codebase search (Preview)

You can add `#codebase` to your query and Copilot Edits will discover relevant files for your task. We've added experimental support for discovering relevant files using additional tools like file and text search, Git repository state, and directory reads. Previously, `#codebase` only performed semantic search.

You can enable it with `setting(github.copilot.chat.edits.codesearch.enabled)`, and please [share any feedback](https://github.com/microsoft/vscode-copilot-release) with us.

### Copilot Vision in VS Code Insiders (Preview)

We're introducing end-to-end vision support in the pre-release version of GitHub Copilot Chat in [VS Code Insiders](https://code.visualstudio.com/insiders/). This lets you attach images and interact with images in Copilot Chat prompts. For example, if you're encountering an error while debugging, quickly attach a screenshot of VS Code and ask Copilot to help you resolve the issue.

![Screenshot that shows an attached image in a Copilot Chat prompt. Hovering over the image shows a preview of it.](images/1_97/image-attachments.gif)

You can now attach images using several methods:

- Drag and drop images from your OS or from the Explorer view
- Paste an image from the clipboard
- Attach a screenshot of the VS Code window (select Attach > Screenshot Window)

A warning is shown if the selected model currently does not have the capability to handle images. The only supported model at the moment will be `GPT 4o`. Currently, the supported image types are `JPEG/JPG`, `PNG`, `GIF`, and `WEBP`.

### Reusable prompts (Experimental)

This feature lets you build, store, and share reusable prompts. A prompt file is a `.prompt.md` Markdown file that follows the same format used for writing prompts in Copilot Chat, and it can link to other files or even other prompts. You can attach prompt files for task-specific guidance, aid with code generation, or keep complete prompts for later use.

To enable prompt files, set `setting(chat.promptFiles)` to `true`, or use the `{ "/path/to/folder": boolean }` notation to specify a different path. The `.github/prompts` folder is used by default to locate prompt files (`*.prompt.md`), if no other path is specified.

Learn more about [prompt files](https://aka.ms/vscode-ghcp-prompt-snippets) in the VS Code documentation.

### Custom title bar on Linux (Experimental)

This milestone, we are starting an experiment to enable the custom title bar for a subset of Linux users.

![Screenshot that shows the custom VS Code title bar on Linux.](images/1_97/custom-title.png)

If you are not part of the experiment, you can manually configure `setting(window.titleBarStyle)` as `custom` to try it out.

You can always revert back to the native title decorations, either from the custom title context menu or by configuring `setting(window.titleBarStyle)` to `native`.

![Screenshot that shows the content menu option to disable the custom title bar on Linux.](images/1_97/restore-title.png)

### TypeScript 5.8 beta support

This release includes support for the TypeScript 5.8 beta release. Check out the [TypeScript 5.8 blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-5-8-beta/) for details on what's in store for this release.

To start using preview builds of TypeScript 5.8, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Share your feedback and let us know if you run into any bugs with TypeScript 5.8.

### Terminal completions for more shells

We have iterated on the general terminal completions that [were introduced in the last version](https://code.visualstudio.com/updates/v1_96#_terminal-completions-for-more-shells) that are built on our new proposed API. Once enabled with `setting(terminal.integrated.suggest.enabled)`, the new completions now replace the previous built-in provider for PowerShell but can now be customized with `setting(terminal.integrated.suggest.providers)`.

Here are the key updates this release:

- Enhanced widget styling and configuration to align with the editor's suggest widget.
- A configurable status bar (`setting(terminal.integrated.suggest.showStatusBar)`) provides contextual actions and information.
- Improved argument awareness for commands, including: `code`, `code-insiders`, `cd`, `ls`, `rm`, `echo`, `mkdir`, `rmdir`, `touch`.
- Displays command or resource paths as additional details.
- Added support for directory navigation shortcuts like `.`, `..`, and `../../`.
- Enabled screen reader usage.
- Entries pulled from the `$PATH` will now only show when they are executable files. Since Windows does not have the concept of an executable bit in file metadata, the list of extensions can be configured with `setting(terminal.integrated.suggest.windowsExecutableExtensions)`. These now also use the actual shell environment when available using an upcoming proposed API.
- Enhanced keyboard support to toggle details, `kb(workbench.action.terminal.suggestToggleDetails)` and toggle suggest details focus `kb(workbench.action.terminal.suggestToggleDetailsFocus)`.
- Suggestions will now always be on every type, aligning closer to how quick suggestions work in the editor.
- PowerShell-specific global completions such as `Get-ChildItem`, `Write-Host`, etc. will now be suggested.

### Tree-Sitter based syntax highlighting for typescript

Since many of our Textmate grammars are no longer maintained, we've been investigating using Tree-Sitter for syntax highlighting. We've started with TypeScript so the team can selfhost it and provide feedback. You can try out an early preview it out with the `setting(editor.experimental.preferTreeSitter)` setting.

## Extension Authoring

### Document paste API

The document paste API allows extensions to hook into copy/paste operations in text documents. Using this API, your extension can:

- On copy, write data to the clipboard. This includes writing metadata that the can be picked up on paste.

- On paste, generate a custom edit that applies the paste. This can change the text content being pasted or make more complex workspace edits, such as creating new files.

- Provide multiple ways that content can be pasted. Users can select how content should be pasted using the paste control or with the `editor.pasteAs.preferences` setting.

VS Code uses the document paste API to implement features such as [updating imports on paste for JavaScript and TypeScript](https://code.visualstudio.com/updates/v1_95#_update-imports-on-paste-for-javascript-and-typescript) and [automatically creating Markdown links when pasting URLs](https://code.visualstudio.com/updates/v1_81#_markdown-paste-urls-as-formatted-links).

To get started with the document paste API, check out the [document paste extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/document-paste). For a more complex example, check out how the built-in Markdown extension [implements pasting of image files](https://github.com/microsoft/vscode/blob/8237317c2ff4d0a3dadfaeb67cd35630cf1a6b75/extensions/markdown-language-features/src/languageFeatures/copyFiles/dropOrPasteResource.ts#L31) to insert images into Markdown documents.

### File `openLabel` shows in the simple file picker

The `openLabel` property in the `OpenDialogOptions` is now supported in the [simple file picker](https://code.visualstudio.com/docs/getstarted/tips-and-tricks#_simple-file-dialog) (in addition to the system file picker, where it was previously exclusively supported). This allows you to provide a custom label for the button in the file picker.

### File-level comments API

The comments API supports making and showing file-level comments. File-level comments show at the top of the file, before the first line. They are not attached to a specific line or range in the file. To show a file-level comment, set the `range` of the comment to `undefined`. To support leaving file-level comments from your commenting range provider, set the `enableFileComments` property on your `CommentingRangeProvider` to `true`.

## Proposed APIs

### Terminal completion provider

You can now [register a terminal completion provider](https://github.com/microsoft/vscode/blob/8d5f6f0050af2abde8c9977f1a269ef6eade6915/src/vscode-dts/vscode.proposed.terminalCompletionProvider.d.ts#L79-L87) and let us know what you think [in this GitHub issue](https://github.com/microsoft/vscode/issues/226562).

An example of this can be found in our terminal suggest extension, which provides completions when enabled via `setting(terminal.integrated.suggest.enabled)`.

### Terminal shell type

Extensions will be able to [access currently active shell type information](https://github.com/microsoft/vscode/blob/8d5f6f0050af2abde8c9977f1a269ef6eade6915/src/vscode-dts/vscode.proposed.terminalShellType.d.ts).
The `shellType` field will be part of `TerminalState`.

Use this shell type information to perform shell-specific operations that you need.

## Engineering

### Housekeeping

As part of our annual housekeeping efforts in December, we focused on cleaning up our GitHub issues and pull requests across all repositories. This year, we achieved a net reduction of 3,821 issues and pull requests, ensuring that our backlog remains relevant and manageable.

By following our issue cleanup guide, we reviewed and triaged outdated, duplicated, and no longer relevant issues. This helps us maintain an efficient development workflow and focus on improving Visual Studio Code for our users.

We appreciate the community’s continued engagement and feedback — your contributions make VS Code better every day! 🚀

![Chart that shows the trend of the number of open issues over the last years. The chart shows a steep decline each year during December, the housekeeping month.](images/1_97/housekeeping.png)

### Resource optimization for file watching in TypeScript workspaces

A couple of optimizations have been made to reduce the overhead that file watching has in large TypeScript workspaces (thousands of TypeScript files or projects). Specifically, when opening such a workspace and initializing the watcher, you should no longer see CPU spikes or CPU usage should settle quickly.

See this [VS Code issue](https://github.com/microsoft/vscode/issues/237351) for more details.

## Notable fixes

* [160325](https://github.com/microsoft/vscode/issues/160325) Suppress terminal launch failure after ctrl+D is pressed
* [230438](https://github.com/microsoft/vscode/issues/230438) Support for code page `1125` aka `cp866u`
* [238577](https://github.com/microsoft/vscode/issues/238577) Increase default window size
* [197377](https://github.com/microsoft/vscode/issues/197377) workspaceFolder variable substitution in launch.json or tasks.json should use URI for virtual filesystems
* [229857](https://github.com/microsoft/vscode/issues/229857) a11y view is blank after running `focus comment on line`

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@albertosantini (Alberto Santini)](https://github.com/albertosantini)
* [@starball5 (starball)](https://github.com/starball5)

### Pull requests

Contributions to `vscode`:

* [@Abrifq (Arda Aydın)](https://github.com/Abrifq): Change `Create New Terminal` to focus accordingly to the terminal location [PR #237404](https://github.com/microsoft/vscode/pull/237404)
* [@adrianstephens](https://github.com/adrianstephens): custom editor preview [PR #235533](https://github.com/microsoft/vscode/pull/235533)
* [@andrewsuzuki (Andrew Suzuki)](https://github.com/andrewsuzuki): Fix 'new Color' string typo for editorBracketHighlight.unexpectedBracket.foreground [PR #237236](https://github.com/microsoft/vscode/pull/237236)
* [@aslezar (Shivam Garg)](https://github.com/aslezar)
  * Fix incorrect GLIBC version parsing [PR #236082](https://github.com/microsoft/vscode/pull/236082)
  * feat: support custom js switch-case indentation [PR #237069](https://github.com/microsoft/vscode/pull/237069)
* [@atreids (Aaron Donaldson)](https://github.com/atreids): chore: fix typo in VSIX progress notification [PR #238845](https://github.com/microsoft/vscode/pull/238845)
* [@BABA983 (BABA)](https://github.com/BABA983): Resolve custom editor with canonical resource [PR #226614](https://github.com/microsoft/vscode/pull/226614)
* [@congyuandong (scott)](https://github.com/congyuandong): fix: remove duplicate `!**/*.mk` entry in dependenciesSrc [PR #236683](https://github.com/microsoft/vscode/pull/236683)
* [@DetachHead](https://github.com/DetachHead): remove `javascript.inlayHints.enumMemberValues.enabled` because javascript does not have enums [PR #236297](https://github.com/microsoft/vscode/pull/236297)
* [@devm33 (Devraj Mehta)](https://github.com/devm33): Use Electron fetch or Node fetch for github-authentication to support proxies [PR #238149](https://github.com/microsoft/vscode/pull/238149)
* [@dmitrysonder (Dmitry Sonder)](https://github.com/dmitrysonder): refactor: use EventType constants for events [PR #236941](https://github.com/microsoft/vscode/pull/236941)
* [@fa0311 (ふぁ)](https://github.com/fa0311): Fix ${unixTime} Placeholder to Use Full Millisecond Timestamp in markdown.copyFiles.destination [PR #239061](https://github.com/microsoft/vscode/pull/239061)
* [@g122622](https://github.com/g122622): Scrollbar for File menu is displaying over Open Recent [PR #236998](https://github.com/microsoft/vscode/pull/236998)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Add a Configure option to overflow menu of Open Editors view [PR #237678](https://github.com/microsoft/vscode/pull/237678)
* [@goodmind (andretshurotshka)](https://github.com/goodmind): Fixes #44237: Add column number in tasks [PR #65264](https://github.com/microsoft/vscode/pull/65264)
* [@HD787 (Henry)](https://github.com/HD787)
  * Add ${unixTime} Placeholder for markdown.copyFiles.destination Option [PR #238027](https://github.com/microsoft/vscode/pull/238027)
  * enable typescript commands when only config files are open [PR #238630](https://github.com/microsoft/vscode/pull/238630)
* [@iamdereky (Derek Yang)](https://github.com/iamdereky): Fix CSS errors when using HTML escaped quotes [PR #235367](https://github.com/microsoft/vscode/pull/235367)
* [@jakebailey (Jake Bailey)](https://github.com/jakebailey): Remove paths from tsconfig.base.json [PR #238475](https://github.com/microsoft/vscode/pull/238475)
* [@janajar (Jawad Najar)](https://github.com/janajar): Fix: When no results in search editor it throws an error [PR #235031](https://github.com/microsoft/vscode/pull/235031)
* [@jaymroy](https://github.com/jaymroy): Issue: #214481 Add Option to Ignore Code Blocks in Text-to-Speech [PR #235697](https://github.com/microsoft/vscode/pull/235697)
* [@jogibear9988 (Jochen Kühner)](https://github.com/jogibear9988): support svg's in image preview [PR #237217](https://github.com/microsoft/vscode/pull/237217)
* [@Jules-Bertholet (Jules Bertholet)](https://github.com/Jules-Bertholet): Support back and forward keys in default shortcuts [PR #237701](https://github.com/microsoft/vscode/pull/237701)
* [@Legend-Master (Tony)](https://github.com/Legend-Master): Reland fix custom task shell doesn't work without manually passing in "run command" arg/flag [PR #236058](https://github.com/microsoft/vscode/pull/236058)
* [@LemmusLemmus](https://github.com/LemmusLemmus): Add $ to surrounding pairs in markdown [PR #233981](https://github.com/microsoft/vscode/pull/233981)
* [@leopardracer (leopardracer)](https://github.com/leopardracer): fix: typos in documentation files [PR #235968](https://github.com/microsoft/vscode/pull/235968)
* [@misode (Misode)](https://github.com/misode): Fix missing uri to file path conversion when loading json schema [PR #237275](https://github.com/microsoft/vscode/pull/237275)
* [@mohankumarelec (mohanram)](https://github.com/mohankumarelec): Fixes #236973 [PR #236974](https://github.com/microsoft/vscode/pull/236974)
* [@notoriousmango (Seong Min Park)](https://github.com/notoriousmango): Add 'open image' context in markdown preview [PR #234649](https://github.com/microsoft/vscode/pull/234649)
* [@numbermaniac](https://github.com/numbermaniac): Fix typo in InlayHintKind docs [PR #238032](https://github.com/microsoft/vscode/pull/238032)
* [@oltolm (oltolm)](https://github.com/oltolm): debug: ignore error when stopping a process [PR #236009](https://github.com/microsoft/vscode/pull/236009)
* [@oxcened (Alen Ajam)](https://github.com/oxcened)
  * fix: set _lastFocusedWidget as undefined on widget blur [PR #234610](https://github.com/microsoft/vscode/pull/234610)
  * fix: check whether lastFocusedList is valid when assigned [PR #238765](https://github.com/microsoft/vscode/pull/238765)
* [@pankajk07 (Pankaj Khandelwal)](https://github.com/pankajk07): fix: synchronous script loading from web workers of extensions [PR #233175](https://github.com/microsoft/vscode/pull/233175)
* [@Parasaran-Python (Parasaran)](https://github.com/Parasaran-Python)
  * fix 227150: Added a recursive git clone button [PR #232497](https://github.com/microsoft/vscode/pull/232497)
  * fix 235221: Sanitizing the html content by closing the unclosed tags [PR #236145](https://github.com/microsoft/vscode/pull/236145)
* [@r3m0t (Tomer Chachamu)](https://github.com/r3m0t): Fix revealing a notebook cell from the debugger stopping and revealing active statement (Fixes #225290) [PR #225292](https://github.com/microsoft/vscode/pull/225292)
* [@rcjsuen (Remy Suen)](https://github.com/rcjsuen): Fix typo in the help text of the icon extension point [PR #238393](https://github.com/microsoft/vscode/pull/238393)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
  * Fix extension preview codeblock language getter [PR #235880](https://github.com/microsoft/vscode/pull/235880)
  * Add `outdated` and `recentlyUpdated` suggestions to extension filter [PR #235884](https://github.com/microsoft/vscode/pull/235884)
* [@remcohaszing (Remco Haszing)](https://github.com/remcohaszing)
  * Mark bun.lock as jsonc [PR #235917](https://github.com/microsoft/vscode/pull/235917)
  * Allow the .ndjson extension for the jsonl language [PR #235921](https://github.com/microsoft/vscode/pull/235921)
* [@RiskyMH (Michael H)](https://github.com/RiskyMH): `bun.lock` as package manager lockfile [PR #236012](https://github.com/microsoft/vscode/pull/236012)
* [@sunnylost (sunnylost)](https://github.com/sunnylost): fix(settings-editor): ensure the width of the key name does not shrink [PR #229919](https://github.com/microsoft/vscode/pull/229919)
* [@tcostew](https://github.com/tcostew): Allow Github Copilot chat to appear in QuickAccess [PR #210805](https://github.com/microsoft/vscode/pull/210805)
* [@tmm1 (Aman Karmani)](https://github.com/tmm1)
  * build: update to include more tsc boilerplate [PR #238422](https://github.com/microsoft/vscode/pull/238422)
  * build: switch `build/tsconfig.json` to `module: nodenext` [PR #238426](https://github.com/microsoft/vscode/pull/238426)
* [@tobil4sk](https://github.com/tobil4sk): Merge diverging findExecutable functions [PR #228373](https://github.com/microsoft/vscode/pull/228373)
* [@zWingz (zWing)](https://github.com/zWingz): fix(git-ext): fix limitWarning block the git status progress [PR #226577](https://github.com/microsoft/vscode/pull/226577)

Contributions to `vscode-eslint`:

* [@ShahinSorkh (Shahin Sorkh)](https://github.com/ShahinSorkh): clarify where to set `eslint.runtime` and `eslint.execArgv` options [PR #1973](https://github.com/microsoft/vscode-eslint/pull/1973)

Contributions to `vscode-extension-samples`:

* [@IsBenben](https://github.com/IsBenben): Fixes cannot find module 'semver' [PR #1129](https://github.com/microsoft/vscode-extension-samples/pull/1129)

Contributions to `vscode-js-debug`:

* [@mdh1418 (Mitchell Hwang)](https://github.com/mdh1418): Update BlazorDebugger telemetry report event [PR #2158](https://github.com/microsoft/vscode-js-debug/pull/2158)

Contributions to `vscode-jupyter`:

* [@gy-mate (Máté Gyöngyösi)](https://github.com/gy-mate): Capitalize 'URL' [PR #16340](https://github.com/microsoft/vscode-jupyter/pull/16340)
* [@pwang347 (Paul)](https://github.com/pwang347): Add `waitUntil` for `onDidStart` event [PR #16375](https://github.com/microsoft/vscode-jupyter/pull/16375)

Contributions to `vscode-loc`:

* [@NicoWeio (Nicolai Weitkemper)](https://github.com/NicoWeio): improve grammar in README [PR #1367](https://github.com/microsoft/vscode-loc/pull/1367)

Contributions to `vscode-prompt-tsx`:

* [@atxtechbro (Morgan Joyce)](https://github.com/atxtechbro)
  * docs: fix typo in Passing Priority description [PR #140](https://github.com/microsoft/vscode-prompt-tsx/pull/140)
  * docs: fix grammar in budget allocation description [PR #141](https://github.com/microsoft/vscode-prompt-tsx/pull/141)

Contributions to `vscode-pull-request-github`:

* [@mikeseese (Mike Seese)](https://github.com/mikeseese): Add opt-in to always prompt for repo for issue creation and add comment to issue file specifying the repo [PR #6115](https://github.com/microsoft/vscode-pull-request-github/pull/6115)
* [@NellyWhads (Nelly Whads)](https://github.com/NellyWhads): Remove the python language user mention exception [PR #6525](https://github.com/microsoft/vscode-pull-request-github/pull/6525)
* [@Ronny-zzl (Zhang)](https://github.com/Ronny-zzl): Don't show hover cards for @-mentioned users in JSDocs in jsx and tsx files [PR #6531](https://github.com/microsoft/vscode-pull-request-github/pull/6531)

Contributions to `vscode-pylint`:

* [@DetachHead](https://github.com/DetachHead): workaround for memory leak caused by pylint bug [PR #585](https://github.com/microsoft/vscode-pylint/pull/585)

Contributions to `vscode-python-debugger`:

* [@rchiodo (Rich Chiodo)](https://github.com/rchiodo)
  * Update to 1.8.11 [PR #536](https://github.com/microsoft/vscode-python-debugger/pull/536)
  * Support the clientOS property in VS code [PR #550](https://github.com/microsoft/vscode-python-debugger/pull/550)
  * Update `debugpy` to 1.8.12 [PR #558](https://github.com/microsoft/vscode-python-debugger/pull/558)

Contributions to `vscode-ripgrep`:

* [@fiji-flo (Florian Dieminger)](https://github.com/fiji-flo): fix long download [PR #62](https://github.com/microsoft/vscode-ripgrep/pull/62)
* [@tmm1 (Aman Karmani)](https://github.com/tmm1): Fix for arm64 windows [PR #63](https://github.com/microsoft/vscode-ripgrep/pull/63)

Contributions to `vscode-test`:

* [@kamaal111 (Kamaal Farah)](https://github.com/kamaal111): docs: update Github Actions link to point to sample [PR #297](https://github.com/microsoft/vscode-test/pull/297)

Contributions to `language-server-protocol`:

* [@asukaminato0721 (Asuka Minato)](https://github.com/asukaminato0721): cython-lsp [PR #2064](https://github.com/microsoft/language-server-protocol/pull/2064)
* [@catwell (Pierre Chapuis)](https://github.com/catwell): Add Teal LSP [PR #2078](https://github.com/microsoft/language-server-protocol/pull/2078)
* [@Enaium (Enaium)](https://github.com/Enaium)
  * Add Jimmer DTO [PR #2070](https://github.com/microsoft/language-server-protocol/pull/2070)
  * Change Maintainer [PR #2071](https://github.com/microsoft/language-server-protocol/pull/2071)
* [@g-plane (Pig Fang)](https://github.com/g-plane): New language server: wasm-language-tools [PR #2065](https://github.com/microsoft/language-server-protocol/pull/2065)
* [@jcs090218 (Jen-Chieh Shen)](https://github.com/jcs090218): chore(_implementors/servers.md): Update Ellsp link [PR #2073](https://github.com/microsoft/language-server-protocol/pull/2073)
* [@kbwo (Kodai Kabasawa)](https://github.com/kbwo): Add testing-language-server in servers.md [PR #2076](https://github.com/microsoft/language-server-protocol/pull/2076)
* [@kylebonnici (Kyle Micallef Bonnici)](https://github.com/kylebonnici): Add Devicetree LSP to list [PR #2085](https://github.com/microsoft/language-server-protocol/pull/2085)
* [@ribru17 (Riley Bruins)](https://github.com/ribru17): Add ts_query_ls (Tree-sitter query language server) [PR #2068](https://github.com/microsoft/language-server-protocol/pull/2068)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
