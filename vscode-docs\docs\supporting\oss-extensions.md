---
Order:
TOCTitle: Microsoft Extension Licenses
ContentId: 661ad650-7bed-41f6-a62b-9eb1407010ab
PageTitle: Microsoft Extension Licenses
DateApproved: 05/08/2025
MetaDescription: List of Microsoft Extension Licenses
---
# Microsoft Extension Licenses

Microsoft follows a policy of open sourcing its extensions whenever possible. However, reliance on existing proprietary source code or libraries, source code that crosses into Microsoft licensed tools or services (for example Visual Studio), and business model differences across the entirety of Microsoft results in some extensions using a proprietary license. We continue to have a heavy investment and commitment to open source, but we also believe that transparency is important to the health of the Visual Studio Code community.

As more and more teams in Microsoft contribute extensions, we realize that it can be difficult to keep track of which extensions are open source and which are not. To help, the following table is a list of Microsoft contributed extensions in the Marketplace, their published licenses, and whether their source code is available. Given the rate new extensions are being released from an increasing number of teams, this list may not be complete. The list does not include contributions published by individual Microsoft employees to the community on their own time.

## Microsoft Extensions

| Extension | Source Code Available | License |
|-----------|-----|---------|
| [Ansible](https://marketplace.visualstudio.com/items?itemName=vscoss.vscode-ansible) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscoss.vscode-ansible/license) |
| [Arduino](https://marketplace.visualstudio.com/items?itemName=vsciot-vscode.vscode-arduino) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vsciot-vscode.vscode-arduino/license) |
| [Atom Keymap](https://marketplace.visualstudio.com/items?itemName=ms-vscode.atom-keybindings) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.atom-keybindings/license) |
| [AutoRest](https://marketplace.visualstudio.com/items?itemName=ms-vscode.autorest) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.autorest/license) |
| [Azure Account](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-account) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.azure-account/license) |
| [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azureappservice/license) |
| [Azure Application Insights](https://marketplace.visualstudio.com/items?itemName=VisualStudioOnlineApplicationInsights.application-insights)| | [Microsoft](https://marketplace.visualstudio.com/items/VisualStudioOnlineApplicationInsights.application-insights/license) |
| [Azure Cache](https://marketplace.visualstudio.com/items?itemName=ms-azurecache.vscode-azurecache) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azurecache.vscode-azurecache/license) |
| [Azure CLI Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azurecli) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.azurecli/license) |
| [Azure Databases](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-cosmosdb) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-cosmosdb/license) |
| [Azure Data Lake Tools](https://marketplace.visualstudio.com/items/usqlextpublisher.usql-vscode-ext) |  | [Microsoft](https://marketplace.visualstudio.com/items/usqlextpublisher.usql-vscode-ext/license) |
| [Azure Data Studio Debug](https://marketplace.visualstudio.com/items/ms-mssql.sqlops-debug)| ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-mssql.sqlops-debug/license) |
| [Azure Dev Spaces](https://marketplace.visualstudio.com/items/azuredevspaces.azds/) | | [Microsoft](https://marketplace.visualstudio.com/items/azuredevspaces.azds/license) |
| [Azure Event Grid](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azureeventgrid) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azureeventgrid/license) |
| [Azure Functions](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azurefunctions) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azurefunctions/license) |
| [Azure IoT Edge](https://marketplace.visualstudio.com/items/vsciot-vscode.azure-iot-edge) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vsciot-vscode.azure-iot-edge/license) |
| [Azure IoT Hub](https://marketplace.visualstudio.com/items/vsciot-vscode.azure-iot-toolkit) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vsciot-vscode.azure-iot-toolkit/license) |
| [Azure IoT Device Workbench](https://marketplace.visualstudio.com/items/vsciot-vscode.vscode-iot-workbench) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vsciot-vscode.vscode-iot-workbench/license) |
| [Azure Logic Apps](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-logicapps) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-logicapps/license) |
| [Azure Machine Learning](https://marketplace.visualstudio.com/items/ms-toolsai.vscode-ai) |  | [Microsoft](https://marketplace.visualstudio.com/items/ms-toolsai.vscode-ai/license) |
| [Azure Pipelines](https://marketplace.visualstudio.com/items/ms-azure-devops.azure-pipelines)| ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azure-devops.azure-pipelines/license) |
| [Azure Repos](https://marketplace.visualstudio.com/items/ms-vsts.team)| ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vsts.team/license) |
| [Azure Resource Manager (ARM) Tools](https://marketplace.visualstudio.com/items/msazurermtools.azurerm-vscode-tools)| ✔ | [MIT](https://marketplace.visualstudio.com/items/msazurermtools.azurerm-vscode-tools/license) |
| [Azure Storage](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azurestorage)| ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azurestorage/license) |
| [Azure Stream Analytics Tools](https://marketplace.visualstudio.com/items/ms-bigdatatools.vscode-asa)|  | [Microsoft](https://marketplace.visualstudio.com/items/ms-bigdatatools.vscode-asa/license) |
| [Azure Terraform](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azureterraform)| ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-azureterraform/license) |
| [Blockchain Development Kit for Ethereum](https://marketplace.visualstudio.com/items?itemName=AzBlockchain.azure-blockchain) | ✔ | [MIT](https://marketplace.visualstudio.com/items/AzBlockchain.azure-blockchain/license) |
| [C/C++](https://marketplace.visualstudio.com/items/ms-vscode.cpptools)  | | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode.cpptools/license) |
| [C#](https://marketplace.visualstudio.com/items/ms-dotnettools.csharp) | ✔ ([Repo](https://github.com/OmniSharp/omnisharp-vscode))  | [Microsoft](https://marketplace.visualstudio.com/items/ms-dotnettools.csharp/license) |
| [Container Tools](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-containers) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-containers/license) |
| [Cordova Tools](https://marketplace.visualstudio.com/items/msjsdiag.cordova-tools) | ✔ ([Repo](https://github.com/microsoft/vscode-cordova)) | [Microsoft](https://marketplace.visualstudio.com/items/msjsdiag.cordova-tools/license) |
| [Debugger for Java](https://marketplace.visualstudio.com/items/vscjava.vscode-java-debug) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-java-debug/license) |
| [docs-article-templates](https://marketplace.visualstudio.com/items?itemName=docsmsft.docs-article-templates) | ✔ | [MIT](https://docsmsft.gallerycdn.vsassets.io/extensions/docsmsft/docs-markdown/0.2.2/1547755350969/Microsoft.VisualStudio.Services.Content.License) |
| [docs-markdown](https://marketplace.visualstudio.com/items/docsmsft.docs-markdown) | ✔ | [MIT](https://marketplace.visualstudio.com/items/docsmsft.docs-markdown/license) |
| [docs-preview](https://marketplace.visualstudio.com/items/docsmsft.docs-preview) | ✔ | [MIT](https://marketplace.visualstudio.com/items/docsmsft.docs-preview/license) |
| [docs-yaml](https://marketplace.visualstudio.com/items?itemName=docsmsft.docs-yaml) | ✔ | [MIT](https://docsmsft.gallerycdn.vsassets.io/extensions/docsmsft/docs-markdown/0.2.2/1547755350969/Microsoft.VisualStudio.Services.Content.License) |
| [Duffle](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.duffle-vscode) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.duffle-vscode/license) |
| [Duffle Coat](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.duffle-coat) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.duffle-coat/license) |
| [Dynamics AL Language](https://marketplace.visualstudio.com/items/ms-dynamics-smb.al) |  | [Microsoft](https://marketplace.visualstudio.com/items/ms-dynamics-smb.al/license) |
| [GitHub Issues](https://marketplace.visualstudio.com/items/ms-vscode.github-issues-prs) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.github-issues-prs/license) |
| [IntelliCode](https://marketplace.visualstudio.com/items/VisualStudioExptTeam.vscodeintellicode) |  | [Microsoft](https://marketplace.visualstudio.com/items/VisualStudioExptTeam.vscodeintellicode/license) |
| [Project Manager for Java](https://marketplace.visualstudio.com/items/vscjava.vscode-java-dependency) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-java-dependency/license) |
| [JavaScript Atom Grammar](https://marketplace.visualstudio.com/items/ms-vscode.js-atom-grammar) | ✔ ([Repo](https://github.com/microsoft/vscode-js-atom-grammar)) | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode.js-atom-grammar/license) |
| [JavaScript Debugger](https://marketplace.visualstudio.com/items?itemName=ms-vscode.js-debug) | ✔ ([Repo](https://github.com/microsoft/vscode-js-debug)) | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode.js-debug/license) |
| [Kubernetes](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.vscode-kubernetes-tools) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-kubernetes-tools.vscode-kubernetes-tools/license) |
| [Live Share](https://marketplace.visualstudio.com/items/MS-vsliveshare.vsliveshare) |  | [Microsoft](https://marketplace.visualstudio.com/items/MS-vsliveshare.vsliveshare/license)  |
| [Markdown Theme Kit](https://marketplace.visualstudio.com/items/ms-vscode.Theme-MarkdownKit) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.Theme-MarkdownKit/license) |
| [Maven for Java](https://marketplace.visualstudio.com/items/vscjava.vscode-maven) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-maven/license) |
| [Mezzurite](https://marketplace.visualstudio.com/items/mezzurite-devs.mezzurite) | ✔ | [MIT](https://marketplace.visualstudio.com/items/mezzurite-devs.mezzurite/license) |
| [Mono Debug](https://marketplace.visualstudio.com/items/ms-vscode.mono-debug) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.mono-debug/license) |
| [Notepad++ Keymap](https://marketplace.visualstudio.com/items/ms-vscode.notepadplusplus-keybindings) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.notepadplusplus-keybindings/license) |
| [OpenPAI VS Code Client](https://marketplace.visualstudio.com/items/OpenPAIVSCodeClient.pai-vscode) | ✔ | [MIT](https://marketplace.visualstudio.com/items/OpenPAIVSCodeClient.pai-vscode/license) |
| [PostgreSQL](https://marketplace.visualstudio.com/items/ms-ossdata.vscode-postgresql) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-ossdata.vscode-postgresql/license) |
| [PowerShell](https://marketplace.visualstudio.com/items/ms-vscode.PowerShell) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.PowerShell/license) |
| [Predawn Theme](https://marketplace.visualstudio.com/items/ms-vscode.Theme-PredawnKit) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.Theme-PredawnKit/license) |
| [Pyright](https://marketplace.visualstudio.com/items?itemName=ms-pyright.pyright) | ✔ | [MIT](https://raw.githubusercontent.com/microsoft/pyright/master/LICENSE.txt) |
| [Python](https://marketplace.visualstudio.com/items/ms-python.python) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-python.python/license) |
| [React Native Tools](https://marketplace.visualstudio.com/items/msjsdiag.vscode-react-native) | ✔ ([Repo](https://github.com/microsoft/vscode-react-native)) | [Microsoft](https://marketplace.visualstudio.com/items/msjsdiag.vscode-react-native/license) |
| [Dev Containers](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-containers) |  | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-containers/license)  |
| [Remote - SSH](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-ssh) |  | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-ssh/license) |
| [WSL](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-wsl) |  | [Microsoft](https://marketplace.visualstudio.com/items/ms-vscode-remote.remote-wsl/license)  |
| [Reference Search View](https://marketplace.visualstudio.com/items/ms-vscode.references-view) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.references-view/license) |
| [Resharper 9 keyboard shortcuts](https://marketplace.visualstudio.com/items/ms-vscode.resharper9-keybindings) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.resharper9-keybindings/license)|
| [Service Fabric Reliable Services](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-service-fabric-reliable-services) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-azuretools.vscode-service-fabric-reliable-services/license) |
| [Spring Boot Dashboard](https://marketplace.visualstudio.com/items/vscjava.vscode-spring-boot-dashboard) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-spring-boot-dashboard/license) |
| [Spark & Hive Tools](https://marketplace.visualstudio.com/items/mshdinsight.azure-hdinsight) |  | [Microsoft](https://marketplace.visualstudio.com/items/mshdinsight.azure-hdinsight/license) |
| [Spring Initializr Java Support](https://marketplace.visualstudio.com/items/vscjava.vscode-spring-initializr) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-spring-initializr/license) |
| [SQL Server](https://marketplace.visualstudio.com/items/ms-mssql.mssql) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-mssql.mssql/license) |
| [Sublime Text Keymap](https://marketplace.visualstudio.com/items/ms-vscode.sublime-keybindings) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.sublime-keybindings/license) |
| [Test Runner for Java](https://marketplace.visualstudio.com/items/vscjava.vscode-java-test) | ✔ | [MIT](https://marketplace.visualstudio.com/items/vscjava.vscode-java-test/license) |
| [Tomorrow and Tomorrow Night Theme](https://marketplace.visualstudio.com/items/ms-vscode.Theme-TomorrowKit) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.Theme-TomorrowKit/license) |
| [TSLint](https://marketplace.visualstudio.com/items/ms-vscode.vscode-typescript-tslint-plugin) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.vscode-typescript-tslint-plugin/license) |
| [TypeScript and Javascript Nightly](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.vscode-typescript-next/license) |
| [Visual Studio Keymap](https://marketplace.visualstudio.com/items/ms-vscode.vs-keybindings) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.vs-keybindings/license) |
| [Word Count](https://marketplace.visualstudio.com/items/ms-vscode.wordcount) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.wordcount/license) |
| [1337 Theme](https://marketplace.visualstudio.com/items/ms-vscode.Theme-1337) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.Theme-1337/license) |
| [3024 Theme](https://marketplace.visualstudio.com/items/ms-vscode.Theme-3024Kit) | ✔ | [MIT](https://marketplace.visualstudio.com/items/ms-vscode.Theme-3024Kit/license) |
