# September 2020 (version 1.50)

## Visual Studio Code Remote Core

### Notification for automatically forwarded ports

Detecting ports for automatic forwarding was added in 1.49, but now there is a notification when a port is automatically forwarded so that you can notice when your remote port is accessible locally.

![Port Forwarded Notification](images/1_50/port-forwarded-notification.png)

### Install extensions without reloading

It is now possible to install/enable extensions when VS Code is connected to a remote. If an extension is not executing (i.e. its code is not executing), it can also be uninstalled/disabled without reloading VS Code.

## Containers (version 0.145.x)

### Improved log access and rendering

When you open a folder locally after attempting to open it in a container failed, the new command **Dev Containers: Show Previous Log** opens the log from the failed attempt to open in a container.

The command **Dev Containers Developer: Show All Logs...** now shows one file per startup (instead of one file per VS Code session, potentially spanning several startups) making it easier to navigate:

![Dev Containers Developer: Show All Logs...](images/1_50/containers-show-all-logs.png)

The current log shown in the DevContainer terminal with the **Dev Containers: Show Container Log** command now comes with coloring:

![Log Coloring](images/1_50/containers-log-coloring.png)

### DevContainer development actions in Remote-Explorer

The Remote-Explorer viewlet's Details section now comes with the `Open Container Configuration File` and `Rebuild Container` actions which previously were only available in the command palette:

![Details Section](images/1_50/containers-details-section.png)

### Clone repository in container volume: better recovery support

When opening a repository in a container volume using `Clone Repository in Container Volume` fails, we now show the creation log of the container together with the `devcontainer.json` in the recovery container. This is now the same experience as when `Open Folder in Container` fails.

### Notable fixes

- [3648](https://github.com/microsoft/vscode-remote-release/issues/3648): `Pass "remoteEnv" variables to "postCreate/Start/AttachCommand"s and dotfiles script.`

## WSL

### Inherit environment from process where `code .` is executed

The WSL window now inherits environment from the WSL shell where `code .` is executed.

![Inherit Environment](images/1_50/wsl-inherit-env.png)
