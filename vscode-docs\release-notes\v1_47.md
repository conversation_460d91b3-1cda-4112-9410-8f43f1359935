---
Order:
TOCTitle: June 2020
PageTitle: Visual Studio Code June 2020
MetaDescription: Learn what is new in the Visual Studio Code June 2020 Release (1.47)
MetaSocialImage: 1_47/release-highlights.png
Date: 2020-7-9
DownloadVersion: 1.47.3
---
# June 2020 (version 1.47)

**Update 1.47.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2020+Recovery+1%22+is%3Aclosed).

**Update 1.47.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2020+Recovery+2%22+is%3Aclosed).

**Update 1.47.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22June+2020+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the June 2020 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Accessibility improvements](#accessibility)** - New screen reader capabilities.
* **[Windows ARM builds](#stable-windows-arm-builds)** - Windows on ARM is now available in stable.
* **[New JavaScript debugger](#new-javascript-debugger)** - Debug in the terminal, profiling support.
* **[Source Control unified view](#single-view)** - All repositories displayed in a single view.
* **[View and sort pending changes](#view-and-sort)** - View files as a tree or list, sort by name, path, or status.
* **[Edit complex settings](#edit-object-settings-from-the-settings-editor)** - Edit object settings from the Settings editor.
* **[New HexEditor extension](#hex-editor)** - Edit files in hexadecimal format from within VS Code.
* **[Notebook UI updates](#notebooks)** - Improved UX and undo/redo support.
* **[Remote Development tutorials](#remote-development)** - Learn to develop over SSH, inside Containers, and in WSL.
* **[Java Pack Installer for macOS](#java-pack-installer-for-macos)** - Get set up to develop in Java with VS Code on macOS.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/events) on Monday, July 13 at 9am Pacific (5pm London), to see a demo of what's new in this release and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Accessibility

This milestone, we again received helpful feedback from our community, which helped us identify and tackle many [accessibility issues](https://github.com/microsoft/vscode/issues?q=label%3Aaccessibility+milestone%3A%22June+2020%22+is%3Aclosed). Highlights:

* Compact folders in the File Explorer now properly narrate expanded/collapsed state and the ARIA level.
* Screen readers can now update the cursor offset in the editor. As a result, the screen reader "Say All" command should work better when stopped and resumed.
* Same ARIA live messages will now properly be re-read by the screen reader.

## Workbench

### Edit object settings from the Settings editor

Before, the Settings editor could only be used to edit the settings of primitive types, like strings and booleans, and you needed to edit `settings.json` directly for more complicated settings types. Now, you can edit non-nested object settings from the Settings editor. Extension authors can use this functionality to increase the visibility of these kinds of settings.

**Before**

In the Settings editor:

![Object setting in the old Settings editor](images/1_47/object-settings-editor-before.jpg)

And in `settings.json`:

![Object setting in the JSON editor](images/1_47/object-settings-json-before.jpg)

**After**

In the Settings editor:

![Object setting in the new Settings editor](images/1_47/object-settings-editor-after.jpg)

### Select and keep focus in a list view

There is a new command, `list.selectAndPreserveFocus`, which lets you select an item from a list, while keeping focus in that list. This can be helpful if you want to select multiple files from a list, such as the File Explorer, without having focus go to the file editor.

The command is not bound to any keyboard shortcut by default, but you can add your own keybinding:

```json
{
  "key": "ctrl+o",
    "command": "list.selectAndPreserveFocus"
}
```

### Stable Windows ARM builds

VS Code for Windows on ARM is now [available](https://aka.ms/win32-arm64-user) for the stable release! 🎉

### Install VSIX through drag and drop

VS Code now supports installing an extension VSIX file through drag and drop onto the Extensions view.

### New Search editor command arguments

There are two new arguments added to the Search editor commands (`search.action.openNewEditor`, `search.action.openNewEditorToSide`) to allow keybindings to configure how a new Search editor should behave:

* `triggerSearch` - Whether a search be automatically run when a Search editor is opened. Default is true.
* `focusResults` - Whether to put focus in the results of a search or the query input. Default is true.

For example, the following keybinding runs the search when the Search editor is opened but leaves the focus in the search query control.

```json
{
    "key": "ctrl+o",
    "command": "search.action.openNewEditor",
    "args": { "query": "VS Code", "triggerSearch":true, "focusResults": false }
}
```

### New Search editor context default

The `search.searchEditor.defaultNumberOfContextLines` setting has been updated to have a default value of 1 instead of 0, meaning one context line will be shown before and after each result line in the Search editor. To go back to the old behavior, set the value back to 0.

### List/Tree: Dynamic horizontal scrolling

The previously existing `workbench.list.horizontalScrolling` setting can now be toggled at runtime without forcing you to reload the workbench.

## Editor

### Case changing in regex replace

VS Code now supports changing the case of regex matching groups while doing a find/replace in the editor. This is done with the modifiers `\u\U\l\L`, where `\u` and `\l` will upper/lowercase a single character, and `\U` and `\L` will upper/lowercase the rest of the matching group.

Example:

![Changing case while doing find and replace](images/1_47/case-change-replace.gif)

The modifiers can also be stacked - for example, `\u\u\u$1` will uppercase the first three characters of the group, or `\l\U$1` will lowercase the first character, and uppercase the rest.

Currently, these are only supported in the editor's Find control, and not in global **Find in Files**.

## Debugging

### New JavaScript Debugger

Our new JavaScript debugger, after being the default debugger on Insiders last month, is now the default debugger for JavaScript (Node.js and Chrome) in VS Code. If you skipped the "Preview Features" section of the last few VS Code changelogs, you can catch up on the [what's new section](https://github.com/microsoft/vscode-js-debug#whats-new) of the debugger README.

You should not need to change any settings or launch configurations to take advantage of the new debugger. If you run into any problems, please [open an issue](https://github.com/microsoft/vscode-js-debug/issues/new/choose)!

### Single file debugging

Until today, the VS Code debugger had no standard way of showing that a file in the editor could be easily debugged with just a click of a button. Some debug extensions would allow you to do so, usually with a debug configuration that prompted you to "Debug file in editor." However, users still had to select the correct configuration in the debug configuration dropdown menu before they can use **F5**. Other debug extensions implement a fallback strategy for **F5**: if no `launch.json` exists, **F5** will try to debug the file currently open in the active editor.

Since both approaches are not easily discoverable, some debug extensions (for example, Python) have started to add a **Run** button to the editor's title area.

Since we haven't found a better approach, and this method can be implemented without any need for new APIs, we wrote some guidelines for how to implement it in a standard way. Extension authors can find these guidelines below in the "Extension Authoring" section.

Users need only to remember these icons:

![Run and debug action in editor title](images/1_47/run-and-debug-actions.png)

If one or both show up on the left-hand side of the editor's title area, then running or debugging the file in the editor is just one click away.

### Less cluttered CALL STACK view

We've started to make the CALL STACK less crowded for common cases: the CALL STACK view now supports hiding debug session nodes that exist for technical reasons, but do not provide much value to users.

The first debug extension that has opted into this feature is the new JavaScript debugger, which could eliminate a parent debug session whenever there is only a single child session.

![Screenshot of two "Call Stack" views. Without compaction, there is an extra child session between the parent session and each attached worker processes.](images/1_47/call-stack-compaction.png)

We hope that other debug extensions will follow. Please see the new proposed API below.

### New command alias **Set Next Statement** for **Jump to Cursor**

To make the command **Jump to Cursor** more discoverable for users coming from Visual Studio, we've added the command alias **Set Next Statement**.

If you don't know what **Jump to Cursor** does: it lets you move program execution to a new location without executing any of the source code in between.

### Breakpoint Path on Hover

When hovering over a source breakpoint in the BREAKPOINTS view, VS Code now shows the absolute path of the breakpoint.

## Tasks

### pnpm package manager support

[pnpm](https://pnpm.js.org) is now a valid choice for the `npm.packageManager` setting, along with `npm` and `yarn`, to run your scripts.

## Source Control

### Single view

The Source Control view has been consolidated into a single view:

![Source Control with a single view](images/1_47/scm.png)

All repositories are rendered in a single view, so you can get a better overview of the entire workspace status. Additionally, the Source Control view can now be moved to the panel and other views can be moved to the Source Control view container.

### View and Sort

We have added support for sorting your changes in the source control view by name, path (default), and state when using the list view option. We have consolidated the view options (list vs. tree) and sort options into a new **View & Sort** menu item in the context menu.

![View & Sort in Source Control](images/1_47/scm-view&sort.png)

### Git: Restore squash message

Similar to usual `git merge` command, the SCM view will now restore the SCM input with the default message if the user is in the middle of a `git merge --squash` command.

## Languages

### TypeScript 3.9.6

VS Code now bundles TypeScript 3.9.6. This minor update fixes a few bugs, including one that could cause the TypeScript server to crash on certain source code patterns.

## Browser support

### Large file upload support

You can now upload large files and folders to the web version of VS Code and progress will be reported accurately so that you can track the number of bytes that have been uploaded, as well as the upload speed.

![Web upload indicator in bottom Status bar](images/1_47/web-upload.gif)

### Towards text file encoding support

During this milestone, a lot of work went into full support for text encodings in the browser for reading and writing files. We rely on two libraries that are now supported in browsers by leveraging webpack:

* [`iconv-lite`](https://github.com/ashtuchkin/iconv-lite) to read and write encodings
* [`jschardet`](https://github.com/aadsm/jschardet): to guess encodings from textual content

This work will continue in July and should be generally available soon.

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### Settings Sync

We have been working the last couple of months to support synchronizing VS Code preferences across machines and this feature is available for preview on the [Insiders](https://code.visualstudio.com/insiders) release.

You can now disable sync on other machine using **Turn off Preferences Sync** context menu action on the machine entry in **Synced Machines** view.

![Turn off preferences sync for a machine](images/1_47/settings-sync-turn-off-machine.png)

We've also improved progress information when turning on sync.

### TypeScript 4.0 support

This iteration, we've continued improving our support for [TypeScript 4.0](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-beta/). Some highlights include:

* **Highlight calls to deprecated symbols in the editor with strikethrough**

  ![Call to a deprecated function rendered in the editor](images/1_47/ts-deprecated.png)

* **Explain reasons why a given refactoring cannot be applied**

  ![Displaying the reason a refactoring cannot be applied](images/1_47/ts-refactor-reason.png)

* **Improved auto imports** - Read more in the [TypeScript 4.0 blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-beta/#smarter-auto-imports).

You can try out these features today by installing the [TypeScript nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

### Partial IntelliSense support while large JavaScript or TypeScript projects are loading

The TypeScript 4.0 beta also brings a new feature that should help reduce the time you spend waiting while a large JavaScript or TypeScript project is being loading. To explain how this works, we first need to cover a little background on how current versions of TypeScript work.

When you open a JavaScript or TypeScript file in VS Code, the TypeScript language service determines which project that file belongs to and then loads the associated project for it. Sometimes this project can be quite large, such as with the core VS Code codebase, which consists of many thousands of TypeScript files. While a project is being loaded, VS Code able to handle basic syntax operations, such as getting the document outline and enabling code folding, but it cannot currently provide IntelliSense or other more advanced language features because they depend on understanding the entire project. In order to provide auto imports, for example, the TypeScript language service needs to know all exported symbols in a project.

The changes in TypeScript 4.0 make it possible for VS Code provide IntelliSense and other more advanced language features while a large project is still loading. The caveat is that the IntelliSense we can provide is limited to consulting the current file instead of the entire project. This means that features such as suggestions and **Go to Definition** will work, but we will only show suggestions from the current file and you can only run **Go to Definition** to jump to another symbol in the current file.

If you work with large JavaScript and TypeScript projects, we are interested in hearing your feedback on this new feature. You can try it today using the  [TypeScript nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Our hope is that it allows you to start work with your code almost instantly until the more complete, project-wide IntelliSense becomes available.

### Undo/Redo for Code Action

We are working on being able to undo and redo Code Actions and refactorings. A prominent example is the Java class rename refactoring, which makes a textual change as well as renaming the file on disk. This can now be undone but it still requires triggering undo two times. Stay tuned as we further improve this and please provide early feedback.

## Contributions to extensions

### Notebooks

The VS Code team is continuing on native support for Notebooks. To help with development, we've created a [GitHub Issue Notebooks](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-github-issue-notebooks) extension, which lets you search GitHub issues and pull requests. This extension is still in preview and requires using VS Code [Insiders](https://code.visualstudio.com/insiders), but it lets experience Notebooks first hand and we welcome your feedback.

**Updated UX**

We've updated the visual look of Notebook cells for a more compact design. We've also added shadow outlines to selected cells to make the selection state more obvious.

![Updated Notebook UX](images/1_47/notebook-ux.gif)

**Notebook Hot Exit support**

We have added Hot Exit support in Notebooks to allow extensions to handle backups and restoration. Your unsaved local changes can now be serialized by extensions and restored when the workspace is reopened.

**Undo/Redo enhancement**

We added support for extensions to contribute to the Undo/Redo stacks. Extensions can now control what operations are undoable. For example, in a GitHub Issue Notebook, you can select the **Lock** button to make the cell content readonly, and also undo/redo this operation through the normal Undo/Redo commands.

![Undo/Redo in GitHub Issue Notebook](images/1_47/notebook-undo-redo.gif)

*Theme: [GitHub Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

**Create Untitled Notebook**

We extended the **New File** command (`kb(workbench.action.files.newUntitledFile)`) to support creating new untitled Notebook files. For example, you can use the keybinding below to quickly create a GitHub Issue Notebook and run queries.

```json
{
    "key": "cmd+i",
    "command": "workbench.action.files.newUntitledFile",
    "args": { "viewType": "github-issues" }
}
```

![Create Untitled GitHub Issue Notebook](images/1_47/notebook-create-untitled.gif)

*Theme: [GitHub Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

**Notebook color tokens**

We've also added the following color tokens for Notebooks:

* `notebook.cellBorderColor`: The border color for Notebook cells
* `notebook.cellHoverBackground`: The background color of a cell when the cell is hovered
* `notebook.cellInsertionIndicator`: The color of the Notebook cell insertion indicator
* `notebook.focusedCellBackground`: The background color of a cell when the cell is focused
* `notebook.focusedCellBorder`: The color of the cell's top and bottom border when the cell is focused
* `notebook.focusedCellShadow`: The color of the cell shadow when cells are focused
* `notebook.focusedEditorBorder`: The color of the Notebook cell editor border

**Two new samples show how to support debugging in Notebooks**

* [Debugging in a JavaScript Notebook](https://github.com/microsoft/vscode-nodebook/blob/main/README.md) sample

  This sample shows how to implement Notebook debugging functionality based on the existing VS Code debugger extensions.

  ![Running and evaluating JavaScript Notebook cells](images/1_47/debugging-in-nodebook.gif)

* [Jupyter Notebook Debugging with the Xeus Kernel](https://github.com/microsoft/vscode-simple-jupyter-notebook/blob/main/README.md) sample

  This sample shows how to implement Notebook debugging functionality based on the xeus kernel and its native support for the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol/).

  ![Running and evaluating Jupyter Notebook cells](images/1_47/debugging-cells.gif)

### Notebook extension guide

If you're interested in creating Notebook extensions for VS Code, there is a new guide detailing the [Notebook API](https://code.visualstudio.com/api/extension-guides/notebook). You'll learn about the API as well as the details of the VS Code Notebook infrastructure and Notebook extension development best practices.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.47 include:

* Remote - SSH: Remote server can listen on a socket instead of a port.
* Dev Containers: Prompt to open repository in a volume.
* Dev Containers and WSL: Check out the recent [Using Dev Containers in WSL 2](https://code.visualstudio.com/blogs/2020/07/01/containers-wsl) blog post.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_47.md).

If you are new to VS Code Remote Development, you can get started with these introductory tutorials:

* [Working over SSH](https://code.visualstudio.com/docs/remote/ssh-tutorial)
* [Developing inside Dev Containers](https://code.visualstudio.com/docs/devcontainers/tutorial)
* [Using Windows Subsystem for Linux](https://code.visualstudio.com/docs/remote/wsl-tutorial)

### Azure Account

The newest version of the Azure Account extension now exposes a credentials object that can be used with the latest [Azure SDK](https://github.com/azure/azure-sdk-for-js).

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Some updates for this release include:

* Markdown hovers in the Issues view.
* Label suggestions in the new issue editor.
* Hover support for issues formatted as GH-123.

To learn about all the new features and updates, you can see the full [changelog for the 0.18.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md) release of the extension.

### Hex Editor

The [HexEditor](https://marketplace.visualstudio.com/items?itemName=ms-vscode.hexeditor) extension continues to be improved upon to provide a native hexadecimal editing experience within VS Code. The major updates to the extension this release include simple editing support (allowing users to undo, redo, edit existing hex cells, and add new cells to the end of the document) and large file optimization allowing users to open files above 18 megabytes, which previously wasn't possible.

![Editing in the HexEditor extension](images/1_47/hex-editor-editing.gif)

A full list of notable changes can be found in the [CHANGELOG](https://github.com/microsoft/vscode-hexeditor/blob/main/CHANGELOG.md). Any feedback or issues experienced can be filed against the [vscode-hexeditor repository](https://github.com/microsoft/vscode-hexeditor).

## Extension authoring

### AccessibilityInformation

We have finalized the `AccessibilityInformation` in the `TreeItem`, `StatusBarItem`, and `TimelineItem` elements to support screen readers. `AccessibilityInformation` contains a `label` and a `role`. The `label` is read out by a screen reader once the item has focus. `role` of the item defines how a screen reader interacts with it. The `role` should be set in special cases when, for example, a tree-like element behaves like a checkbox. If `role` is not specified, VS Code will pick the appropriate `role` automatically.

### Fallback message for "go to location"

The `editor.action.goToLocations` command can now show a fallback message when no locations could be found. This allows extensions to implement features like **Go to Super Type** that behave just like built-in features, for example **Go to Definition**. The snippet below is a full sample of an extension invoking this command:

```ts
vscode.commands.executeCommand(
    'editor.action.goToLocations',
    vscode.window.activeTextEditor.document.uri, //anchor uri and position
    vscode.window.activeTextEditor.selection.start,
    [], // results (vscode.Location[])
    'goto', // mode
    'No Super Types Found' // <- message
);
```

### Guidelines for improving the single file debug experience

For debug extensions that want to improve the single file debug experience by adding a "Run" and/or "Debug" button to the editor, we recommend following these guidelines for a consistent look and feel:

* Contribute Run and/or Debug commands in the package.json (see [Mock Debug](https://github.com/microsoft/vscode-mock-debug/blob/dc3ccfeb2195a1d2e5876f40fc9436b3b6f58a40/package.json#L82-L93)):
  * Use the command titles "Run File"/"Debug File" or "Run Python File"/"Debug Python File".
  * Use the `$(play)` icon for Run and `$(debug-alt-small)` for Debug.
* Add the commands to the editor title area ([see Mock Debug](https://github.com/microsoft/vscode-mock-debug/blob/dc3ccfeb2195a1d2e5876f40fc9436b3b6f58a40/package.json#L68-L81)):
  * To make the commands only visible for specific languages use a "when" clause, for example, `"resourceLangId == python"`.
  * Put the Run command in the `1_run@10` group and the Debug command in the `1_run@20` group.

### CodeActionProviderMetadata.documentation

The new `documentation` property on `CodeActionProviderMetadata` lets extension provide static documentation for the code actions they return. This documentation is shown at the bottom of the list of code actions when the provide returns a code action:

![A documentation entry in the Code Actions menu](images/1_47/ts-refactor-docs.png)

A command is executed when the user selects the documentation entry. This command can show the documentation in the editor or open it in a browser.

### Open and Save dialog title

The API for providing a `title` for Open and Save file dialogs via the `OpenDialogOptions` and `SaveDialogOptions` has been finalized. Note that not all operating systems allow this title to be set, so the behavior may differ depending on where your extension is running.

### New extension categories

You can now use following newly added categories to categorize your extensions.

* Data Science
* Machine Learning
* Visualization
* Testing
* Notebooks

### Secondary button style

We've introduced a secondary button style to be used when needing to make a button less prominent:

* `button.secondaryForeground`
* `button.secondaryBackground`
* `button.secondaryHoverBackground`

### GitHub Workflow badges

You can now add [GitHub Workflow badges](https://docs.github.com/actions/monitoring-and-troubleshooting-workflows/adding-a-workflow-status-badge) to the list of badges of a published extension, since `github.com` was added to the [list of Approved Badge sources](https://code.visualstudio.com/api/references/extension-manifest#approved-badges).

![GitHub Workflow badge](images/1_47/github-badge.png)

### Enabling Proposed API through argv.json

The field `enable-proposed-api` is now supported in `argv.json`. This allows extension authors to run their proposed API extension in released builds for long-term dogfooding, without needing to open VS Code via the command line and pass the `--enable-proposed-api` CLI flag.

### New codicon icons

We've added the following new icons to our [codicon](https://code.visualstudio.com/api/references/icons-in-labels) library:

* `debug-alt-small`
* `vm-connect`

![Codicons icons](images/1_47/codicons-icons.png)

## Language Server Protocol

Support for [semantic tokens](https://microsoft.github.io/language-server-protocol/specifications/specification-3-16/#textDocument_semanticTokens) and lazy resolving of additional text edits in [completion items](https://microsoft.github.io/language-server-protocol/specifications/specification-3-16/#textDocument_completion) has been added to the upcoming [3.16](https://microsoft.github.io/language-server-protocol/specifications/specification-3-16) specification.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Terminal link provider

Building on top of [the terminal's new link system added in the previous release](https://code.visualstudio.com/updates/v1_46#_improved-link-support), this new proposal `window.registerLinkProvider` allows an extension to attach links to the terminal, including a tooltip label that is displayed on hover.

![Terminal Link Provider](images/1_47/terminal-link-provider.png)

This contrasts with the previously proposed (now deprecated) ["link handler" API,](https://code.visualstudio.com/updates/v1_44#_terminal-link-handlers) which would allow your extension to possibly handle the link in question, and if it doesn't, there would be a fallback chain. This new model where links will definitely be handled fits in better with the vision in mind where links could have a list of possible actions.

### New API for Run without Debugging

VS Code's "Run without Debugging" feature is a variant of debugging where an existing debug configuration is reused for running a program instead of debugging it. A consequence of this run mode is that the program does not break into the debugger, neither for hitting breakpoints nor for any other reason.

In this milestone we provide official extension API for "Run without Debugging": a new property `noDebug` has been added to the `DebugSessionOptions` of the `debug.startDebugging` function. `noDebug` controls whether the session about to start should run with or without debugging. When the `noDebug` property is missing, the value from the parent session (if there is one) is used. If there is no parent session, the value 'false' is assumed for a missing `noDebug` property.

Please note: Previously it was possible to add a `noDebug` flag to a launch configuration to achieve the same effect. This unofficial API is now deprecated (but still supported) and we suggest that extensions transition to the new API.

### New API for reducing clutter in the CALLSTACK view

With the arrival of sophisticated debug extensions and debug setups, more debug sessions show up in the CALL STACK tree view and make them look crowded.
In most cases the debug sessions are useful to users and they need to have full control over them. In other cases some debug sessions are just implementation artifacts or a grouping mechanism for a set of child sessions.

In order to make the CALL STACK view less crowed in common cases, we have introduced a feature to hide a parent debug session node if it only has a single child session.
This feature is called "compact mode" and is available through a new optional `compact` property on the `DebugSessionOptions` of the `debug.startDebugging` function.

If `compact` is true, the parent of the newly created debug session will be hidden in the CALL STACK view as long as it only has a single child.
If `compact` is false or missing, the parent node stays visible (which is the current behavior).

### Markdown hovers in trees

There is new proposed API that allows the `tooltip` of a `TreeItem` to be a `MarkdownString`. In addition to allowing Markdown for the tooltip, there is also a new `resolveTreeItem` on the `TreeDataProvider`. The idea behind `resolveTreeItem` is that properties that might take longer to calculate (currently only the `tooltip`) can be resolved later, instead of calculated up front for every tree item. You can see `resolveTreeItem` and the Markdown `tooltip` in use in the [GitHub Pull Requests and Issues extension](https://github.com/microsoft/vscode-pull-request-github/commit/c94ce87cc2f66d372d891ab19052b54f31ae6146#diff-ec21c136b5245a574a4d0925bf536fef).

## Engineering

### Using TypeScript 4.0 to build VS Code

VS Code is now built using a [nightly build](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next) of TypeScript 4.0.

This new TypeScript version helped us catch a few [potential bugs around accessors](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0-beta/#breaking-changes) and also helps us test the upcoming TypeScript release.

## Documentation and extensions

### CMake Tools for C++

New tutorial for [developing C++ applications with CMake](https://code.visualstudio.com/docs/cpp/cmake-linux) and the [CMake Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.cmake-tools) extension.

![CMake Tools extension](images/1_47/cmake-extension.png)

### Java Pack installer for macOS

The [Visual Studio Code for Java Pack Installer](https://aka.ms/vscode-java-installer-mac) downloads the necessary dependencies and extensions for Java development in Visual Studio Code on macOS.

![Java Pack installer for macOS](images/1_47/java-installer-macos.png)

## Notable fixes

* [71291](https://github.com/microsoft/vscode/issues/71291): Horizontal scroll breaks rendering in tree
* [93230](https://github.com/microsoft/vscode/issues/93230): Local variables are collapsed in annoying ways
* [98309](https://github.com/microsoft/vscode/issues/98309): Support multiple files in working copy file service
* [99061](https://github.com/microsoft/vscode/issues/99061): Allow to reopen closed diff editors
* [99290](https://github.com/microsoft/vscode/issues/99290): Git: Restore diff editors on reload
* [99704](https://github.com/microsoft/vscode/issues/99704): Top debug session row says "paused" when not paused
* [99786](https://github.com/microsoft/vscode/issues/99786): Unable to start additional session when Debug Tool Bar Location docked
* [100524](https://github.com/microsoft/vscode/issues/100524): SCM Decorations missing
* [101132](https://github.com/microsoft/vscode/issues/101132): Release notes can never be opened after you try to open them when offline

## Thank you

Last but certainly not least, a big *__Thank You__* to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

* [AlexStrNik (@AlexStrNik)](https://github.com/AlexStrNik): Fix #97526 [PR #97611](https://github.com/microsoft/vscode/pull/97611)
* [Anoesj Sadraee (@Anoesj)](https://github.com/Anoesj): Add "pnpm" option to "npm.packageManager" setting [PR #100654](https://github.com/microsoft/vscode/pull/100654)
* [@champignoom](https://github.com/champignoom): stage ibus-gtk3 to try to make ibus work [PR #100480](https://github.com/microsoft/vscode/pull/100480)
* [Daniel Davis (@daniel-j-davis)](https://github.com/daniel-j-davis)
  * Adding .git/SQUASH_MSG detection to commit message auto-fill #101078 [PR #101114](https://github.com/microsoft/vscode/pull/101114)
  * Allow pasting in debug window #100235 [PR #100288](https://github.com/microsoft/vscode/pull/100288)
* [Daybrush (Younkue Choi) (@daybrush)](https://github.com/daybrush): Support `metaKey` for iOS [PR #100869](https://github.com/microsoft/vscode/pull/100869)
* [Don Jayamanne (@DonJayamanne)](https://github.com/DonJayamanne): Disable auto saving of untitled notebooks [PR #100256](https://github.com/microsoft/vscode/pull/100256)
* [Dan Foad (@foad)](https://github.com/foad): Allow numeric CLI argument [PR #99540](https://github.com/microsoft/vscode/pull/99540)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
  * fix #99854 make 'Edit in settings.json' add object-type default [PR #99912](https://github.com/microsoft/vscode/pull/99912)
  * Fix #71295 Permit command URIs in markdownDescription of extension setting [PR #100304](https://github.com/microsoft/vscode/pull/100304)
  * fix #100068 Better 'Read More/Less' tips on suggest widget controls [PR #100070](https://github.com/microsoft/vscode/pull/100070)
* [Fedor Nezhivoi (@gyzerok)](https://github.com/gyzerok)
  * update iconv-lite-umd to 0.6.4 for #79275 [PR #101213](https://github.com/microsoft/vscode/pull/101213)
  * switch to use iconv-lite-umd for #79275 [PR #100472](https://github.com/microsoft/vscode/pull/100472)
  * decouple vs/base/node/encoding.ts from node streams for #79275 [PR #99413](https://github.com/microsoft/vscode/pull/99413)
  * Move encoding to common for #79275 [PR #100539](https://github.com/microsoft/vscode/pull/100539)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
  * Allow table specific tags for markdown in hovers (fixes #99983) [PR #99988](https://github.com/microsoft/vscode/pull/99988)
  * Step over surrogate pairs on zero-lenth matches (fixes #100134) [PR #100482](https://github.com/microsoft/vscode/pull/100482)
* [谭九鼎 (@imba-tjd)](https://github.com/imba-tjd): Use https for stackoverflow [PR #100655](https://github.com/microsoft/vscode/pull/100655)
* [Ken (@irridia)](https://github.com/irridia): Initial implementation: Support \U\u\L\l replace modifiers [PR #96128](https://github.com/microsoft/vscode/pull/96128)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fixes Error Cannot read property 'id' of undefined when dragging view to Panel with no pinned views [PR #99582](https://github.com/microsoft/vscode/pull/99582)
  * Fixes terminal Env Variables Icon Not Updating Position [PR #98015](https://github.com/microsoft/vscode/pull/98015)
  * Better experience for replacing folders in web upload [PR #99601](https://github.com/microsoft/vscode/pull/99601)
  * Fix pinned tabs do not preserve when joining editor groups [PR #100344](https://github.com/microsoft/vscode/pull/100344)
  * Fixes dragging activity icon above hamburger menu drops it to the end [PR #98050](https://github.com/microsoft/vscode/pull/98050)
  * Fixes problems filter focus outline is cut off in pane header [PR #97438](https://github.com/microsoft/vscode/pull/97438)
  * Fixes focused status bar entry should use orange border when using the high contrast theme [PR #99453](https://github.com/microsoft/vscode/pull/99453)
* [Justin Hutchings (@jhutchings1)](https://github.com/jhutchings1): Add CodeQL security scanning [PR #99411](https://github.com/microsoft/vscode/pull/99411)
* [Keshav Bohra (@keshav-bohr)](https://github.com/keshav-bohr): Word fixes [PR #100301](https://github.com/microsoft/vscode/pull/100301)
* [Wenlu Wang (@Kingwl)](https://github.com/Kingwl): Add ts/js deprecated support [PR #97742](https://github.com/microsoft/vscode/pull/97742)
* [Rhitik Bhatt (@lambainsaan)](https://github.com/lambainsaan)
  * Adds basic keybinding for focusing on an element in outline pane [PR #91799](https://github.com/microsoft/vscode/pull/91799)
  * Fixes buggy onEnter behavior when used with generator method [PR #100076](https://github.com/microsoft/vscode/pull/100076)
* [Mads Kristensen (@madskristensen)](https://github.com/madskristensen)
  * Use HTTPS urls for SchemaStore.org [PR #101047](https://github.com/microsoft/vscode/pull/101047)
  * Fix SchemaStore.org URLs [PR #101046](https://github.com/microsoft/vscode/pull/101046)
* [Milo Moisson (@MrNossiom)](https://github.com/MrNossiom): Update logging [PR #100657](https://github.com/microsoft/vscode/pull/100657)
* [@nlchar](https://github.com/nlchar): fixed state restoring on feedback window reopen [PR #101074](https://github.com/microsoft/vscode/pull/101074)
* [Noelle Caldwell (@noellelc)](https://github.com/noellelc): adding rich navigation workflow [PR #100014](https://github.com/microsoft/vscode/pull/100014)
* [Pascal Fong Kye (@pfongkye)](https://github.com/pfongkye): File operation events support multiple resources [PR #98988](https://github.com/microsoft/vscode/pull/98988)
* [Robert Massaioli (@robertmassaioli)](https://github.com/robertmassaioli): Update for-in loop to work with eslint:recommended [PR #99721](https://github.com/microsoft/vscode/pull/99721)
* [Stoyan Nikolov (@stoyannk)](https://github.com/stoyannk): Removed a redundand 'resolve' in SimpleFileDialog by reusing the alre… [PR #99432](https://github.com/microsoft/vscode/pull/99432)
* [Arman Tabaddor (@tabaddor)](https://github.com/tabaddor): Added terminal name when focusing on textarea [PR #100087](https://github.com/microsoft/vscode/pull/100087)
* [Thibault Malbranche (@Titozzz)](https://github.com/Titozzz): fix(search): Replace crash empty string pattern [PR #101017](https://github.com/microsoft/vscode/pull/101017)
* [@VoidNoire](https://github.com/VoidNoire): Make POSIX-compliant to increase portability and remove `bash` depend… [PR #100145](https://github.com/microsoft/vscode/pull/100145)

Contributions to `vscode-extension-samples`:

* [Matt Petty (@lodestone)](https://github.com/lodestone): Improve comments [PR #317](https://github.com/microsoft/vscode-extension-samples/pull/317)

Contributions to `vscode-eslint`:

* [Jimmy Yuen Ho Wong (@wyuenho)](https://github.com/wyuenho): fix typo [PR #989](https://github.com/microsoft/vscode-eslint/pull/989)

Contributions to `debug-adapter-protocol`:

* [Lukas Zima (@zimlu02)](https://github.com/zimlu02): update on Broadcom extension [PR #121](https://github.com/microsoft/debug-adapter-protocol/pull/121)
* [Dave Holoway (@adelphes)](https://github.com/adelphes): Add Android debug adapter [PR #125](https://github.com/microsoft/debug-adapter-protocol/pull/125)

Contributions to `language-server-protocol`:

* [Sacha Ayoun (@giltho)](https://github.com/giltho): Specify logTrace notification [PR #953](https://github.com/microsoft/language-server-protocol/pull/953)
* [Josh Soref (@jsoref)](https://github.com/jsoref): Spelling and grammar [PR #1027](https://github.com/microsoft/language-server-protocol/pull/1027)
* [Radek Simko (@radeksimko)](https://github.com/radeksimko): website: fix typo (Genernal -> General) [PR #1035](https://github.com/microsoft/language-server-protocol/pull/1035)

Contributions to `vscode-languageserver-node`:

* [Sacha Ayoun (@giltho)](https://github.com/giltho): Aligns language server with protocol proposition [PR #611](https://github.com/microsoft/vscode-languageserver-node/pull/611)
* [Andrew Arnott (@AArnott)](https://github.com/AArnott)
  * Expand/restrict types per the JSON-RPC spec [PR #631](https://github.com/microsoft/vscode-languageserver-node/pull/631)
  * Document MessageReader/MessageWriter interfaces [PR #636](https://github.com/microsoft/vscode-languageserver-node/pull/636)

Contributions to `vscode-css-languageservice`:

* [Justin Hutchings (@jhutchings1)](https://github.com/jhutchings1): Add CodeQL security scanning [PR #218](https://github.com/microsoft/vscode-css-languageservice/pull/218)

Contributions to `node-jsonc-parser`:

* [Michael Bullington (@mbullington)](https://github.com/mbullington): Allow for array modifications, add inPlace formatting option. [PR #35](https://github.com/microsoft/node-jsonc-parser/pull/35)

Contributions to `vscode-generator-code`:

* [Pranav Shikarpur (@snpranav)](https://github.com/snpranav): Created Dockerfile for the generator [PR #210](https://github.com/microsoft/vscode-generator-code/pull/210)

Contributions to `vscode-vsce`:

* [Adam S (@ItsMajestiX)](https://github.com/ItsMajestiX): Allow badges from Github Workflows [PR #396](https://github.com/microsoft/vscode-vsce/pull/396)
* [Tomas (@viktomas)](https://github.com/viktomas)
  * Fix issues with missing out directory [PR #462](https://github.com/microsoft/vscode-vsce/pull/462)
  * Update launch.json to be useful for extension debugging [PR #461](https://github.com/microsoft/vscode-vsce/pull/461)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
