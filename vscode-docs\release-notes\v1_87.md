---
Order: 96
TOCTitle: February 2024
PageTitle: Visual Studio Code February 2024
MetaDescription: Learn what is new in the Visual Studio Code February 2024 Release (1.87)
MetaSocialImage: 1_87/release-highlights.png
Date: 2024-2-28
DownloadVersion: 1.87.2
---
# February 2024 (version 1.87)

**Update 1.87.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22February+2024+Recovery+1%22+).

**Update 1.87.2**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22February+2024+Recovery+2%22+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

Welcome to the February 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

- **[Voice dictation in editor](#use-dictation-in-the-editor)** - Use your voice to dictate directly in the editor.
- **[Multi-cursor inline suggestions](#inline-completions-for-multiple-cursors)** - Review and accept inline suggestions for multiple cursors.
- **[Copilot-powered rename suggestions](#rename-suggestions)** - Get rename suggestions for symbols from Copilot.
- **[Side-by-side preview refactoring](#refactor-preview-in-multi-diff-editor)** - Preview refactorings across files with multi diff editor.
- **[Smarter Python imports](#improvements-for-adding-missing-imports)** - Improvements for adding missing Python imports.
- **[Sticky scroll in editor](#editor-sticky-scroll)** - Sticky scroll is enabled by default in the editor.
- **[Multi-language support for speech](#multiple-languages-supported-for-speech-recognition)** - Multiple languages supported for speech recognition.
- **[Copilot suggestions for dev containers](#remote-development)** - Get template and feature suggestions for dev container configurations.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Use dictation in the editor

You can now use your voice to dictate directly into the editor, provided that you have installed the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension.

<video src="images/1_87/editor-dictate.mp4" autoplay loop controls muted title="Dictate to the editor using voice."></video>

We've added new commands to start and stop editor dictation: **Voice: Start Dictation in Editor** (`kb(workbench.action.editorDictation.start)`) and **Voice: Stop Dictation in Editor** (`kb(workbench.action.editorDictation.stop)`).

You can press and hold the keybinding for the start command (`kb(workbench.action.editorDictation.start)`) to enable walky-talky mode, where the voice recognition stops as soon as you release the keys.

**Note:** dictation even works in other places where a rich editor is used, such as the SCM commit input box and the comments input field when reviewing pull requests.

### Multiple languages supported for speech recognition

When you use the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension, you can now select from one of the 26 supported languages by using the <code codesetting="accessibility.voice.speechLanguage">accessibility.voice.speechLanguage</code> setting.

<video src="images/1_87/voice-language.mp4" autoplay loop controls muted title="Speech languages support via settings."></video>

Each language for the speech extension comes as its own extension. When you start speech recognition for the first time, you will see an extension installation for each language you selected.

### Accessibility signals

Accessibility signals encompass both sounds, formerly called audio cues, and announcements, formerly called alerts.

The commands **Help: List Signal Sounds** and **Help: List Signal Announcements** allow users to view the available signals and configure them. Migration to this new configuration happens automatically.

Before:

```json
"audioCues.clear": "on | auto | off"
"accessibility.alert.clear": true | false
```

After:

```json
"accessibility.signals.clear": {
    "sound": "auto | on | off",
    "announcement" (optional): "auto | off"
}
```

## Workbench

### Interactive settings in the release notes

To make it easier for you to get started with a new feature, you can now enable or view a setting directly from the release notes. When you view the release notes from within VS Code (**Show Release Notes** command), notice that some settings have a gear icon. Select the setting or gear icon to immediately interact with the setting.

![Example of an interactive setting in release notes](images/1_87/setting-in-release-notes.png)

### Transparency and control of language model access

This iteration, we introduced a proposed API that gives extensions the ability to use GitHub Copilot Chat's language models. To provide the most transparency and control of access to the models, we have added the following features.

#### Manage language model access like you manage authentication access

We found that managing access to language models was very similar to managing access to your GitHub account or Microsoft account (or anything that leverages the `AuthenticationProvider` model) in VS Code. Because of these similarities, we've leveraged VS Code's auth stack for access to language models as well.

When an extension wants to access the language models of another extension, they'll see a one-time prompt for access that will be persisted:
![Modal dialog showing an extension requiring language model access](images/1_87/auth-language-model.png)

Once access is granted, you'll be able to manage that access in the same place you manage access to your accounts, in the account menu:

![Manage trusted extensions options in the account menu](images/1_87/auth-account-menu.png)

"Manage Trusted Extensions" leading you to a quick pick where you can manage access if you so choose:

![Manage trusted extensions quick pick](images/1_87/manage-trusted-extensions.png)

With the initial work done, we'll continue to refine the experience so it's as smooth and clear as possible.

#### Track language model usage of an extension

You can track language model usage of an extension in the Extension Editor and Runtime Extensions Editor. Following images show the number of requests made by the Copilot Chat Sample Extension to the GitHub Copilot Chat language model.

![Tracking language model usage in extension editor](images/1_87/extension-editor-lang-model-tracking.png)

![Tracking language model usage in running extensions editor](images/1_87/running-extensions-lang-model-tracking.png)

## Editor

### Editor Sticky Scroll

This iteration we're enabling Sticky Scroll in the editor by default. You can change this using the setting <code codesetting="editor.stickyScroll.enabled">editor.stickyScroll.enabled</code>.

We have also increased the maximum number of lines that can be displayed within the editor sticky widget from 10 to 20. You can configure the maximum line count with the setting <code codesetting="editor.stickyScroll.maxLineCount">editor.stickyScroll.maxLineCount</code>.

### Inline completions for multiple cursors

This iteration we have added support for multi-cursor inline completions. Now, the inline completions are previewed and applied at both the primary and the secondary cursor positions.

<video src="images/1_87/multicursor-inline-completions.mp4" autoplay loop controls muted title="Multi-cursor inline Completions."></video>

### Refactor preview in multi diff editor

With Refactor preview, you can review the changes that will be applied by a code refactoring. Refactor preview changes are now shown in a multi diff editor, which facilitates seeing all changes at a glance and comparing them against the previous version.

<video src="images/1_87/refactor-preview-multidiff-editor.mp4" autoplay loop controls muted title="Multi-diff Editor opened from Refactor preview."></video>

## Terminal

### Command duration tracked

Terminal duration is now tracked and details are shown in the command hover when shell integration is enabled:

![Hovering the terminal command shows how long it took in milliseconds](./images/1_87/terminal_duration.png)

### New commands to zoom in, out and reset

There are new commands to zoom in, zoom out, and reset the terminal font size. These are unbound by default, but you can bind them to your preferred keybindings.

- **Terminal: Increase Font Size** (`workbench.action.terminal.fontZoomIn`)
- **Terminal: Decrease Font Size** (`workbench.action.terminal.fontZoomOut`)
- **Terminal: Reset Font Size** (`workbench.action.terminal.fontZoomReset`)

## Source Control

### Include repository or branch name in the window title

Users can customize the window title by using the <code codesetting="window.title">window.title</code> setting. This iteration, we added two new variables that can be used with this setting: `${activeRepositoryName}`, and `${activeRepositoryBranchName}`. These variables are replaced with the name of the active repository and the active branch, respectively.

### Commit input validation improvements

This iteration, we explored using language diagnostics to provide better input validation for authoring commit messages. Using language diagnostics enables us to surface code actions that can be invoked to resolve the input validation warnings. We added code actions to remove whitespace characters, hard wrap lines based on the <code codesetting="git.inputValidationSubjectLength">git.inputValidationSubjectLength</code>, and <code codesetting="git.inputValidationLength">git.inputValidationLength</code> settings, and we are looking to add more code actions in the future.

To enable it, toggle the <code codesetting="git.inputValidation:true">git.inputValidation</code> setting. We have some issues to work through before we can enable this by default, but in the meantime feel free to enable it and let us know your feedback.

### Incoming/Outgoing changes settings management

We continue to polish the **Incoming/Outgoing** section of the Source Control view. This iteration, we added a settings action to the "Incoming/Outgoing" separator that allows users to toggle the <code codesetting="scm.showIncomingChanges">scm.showIncomingChanges</code>, <code codesetting="scm.showOutgoingChanges">scm.showOutgoingChanges</code>, and <code codesetting="scm.showChangesSummary">scm.showChangesSummary</code> settings more easily. These actions are also available in the **Incoming & Outgoing** menu in the `...` menu of the Source Control view's title bar.

### Close All Unmodified Editors command

To help with editor management, we have added the **Close All Unmodified Editors** command to the command palette that will close all editors that have unmodified files. The command will not close editors that have unsaved changes.

## Notebooks

### Indentation settings for notebooks

We now support notebook-specific indentation settings via the <code codesetting="notebook.editorOptionsCustomizations">notebook.editorOptionsCustomizations</code> setting. This setting allows users to set a specific indentation style for notebooks, via the `editor.tabSize`, `editor.indentSize`, and `editor.insertSpaces` settings.

Users will also notice a status bar entry named **Notebook Indentation** that shows the current indentation settings for the notebook editor. This entry has a quick pick menu, which enables managing the indentation settings without having to open the Settings editor. This is the same quick pick menu that is also available in the editor.

## Debug

### Support for new `BreakpointMode`

VS Code supports a new addition to the Debug Adapter Protocol (DAP) that allows you to set different 'modes' of breakpoints. This functionality might commonly be used by debuggers of native code, for example, to set hardware versus software breakpoints. The mode of a breakpoint can be changed with the **Edit Mode** action in its context menu.

![Breakpoint context menu now has an 'Edit Mode...' option to change the breakpoint mode](./images/1_87/bp-modes.png)

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

- GitHub Copilot Chat suggests templates and features when adding dev container configuration files to a workspace

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_87.md).

Don't miss the recent [Remote development with Visual Studio Code Learn path](https://aka.ms/vsc-remote-learn) to get an introduction to the different remote development features in VS Code.

## Contributions to extensions

### GitHub Copilot

#### Rename suggestions

We are gradually rolling out rename suggestions by Copilot. When you rename a symbol in the editor, GitHub Copilot suggests a list of possible new names for that symbol, based on your code.

<video src="images/1_87/rename_suggestions.mp4" title="Rename suggestions in the editor generated by GitHub Copilot." autoplay loop controls muted></video>

#### Inline chat accessibility view

Inline chat now has an accessibility view that shows code changes just like the accessible diff viewer does. The accessibility view is enabled when you use screen reader mode. You can also configure it to always be on or off by using the <code codesetting="inlineChat.accessibleDiffView">inlineChat.accessibleDiffView</code> setting.

Also, change-hunks can now be navigated with the keyboard with the `kbstyle(F7)` and `kbstyle(Shift+F7)` keybindings.

#### Default mode for inline chat

We have retired the `live preview` mode and made `live` the default mode for inline chat. This means that suggestions are applied directly in the editor and changes are highlighted via inline diffs.

#### Microphone icon is always visible

The microphone icon to start voice recognition in Copilot Chat is now always visible. When the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension is not yet installed, selecting the icon firsts asks to install the extension to enable speech-to-text capabilities.

![Microphone icon in chat input](images/1_87/chat-mic.png)

If you are not interested in this feature, or you only want to use it via keyboard shortcuts, you can hide the functionality from the context menu:

![Hide microphone icon in chat input](images/1_87/chat-mic-hide.png)

#### Voice support for agents and slash commands

When you use the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension to fill the chat input field via voice, phrases such as "at workspace" or "slash fix" now translate into the respective agent and slash commands. This works in both the Chat view and in inline chat.

<video src="images/1_87/voice-agent.mp4" autoplay loop controls muted title="Agent and slash commands support in Voice."></video>

#### Walky-talky mode everywhere

When you use the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension, the command **Voice: Start Voice Chat** (`kb(workbench.action.chat.startVoiceChat)`) now works everywhere and brings up a voice chat, depending on where your focus is (inline chat in the editor, panel chat otherwise).

To enable walky-talky mode, press and hold the keybinding. Voice recognition is active until you release the keys, after which the request is submitted automatically.

#### `Copilot: Explain This` based on cursor position

Previously, entering `Explain This` in chat required that you first selected the text to explain in your active editor. You can now also place your cursor on an identifier to get Copilot to see the identifier's definition. If the definition is in another file, this requires rich language support to be available.

#### Preview: `#codebase` variable

This iteration, in the **pre-release** version of GitHub Copilot Chat, we introduced a new chat variable called `#codebase`. This variable can be used to provide workspace context, based on your query, to Copilot or a chat participant you are talking to.

<video src="images/1_87/codebase.mp4" title="Use of the #codebase chat variable" autoplay loop controls muted></video>

In this example, you can think of `#codebase` as asking `@workspace` the question first, and then adding that response to the question you're asking `@terminal`.

This is a preview feature, so `#codebase` might not be the final name of this variable but the intent is that we will provide some way to include workspace context to chat participants.

Again, this is only in the pre-release version of GitHub Copilot Chat. Let us know what you think and how you use it!

#### Terminal workspace context

The terminal no longer automatically pulls in workspace context, which could take some time. Instead, you need to explicitly include it by using the `#codebase` variable.

#### Terminal chat location

There is a new setting `github.copilot.chat.terminalChatLocation`, which controls the terminal chat experience. The default value is `chatView`, and can be configured to `quickChat` if you prefer the previous behavior.

### Jupyter

#### Language Server support for locally running Jupyter Servers

When connecting to local Jupyter Servers, the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension previously treated these Jupyter Servers as remote. As a result, the [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) extension was unable to detect the installed packages.

![Failed language features for remote Jupyter Servers](images/1_87/jupyter-remote-failed-ls-features.png)

This iteration, the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension detects whether the connected Jupyter Server is local or remote. This enables the [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) extension to provide richer language features, based on packages installed (within a local Python Environment).

![Language features for remote Jupyter Servers](images/1_87/jupyter-remote-ls-features.png)

### Python

#### Shell integration for the Python REPL

We now have shell integration for Python REPL enabled on Mac, Linux, and Windows via the [WSL extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl). When you execute commands in the Python REPL, colored circles decorators indicate whether the commands succeeded or failed. Additionally, we also support the **Terminal: Run Recent Command…** command for Python REPL, which enables you to view and utilize the REPL’s command history.

We now also support Python’s shell file history for all operating systems, accessible via **Terminal: Run Recent Command**.

#### Reducing risk of ignored first command

Previously, a number of Python REPL users noticed issues with their first command to the Python REPL (for example, by using `kbstyle(Shift+Enter)`). Either their Python command was ignored and not sent to Python REPL, or the command was pasted multiple times in the shell that launches Python REPL instead of the Python REPL itself. This behavior happened especially for users on Windows, or users working on older machines.

In this iteration, we made efforts to mitigate and reduce the risk of this behavior happening. Users now experience less occurrence of their first command being lost when they send their Python command to the first REPL instance inside VS Code.

#### Improvements for adding missing imports

The [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) extension provides an **Add Imports** code action for adding missing imports. In the pre-release version of Pylance, we improved this code action. Pylance now uses heuristics to show only the top three high-confidence import options, prioritized based on the following criteria: most recently used imports, symbols from the same module, symbols from the standard library, symbols from user modules, symbols from third-party packages, and finally sorting by module and symbol name.

Additionally, two new code actions are introduced: **Search for additional import matches**, which displays a quick pick menu that allows you to search for import options that prefix-match the missing import symbol, and **Change spelling**, which offers import suggestions for missing imports due to typos.

<video src="images/1_87/pylance-add-imports.mp4" title="Add missing import and Search for additional import matches code actions." autoplay loop controls muted></video>

You can enable this behavior now with the <code codesetting="python.analysis.addImport.heuristics:true">python.analysis.addImport.heuristics</code> setting. We plan to make this the new default behaviour soon, and deprecate the setting in a future release.

#### Automatically open the browser when debugging Django or Flask apps

Developing and testing your Python web applications is now more convenient with the [Python Debugger extension](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy)! You can now get the browser to automatically open when starting the debugger with Django or Flask apps, by setting `autoStartBrowser: true` in your `launch.json` configuration:

```json
{
    "name": "Python Debugger: Flask",
    "type": "debugpy",
    "request": "launch",
    "module": "flask",
    "env": {
        "FLASK_APP": "hello_app.webapp",
        "FLASK_DEBUG": "1"
    },
    "args": [
        "run"
    ],
    "jinja": true,
    "autoStartBrowser": true
}
```

#### Bug fix for Pytest in symlinked workspaces

We implemented a bug fix that enables pytest tests to function correctly for workspaces that have symlinks. On the testing rewrite, test from symlink locations are referenced and run correctly by their symlink paths.

### GitHub Pull Requests

There has been more progress on the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which enables you to work on, create, and manage pull requests and issues. New features include:

- Renamed from "GitHub Pull Requests and Issues" to "GitHub Pull Requests"
- The email associated with a merge or squash commit can be chosen at merge/squash time
- The setting `githubPullRequests.labelCreated` can be used to configure the labels that are automatically added to PRs that are created
- "Owner level" PR templates are now supported
- Projects can be added at PR and issue creation time

Review the [changelog for the 0.82.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0820) release of the extension to learn about the other highlights.

## Preview Features

### For extension authors: Preview of `@vscode/l10n-dev` and Azure AI Translator

This iteration, we have introduced a new command, a subcommand of `@vscode/l10n-dev`, that allows you to translate your strings using [Azure AI Translator](https://azure.microsoft.com/en-us/products/ai-services/ai-translator).

After exporting your strings, and creating an Azure AI Translator instance, you can set the `AZURE_TRANSLATOR_KEY` & `AZURE_TRANSLATOR_REGION` environment variables and then you can run the following command to generate translations using Azure AI Translator:

```bash
npx @vscode/l10n-dev generate-azure -o ./l10n/ ./l10n/bundle.l10n.json ./package.nls.json
```

<video src="images/1_87/azure-translator.mp4" title="l10n-dev using Azure Translator" autoplay loop controls muted></video>

Azure AI Translator offers a free tier. We thought it was a good opportunity to offer tooling to extension authors so that they can more easily support the many users who speak various languages. Do keep in mind that these are machine translations, so they might not always be perfect. They can be a good jumping off point to more refined translations from people who speak the language, if you have the ability to do so.

For more information, take a look at [the l10n-dev documentation on the Azure AI Translator integration.](https://github.com/microsoft/vscode-l10n/blob/main/l10n-dev/README.md#azure-ai-translator-experimental)

## Extension Authoring

### Test Coverage in Extensions

Extension authors who are using the [test CLI](/api/working-with-extensions/testing-extension#quick-setup-the-test-cli) for their extensions can generate test coverage by updating to the latest version of the `@vscode/test-cli` package.

Coverage can be generated on the command line by passing the `--coverage` flag when running tests, and in the VS Code UI by using the **Run with Coverage** actions.

### Test configurations in launch.json

You can now reference test configuration files in your `launch.json` configuration:

```diff
{
    "type": "extensionHost",
    "request": "launch",
    "name": "My extension tests",
+   "testConfiguration": "${workspaceFolder}/.vscode-test.js",
-   "args": ["--extensionDevelopmentPath=${workspaceFolder}"]
},
```

The various `args` that were previously required, are generated for you, but any additional arguments passed will be appended to VS Code's command line.

### Contributing Additional Data in Issue Reporter

Last iteration, we added fields for `data` and `uri` in the `workbench.action.openIssueReporter` command. This enabled extensions to directly open the native issue reporter with more prefilled information.

To let extensions benefit from this in the separate case of navigating the normal issue reporter flow via `Help: Reporter Issues...`, extensions can contribute a custom command (which will invoke `openIssueReporter`) and a menu contribution point to `issue/reporter`.

An example of a contributed command and menu for `contributes` in `package.json`:

``` json
"commands": [
    {
        "command": "extension.myCommand",
        "title": "Report Issue"
    }
],
    "menus": {
        "issue/reporter": [
            {
                "command": "extension.myCommand"
            }
        ]
    }

```

Subscribe to [issue #196863](https://github.com/microsoft/vscode/issues/196863) for updates or changes to the API and `openIssueReporter` command.

## Proposed APIs

### Chat and Language Model APIs nearing their finalization

We have been working on support for extensions to contribute to the Chat view and general use of language models. We are now in the final stages of finalizing the following APIs and would love to hear your feedback:

- Chat participant ([issue #199908](https://github.com/microsoft/vscode/issues/199908)) - [`vscode.proposed.chatParticipant.d.ts`](https://github.com/microsoft/vscode/blob/main/src/vscode-dts/vscode.proposed.chatParticipant.d.ts)
- Language model use ([issue #206265](https://github.com/microsoft/vscode/issues/206265)) - [`vscode.proposed.languageModels.d.ts`](https://github.com/microsoft/vscode/blob/main/src/vscode-dts/vscode.proposed.languageModels.d.ts)

Learn more about how to [build chat extensions](https://code.visualstudio.com/api/extension-guides/chat) and how you can [use language models](https://code.visualstudio.com/api/extension-guides/language-model) in our extension guides.

### FindFiles2 API

We have added a new proposed extension API that is an improved version of the existing `workspace.FindFiles` API. The new `workspace.FindFiles2` API introduces new options that allow a workspace file search to:

- Respect `files.exclude` and `search.exclude` settings
- Respect ignore files
- Perform a fuzzy search
- Follow symlinks

You can review the new API [here](https://github.com/microsoft/vscode/blob/4e2aa982224429f161423cbdded9587e39766782/src/vscode-dts/vscode.proposed.findFiles2.d.ts).

Note that `FindFiles2` is a tentative name and that this functionality might be provided as an alternative overload of `FindFiles` in the future.

### Test Coverage API

This iteration, the Test Coverage API and in-editor experience are in feature-complete state. We encourage extension authors to try them out and provide feedback before their forecasted finalization in the VS Code 1.88 release.

The [Extension Pack for Java](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-pack) has already adopted the Test Coverage API. Developers can already see the coverage results running Java tests. Now you can get to test coverage by selecting the **Run Test With Coverage** button, and view the **Test Coverage** panel in the Test Explorer view. Learn more about the Test Coverage with the Extension Pack for Java in the team's [December](https://devblogs.microsoft.com/java/java-on-visual-studio-code-update-december-2023/) and [January](https://devblogs.microsoft.com/java/java-on-visual-studio-code-update-january-2024/) update.

While the API is too lengthy to include here, we believe it to be fairly straightforward, and would welcome your input on the proposal in [issue #123713](https://github.com/microsoft/vscode/issues/123713).

### Debug Visualizer API

We have a new API that enables extensions to contribute *visualizers* for a variable. These visualizers might be either actions that run a command, such as opening a new editor, or trees embedded inline into the Debug views and that take the place of the data's default representation.

You can review the new API [here](https://github.com/microsoft/vscode/blob/main/src/vscode-dts/vscode.proposed.debugVisualization.d.ts).

### New Symbol Names Provider API

This API allows extensions to provide name suggestions when the user wants to rename a symbol.

## Notable fixes

* [184046](https://github.com/microsoft/vscode/issues/184046) Links sometimes stop working completely in terminals

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@starball5 (starball)](https://github.com/starball5)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart): Prevent F1 from opening browser help in webviews [PR #204499](https://github.com/microsoft/vscode/pull/204499)
* [@aramikuto (Aleksandr Kondrashov)](https://github.com/aramikuto)
  * Do not use respectMultiSelection for upload and paste [PR #201145](https://github.com/microsoft/vscode/pull/201145)
  * Ensure that cursor state change is always emitted upon restoring state [PR #203451](https://github.com/microsoft/vscode/pull/203451)
* [@CGNonofr (Loïc Mangeonjean)](https://github.com/CGNonofr)
  * Replace map by foreach [PR #199194](https://github.com/microsoft/vscode/pull/199194)
  * Fix inoperative try/catch [PR #203904](https://github.com/microsoft/vscode/pull/203904)
* [@charlypoirier (Charly)](https://github.com/charlypoirier): Fix typo in configurationEditingMain.ts [PR #203970](https://github.com/microsoft/vscode/pull/203970)
* [@emilan (Emil)](https://github.com/emilan): Fix for automatic folderOpen tasks with Remote SSH extension [PR #204008](https://github.com/microsoft/vscode/pull/204008)
* [@fmarier (Francois Marier)](https://github.com/fmarier): Use HTTPS for the apt repository [PR #203833](https://github.com/microsoft/vscode/pull/203833)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Reinstate command items when filtering checkout quickpick (fix #202870) [PR #204107](https://github.com/microsoft/vscode/pull/204107)
  * Make Collapse/Expand All button of Search tree initialize correctly (fix #204316) [PR #205235](https://github.com/microsoft/vscode/pull/205235)
* [@harbin1053020115 (ermin.zem)](https://github.com/harbin1053020115): chore: update vscode known variables [PR #204568](https://github.com/microsoft/vscode/pull/204568)
* [@hsfzxjy (Xie Jingyi)](https://github.com/hsfzxjy): Add duration to the terminal command SI tooltip [PR #199357](https://github.com/microsoft/vscode/pull/199357)
* [@jcarrus (Justin Carrus)](https://github.com/jcarrus): Increase editor.stickyScroll.maxLineCount from 10 to 20 [PR #201451](https://github.com/microsoft/vscode/pull/201451)
* [@jnnklhmnn (Jannik Lehmann)](https://github.com/jnnklhmnn): Introduce Collapse All Action to Loaded Scripts [PR #203560](https://github.com/microsoft/vscode/pull/203560)
* [@Krzysztof-Cieslak (Krzysztof Cieślak)](https://github.com/Krzysztof-Cieslak)
  * Improve inline edit commands' preconditions [PR #205373](https://github.com/microsoft/vscode/pull/205373)
  * Don't run `onDidBlurEditorWidget` and `onDidFocusEditorText` if inline edit is disabled [PR #205378](https://github.com/microsoft/vscode/pull/205378)
  * Inline edit - don't send reject callback on blur [PR #205976](https://github.com/microsoft/vscode/pull/205976)
* [@luctowers (Lucas Towers)](https://github.com/luctowers): Fix markdown light and dark mode when using high contrast themes [PR #203690](https://github.com/microsoft/vscode/pull/203690)
* [@NorthSecond (Yifei Yang)](https://github.com/NorthSecond): Fix: GLIBCXX version detection bug in check-requirements-linux.sh (issue #204186) [PR #204635](https://github.com/microsoft/vscode/pull/204635)
* [@notnoop (Mahmood Ali)](https://github.com/notnoop): vscode server: cope with multiple libc/libc++ installations [PR #204032](https://github.com/microsoft/vscode/pull/204032)
* [@orgads (Orgad Shaneh)](https://github.com/orgads): Tunnel: Extend port mapping lookup also for querystring [PR #203908](https://github.com/microsoft/vscode/pull/203908)
* [@owlhuang (Dennis)](https://github.com/owlhuang): Pass the check if any one of the library (of the arch) satisfies the requirement. [PR #204221](https://github.com/microsoft/vscode/pull/204221)
* [@perplexyves (Yves Daaboul)](https://github.com/perplexyves): fix(193523): JSDoc optional parameters don't display in functions [PR #202963](https://github.com/microsoft/vscode/pull/202963)
* [@petvas (Peter V)](https://github.com/petvas): Fix `IRawGalleryExtension.shortDescription` can be undefined. [PR #202780](https://github.com/microsoft/vscode/pull/202780)
* [@pyrrho (Drew Pirrone-Brusse)](https://github.com/pyrrho): Extend TextEditorLineNumbersStyle with Interval [PR #198787](https://github.com/microsoft/vscode/pull/198787)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
  * Contribute to json language server with a custom language. [PR #198583](https://github.com/microsoft/vscode/pull/198583)
  * Enable json language support for `code-snippets` files [PR #204090](https://github.com/microsoft/vscode/pull/204090)
  * Improve extension `README` preview markdown codeblock language detection [PR #205329](https://github.com/microsoft/vscode/pull/205329)
* [@rzvc](https://github.com/rzvc): Fix docblock expansion in TS, when asterisk not preceded by a space [PR #204400](https://github.com/microsoft/vscode/pull/204400)
* [@sandersn (Nathan Shively-Sanders)](https://github.com/sandersn): Split TS' AI-backed code actions into separate entries [PR #201140](https://github.com/microsoft/vscode/pull/201140)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke)
  * fix: memory leak in notebook editor widget [PR #204892](https://github.com/microsoft/vscode/pull/204892)
  * fix: memory leak in code editor widget [PR #205488](https://github.com/microsoft/vscode/pull/205488)
* [@timotheeMM (timotheeMM)](https://github.com/timotheeMM): Fix a typo in src/vs/platform/terminal/common/terminal.ts [PR #204245](https://github.com/microsoft/vscode/pull/204245)
* [@tisilent (xiejialong)](https://github.com/tisilent): dispose sash [PR #199081](https://github.com/microsoft/vscode/pull/199081)
* [@tomqwpl](https://github.com/tomqwpl): fix: Not populating extension when selecting notebook kernel (#197619) [PR #197810](https://github.com/microsoft/vscode/pull/197810)
* [@vbem (Lei LI)](https://github.com/vbem): Fix typo in shellIntegration-bash.sh [PR #203407](https://github.com/microsoft/vscode/pull/203407)
* [@weartist (Hans)](https://github.com/weartist): Fix the broken links to the latest valid links [PR #184131](https://github.com/microsoft/vscode/pull/184131)
* [@werat (Andy Hippo)](https://github.com/werat)
  * Fix memory leak in comments browser [PR #205162](https://github.com/microsoft/vscode/pull/205162)
  * Fix memory leaks [PR #205589](https://github.com/microsoft/vscode/pull/205589)
* [@wy-luke](https://github.com/wy-luke): docs: update comment for hideFromUser [PR #202730](https://github.com/microsoft/vscode/pull/202730)
* [@xavierdecoster (Xavier Decoster)](https://github.com/xavierdecoster): Update comment in extensionGalleryService.ts [PR #205004](https://github.com/microsoft/vscode/pull/205004)
* [@xiaoyun94 (暴躁暴躁最暴躁/Bigforce)](https://github.com/xiaoyun94): Fix browser host open additional files in merge mode [PR #205663](https://github.com/microsoft/vscode/pull/205663)
* [@yiliang114 (易良)](https://github.com/yiliang114): Add tips for debug views [PR #205861](https://github.com/microsoft/vscode/pull/205861)

Contributions to `vscode-black-formatter`:

* [@bn-d (Boning)](https://github.com/bn-d): Update min vscode version [PR #445](https://github.com/microsoft/vscode-black-formatter/pull/445)

Contributions to `vscode-hexeditor`:

* [@deitry (Dmitry Vornychev)](https://github.com/deitry): Fix plugin description [PR #480](https://github.com/microsoft/vscode-hexeditor/pull/480)
* [@jogo-](https://github.com/jogo-)
  * Display unsigned before signed int64 [PR #482](https://github.com/microsoft/vscode-hexeditor/pull/482)
  * Display ascii character in data inspector [PR #483](https://github.com/microsoft/vscode-hexeditor/pull/483)
  * Show status offset and selection in dec and hexa [PR #486](https://github.com/microsoft/vscode-hexeditor/pull/486)
  * Display in data inspector ULEB128 and SLEB128 [PR #488](https://github.com/microsoft/vscode-hexeditor/pull/488)
  * Fix typos in CHANGELOG.md [PR #489](https://github.com/microsoft/vscode-hexeditor/pull/489)
* [@liudonghua123 (liudonghua)](https://github.com/liudonghua123): add common cjk encoding (gb18030 for simple Chinese, big5 for traditional Chinese, euc-kr for Korean, euc-jp for Japanese) datatype support [PR #465](https://github.com/microsoft/vscode-hexeditor/pull/465)

Contributions to `vscode-isort`:

* [@archont94](https://github.com/archont94): Fix for selecting `isort` settings from path [PR #386](https://github.com/microsoft/vscode-isort/pull/386)
* [@connorads (Connor Adams)](https://github.com/connorads): Update config example [PR #390](https://github.com/microsoft/vscode-isort/pull/390)

Contributions to `vscode-languageserver-node`:

* [@michaelpj (Michael Peyton Jones)](https://github.com/michaelpj)
  * Remove proposed tags from new names for structure literals [PR #1417](https://github.com/microsoft/vscode-languageserver-node/pull/1417)
  * Add Haskell to LanguageKind [PR #1421](https://github.com/microsoft/vscode-languageserver-node/pull/1421)
* [@w0rm (Andrey Kuzmin)](https://github.com/w0rm): Round progress percentage according to the spec [PR #1413](https://github.com/microsoft/vscode-languageserver-node/pull/1413)

Contributions to `vscode-pull-request-github`:

* [@Malix-off (Malix)](https://github.com/Malix-off): Fix #5693 [PR #5694](https://github.com/microsoft/vscode-pull-request-github/pull/5694)
* [@umakantv (Umakant Vashishtha)](https://github.com/umakantv): Feature: Auto Populate Labels  [PR #5679](https://github.com/microsoft/vscode-pull-request-github/pull/5679)

Contributions to `language-server-protocol`:

* [@alanwsmith (Alan Smith)](https://github.com/alanwsmith): Removed deprecated Rust Language Server (RLS) [PR #1899](https://github.com/microsoft/language-server-protocol/pull/1899)
* [@debonte (Erik De Bonte)](https://github.com/debonte): Add WorkspaceEditMetadata support [PR #1881](https://github.com/microsoft/language-server-protocol/pull/1881)
* [@falko17 (Falko)](https://github.com/falko17): Snippet grammar fixes and minor formal improvements throughout the LSP spec [PR #1886](https://github.com/microsoft/language-server-protocol/pull/1886)
* [@MariaSolOs (Maria José Solano)](https://github.com/MariaSolOs)
  * Add snippet text edit specification [PR #1892](https://github.com/microsoft/language-server-protocol/pull/1892)
  * Add string value definition [PR #1893](https://github.com/microsoft/language-server-protocol/pull/1893)
  * Remove insertTextFormat from InlineCompletionItem [PR #1894](https://github.com/microsoft/language-server-protocol/pull/1894)
* [@michaelpj (Michael Peyton Jones)](https://github.com/michaelpj): Add Haskell to language kind table [PR #1898](https://github.com/microsoft/language-server-protocol/pull/1898)
* [@qvalentin (valentin)](https://github.com/qvalentin): feat(implementors): add helm-ls [PR #1895](https://github.com/microsoft/language-server-protocol/pull/1895)

Contributions to `node-pty`:

* [@kkocdko (kkocdko)](https://github.com/kkocdko): Port to NAPI [PR #644](https://github.com/microsoft/node-pty/pull/644)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
