# January 2025 (version 1.97)

## Core

### Setting to control use of local proxy configuration

The new `setting(http.useLocalProxyConfiguration)` setting controls whether or not remote extensions use the local proxy configuration. The default is to use the local configuration for WSL and local Dev Containers and to not use it for Remote-SSH, Remote-Tunnels and remote Dev Containers. The defaults are the same as the existing behavior.

## Remote - SSH

Remote - SSH `0.117.0` will be released alongside VS Code `1.97`.

### Chat participant improvements

Various improvements were made to the `@remote-ssh` chat participant [introduced last release](./v1_96.md#the-remote-ssh-chat-participant).

We are continuing to improve this experience and would love to [hear your feedback](https://github.com/microsoft/vscode-remote-release).

### SSH configuration improvements

This release improves SSH configuration file parsing and performance. Notably, users with many `Match` statements in their SSH configurations may see a noticeable performance improvement.

### Default remote extensions

We introduced a new setting that lists the extensions that should be installed automatically into a remote, if they are already installed locally. This mirrors the setting already available in the Dev Containers extension.

Configure with `setting(remote.SSH.defaultExtensionsIfInstalledLocally)`.

### Migration path for Linux legacy server

We are fast approaching the end of support for [Linux legacy server](https://aka.ms/vscode-remote/faq/old-linux). VS Code release v1.98 (February 2025) will be the last release that supports Linux servers with `glibc` < 2.28 or `libstdc++` < 3.4.25. As of release 1.99, you can no longer connect to these servers.

We are considering providing a workaround option for the SSH extension where you provide a custom build of `glibc` and `libstdc++` that enables connecting to the server. More details can be found in this [draft](https://github.com/microsoft/vscode-docs/pull/7953/files) with test builds available [here](https://github.com/microsoft/vscode/pull/235232#issuecomment-2615764717).

This workaround aims to provide additional time for migrating to a supported Linux distro, without having to disable VS Code updates. Please give us your feedback [on this VS Code issue](https://github.com/microsoft/vscode/issues/231623).