---
Order:
TOCTitle: March 2020
PageTitle: Visual Studio Code March 2020
MetaDescription: Learn what is new in the Visual Studio Code March 2020 Release (1.44)
MetaSocialImage: 1_44/release-highlights.png
Date: 2020-4-8
DownloadVersion: 1.44.2
---
# March 2020 (version 1.44)

**Update 1.44.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22March+2020+Recovery%22+is%3Aclosed).

**Update 1.44.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22March+2020+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the March 2020 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Accessibility improvements](#accessibility)** - Easier diff view navigation, clearer role designations for UI controls.
* **[Timeline view](#timeline-view)** - See time-series events like Git commit history from within VS Code.
* **[Better Quick Open for files](#quick-open-rewrite)** - Navigate smoothly from files to symbols, filter files by folder name.
* **[Extension Pack display](#improved-extension-pack-display)** - Quickly see what is bundled in an Extension Pack.
* **[Persisted Undo/Redo stack](#keep-undo-stack-when-reopening-files)** - Undo/Redo stack preserved for reopened files.
* **[Remote Development](#remote-development)** - Directly check out Pull Requests into containers, support for Kubernetes.
* **[Settings Sync preview](#settings-sync)** - Settings Sync now lets you share snippets and UI state across machines.
* **[New Python tutorials](#python)** - Tutorials for creating Python containers and building Data Science models.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Accessibility

This milestone we received great feedback from our community, which helped us identify and tackle many [accessibility issues](https://github.com/microsoft/vscode/issues?q=label%3Aaccessibility+milestone%3A%22March+2020%22+is%3Aclosed).

* Diff view is now more accessible. Use `kb(editor.action.accessibleDiffViewer.next)` and `kb(editor.action.accessibleDiffViewer.prev)` to navigate through changes in the Accessible Diff Viewer and use the **Stage / Unstage / Revert Selected Ranges** commands to stage, unstage, or revert the current change.
* Better roles used across our widgets. The `listbox` role is used for the Suggest widget and Quick Pick, `list` for static lists, and `document` for read-only content such as the Welcome view and Markdown preview.
* Tuned the behavior of our Quick Pick widget.
* The current line CodeLens can now be shown in the Quick Pick with the **Show CodeLens Commands For Current Line** command.

## Timeline view

The Timeline view is now out of preview and enabled by default. This is a unified view for visualizing time-series events (for example, Git commits, file saves, test runs, etc.) for a file. The Timeline view automatically updates showing the timeline for the currently active editor, by default. You can control this default behavior by toggling the eye icon in the view toolbar. Also, similar to other views, the Timeline view supports find or filter as you type.

The Timeline view is collapsed by default at the bottom of the File Explorer. Selecting the **Timeline** sash will expand the Timeline view.

![Collapsed Timeline view](images/1_44/timeline-sash.png)

In this release, the built-in Git extension contributes a timeline source that provides the Git commit history of the specified file. Selecting a commit will open a diff view of the changes introduced by that commit. A context menu provides commands to **Copy Commit ID** and **Copy Commit Message**. There is also a new **Open Timeline** command on the File Explorer's context menu, to quickly show the timeline for the selected file.

Here is the Timeline view in action:

![Timeline view](images/1_44/timeline.gif)

*Theme: [Dark Amethyst](https://marketplace.visualstudio.com/items?itemName=eamodio.amethyst-theme)*

Additional timeline sources can be contributed by extensions (although the API is still in the proposed stage), which will be shown in the unified Timeline view. Extensions can also contribute menu items to the Timeline view, and to individual timeline items. You can also easily choose which sources you'd like included in the view.

## Workbench

### Quick Open rewrite

The Quick Open (`kb(workbench.action.quickOpen)`) control was using an old version of our tree widget, and in this iteration, it was migrated to use our latest list control, already used in the rest of the VS Code UI. Functionally, you should not be able to tell the difference, as all commands will work as before.

We did take this opportunity to add some features that you might find useful.

![Quick Open](images/1_44/quickopen.png)

**Navigate from files to symbols**

You can now continue to navigate to the symbols of a file result simply by typing `@`. For the file that is currently selected, all symbols will appear and the editor will open in the background to reveal the active symbol. The video below also shows that you can group symbols by category by following `@` with `:`.

![Quick Open navigate to symbols](images/1_44/quick-open-symbols-access.gif)

**Filter results by separating multiple queries with spaces**

If you are getting too many results for a file search, you can add more search patterns after typing a space. For example, you can narrow down results by folder if you type `<file name> <folder name>` one after the other.

![Quick Open filtering](images/1_44/quick-open-filtering.gif)

**Note:** This also works for the editor (`kb(workbench.action.gotoSymbol)`) and workspace (`kb(workbench.action.showAllSymbols)`) symbol picker. Any text after a space character will be used to filter by the container of that symbol.

**Input is preserved when switching providers**

If you leave Quick Open visible and switch to another provider (for example, from file search to symbol search), VS Code will apply whatever filter is typed and use it for the new provider. This allows you to quickly reuse the typed input for symbol search when it was previously used for a file search.

**Sort editor history by most recently used**

A new setting `"search.quickOpen.history.filterSortOrder": "recency"` allows you to sort editor history by the most recently opened items, even when starting to search. By default, editor history results will be sorted by relevance based on the filter pattern that was used.

**New command to open editor to the side**

A new command `workbench.action.alternativeAcceptSelectedQuickOpenItem` lets you add an additional keyboard shortcut to open files or symbols from Quick Open to the side. By default, `Ctrl+Enter` (macOS: `Cmd+Enter`) will open the file to the side in a new editor group, rather than in the current one.

### Improved Extension Pack display

To identify Extension Packs in the Extensions view, there is now a number badge that shows the number of extensions included in the Extension Pack.

![Extension Pack number of extensions badge](images/1_44/extension-pack-badge.png)

*Theme: GitHub Sharp with Customizations*

The Extension details page now shows all extensions bundled in the Extension Pack.

![Extension Pack details page showing bundled extensions](images/1_44/extension-pack-editor.png)

*Theme: GitHub Sharp with Customizations*

### Adjust height of editor tabs scroll bar

A new setting `workbench.editor.titleScrollbarSizing` lets you increase the size of the scroll bar for editor tabs and breadcrumbs. This makes it easier to scroll through a long list of open editors.

Configure it to `large` for a larger scroll bar:

![Large scrollbar](images/1_44/large-scrollbar.png)

### File Explorer can show hidden files

If a file is hidden (via the `files.exclude` setting) but it is open and visible in the editor, the file is shown in the File Explorer along with its parent chain. Those files and their parents are displayed in the File Explorer using a dimmed color as long as the file is visible in the editor area.

### View: Reopen with

The new **View: Reopen with** command lets you reopen the currently active file using a different [custom editor](#custom-text-editors).

![Using the Reopen With command with the custom editor example extension](images/1_44/reopen-with.png)

You can use this command to switch between VS Code's standard text editor and the custom editor, or to switch between multiple custom editors for a resource.

### workbench.editorAssociations setting

Also for [custom editors](#custom-text-editors), the new `workbench.editorAssociations` setting lets you configure which editor is used for a specific resource.

The example below configures all files ending in `.catScratch` to open using the [example custom text editor](https://github.com/microsoft/vscode-extension-samples/tree/main/custom-editor-sample) from our extension samples.

```json
"workbench.editorAssociations": [
  {
    "viewType": "catCustoms.catScratch",
    "filenamePattern": "*.catScratch"
  }
]
```

### View progress now shown on the view

We have changed progress indication for custom views to now show on each view itself, rather than at the top of the view container. This allows for a more direct connection between the long-running operation and its progress. There is also a [new API option](#view-level-progress-indication) for extensions to show custom progress on views as well.

Here you can see the progress display on the GitLens extension view:

![View progress indication](images/1_44/view-progress.gif)

### New default monospace font

We have updated the monospace font used in VS Code UI to:

```css
.mac { --monaco-monospace-font: "SF Mono", Monaco, Menlo, Courier, monospace; }
.windows { --monaco-monospace-font: Consolas, "Courier New", monospace; }
.linux { --monaco-monospace-font: "Ubuntu Mono", "Liberation Mono", "DejaVu Sans Mono", "Courier New", monospace; }
```

This change does not affect the font choice in the editor.

## Editor

### Keep Undo stack when reopening files

VS Code will now keep the **Undo/Redo** stack of a file when the file is closed. When the file is reopened, if its contents hasn't changed since it was closed, the Undo/Redo stack will be restored.

This is very useful if you limit the number of open editors with the `workbench.editor.limit.value` setting or you accidentally close a file you are working on.

### Explicit ordering for Code Actions on save

You can now set `editor.codeActionsOnSave` to an array of Code Actions to execute in order. You can use this to guarantee that a specific Code Action is always run before or after another one that may conflict with it.

The following `editor.codeActionsOnSave` will always run **Organize Imports** followed by **Fix All** once organize imports finishes:

```json
"editor.codeActionsOnSave": [
    "source.organizeImports",
    "source.fixAll"
]
```

### Using editor.wordSeparators for Next Find Match

The command **Add Selection to Next Find Match** (`kb(editor.action.addSelectionToNextFindMatch)`) now respects the setting `editor.wordSeparators`. Previously, the command would use the word definition as defined by the current file's language.

## Debugging

### Call Stack view improvements

The CALL STACK view is a complex view because it shows different types of objects (sessions, processes, threads, stack frames, and separator elements) and provides different actions for different types. In order to make it easier to differentiate the object types and to understand their supported interactions, we have started to make some visual improvements:

* Debug sessions are now decorated with an icon.
* Clickable elements use the link color.
* Smaller separator and presentation elements.

![Call Stack](images/1_44/callstack.png)

### New Debug Console icon

We've introduced a new icon for the Debug Console and also use it in the Activity Bar when the Debug Console view is moved from the panel.

![Debug Console icon](images/1_44/debug-console-icon.png)

### Progress feedback UI for debug extensions

VS Code now supports the "progress events" that were [proposed](https://github.com/microsoft/debug-adapter-protocol/issues/92) for the Debug Adapter Protocol (DAP) in the last milestone. With progress events, a debug extension can provide feedback to users for long running operations.

The VS Code debugging UI shows progress feedback in two locations:

* As a progress bar at the top of the debug view.
* As a "silent notification", which means that progress is shown "silently" (without interrupting the user) in the Status bar and can be opened into a notification by clicking on it. The notification shows more detailed information and allows cancellation of the underlying long running operation (if the operation supports cancel).

In order to avoid flicker for short operations, the progress UI only starts after a 0.5 second delay.

The following video shows the new progress UI for a (simulated) long running operation in Mock Debug (the only supporting debug extension as of today). We expect that other debug extensions will adopt the progress support soon.

![Debug progress UI](images/1_44/debug-progress.gif)

### Code completion selection in the Debug Console

In the last milestone, we added [selection control](https://github.com/microsoft/debug-adapter-protocol/issues/87) to the "completion" request of the Debug Adapter Protocol. With this release, VS Code now fully supports the protocol addition and a debug extension can adjust the selection (or insertion point) after a completion item has been inserted.

## Integrated Terminal

### Allow menu bar mnemonics to skip the terminal

When the new setting `terminal.integrated.allowMenubarMnemonics` is enabled, all keystrokes using `Alt` will skip the terminal so they are processed by VS Code's keybinding manager, making all menu mnemonics work at the cost of `Alt` hotkeys within the terminal. This setting is disabled by default.

## Languages

### Auto import style in JavaScript

The new `javascript.preferences.importModuleSpecifierEnding` setting lets you control the style of imports that VS Code's auto imports use. This can be useful if you are writing code for platforms such as browsers that support native ES6 modules.

Possible values are:

* `auto` - The default. Uses the project's [jsconfig](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_javascript-projects-jsconfigjson) to determine the import style to use.
* `minimal` - Use's Node.js style imports. This shortens imports for `src/component/index.js` to `src/component`.
* `index` - Include the `index` part of the path as well. This shortens `src/component/index.js` to `src/component/index`.
* `js` - Use the full path, including the file extension (`.js`).

## Tasks

### Faster task Quick Pick

The task Quick Pick used to fetch all tasks from all task-providing extensions before displaying the list of tasks to choose from. Now, VS Code doesn't fetch any additional extension tasks before showing the Quick Pick dropdown, making it much faster. Extension contributed tasks are also only fetched when you ask for them, which frees up the extension host.

Below the TypeScript contributed `tsc` tasks are only fetched after the user selects the **typescript** extension.

![Task Quick Pick](images/1_44/task-pick.gif)

*Theme: One Dark Pro*

## Preview features

Preview features are not ready for release but are functional enough to use. We welcome your early feedback while they are under development.

### Settings Sync

We have been working the last couple of months to support synchronizing VS Code settings, extensions, and keyboard shortcuts across machines and this feature is available for preview on the [Insiders](https://code.visualstudio.com/insiders/) release.

In this milestone, we added synchronizing User Snippets and more UI State.

![Settings Sync configuration](images/1_44/sync-configure.png)

*Theme: GitHub Sharp with Customizations*

Currently the following UI State is synchronized:

* Display Language
* Activity Bar entries
* Panel entries
* Views layout and visibility
* Recently used commands
* "Do not show again" notifications

**Note:** At present, only user language snippets are synchronized. Support for global snippets will be added in the next milestone.

We also added remote and local sync backup views for restoring your data and to aid in trouble shooting. You can access these views using the commands **Preferences Sync: Show Remote Backup** and **Preferences Sync: Show Local Backup**.

![Settings Sync backups view](images/1_44/sync-backup-views.png)

*Theme: GitHub Sharp with Customizations*

To learn more, you can go to the [Settings Sync](https://aka.ms/vscode-settings-sync-help) documentation.

### Account management

Extensions that contribute an [Authentication Provider](https://github.com/microsoft/vscode/issues/88309) will now have their accounts appear on a new **Accounts** context menu above the settings gear. You can view all accounts you are currently signed into, sign out of them, and manage trusted extensions. Signing into a Microsoft account from here is another way to start Settings Sync.

![Accounts context menu](images/1_44/accounts_menu.png)

*Theme: Pop Light with Customizations*

### New JavaScript debugger

This month we've continued making progress on our new JavaScript debugger. It's installed by default on Insiders, and can be installed [from the Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.js-debug-nightly) in VS Code Stable. You can start using it with your existing launch configurations by enabling the `debug.javascript.usePreview` setting. Here are some new features added this month:

**Link handling in the debug terminal**

You could use the debug terminal (**Debug: Create JavaScript Debug Terminal**) to automatically debug any Node.js process. Now, the debugger also handles links to start debugging your browser whenever you `Cmd/Ctrl+Click` on links, no configuration needed.

![Animation showing clicking on a link in the debug terminal, Chrome opening, and hitting a breakpoint](images/1_44/js-debug-link-handler.gif)

*Theme: Earthsong, Font: Fira Code*

**Reference $returnValue in Watch and Debug Console**

When it's available in the call stack, you can now reference a function's `$returnValue` in the Debug Console and Watch expressions.

![Screenshot showing $returnValue being referenced in the Debug Console and Watch expression](images/1_44/js-debug-returnvalue.png)

### TypeScript 3.9 Beta support

This iteration we added support for the new [TypeScript 3.9 Beta features](https://devblogs.microsoft.com/typescript/announcing-typescript-3-9-beta). Some highlights of TypeScript 3.9 include:

* Suggestions for the `// @ts-expect-error` directive.
* Refactorings that better preserve newlines and formatting.
* Auto imports in JavaScript can now add common JS style imports (`require(...)`).

You can try the new TypeScript 3.9 features today by installing the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with the TypeScript 3.9 beta!

### Increased view placement flexibility

For several iterations, we have worked to improve the flexibility of our layout by allowing individual views to be moved around. With this iteration, we not only added features but also polished the experience.

**New Drag and Drop experience**

The dragging and dropping of Activity Bar icons, panels, and views within the Side Bar has been simple, but ambiguous. It could be difficult to know exactly what was going to happen when you actually dropped any of those items. The new experience aims to improve clarity and highlight the new features we are enabling along the way.

Below, the user begins by reordering views within the Side Bar, followed by reordering tabs in the Panel and icons in the Activity Bar:

![New Drag and Drop experience](images/1_44/dnd.gif)

**Multiple views in a single panel**

You can now have multiple views within a single panel to make better use of horizontal space. One common request is to see the terminal at the same time as the Problems view. This can now be done by dragging the terminal into the Problems view as shown below.

![Terminal and Problems view side by side](images/1_44/multipanel.gif)

**Creating new Side Bar groups and moving panels**

It is now possible to move views to new Side Bar groups, creating a new icon entry in the Activity Bar. The built-in panels are no exception and can now be moved into the existing Side Bar entries or their own entries.

The next video shows the user dragging the Terminal into the Activity Bar, which creates a Terminal icon and space for the Terminal in the Side Bar. Next, the user drops Output within the Terminal Side Bar. Finally, Outline is moved from the Explorer into the Activity Bar, creating its own Side Bar area.

![Dragging views into the Activity Bar](images/1_44/viewsinactivitybar.gif)

**Contributing a View Container to the panel**

We now have everything required to allow an extension to contribute a view directly to the panel, much like you can today with the Activity Bar, which is documented in the [Tree View API guide](https://code.visualstudio.com/api/extension-guides/tree-view#_view-container). Note that this feature is still in preview and the Tree View documentation will not reflect these changes until they are considered stable. However, to try it out, you can update the snippet in the documentation to this:

```json
"contributes": {
  "viewsContainers": {
    "panel": [
      {
        "id": "package-explorer",
        "title": "Package Explorer",
        "icon": "media/dep.svg"
      }
    ]
  }
}
```

**Commands for view movement**

Finally, there are new commands to move views with the keyboard and to reset your layout. The command **View: Move Focused View** (`workbench.action.moveFocusedView`) has been updated to support the new features of the iteration. The command **View: Reset Focused View Location** (`workbench.action.resetFocusedViewLocation`) was added to place an individual view back into its default location.

**Activity Bar icons for panel and custom views**

With the introduction of the flexible layout, there are now dedicated icons for the default panel views when they move into the Activity Bar. There is also a (default) icon for custom views when combining multiple views.

![Panel view icons](images/1_44/panel-view-icons.png)

### Synced Regions

We have improved the [mirror cursor](https://code.visualstudio.com/updates/v1_41#_html-mirror-cursor) feature introduced last November with a new implementation called Synced Regions. Currently this feature is available for HTML and you can try it out by one of the following ways:

* Running the command **On Type Rename Symbol** on an HTML tag (bound to `kb(editor.action.onTypeRename)` by default).
* Turning on the `editor.renameOnType` setting and move the cursor to an HTML tag.

![HTML rename on type](images/1_44/html-rename-on-type.gif)

The red regions are Synced Regions. As their name suggests, any change in one region will be synced to other regions. You can exit this mode by either moving your cursor out of the regions or pressing `ESC`. Additionally, typing or pasting any content leading with a whitespace in any region exits this mode.

We look forward to [providing an API](https://github.com/microsoft/vscode/issues/94316) that could make this rename-on-type experience available to other languages such as JSX, XML, or even local variables in TypeScript.

## Contributions to extensions

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.44 include:

* Dev Containers: Check out a PR directly into a container.
* Dev Containers: Kubernetes container attach support.
* Dev Containers: Experimental WSL 2 Docker engine support.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_44.md).

## Extension authoring

### Welcome View Content API

Certain views now support Welcome content contributed from extensions. Refer to the [original issue](https://github.com/microsoft/vscode/issues/89080) for more information. For details about how to use this API with extension provided views, you can read the [Tree View documentation](https://code.visualstudio.com/api/extension-guides/tree-view#_welcome-content).

This API was added as proposed in the last milestone and we have now finalized it.

### Semantic tokens provider API

The [semantic tokens provider](https://code.visualstudio.com/api/references/vscode-api#DocumentSemanticTokensProvider) API has now been finalized.

### Theme support for semantic tokens

Color themes can now write rules to color semantic tokens reported by language extensions like TypeScript.

```json
"semanticHighlighting": true,
"semanticTokenColors": {
    "variable.declaration.readonly:java": { "foreground": "#00ff00" "fontStyle": "bold" }
}
```

The rule above defines that all declarations of readonly variables in Java should be colored green and bold.

The [Semantic Highlighting Wiki Page](https://github.com/microsoft/vscode/wiki/Semantic-Highlighting-Overview) has more information.

### TypeScript semantic token improvements

More semantic token types are returned by the Typescript Language Server. Color themes can take advantage of these new token types for [styling](#theme-support-for-semantic-tokens).

* All symbols from the JavaScript default library get a `defaultLibrary` modifier.
* Function parameters that are callbacks are classified as `function`.

Read the [TypeScript Semantic Highlighting plugin README](https://github.com/aeschli/typescript-vscode-sh-plugin/blob/main/README.md) for an overview of all semantic token types and modifiers returned by the TypeScript language server.

### File system error code

We have added [FileSystemError#code](https://github.com/microsoft/vscode/blob/b91e67b58ce596a3a57bb4711ed22ae8a0b73ce0/src/vs/vscode.d.ts#L6285), which is a string identifying the error. When a file system error is created through any of its factory functions, then `code` is the name of that function, for example `FileSystemError.FileNotFound(msg).code === 'FileNotFound'`.

### Arguments for StatusBarItem.command

`StatusBarItem.command` now accepts a full `Command` object instead of just a command ID. This lets extensions pass arguments to the command when the Status bar item is activated.

### Custom text editors

With custom text editors, extensions can now replace VS Code's standard editor with a custom [webview](https://code.visualstudio.com/api/extension-guides/webview)-based view for specific text-based resources. Potential use cases include:

* Previewing assets, such as shaders or `.obj` files.
* Creating WYSIWYG editors for markup languages such as XAML.
* Providing alternative, interactive views of data files such as JSON or CSV.

The [custom editors documentation](https://code.visualstudio.com/api/extension-guides/custom-editors) covers how to use the new custom text editor API and how to make sure your new editor works well with VS Code. Also be sure to check out the [custom editors extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/custom-editor-sample).

![The custom editor example extension](images/1_44/custom-editors.png)

Next iteration, we're hoping to finalize the full custom editor proposal that extends custom editors to also support binary files.

### View-level progress indication

We've added a [new option](https://github.com/microsoft/vscode/blob/e8dbc5ea6092ba73ae70069d3e730f2cc56addff/src/vs/vscode.d.ts#L8216) to the `ProgressOptions.location` of the `window.withProgress` API to allow progress to be shown on a specific view. To use, set `ProgressOptions.location = { viewId: <view-id> };` where `<view-id>` specifies the ID of the view you'd like to show progress for. There is short video of this in action in the [view progress section](#view-progress-now-shown-on-the-view) above.

### Extensions view context menu when-clause context

We've added a new `extension` when-clause context to the Extension view's context menu, which contains the extension's identifier.

Example:

```json
  "extension/context": [
      {
          "command": "gitlens.showSettingsPage",
          "group": "2_configure",
          "when": "extension == eamodio.gitlens && extensionStatus == installed",
      }
  ]
```

### New Codicons

We've added the following icons to the [Codicon](https://code.visualstudio.com/api/references/icons-in-labels) library:

* account
* bell-dot
* debug-console
* library
* output
* run-all
* sync-ignored

![New codicons](images/1_44/new-codicons.png)

### Documentation for writing embedded language server

Writing language server for embedded programming languages is more involved than writing a plain language server. Given the popularity of embedded languages today, we added an [Embedded Languages](https://code.visualstudio.com/api/language-extensions/embedded-languages) topic to our Language Extensions section. The documentation contains two samples to illustrate the two approaches to building embedded language servers: Language services and Request forwarding. Start by reviewing the [Embedded Languages](https://code.visualstudio.com/api/language-extensions/embedded-languages) documentation or go directly to the two samples:

* [Language Server for Embedded Language with Language Services](https://github.com/microsoft/vscode-extension-samples/tree/main/lsp-embedded-language-service)
* [Language Server for Embedded Language with Request Forwarding](https://github.com/microsoft/vscode-extension-samples/tree/main/lsp-embedded-request-forwarding)

## Debug Adapter Protocol

### New progress events

We've finalized support for reporting progress in the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol/). Debug adapters can now send progress events (`progressStart`, `progressUpdate`, and `progressEnd`) to the frontend client, in order to display progress for long running debug adapter operations. The `progressStart` event can be marked as cancellable to make the client present a cancellation UI and send an `cancel` request. In order to enable progress events in a supporting debug adapter, the frontend client needs to include the new **client** capability `supportsProgressReporting` in the `initialize` request.

More details can be found in the corresponding [DAP feature request](https://github.com/microsoft/debug-adapter-protocol/issues/92). VS Code supports progress events starting with this milestone.

### Clipboard context value for the "evaluate" request

DAP clients (frontends) use the `evaluate` request when copying variable and expression values to the clipboard. To help debug adapters detect this scenario, a new value `clipboard` has been added to the value set for the `context` argument that is passed to the `evaluate` request. To ensure backward compatibility, a client is only allowed to pass the new value if the debug adapter returns a `supportsClipboardContext` capability.

## Language Server Protocol

A new version (0.5.0) of the Language Server Index Format [specification](https://microsoft.github.io/language-server-protocol/specifications/lsif/0.5.0/specification/) has been published. The version supports linking reference results across project dumps using monikers. In addition, new versions of the LSP client (6.2.0-next.2) and server (6.2.0-next.2) libraries have been published.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Terminal link handlers

This new API allows extensions to intercept and handle links that are clicked in the terminal.

```ts
window.registerTerminalLinkHandler({
  handleLink((terminal, link) => {
    if (link === 'https://code.visualstudio.com') {
      window.showInformationMessage('Handled');
      return true;
    }
    return false;
  });
});
```

### Contribute to terminal environments

This new API allows extensions to change environment variables when the terminal is starting up.

```ts
const collection = window.getEnvironmentVariableCollection(true);
const separator = process.platform === 'win32' ? ';' : ':';
collection.prepend('PATH', `/foo${separator}`);
collection.replace('JAVA_HOME', '/bar');
```

These collections are extension and workspace-specific and VS Code handles how conflicts between multiple extensions are dealt with. They can optionally be persisted across window reloads and care was taken to ensure this is done in such a way that terminals created immediately after the window is loaded do not block on the extension host launching but instead use the last known version. These persisted collections can get removed by disposing of the collection or creating a new non-persistent collection, or when the extension is uninstalled.

The plan is to surface these environment modifications to the user through some indicator that allows updating "stale" terminal environments but this is still a work in progress. You can follow the discussion in [issue #46696](https://github.com/microsoft/vscode/issues/46696).

## Browser support

### Remote indicator

When using VS Code in a browser, there is now a remote indicator. This lets you know the remote environment you are connected to and allows extensions to add additional commands associated with the remote. With this change, the **Open in Desktop** link was removed from the Status bar and will move into the remote picker.

**Note:** VS Code currently does not support connecting to a different remote - such as SSH, a container, or WSL - from the browser.

![Web remote indicator](images/1_44/web-remote-indicator.png)

### Navigational links

A new menu location lets extensions running in the browser contribute links to the hamburger menu in the upper left.

![Web navigational links](images/1_44/web-navigational-links.png)

## Engineering

### VS Code compiles to ES6

VS Code has adopted ES6 - all the way. Thanks to TypeScript, we have been using ES6 syntax for a long time, but we now assume that the target runtime also supports ES6. This allows us to use ES6 "globals" like `Strings#endsWith` and allows the use of new language features, like generator-functions and symbols.

### Automating issue triage with GitHub Actions

This iteration we migrated our existing triage automations from using GitHub Apps over to [GitHub Actions](https://github.com/features/actions). With this came some nice benefits, such as getting logs in the [vscode repo](https://github.com/microsoft/vscode/actions), and an increase in development speed. We additionally added several new automations, such as an Action that notifies issue authors when fixes get pulled into [VS Code Insiders](https://code.visualstudio.com/insiders/), allowing authors to quickly verify whether fixes are effective.

The full set of Actions we use is released on [GitHub](https://github.com/microsoft/vscode-github-triage-actions) under an MIT license.

## Documentation and extensions

### Python

There are two new Python tutorials:

* [Python in a container](https://code.visualstudio.com/docs/containers/quickstart-python) - Learn how to build a Python application in a Docker container.
* [Python for Data Science](https://code.visualstudio.com/docs/datascience/data-science-tutorial) - Use Python data science libraries to create a machine learning model.

### C++

The [C++ extension tutorials](https://code.visualstudio.com/docs/cpp/config-mingw) have been rewritten and there is a new tutorial specific to [Using C++ on Linux](https://code.visualstudio.com/docs/cpp/config-linux).

### Docker extension

The 1.0 version of the Microsoft [Docker](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker) extension is now available. The extension can add Docker files to your project, build and debug Docker images, and includes an Explorer to easily start, stop, inspect, and remove containers and images.

![Docker extension](images/1_44/docker-extension.png)

### Azure extensions

There are two new extensions for working with Azure assets directly from within VS Code.

* [Azure Virtual Machines](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurevirtualmachines) - Create Ubuntu Virtual Machines with pre-configured SSH access so you can connect to them using the [Remote-SSH](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh) extension.

* [Azure Resource Groups](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureresourcegroups) - View all of your Azure Resources and quickly navigate to them in the Azure View.

![Azure Virtual Machines extension](images/1_44/azure-virtual-machines.png)

## Notable fixes

* [75932](https://github.com/microsoft/vscode/issues/75932): Debug failed to load: Process picker failed (your 131072x1 screen size is bogus, expect trouble)
* [84271](https://github.com/microsoft/vscode/issues/84271): Add "x" to remove a file from recently opened (quick pick)
* [89658](https://github.com/microsoft/vscode/issues/89658): No debug adapter found
* [92381](https://github.com/microsoft/vscode/issues/92381): "Add folder to workspace" prompt duplicates itself if you add and then remove a folder from the workspace repeatedly
* [93344](https://github.com/microsoft/vscode/issues/93344): Welcome view should scroll
* [93634](https://github.com/microsoft/vscode/issues/93634): Progress: let window progress turn into silent notification progress

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [José Vilmar Estácio de Souza (@jvesouza)](https://github.com/jvesouza)
* [joanmarie (@joanmarie)](https://github.com/joanmarie)

Contributions to `vscode`:

* [Andrew Branch (@andrewbranch)](https://github.com/andrewbranch): [typescript-language-features] Add importModuleSpecifierEnding preference [PR #90405](https://github.com/microsoft/vscode/pull/90405)
* [Andy Barron (@AndyBarron)](https://github.com/AndyBarron): editor.codeActionsOnSave: support both object and array configs [PR #92681](https://github.com/microsoft/vscode/pull/92681)
* [Benny Neugebauer (@bennyn)](https://github.com/bennyn): fix typing for proxy auth window options [PR #91954](https://github.com/microsoft/vscode/pull/91954)
* [Benno (@blueworrybear)](https://github.com/blueworrybear): update #91296 allow multiple workspace roots [PR #92082](https://github.com/microsoft/vscode/pull/92082)
* [Charles Gagnon (@Charles-Gagnon)](https://github.com/Charles-Gagnon): Alert input box messages anytime shown [PR #92531](https://github.com/microsoft/vscode/pull/92531)
* [Gustavo Sampaio (@GustavoKatel)](https://github.com/GustavoKatel): Easy pick codelenses with keyboard shortcut [PR #91232](https://github.com/microsoft/vscode/pull/91232)
* [Huachao Mao (@Huachao)](https://github.com/Huachao): Fix typo of word extension [PR #93178](https://github.com/microsoft/vscode/pull/93178)
* [Hyun Sick Moon (@hyun6)](https://github.com/hyun6): fix typo [PR #93291](https://github.com/microsoft/vscode/pull/93291)
* [Ilia Pozdnyakov (@iliazeus)](https://github.com/iliazeus)
  * Add error code getter to FileSystemError #90517 [PR #90586](https://github.com/microsoft/vscode/pull/90586)
  * Add a default value to FileSystemError#code #90517 [PR #92427](https://github.com/microsoft/vscode/pull/92427)
* [@jaqra](https://github.com/jaqra): Fix search view 'Toggle Search Detail' padding [PR #91429](https://github.com/microsoft/vscode/pull/91429)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Show warning when filename start/end with whitespace in explorer inputbox [PR #92221](https://github.com/microsoft/vscode/pull/92221)
  * Fixes no link detection in SCM input box [PR #89868](https://github.com/microsoft/vscode/pull/89868)
  * Fixes webview inside diff editor doesn't gain focus [PR #91830](https://github.com/microsoft/vscode/pull/91830)
  * Fixes untitled editor name changes when double click on tab title container [PR #93165](https://github.com/microsoft/vscode/pull/93165)
* [Jonas Dellinger (@JohnnyCrazy)](https://github.com/JohnnyCrazy): Allow users to make the scrollbars in the title area larger [PR #92720](https://github.com/microsoft/vscode/pull/92720)
* [@nrayburn-tech](https://github.com/nrayburn-tech): Use ResizeObserver for monaco editor automaticLayout [PR #93630](https://github.com/microsoft/vscode/pull/93630)
* [Raul Piraces Alastuey (@piraces)](https://github.com/piraces): Fix history entries navigation taking extra navigates in debug console [PR #93125](https://github.com/microsoft/vscode/pull/93125)
* [Robert Rossmann (@robertrossmann)](https://github.com/robertrossmann): Inherit theme text colour in Settings Editor's section headings [PR #93605](https://github.com/microsoft/vscode/pull/93605)
* [Sharak (@SharakPL)](https://github.com/SharakPL): WebView width fix [PR #93085](https://github.com/microsoft/vscode/pull/93085)
* [Tobias Hernstig (@thernstig)](https://github.com/thernstig): Add .npmrc file association as ini-file [PR #92397](https://github.com/microsoft/vscode/pull/92397)
* [Matej Urbas (@urbas)](https://github.com/urbas)
  * fileSearch: removed unused `size` and redundant `basename` fields from IRawFileMatch [PR #91458](https://github.com/microsoft/vscode/pull/91458)
  * file search: include workspace folder in filter [PR #89765](https://github.com/microsoft/vscode/pull/89765)
* [Alvaro Videla (@videlalvaro)](https://github.com/videlalvaro)
  * fixes #92860 [PR #92862](https://github.com/microsoft/vscode/pull/92862)
  * fixes #92863 [PR #92865](https://github.com/microsoft/vscode/pull/92865)
* [David Teller (@Yoric)](https://github.com/Yoric): Fixes #91913 : Exclude **/.hg/store/** from the file watcher [PR #91941](https://github.com/microsoft/vscode/pull/91941)

Our accessibility community for providing constant feedback! To name a few:

* [Florian Bejers (@zersiax)](https://github.com/zersiax)
* [José Vilmar Estácio de Souza (@jvesouza)](https://github.com/jvesouza)
* [Andy Borka (@ajborka)](https://github.com/ajborka)
* [Dickson Tan (@Neurrone)](https://github.com/Neurrone)
* [Joan Marie (@joanmarie)](https://github.com/joanmarie)
* [Pawel Lurbanski (@pawelurbanski)](https://github.com/pawelurbanski)
* [Bryan Duarte (@RedEyeDragon)](https://github.com/RedEyeDragon)
* [Alex Hall (@mehgcap)](https://github.com/mehgcap)
* [Michał Zegan (@webczat)](https://github.com/webczat)
* [Nolan Darilek (@ndarilek)](https://github.com/ndarilek)
* [Alexander Epaneshnikov (@alex19EP)](https://github.com/alex19EP)
* [Marco Zehe (@MarcoZehe)](https://github.com/MarcoZehe)
* [Leonard de Ruijter (@leonardder)](https://github.com/leonardder)
* [Ivan Fetch (@ivanfetch)](https://github.com/ivanfetch)

Contributions to `vscode-vsce`:

* [Bob Brown (@bobbrow)](https://github.com/bobbrow): Change a console.warn to console.log for an informational message [PR #434](https://github.com/microsoft/vscode-vsce/pull/434)
* [James George (@jamesgeorge007)](https://github.com/jamesgeorge007)
  * Fixed markdown-it security vulnerability [PR #437](https://github.com/microsoft/vscode-vsce/pull/437)
  * Migrate to a better matching engine [PR #436](https://github.com/microsoft/vscode-vsce/pull/436)

Contributions to `language-server-protocol`:

* [Aleksey Kladov (@matklad)](https://github.com/matklad): Add link to section [PR #942](https://github.com/microsoft/language-server-protocol/pull/942)

Contributions to `vscode-languageserver-node`:

* [Remy Suen (@rcjsuen)](https://github.com/rcjsuen): Reword type and modifier descriptions [PR #588](https://github.com/microsoft/vscode-languageserver-node/pull/588)
* [Tom Raviv (@tomrav)](https://github.com/tomrav): Fix npm badges in text-document package readme [PR #592](https://github.com/microsoft/vscode-languageserver-node/pull/592)
* [Heejae Chang (@heejaechang)](https://github.com/heejaechang):
  * added file-based cancellation support to JSON-RPC [PR #587](https://github.com/microsoft/vscode-languageserver-node/pull/587)
  * added cancellation tests in json rpc [PR #596](https://github.com/microsoft/vscode-languageserver-node/pull/596)
  * re-export MessageConnection so that vscode-languageserver/client is s… [PR #598](https://github.com/microsoft/vscode-languageserver-node/pull/598)

Contributions to `vscode-generator-code`:

* [Sibiraj (@sibiraj-s)](https://github.com/sibiraj-s): Remove deprecated `useColors` mocha api [PR #196](https://github.com/microsoft/vscode-generator-code/pull/196)

Contributions to `vscode-loader`:

* [Roberto Araujo (@Roberto-Araujo)](https://github.com/Roberto-Araujo): Use head instead document.head [PR #26](https://github.com/microsoft/vscode-loader/pull/26)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 170 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Chinese (Simplified, China)** Tingting Yi, Yizhi Gu, Charles Dong, Justin Liu, Joel Yang, Tony Xia, 朱知阳, meng shao, 普鲁文, paul cheung, 张锐, Yiting Zhu, Nong Zhichao, Liam Kennedy, 武 健, Zhao Liguo, 宁 倬, Bochen Wang, 一斤瓜子, 顺 谭, 云 何, Yun Liu, yungkei fan.
* **Chinese (Traditional, Taiwan)** 船長, Winnie Lin, 予 恆.
* **Czech** David Knieradl.
* **Danish (Denmark)** Javad Shafique, Lasse Stilvang.
* **English (United Kingdom)** Martin Littlecott, Oren Recht, Faris Ansari.
* **Finnish (Finland)** Teemu Sirkiä.
* **French (France)** Antoine Griffard, Thierry DEMAN-BARCELÒ, Rodolphe NOEL, Nathan Bonnemains.
* **Hebrew (Israel)** Chayim Refael Friedman, Asaf Amitai.
* **Indonesian (Indonesia)** Gerry Surya, Laurensius Dede Suhardiman.
* **Italian (Italy)** Alessandro Alpi, Riccardo Cappello.
* **Japanese (Japan)** Ikko Ashimine, Aya Tokura, Takayuki Fuwa, ちゃん きさらぎ, 住吉 貴志, Koichi Makino, Yoshihisa Ozaki, TENMYO Masakazu.
* **Korean (Korea)** Kyunghee Ko, June Heo.
* **Norwegian (Norway)** Torbjørn Viem Ness.
* **Polish (Poland)** Makabeus Orban, Kacper Łakomski.
* **Portuguese (Brazil)** Alessandro Trovato, Marcelo Fernandes, Arthur Lima, Luciana de Melo, Luiz Gustavo Nunes.
* **Portuguese(Portugal)** Pedro Filipe.
* **Russian (Russia)** Andrey Veselov, Vadim Svitkin, Минаков Антон.
* **Spanish (Spain, International Sort)** Sifredo Da Silva, Ariel Costas Guerrero, David Roa, Abdón Rodríguez P., Luis Manuel, Carlos A. Echeverri V.
* **Swedish (Sweden)** Per Ragnar Edin.
* **Tamil (India)** krishnakoumar c.
* **Turkish (Türkiye)** Umut Can Alparslan, Mehmet Yönügül.
* **Ukrainian (Ukraine)** Nikita Potapenko.
* **Vietnamese (Vietnam)** Hieu Nguyen Trung.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
