---
Order: 103
TOCTitle: September 2024
PageTitle: Visual Studio Code September 2024
MetaDescription: Learn what is new in the Visual Studio Code September 2024 Release (1.94)
MetaSocialImage: 1_94/release-highlights.png
Date: 2024-10-3
DownloadVersion: 1.94.2
---
# September 2024 (version 1.94)

**Update 1.94.1**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22September+2024+Recovery+1%22+).

**Update 1.94.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22September+2024+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the September 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [Find in Explorer](#find-in-explorer) - Quickly find files in the Explorer view with the improved Find control.
* [Source Control Graph](#source-control) - More filtering options and interactivity in the Source Control Graph.
* [Python test coverage](#python) - Run Python tests with coverage and get rich results in the editor.
* [ESM](#esm-is-shipping-for-vs-code) - Faster VS Code startup thanks to the migration to ESM.
* [Account preference](#change-an-extensions-account-preference) - Specify which account to use for an extension.
* [Copilot in Native REPL](#inline-chat-and-completions-in-python-native-repl) - Get code completions and Inline Chat in the Native REPL.
* [Improved chat context](#drag-and-drop-files-to-add-chat-context) - Drag & drop files or use IntelliSense for more relevant chat context.
* [Test environment setup](#automated-test-setup-override) - Get help with setting up a test framework for your workspace.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## GitHub Copilot

### Switch language models in chat

Previously, we announced that you can [sign up for early access to OpenAI o1 models](https://github.com/o1-waitlist-signup). Once you have access, you will have a Copilot Chat model picker control in Copilot Chat in VS Code to choose which model version to use for your chat conversations.

![Copilot model picker control in the Chat view enables switching to another language model.](./images/1_94/copilot-model-picker.png)

### GPT-4o in Inline Chat

We've upgraded Copilot Inline Chat to GPT-4o, to give you faster, more accurate, and higher-quality code and explanations when you use Chat in the editor.

### Public code matching in chat

You can allow GitHub Copilot to return code that could match publicly available code on GitHub.com. When this functionality is enabled for your [organization subscription](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/setting-policies-for-copilot-in-your-organization/managing-policies-for-copilot-in-your-organization#policies-for-suggestion-matching) or [personal subscription](https://docs.github.com/en/copilot/managing-copilot/managing-copilot-as-an-individual-subscriber/managing-copilot-policies-as-an-individual-subscriber#enabling-or-disabling-suggestions-matching-public-code), Copilot code completions already provided you with details about the matches that were detected. We now show you these matches for public code in Copilot Chat as well.

If this is enabled for your organization or subscription, you might see a message at the end of the response with a **View matches** link. If you select the link, an editor opens that shows you the details of the matching code references with more details.

![Chat code referencing example.](images/1_94/code-references.png)

Get more information about [code referencing in GitHub Copilot](https://github.blog/news-insights/product-news/code-referencing-now-generally-available-in-github-copilot-and-with-microsoft-azure-ai/) on the GitHub Blog.

### File suggestions in chat

In chat input fields, you can now type `#<filename>` to get file name suggestions and quickly attach them to your prompt as context. This works in chat locations that support file attachments, such as the Chat view, Quick Chat, Inline Chat, and Notebook Chat.

<video src="images/1_94/chat-file-complete.mp4" title="File suggestions when typing #filename" autoplay loop controls muted></video>

### Improved file links in chat responses

We've improved the rendering of any workspace file paths that are mentioned in Copilot responses. These paths are very common when  you ask [`@workspace`](https://code.visualstudio.com/docs/copilot/copilot-chat#_chat-participants) questions.

The first thing you'll notice, is that paths to workspace files now include a file icon. This enables you to easily distinguish them in the chat response. The file icon is based on your current [file icon theme](https://code.visualstudio.com/docs/getstarted/themes#_file-icon-themes).

![Paths to workspace files in the response now render using file icons.](images/1_94/copilot-path-overview.png)

These paths are interactive links, so just select them to open the corresponding file. You can even use drag and drop to open the file in a new editor group, or insert it into a text editor by holding `kbstyle(Shift)` before dropping.

<video src="images/1_94/copilot-path-dnd.mp4" title="Dragging and dropping a workspace file from copilot into the editor" autoplay loop controls muted></video>

By default, these links only show the file name but you can hover over them to see the full file path.

![Hovering over a workspace path to see the full workspace path.](images/1_94/copilot-path-hover.png)

You can also right-click on one of these paths to open a context menu with additional commands, such as copying a relative path to the resource, or revealing the file in your operating system's file explorer.

![The context menu for a workspace path in chat provides options to open the file or copy its path.](images/1_94/copilot-path-right-click.png)

We plan to further improve workspace path rendering in the coming iterations, as well as make similar improvements to symbol names in responses.

### Drag and drop files to add chat context

You can now easily attach additional files as context for a chat prompt by dragging files or editor tabs from the workbench directly into chat. For Inline Cat, hold `kbstyle(Shift)` and drop a file to add it as context instead of opening it in the editor.

<video src="images/1_94/copilot-attach-dnd.mp4" title="Dragging files and editors into chat" autoplay loop controls muted></video>

### File attachments included in history

There are multiple ways to attach a file or editor selection as relevant context to your chat request. Previously, this context was added only for the current request and was not included in the history of follow-on requests. Now, these attachments are kept in history, so that you can keep referring to them without having to reattach them.

![Chat conversation shows that Copilot keeps track of attached files across multiple prompts.](images/1_94/file-attachment.png)

### Inline Chat and completions in Python native REPL

The native REPL editor, used by the Python extension, now supports Copilot Inline Chat and code completions directly in the input box.

<video src="images/1_94/copilot-in-REPL.mp4" title="Using Copilot Inline Chat in the native REPL editor" autoplay loop controls muted></video>

### Accept and run generated code in notebook

When you use Copilot Inline Chat to generate code in a notebook, you can now accept and directly run the generated code from Inline Chat.

<video src="images/1_94/notebook-accept-run.mp4" title="Accept and run generated code directly from Inline Chat" autoplay loop controls muted></video>

### Attach variables in notebook chat

When you use Copilot in a notebook, you can now attach variables from the Jupyter kernel in your requests. Adding variables gives you more precise control over the context for your chat request, so that you get more relevant responses from Copilot.

Either type `#`, followed by the variable name, or use the 📎 control (`kb(workbench.action.chat.attachContext)`) in Inline Chat to add a context variable.

<video src="images/1_94/notebook-kernel-variable.mp4" title="Attach a context variable by using `#` in a notebook chat request" autoplay loop controls muted></video>

### Refreshed chat user experience

We've refreshed the Chat view with a clean new welcome experience, and we've updated the layout of the chat input area. You can now use the `@` button to easily find the list of available chat participants and slash commands, both the built-in ones and chat participants from extensions that you've installed. You can also still find participants and slash commands by typing `/` or `@` in the chat input box.

![Updated Chat view welcome experience.](images/1_94/chat-welcome.png)

### Semantic search results (Preview)

**Setting**: `setting(github.copilot.chat.search.semanticTextResults)`

The Search view enables you to perform an exact search across your files. We have now added functionality to the Search view that uses Copilot to give search results that are semantically relevant.

<video controls src="./images/1_94/semantic-search-in-search-view.mp4" title="Semantic Search in Search View" autoplay loop controls muted></video>

This functionality is still in preview and by default, the setting is not enabled. Try it out and let us know what you think!

### Fix test failure (Preview)

**Setting**: `setting(github.copilot.chat.fixTestFailure.enabled)`

We've added specialized logic to help you to diagnose failing unit tests. This logic is triggered in some scenarios by the `/fix` slash command, and you can also invoke it directly with the `/fixTestFailure` slash command. The command is enabled in chat by default but can be disabled via the setting `setting(github.copilot.chat.fixTestFailure.enabled)`.

<h2 id="automated-test-setup-override"><div id="_automated-test-setup-override"/></h2>

### Automated test setup (Experimental)

**Setting**: `setting(github.copilot.chat.experimental.setupTests.enabled)`

We added an experimental `/setupTests` slash command that can help you configure the testing set up for your workspace. This command can recommend a testing framework, provide steps to set up and configure it, and suggest a VS Code extension to provide [testing integration in VS Code](https://code.visualstudio.com/docs/debugtest/testing). This can save you time and effort to get started with testing for your code.

When you use the `/tests` command to generate tests for your code, it can recommend `/setupTests` and testing extensions if looks like such an integration has not been set up yet in your workspace.

### Start debugging from Chat (Experimental)

**Setting**: `setting(github.copilot.chat.experimental.startDebugging.enabled)`

In this milestone, we made improvements to the experimental `/startDebugging` slash command. This command enables you to easily find or create a launch configuration and start [debugging](https://code.visualstudio.com/docs/debugtest/debugging) your application seamlessly. When you use `@vscode` in Copilot Chat, `/startDebugging` is now available by default.

![A user types /startDebugging flask app port 3000 in the panel chat and is provided with the launch configuration.](images/1_94/start-debugging.png)

### Chat in Command Center (Experimental)

**Setting**: `setting(chat.commandCenter.enabled)`

We are experimenting with a Command Center entry for accessing chat. It provides quick access to all relevant chat commands, like starting the different chat experiences or attaching context to your prompt. Note that the Command Center itself needs to be enabled for the chat Command Center entry to show.

![Chat Command Center button and the drop-down menu with relevant chat actions.](images/1_94/chat-command-center.png)

### Improved temporal context (Experimental)

**Setting**: `setting(github.copilot.chat.experimental.temporalContext.enabled)`

With temporal context, you can instruct Inline Chat to consider recently opened or edited files as part of the chat context. We have improved this feature and invite everyone to give it a go.

### Custom instructions (Experimental)

**Setting**: `setting(github.copilot.chat.experimental.codeGeneration.useInstructionFiles)`

**Setting**: `setting(github.copilot.chat.experimental.testGeneration.instructions)`

Last milestone, we introduced custom [code-generation instructions](https://code.visualstudio.com/updates/v1_93#_code-generation-instructions). We've further expanded this functionality to define **shared instructions** for code generation in a `.github/copilot-instructions.md` file in your workspace. These common instructions supplement your own personal code generation instructions. Enable the code-generation instruction file with the `setting(github.copilot.chat.experimental.codeGeneration.useInstructionFiles)` setting.

In addition, you can now define instructions for **test generation** in settings or import them from a file. For example, if you always want to use a particular unit testing framework for your tests. Configure the test-generation instructions in the `setting(github.copilot.chat.experimental.testGeneration.instructions)` setting.

## Accessibility

### Getting started

Our **Help** menu now includes a **Get Started with Accessibility Features** walkthrough, which makes it easier for you to explore and utilize the accessibility options. The walkthrough introduces you to functionality, such as the accessibility help dialog, accessibility signals, keyboard shortcuts, and more.

![Get Started with Accessibility Features product walkthrough.](images/1_94/accessibility-walkthrough.png)

### Comment accessibility improvements

We have introduced an accessible view for comment thread controls. This view includes the relevant editor context, enabling you to stay focused without needing to switch between the editor and the accessible view. Likewise, the editor context is now provided in the accessible view for the comments panel.

We've also introduced the **Comments: Focus Comment on Current Line** command that lets you quickly move to the Comments control from the Editor by using the keyboard. There are also new actions to go to the next and previous commented ranges in the Editor: **Comments: Go to Next Commented Range** and **Comments: Go to Previous Commented Range**.

## Workbench

### Change an extension's account preference

In this iteration, we explored how to improve the experience of changing the preferred account for an extension. For example, if you have multiple GitHub accounts and you accidentally signed in to GitHub Copilot with the wrong account and now need to use the other one.

It's now possible to change that preference after the fact in multiple ways.

* **Account menu in the Activity Bar** > **&lt;Your Account&gt;** > **Manage Trusted Extensions** > select the gear icon for an extension

  ![Manage trusted extensions Quick Pick, with gear button highlighted.](images/1_94/accountPreferenceManageTrustedExtensions.png)

* **Extensions** view > context menu (or gear icon) on an extension that uses auth > select **Account Preferences**

  ![Account preferences option in the context menu of an extension.](images/1_94/accountPreferenceContextMenu.png)

* Extension detail view > gear icon > select **Account Preferences**

  ![Account preferences option in the gear menu of an extension.](images/1_94/accountPreferenceGear.png)

Choosing any of these options takes you to a Quick Pick where you can change the account that an extension uses.

![The account preference Quick Pick that enables you to select extensions for a given account.](images/1_94/accountPreferenceQuickPick.png)

When you change an extension's account preference, this sends an event to the extension and it is up to the extension to handle it properly. If you don't see the expected behavior, report an issue going for that extension, so the account preference experience can be handled.

Also, let us know if you have any feedback on this flow.

### View folders and workspaces associated with a profile

In this milestone, we introduced a **Folders & Workspaces** section in the Profiles editor. This section lists all folders and workspaces that are associated with a specific profile from a central place. From this section, you can add or modify folders, or open the folder or workspace in a new window.

![Folders & Workspaces section in the Profile editor.](images/1_94/profiles-editor-folders-workspaces.png)

### Update extensions across all profiles

In this milestone, we introduced the ability to update extensions across all profiles. This is useful if you have multiple profiles and you want to keep your extensions versions in sync. Previously, you had to switch to each profile and update the extensions for that profile.

### Warnings in the Extensions view

The Extensions view now shows a warning badge and associated information when there are any invalid extensions or extensions that are disabled due to version incompatibility.

![Extensions view shows a warning badge and description about the warning.](images/1_94/extensions-warning-ux.png)

### Find in Explorer

We’ve improved the Find feature in the Explorer view to make it easier to search for files in large projects. You can open the Find control in the File Explorer by using the `kb(list.find)` keyboard shortcut. While searching, you can switch between fuzzy matching and continuous matching for more flexible results.

Note that some context menu actions are temporarily disabled during searches. Stay tuned for more improvements coming soon!

<video src="images/1_94/file-explorer-find.mp4" title="Searching for files in explorer find" autoplay loop controls muted></video>

### Release notes

We have a simplified syntax for referring to settings in our release notes (`setting(setting.name)`), which also has the now-familiar settings-gear rendering when displayed in the release notes editor.

![Setting URL in release notes enables navigating to the Settings editor directly.](./images/1_92/setting-url-in-release-notes.gif)

## Editor

### Inlay hint improvements

We have added the `setting(editor.inlayHints.maximumLength)` setting, which controls after how many characters inlay hints are truncated.

We have also revised the updating strategy for inlay hints and now, while typing, they should update sooner but not cause any horizontal movements of your cursor.

### Experimental Edit Context

This milestone, we introduced a new experimental setting `setting(editor.experimentalEditContextEnabled)`. This setting enables the [EditContext API](https://www.w3.org/TR/edit-context/) to power the editing experience in VS Code. The adoption of the [EditContext API](https://www.w3.org/TR/edit-context/) has enabled us to fix certain IME composition bugs. Generally we believe it will improve the editing experience in the long term, and ultimately it will be enabled by default.

Make sure to reload your VS Code window after enabling this setting to take advantage of it.

## Source Control

### Source Control Graph view improvements

Last milestone, we added the new **Source Control Graph** view. This milestone, we have been working on expanding the functionality available in the newly added view as well as polishing the layout of the view.

#### Repository picker

When you open a folder/workspace that contains multiple repositories, the Source Control Graph view title shows a repository picker. By default, the Source Control Graph view shows the active repository, matching the information in the Status Bar. You can use the repository picker to *lock* the Source Control Graph view to a particular repository.

![Repository picker control in the title of the Source Control Graph view.](images/1_94/scm-repository-picker-button.png)

#### History item reference picker

This milestone, we have added a new history item reference picker to the Source Control Graph view title. You can use this reference picker to filter the history items shown in the graph to a different branch, or to view multiple branches.

![History item reference Quick Pick control to choose one or more items.](images/1_94/scm-reference-quick-pick.png)

By default the history item reference picker is set to `Auto`, which renders the graph for the current history item reference, its remote, and an optional base.

![History item reference picker control in the title of the Source Control Graph view.](images/1_94/scm-reference-picker-button.png)

#### History item actions

This milestone, we have expanded the list of actions that are available in the context menu for source control history items. We have added actions to create a new branch/tag from a history item, cherry-pick a history item, and checkout (detached) an item.

![Context menu for items in the Source Control Graph view.](images/1_94/scm-context-menu.png)

#### Source Control Graph settings

This milestone, we have added a set of new settings, so that you can customize the graph:

* `setting(scm.graph.badges)` - controls which badges are shown in the Source Control Graph view
* `setting(scm.graph.pageOnScroll)` - controls whether the Source Control Graph view loads the next page of items when you scroll to the end of the list
* `setting(scm.graph.pageSize)` - the default number of items to show in the Source Control Graph view and when loading more items

## Notebooks

### Multi cursor support across cells (preview)

The Notebook editor now supports multi-cursor editing between cells with the setting `setting(notebook.multiCursor.enabled:true)`. Currently, this can only be triggered with the shortcut `kbstyle(Ctrl+D)` and supports core editor actions alongside a limited subset of editor commands.

<video src="images/1_94/notebook-multi-cursor.mp4" title="Notebook multi cursor demo" autoplay loop controls muted></video>

### Diff editor shows document metadata changes

The notebook diff editor now also shows changes made to the document metadata, such as kernel information and cell language.

![Notebook dif editor showing side-by-side changes to the document metadata.](./images/1_94/notebook-diff-document-metadata.png)

### Collapse unchanged regions in diff view

The notebook diff view now respects the setting `setting(diffEditor.hideUnchangedRegions.enabled)`. When enabled, unchanged code blocks are collapsed by default, which makes reviewing changes in large notebooks easier.

![Diff editor shows unchanged code blocks as collapsed.](./images/1_94/notebook-unchanged-region.png)

### Notebook serialization in web worker (Experimental)

This release introduces an experimental feature that enables notebook serialization in a web worker. This can help reduce main thread blocking time in the Extension Host process when you work with large notebooks. By default, this feature is disabled but can be enabled by setting `setting(ipynb.experimental.serialization:true)` to `true`.

## Debug

### Support for data colorization

VS Code supports new text styling capabilities from the Debug Adapter Protocol. This enables data in the Variables view, Watch view, hovers, and Debug Console to be colorized via ANSI escape sequences.

### JavaScript Debugger

#### Improved display of HTML elements

We've improved how HTML elements are displayed in the JavaScript debugger. Previously, they were rendered as naive objects, which were hard to navigate. Now, they more closely reflect DOM structure, and we take advantage of new colorization capabilities to provide some basic syntax highlighting.

![HTML elements are colorized in the JavaScript Debug Console.](./images/1_94/js-debug-html.png)

#### Autocomplete of Node commands in launch configuration

There is a new autocompletion helper available in `launch.json` files for command-line applications that are installed in your `node_modules`. This makes it easier to set up debugging for tools like `vitest` or `nest`.

#### Cleaner Loaded Sources view

We changed how source paths are structured for Node.js built-in modules, evaluated scripts, and WebAssembly modules to make the **Loaded Sources** view less noisy and easier to browse.

## Languages

### TypeScript 5.6

Our JavaScript and TypeScript support now uses TypeScript 5.6. This major update includes a number of language and tooling improvements, along with important bug fixes and performance optimizations.

You can read all about the TypeScript 5.6 release [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-5-6/). We've also included a few tooling highlights in the following sections.

#### Detection of some common 'always true' programming mistakes

Say you're using a regular expression in JavaScript or TypeScript and write some code like this:

```typescript
const str = '...'
if (/\d+(\.\d+)?/) {
  ...
} else {
  ...
}
```

Uh oh! Looks like we've forgotten to call `.test()` on the regular expression, meaning that the `if` conditional always evaluates to true. That's not what we want.

Even though this problem is obvious when pointed out, mistakes like this are surprisingly easy to make and have even caused real bugs in VS Code! Thankfully, TypeScript now reports some of the most common 'always true' errors in your program. This includes testing an `if` conditional against a value that is never value, or a conditional expression where the one side is unreachable, such as `/abc/ ?? /xyz/`.

[Check out the TypeScript release notes](https://devblogs.microsoft.com/typescript/announcing-typescript-5-6/#disallowed-nullish-and-truthy-checks) for more examples and details about how this feature works.

#### Region prioritized diagnostics

Working in a very long JavaScript or TypeScript file? You should start seeing diagnostics for type error show up a little faster thanks to region prioritized diagnostics. This means that we try to get the diagnostics for the current visible code and show these first, even if the diagnostics for the rest of the file are still being computed.

This optimization is most relevant for complex files with thousands and thousands of lines. You may not notice any changes for smaller files.

#### Improved commit character for JavaScript and TypeScript

Commit characters can speed up coding by automatically accepting completions when typed. For example, in JavaScript and TypeScript, `.` is often considered a commit character. This means to type `myVariable.property.`, you can just type `myv`, `.`, `p`, `.` with the first `.` accepting the completion for `myVariable` and the second `.` accepting the completion for `property`.

These commit characters are now computed by TypeScript, which means that they can better take the program's structure into account. We can also continue improving our support for them over time.

Commit characters are enabled by default but can be disabled by setting `setting(editor.acceptSuggestionOnCommitCharacter: false)` to `false`.

#### Exclude Patterns for Auto-Imports

The new `autoImportSpecifierExcludeRegexes` lets you exclude [auto import](https://code.visualstudio.com/docs/typescript/typescript-editing#_auto-imports) from specific packages by using a regular expression. For example, to exclude auto imports from the subdirectory of a module like lodash, you can set:

```json
{
    "typescript.preferences.autoImportSpecifierExcludeRegexes": [
        "^lodash/.*$"
    ]
}
```

You can configure this by using `setting(javascript.preferences.autoImportSpecifierExcludeRegexes)` for JavaScript and `setting(typescript.preferences.autoImportSpecifierExcludeRegexes)` for TypeScript. For more details see the [TypeScript 5.6 release notes](https://devblogs.microsoft.com/typescript/announcing-typescript-5-6/#exclude-patterns-for-auto-imports)

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* Attach to a Kubernetes container over SSH/Tunnel
* Manually specify GPU availability

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_94.md).

## Contributions to extensions

### Python

#### Run tests with coverage

You can now run Python tests with coverage in VS Code! To run tests with coverage, select the coverage run icon in the Test Explorer or “Run with coverage” from any menu you normally trigger test runs from. The Python extension will run coverage by using the [`pytest-cov`](https://pypi.org/project/pytest-cov/) plugin if you are using pytest, or with [`coverage.py`](http://coverage.py/) for unittest.

Once the coverage run is complete, lines are highlighted in the editor for line level coverage. These can be closed and re-opened via the Run Results panel in the bottom, where it says "Close Test Coverage" or "View Test Coverage" under the most recent test run. Additionally, a Test Coverage tab appears below the Testing tab in the Test Explorer, also with a beaker icon, which you can also navigate to with **Testing: Focus on Test Coverage View** in Command Palette (`kb(workbench.action.showCommands)`). On this panel you can view line and branch coverage metrics for each file and folder in your workspace.

For more information on running Python tests with coverage, see our [Python documentation](https://code.visualstudio.com/docs/python/testing#_run-tests-with-coverage). For general information on test coverage, see VS Code's [Test Coverage documentation](https://code.visualstudio.com/docs/debugtest/testing#_test-coverage).

#### Python default problem matcher

The Python extension now includes a default problem matcher, simplifying issue tracking in your Python code and offering more contextual feedback. To integrate it, add `"problemMatcher": "$python"` to your tasks in `task.json`. A problem matcher scans the task's output for errors and warnings and displays them in the Problems panel, enhancing your development workflow.

Below is an example of a `task.json` file that uses the default problem matcher for Python:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Run Python",
            "type": "shell",
            "command": "${command:python.interpreterPath}",
            "args": [
                "${file}"
            ],
            "problemMatcher": "$python"
        }
    ]
}
```

#### Shell integration in Python terminal REPL

The Python extension now includes a setting to opt in and out of `PYTHONSTARTUP` script, which runs before you type `python` or any other way to launch the Python REPL in the terminal. If you opt in, you can use features from terminal shell integrations, such as command decorations, re-run command, run recent commands, if they are in Mac or Linux. You can enable this via setting `setting(python.terminal.shellIntegration.enabled:true)`.

#### Pylance Language Server Mode

There's a new setting `setting(python.analysis.languageServerMode)` that enables you to choose between our current IntelliSense experience or a lightweight one that is optimized for performance.

If you don't require the full breadth of IntelliSense capabilities and prefer Pylance to be as resource-friendly as possible, you can set `setting(python.analysis.languageServerMode)` to `light`. Otherwise, to continue with the experience you have with Pylance today, you can set it to `default`.

This new functionality overrides the default values of the following settings:

| Setting                           | `light` mode   | `default` mode   |
| :----------------------------- | :--------- | :--------- |
| "python.analysis.exclude"                 | ["**"]      | []         |
| "python.analysis.useLibraryCodeForTypes"    | false       | true       |
| "python.analysis.enablePytestSupport"       | false       | true       |
| "python.analysis.indexing"                 | false       | true       |

The settings above can still be changed individually to override the default values.

### GitHub Pull Requests

There has been more progress on the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which enables you to work on, create, and manage pull requests and issues. Review the [changelog for the 0.98.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0980) release of the extension to learn about the highlights.

## Extension Authoring

### Remove custom allocator in the desktop app

In this version, we have removed the custom allocator that was added in version 1.78 to the desktop application extension host. This custom allocator acts as a bridge for supporting V8 sandbox incompatible Node.js native addons that are built against the Electron runtime. You can refer to this [tracking issue](https://github.com/microsoft/vscode/issues/202385#issuecomment-2367174453) for additional context.

We have ensured that the top 5000 extensions are unaffected by this change. If your extension or a dependency of your extension is affected by this change, you can try the following remediation suggestions:

* If your extension uses [n-api](https://nodejs.org/docs/latest/api/n-api.html) then the status `napi_no_external_buffers_allowed` will be returned when using external array buffers. In which case, you can switch to use the copy version of the API [napi_create_buffer_copy](https://nodejs.org/docs/latest/api/n-api.html#napi_create_buffer_copy).
* If your extension uses [node-addon-api](https://github.com/nodejs/node-addon-api) then refer to this [document](https://github.com/nodejs/node-addon-api/blob/main/doc/external_buffer.md) for alternative API and compile time settings.
* If you want to avoid the performance cost from the copy, you can use the [V8 allocator](https://v8.github.io/api/head/classv8_1_1ArrayBuffer_1_1Allocator.html) to ensure that the buffer backing store is compatible with the V8 sandbox.

We have also added telemetry to identify extensions and native addons that might be affected, so we can proactively reach out to extension authors and offer help where possible. If your extension is effected, and none of the above suggestions work for you, comment in our [discussion thread](https://github.com/microsoft/vscode-discussions/discussions/1771) and we will gladly help.

## Debug Adapter Protocol

We formalized how text can be colorized and styled in the display of variables and output in the [Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol). Colorization works via ANSI control sequences and require that both the client and debug adapter `supportsANSIStyling` in their initialize request and capabilities, respectively.

## Preview Features

### Multiple GitHub accounts

It's now possible to be logged in to multiple GitHub accounts in VS Code at the same time.

This functionality is enabled by default in VS Code Insiders. In Stable builds of VS Code, you can turn this on with the `setting(github.experimental.multipleAccounts)` setting.

Here are a couple of scenarios in which you might need multiple accounts:

* Use *Account1* for Settings Sync and *Account2* for the GitHub Pull Request extension
* Use *Account1* for the GitHub extension (to push) and *Account2* for GitHub Copilot

To use this functionality, simply trigger a log in action (either with a built-in feature like Settings Sync or with an extension), and you'll be given the option to log in to a different account. This feature also pairs nicely with the new [Account Preference Quick Pick](#change-an-extensions-account-preference) should you need to change the account at a later stage.

While most things should just continue to work with your existing extensions, some behaviors might not play perfectly nice with this multi-account world just yet. If you think there's room for improvement, do open an issue on those extensions. With the help of the relatively new `vscode.authentication.getAccounts('github')` API, extensions have a lot of power to handle multiple accounts.

Next iteration, we are planning on turning this feature on by default for all users.

### MSAL-based Microsoft Authentication

We have been moving towards having our Microsoft Authentication stack use [MSAL (Microsoft Authentication Library)](http://github.com/AzureAD/microsoft-authentication-library-for-js). It's been a huge undertaking, but we have made great progress in this iteration. This work spans all VS Code clients, so that includes VS Code and [VS Code for the Web](https://vscode.dev).

* For vscode.dev, we have enabled the browser-based `MSAL.js` for all Microsoft authentication requests. In other words, vscode.dev is now entirely on MSAL.

* For VS Code, the desktop client, we have this feature behind a setting, `setting(microsoft.useMsal)`. It's behind a setting for now, as we plan on moving towards [the broker flow](https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/brokering.md), which would enable VS Code to use the authentication state of the operating system. So, to prevent as little interruption as possible, we will do that work first before enabling this widely. That said, if you're eager to try this new authentication, you are welcome to try and provide us feedback.

You can see the detailed status of this transition to MSAL across all of VS Code in [Issue #178740](https://github.com/microsoft/vscode/issues/178740).

### TypeScript 5.7

This release includes initial support for the upcoming TypeScript 5.7 release. Check out the [TypeScript 5.7 plan](https://github.com/microsoft/TypeScript/issues/59905) for details.

To start using preview builds of TypeScript 5.7, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

## Proposed APIs

### Tools for language models

We continue to iterate on our `LanguageModelTool` API. The API comes with two major parts:

1. The ability for extensions to register a *tool*. A tool is a piece of functionality that is meant to be used by language models. For example, reading the Git history of a file.

2. The mechanics for language models to support tools, such as extensions passing tools when making a request, language models requesting a tool invocation, and extensions communicating back the result of a tool invocation.

In this milestone, we added the ability for tools to request user confirmation before running. This is helpful for tools that may have some side-effect.

Take a look at [issue #213274](https://github.com/microsoft/vscode/issues/213274) for more details or to give us feedback.

> **Note**: The API is still under active development, and things will change.

## Engineering

### ESM is shipping for VS Code

We are finally shipping our ESM work in VS Code Stable releases. That means that all layers of VS Code core (electron, node.js, browser, workers) use the `import` and `export` syntax in JavaScript for module loading and exporting. All usages of our legacy AMD loader are disabled and will be removed as part of our debt week in October.

The move to ESM massively improves startup performance. For one, a lot of AMD overhead is removed, but then the main workbench bundle size is also reduced by more than 10%:

![Graph showing the trend of the main bundle load time, showing a large drop after introducing ESM.](images/1_94/esm.png)

Now that we are fully converted to ESM, we plan to improve our engineering system for VS Code. With ESM, a lot of modern tooling will work for us and we are very excited to share more details about this in the near future.

> **Note**: extensions are not impacted by this change and not loaded via ESM, please see [https://github.com/microsoft/vscode/issues/130367](https://github.com/microsoft/vscode/issues/130367) for details.

### Use NPM as default package manager

Beginning of September 2024, we have completed the [switch from yarn to npm](https://github.com/microsoft/vscode/issues/196795) for package management in the [microsoft/vscode](https://github.com/microsoft/vscode) repo. This decision was based on the specific requirements of VS Code and center around these criteria:

* Performance: we initially moved to yarn because of performance reasons and npm can now also meet our requirements
* Security: we make our supply chain more secure by limiting exposure and reducing the number of tools we depend upon

## Notable fixes

* [226401](https://github.com/microsoft/vscode/issues/226401) fileWatcher keeps consuming CPU at 200%+
* [10054](https://github.com/microsoft/vscode-remote-release/issues/10054) [WSL]: Ports tab incorrectly reports Ports in WSL being forwarded to local when `localhostForwarding = false`

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)

### Pull requests

Contributions to `vscode`:

* [@BABA983 (BABA)](https://github.com/BABA983)
  * fix terminal tab selection not work properly [PR #224394](https://github.com/microsoft/vscode/pull/224394)
  * Register fold import action [PR #227216](https://github.com/microsoft/vscode/pull/227216)
* [@BlackHole1 (Kevin Cui)](https://github.com/BlackHole1): ci: ensure retry logic consistency [PR #226038](https://github.com/microsoft/vscode/pull/226038)
* [@Cecil0o0 (hj)](https://github.com/Cecil0o0): chore: rm unreached ignore items when build extensions. [PR #227906](https://github.com/microsoft/vscode/pull/227906)
* [@dangerman (Anees Ahee)](https://github.com/dangerman): Fix Image Preview transparency grid scaling [PR #226505](https://github.com/microsoft/vscode/pull/226505)
* [@g-cappai (Gianluca Cappai)](https://github.com/g-cappai): Fix open html anchor link in markdown preview [PR #228633](https://github.com/microsoft/vscode/pull/228633)
* [@gabritto (Gabriela Araujo Britto)](https://github.com/gabritto): [typescript-language-features] Expandable hover [PR #228255](https://github.com/microsoft/vscode/pull/228255)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Add actions to preview editor tab's hover [PR #226023](https://github.com/microsoft/vscode/pull/226023)
  * Fix typo in workspace extension activation failure message [PR #227348](https://github.com/microsoft/vscode/pull/227348)
  * Correct tooltip capitalization of debug panel in status bar (fix #228088) [PR #228089](https://github.com/microsoft/vscode/pull/228089)
* [@henricryden](https://github.com/henricryden): additional search path for libc.so.6 in check-requirements-linux.sh [PR #227713](https://github.com/microsoft/vscode/pull/227713)
* [@janssen-tiobe (janssen)](https://github.com/janssen-tiobe): Fix: agressive URI encoding in table view of the problems panel [PR #224841](https://github.com/microsoft/vscode/pull/224841)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fix initial terminal dimensions are reload are incorrect [PR #225554](https://github.com/microsoft/vscode/pull/225554)
* [@jjaeggli (Jacob Jaeggli)](https://github.com/jjaeggli): Document focus suggest details with ctrl+alt+space [PR #190096](https://github.com/microsoft/vscode/pull/190096)
* [@juliapz (Julia Pozdeeva)](https://github.com/juliapz): Prevent find widget from being cropped in AUX window [PR #229001](https://github.com/microsoft/vscode/pull/229001)
* [@marrej (Marcus Revaj)](https://github.com/marrej): # Render file creation in the refactor preview [PR #226950](https://github.com/microsoft/vscode/pull/226950)
* [@nojaf (Florian Verdonck)](https://github.com/nojaf)
  * Use Worker to serialize Notebook [PR #226632](https://github.com/microsoft/vscode/pull/226632)
  * Include id in ErrorNoTelemetry message [PR #226715](https://github.com/microsoft/vscode/pull/226715)
* [@PhantomPower82](https://github.com/PhantomPower82): Polish getting started page (fix #226991) [PR #226994](https://github.com/microsoft/vscode/pull/226994)
* [@rafamerlin (Rafael Merlin)](https://github.com/rafamerlin): Make Inlay hint length configurable [PR #221276](https://github.com/microsoft/vscode/pull/221276)
* [@rehmsen (Ole)](https://github.com/rehmsen)
  * Initialize test variables in `setup` to avoid order dependency. [PR #226596](https://github.com/microsoft/vscode/pull/226596)
  * Support multiple comment widgets per notebook cell. [PR #226770](https://github.com/microsoft/vscode/pull/226770)
* [@remcohaszing (Remco Haszing)](https://github.com/remcohaszing): Add test-results.xml to .gitignore [PR #214238](https://github.com/microsoft/vscode/pull/214238)
* [@repraze (Thomas Dubosc)](https://github.com/repraze): fix: swap end for flex-end in browser/hover.css [PR #224102](https://github.com/microsoft/vscode/pull/224102)
* [@segevfiner (Segev Finer)](https://github.com/segevfiner): Adopt ext host restart for custom text editors [PR #225985](https://github.com/microsoft/vscode/pull/225985)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke): fix: memory leak in debug view [PR #225334](https://github.com/microsoft/vscode/pull/225334)
* [@sunnylost (sunnylost)](https://github.com/sunnylost): extensions: wrap table cell content in paragraph element [PR #228365](https://github.com/microsoft/vscode/pull/228365)
* [@tisilent (xiejialong)](https://github.com/tisilent): Add editor gpu rendering fallback [PR #228414](https://github.com/microsoft/vscode/pull/228414)
* [@tmm1 (Aman Karmani)](https://github.com/tmm1): Ignore tsserver requests for createDirectoryWatcher(~/Library) on macOS [PR #227653](https://github.com/microsoft/vscode/pull/227653)

Contributions to `vscode-docs`:

* [@0dinD (Odin Dahlström)](https://github.com/0dinD): Update supported Java versions [PR #7561](https://github.com/microsoft/vscode-docs/pull/7561)
* [@alexwkleung (Alex Leung)](https://github.com/alexwkleung): Update enter jupyter server url screenshot [PR #7584](https://github.com/microsoft/vscode-docs/pull/7584)
* [@DiskCrasher (Mike)](https://github.com/DiskCrasher): Fixed missing "!" in code [PR #7595](https://github.com/microsoft/vscode-docs/pull/7595)
* [@gaganshera (Gaganjot Singh)](https://github.com/gaganshera): Update prompt-crafting.md [PR #7555](https://github.com/microsoft/vscode-docs/pull/7555)
* [@harish-s-developer (Harish Srinivasan)](https://github.com/harish-s-developer): Added new Managing dependencies section, fixed one typo [PR #7617](https://github.com/microsoft/vscode-docs/pull/7617)
* [@harrydowning (Harry Downing)](https://github.com/harrydowning): Remove incorrect statement about pre release tags [PR #7593](https://github.com/microsoft/vscode-docs/pull/7593)
* [@listsarah (Sarah Listzwan)](https://github.com/listsarah): To Close Issue #7536: Update "Embedded Programming Languages" [PR #7539](https://github.com/microsoft/vscode-docs/pull/7539)
* [@mistymadonna (Misty Hays)](https://github.com/mistymadonna): Updated Azure Extensions homepage, Created new Getting Started page [PR #7520](https://github.com/microsoft/vscode-docs/pull/7520)
* [@muzimuzhi (Yukai Chou)](https://github.com/muzimuzhi): Typo, add missing inline code markup [PR #7589](https://github.com/microsoft/vscode-docs/pull/7589)
* [@partev](https://github.com/partev): fix a URL redirect [PR #7608](https://github.com/microsoft/vscode-docs/pull/7608)
* [@Sarke (Peter Stalman)](https://github.com/Sarke): Another Flatpak/KDE5 wallet workaround [PR #7606](https://github.com/microsoft/vscode-docs/pull/7606)
* [@seaniyer (Sean)](https://github.com/seaniyer): Update publishing-extension.md [PR #7540](https://github.com/microsoft/vscode-docs/pull/7540)
* [@vinistock (Vinicius Stock)](https://github.com/vinistock): Update Ruby links to point to new documentation [PR #7607](https://github.com/microsoft/vscode-docs/pull/7607)
* [@wjandrea (William Andrea)](https://github.com/wjandrea)
  * tasks.md: add `source` property to `problemMatcher` [PR #7493](https://github.com/microsoft/vscode-docs/pull/7493)
  * tasks.md: mention `problemMatcher.severity` [PR #7494](https://github.com/microsoft/vscode-docs/pull/7494)

Contributions to `vscode-extension-samples`:

* [@liu3hao (Weihao)](https://github.com/liu3hao): Add missing activation event [PR #1057](https://github.com/microsoft/vscode-extension-samples/pull/1057)

Contributions to `vscode-js-debug`:

* [@lucacasonato (Luca Casonato)](https://github.com/lucacasonato): fix: don't inject NODE_OPTIONS in Deno [PR #2080](https://github.com/microsoft/vscode-js-debug/pull/2080)

Contributions to `vscode-jupyter`:

* [@rchiodo (Rich Chiodo)](https://github.com/rchiodo): Make display_name changes also cause a kernel change event [PR #15967](https://github.com/microsoft/vscode-jupyter/pull/15967)

Contributions to `vscode-languageserver-node`:

* [@StellaHuang95 (Stella)](https://github.com/StellaHuang95): Support  `llmGenerated` property on `CodeAction` [PR #1557](https://github.com/microsoft/vscode-languageserver-node/pull/1557)

Contributions to `vscode-pull-request-github`:

* [@ixzhao](https://github.com/ixzhao): fix quote reply [PR #6230](https://github.com/microsoft/vscode-pull-request-github/pull/6230)

Contributions to `vscode-python-debugger`:

* [@rchiodo (Rich Chiodo)](https://github.com/rchiodo): Update debugpy info to latest shipped version [PR #462](https://github.com/microsoft/vscode-python-debugger/pull/462)

Contributions to `vscode-vsce`:

* [@mlasson (Marc Lasson)](https://github.com/mlasson): Fix typo in option hint [PR #1046](https://github.com/microsoft/vscode-vsce/pull/1046)

Contributions to `vscode-wasm`:

* [@mlugg (Matthew Lugg)](https://github.com/mlugg): Fix stream bugs [PR #196](https://github.com/microsoft/vscode-wasm/pull/196)

Contributions to `language-server-protocol`:

* [@aschaber1 (Alexander Schaber)](https://github.com/aschaber1): chore: fix typo from `kubernets` to `kubernetes` [PR #2013](https://github.com/microsoft/language-server-protocol/pull/2013)
* [@dawedawe (dawe)](https://github.com/dawedawe): fix typo in the documentation of CompletionParams [PR #2019](https://github.com/microsoft/language-server-protocol/pull/2019)
* [@didrikmunther (Didrik Munther)](https://github.com/didrikmunther): Fix misspelling in pullDiagnostics.md [PR #2022](https://github.com/microsoft/language-server-protocol/pull/2022)
* [@InSyncWithFoo (InSync)](https://github.com/InSyncWithFoo)
  * Fix a few typos in `percentage`'s doc comments [PR #2010](https://github.com/microsoft/language-server-protocol/pull/2010)
  * Add Taplo to implementor list [PR #2021](https://github.com/microsoft/language-server-protocol/pull/2021)
* [@SamB (Samuel Bronson)](https://github.com/SamB): Update servers.md: vscode-markdown-languageserver moved [PR #2012](https://github.com/microsoft/language-server-protocol/pull/2012)
* [@sh-cho (Seonghyeon Cho)](https://github.com/sh-cho): Add fluent-bit language server implementation [PR #2016](https://github.com/microsoft/language-server-protocol/pull/2016)
* [@StellaHuang95 (Stella)](https://github.com/StellaHuang95): Support  `llmGenerated` property on `CodeAction` [PR #2020](https://github.com/microsoft/language-server-protocol/pull/2020)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
