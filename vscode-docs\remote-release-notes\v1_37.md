# July 2019 (version 1.37)

## VS Code Remote Core

### Empty remote window presentation

When an empty remote window is opened (no folder, no workspace file) and the startup editor is not the Welcome page, we now show the projects view and the terminal. Use the **Open** button from the explorer to select a remote folder, or use the terminal to open files and folders.

### Install Local Extensions in Remote Server

There is a new command **Remote: Install Local Extensions in** when running remotely to select and install local extensions in the remote server.

![Install Local Extensions](images/1_37/install-local-extensions-remote.gif).

### Open in terminal/external terminal integration

The external terminal integration now uses the integrated terminal in remote, this includes the **Open in terminal** entry in the Explorer context menu as well as the **Open New Terminal** command.

### Remote settings

The Remote settings file now supports edit actions.

![Edit Remote Settings](images/1_37/remote-settings.png).

## WSL

### Experimental Alpine Linux support

The WSL extension now supports the Alpine WSL distribution on VS Code Insiders.

To use an Alpine distro:

- You need to be on Windows 10, May 2019 Update (1903).
- Install **Alpine WSL** from the Microsoft Store.
- Install the latest WSL extension in a [VS Code Insiders](https://code.visualstudio.com/insiders/) build.
- The WSL extension will prompt you to install `libstdc++` inside Alpine. To do that, open an Alpine WSL shell and run `su -c 'apk update && apk add libstdc++'`.

To bring up the WSL window (instance) either:

- Run `code-insiders .` from the WSL shell.
- Invoke the **WSL: New Window using Distro** command and pick **Alpine**.

![Running the Alpine distribution](images/1_37/alpine.png)

**Note**: We are holding this back from the Stable release to allow extension authors to catch up with the additional platform. See [Supporting Remote Development](https://code.visualstudio.com/api/advanced-topics/remote-extensions) for details.

## SSH

### SSH Agent forwarding

OpenSSH supports a configuration option, `ForwardAgent`, which allows your local SSH agent to be accessed from the host that you have connected to. This allows you to use the local SSH keys stored in the agent from the host. For example, you can use this to clone a private Git repository without having to copy your SSH keys to the host. This is now supported by the Remote - SSH extension.

If you haven't set up your SSH agent, see [our SSH Agent documentation](https://code.visualstudio.com/docs/remote/troubleshooting#_setting-up-the-ssh-agent) first. Then, set the experimental setting to enable it in the SSH extension: `"remote.SSH.enableAgentForwarding": true`. And in your SSH config, add `ForwardAgent yes`. You can then test it by running `ssh-add -l` from the host, it should print the same list of SSH keys that you see when running that command in a local terminal.

#### Note about reconnection

If you lose your internet connection during a remote session, VS Code has to reconnect to the host. If you had any terminals open during reconnection, they will have a stale value for the `$SSH_AUTH_SOCK` environment variable and won't be able to access your local SSH agent. You can refresh the value of this variable by running this command: ```SSH_AUTH_SOCK=`cat $SSH_AUTH_SOCK_LOCATION` ```. New terminals opened after reconnection should already be set up correctly. We are working on improving this!

### Improved password authentication

If you are using password-based authentication for your SSH remote, then currently you have to enter your password twice for each window, each reconnection, and again if you want to forward a port. We are experimenting with a new way to set up the SSH connection that will only require one password when the window opens, and no extra password entry to forward a port. To try it out, set `"remote.SSH.enableDynamicForwarding": true` and report any issues that you see. This mode is also a little faster connecting, even if you aren't using password-based authentication.

And we still recommend [setting up key-based authentication](https://code.visualstudio.com/docs/remote/troubleshooting#_configuring-key-based-authentication) for the best experience.

### Hosts using distributed filesystems

Some users have reported issues with installing the remote server on hosts that mount a home directory from a distributed filesystem like NFS or AFS. To work around this, we've added a setting to keep the installation lockfiles in `/tmp` instead of in the server install directory. Set `"remote.SSH.lockfilesInTmp": true` to use this.
