# August 2022 (version 1.71)

## Development Containers (Dev Containers version 0.251.x)

### Environment variable default values

When using environment variables in a `devcontainer.json` file, you can now include a default value that will be used when the environment variable is not set:

```json
{
    "image": "ubuntu:latest",
    "remoteEnv": {
        "MY_LOCAL_VAR": "${localEnv:MY_VAR:my_local_default}",
        "MY_CONTAINER_VAR": "${containerEnv:MY_VAR:my_container_default}"
    }
}
```

### Forward built-in CLI to OSS CLI

The built-in CLI `devcontainer` (or `devcontainer-insiders` for VS Code Insiders) of the Dev Containers extension now forwards to the built-in open source CLI. The `open` command and the previous CLI arguments for the `build` command are also supported.

### Features and images

As part of the [dev container specification](https://containers.dev), we recently open sourced two new repos, where we host a specific set of features and images that were previously in the [vscode-dev-containers repository](https://github.com/microsoft/vscode-dev-containers/issues/1589):

* [Development Container Features](https://github.com/devcontainers/features)
* [Development Containers Images](https://github.com/devcontainers/images)

You could always publish your own dev container image, and now you can publish Features as well! To get started, you can use this [Features template repository](https://github.com/devcontainers/feature-template) to create a set of Features. It includes a GitHub Actions workflow to automate the publishing process.

If you'd like your contributions to appear in the VS Code or GitHub Codespaces UI for dev container creation, you can do the following:

* Go to [devcontainers.github.io](https://github.com/devcontainers/devcontainers.github.io)
* Open a PR to modify the [collection-index.yml](https://github.com/devcontainers/devcontainers.github.io/blob/gh-pages/_data/collection-index.yml) file.

We expect the general model for distributing templates to be the same as Features - so stay tuned.

For more details on the process, you can review the [announcement issue](https://github.com/microsoft/vscode-dev-containers/issues/1589).
