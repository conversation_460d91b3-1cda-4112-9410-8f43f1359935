# January 2024 (version 1.86)

## Core

## Dev Containers

### Create devcontainer.json in user data folder

When using a dev container with a repository that doesn't have a `devcontainer.json` checked in yet, you can now create a `devcontainer.json` in your user data folder to avoid having an outgoing change. The **Dev Containers: Add Development Container Configuration Files...** command now asks whether you want to create the file in the repository or in your user data folder:

![Choose between user data and workspace folder](images/1_86/config-in-user-data.png)

When you later want to add the `devcontainer.json` to the repository, then the **Dev Containers: Add Development Container Configuration Files...** command will ask if you want to use the existing configuration from your user data folder:

![Choose to copy config from user data to workspace folder](images/1_86/move-config-from-user-data.png)

### Build options when using a Dockerfile

When using a Dockerfile to build the container image, you can now specify build options in the `devcontainer.json`:

```json
{
    "name": "My Dev Container",
    "build": {
        "dockerfile": "Dockerfile",
        "options": [
            "--network=host",
        ]
    }
}
```

These options will be passed to the `docker build` command.
