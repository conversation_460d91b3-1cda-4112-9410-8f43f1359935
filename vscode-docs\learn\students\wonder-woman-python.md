---
Order: 3
Area: students
TOCTitle: Wonder Woman Lessons
ContentId: 59288bcc-97b7-411e-86da-c022a00e25ec
PageTitle: Get Started Tutorial for Wonder Woman project in Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: A Wonder Woman project tutorial using the Python extension in Visual Studio Code.
---
# Learn Python with <PERSON> Woman

Check out these WONDER WOMAN 1984 inspired lessons built to help you write your first lines of Python code. In the first module, learn how to crack a Caesar Cipher code that reveals a secret message with a location. In the second module, create a personality quiz to determine which character you're most like!

Get a glimpse into the Python programming language with this introductory learning path that requires no prior background.

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/python-partnership/?WT.mc_id=python-0000-cxa"><h2 class="title faux-h3">Learn Python with Wonder Woman</h2></a>
    </div>
    <p class="description">Build a message decoder program and a personality quiz in these lessons inspired by the WONDER WOMAN 1984 film.</p>
    <a href="https://learn.microsoft.com/training/paths/python-partnership/?WT.mc_id=python-0000-cxa" title="Wonder Woman module">
        <img src="/assets/learn/students/wonder-woman-python/ww-lesson.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>

For a video walkthrough of the first lesson, you can follow along with this:

<iframe src="https://www.youtube-nocookie.com/embed/VH_mU42lQkQ" frameborder="0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="Crack the code and reveal a secret with Python and Visual Studio Code"></iframe>
