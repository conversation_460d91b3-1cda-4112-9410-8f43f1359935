trigger:
  batch: true
  branches:
    include:
      - main
  paths:
    exclude:
    - .config
    - .devcontainer
    - .pipelines
    - .vscode
pr: none

resources:
  repositories:
    - repository: 1esPipelines
      type: git
      name: 1ESPipelineTemplates/1ESPipelineTemplates
      ref: refs/tags/release

  pipelines:
    - pipeline: vscode-website-prod
      source: vscode-website (official)
      branch: release/prod
      trigger: true

variables:
  Codeql.Enabled: true
  Codeql.SourceRoot: $(Pipeline.Workspace)/vscode-website
  Codeql.TSAEnabled: true
  Codeql.TSAOptionsPath: $(Build.SourcesDirectory)/.config/tsaoptions.json

extends:
  template: v1/1ES.Official.PipelineTemplate.yml@1esPipelines
  parameters:
    sdl:
      credscan:
        suppressionsFile: $(Build.SourcesDirectory)/.config/CredScanSuppressions.json
      git:
        fetchDepth: 1
        lfs: true
        retryCount: 3
      policheck:
        break: true
      sourceAnalysisPool: 1es-windows-2022-x64
      tsa:
        enabled: true

    stages:
    - template: templates/stages/build-main.yml@self
