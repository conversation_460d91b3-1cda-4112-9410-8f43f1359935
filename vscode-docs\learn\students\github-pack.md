---
Order:
Area: students
TOCTitle: GitHub in VS Code
ContentId: 683595d8-cf1f-4b24-a206-317b0855f284
PageTitle: Get Started with GitHub in Visual Studio Code
DateApproved: 1/7/2021
MetaDescription: Overview of resources for using GitHub in Visual Studio Code.
---
# GitHub Student Developer Pack

Welcome to Visual Studio Code! VS Code is a free coding editor that helps you start coding quickly. Use it to code in any programming language, without switching editors. VS Code comes with built-in source control, so you can compare versions of your code side-by-side and save your work over time by backing it up on [GitHub](https://github.com). We have resources specifically created for students and educators, including our [Java and .NET Coding Packs](/learntocode), programming lessons built in partnership with [NASA](/learn/students/nasa-python.md) and [Netflix](/learn/students/spacejam-python.md), and [videos](/learn/get-started/basics.md) to quickly get up to speed.

![GitHub Classroom + VS Code](images/github-classroom/banner.png)

To get started with VS Code and learn how to best use GitHub, check out the resources below:

## Lessons

[Introduction to GitHub in VS Code](https://learn.microsoft.com/training/modules/introduction-to-github-visual-studio-code)

In this 20-minute tutorial, you'll learn how to search GitHub for repositories, clone them, and publish your own projects onto GitHub right from VS Code.

[![Learn module for Introduction to GitHub](images/github-classroom/learn-1.png)](https://learn.microsoft.com/training/modules/introduction-to-github-visual-studio-code)

[Introduction to Git](https://learn.microsoft.com/training/modules/intro-to-git)

In this module, learn the fundamentals of version control systems like Git.

[![Learn module for Introduction to Git](images/github-classroom/learn-2.png)](https://learn.microsoft.com/training/modules/intro-to-git)

## Tutorials

[Build a Node.js app using GitHub and Azure](https://www.youtube.com/watch?v=Myc1T4n6wn0)

<iframe src="https://www.youtube-nocookie.com/embed/Myc1T4n6wn0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="End to end app development using Visual Studio Code"></iframe>

In this video, you'll see how GitHub is used when building a project. After completing this one hour tutorial and you'll end up creating a Node.js app, hosting it on GitHub, and deploying it to the cloud.

## Helpful extensions

[GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github)

With this extension, you can quickly create pull requests and review other pull requests or issues, right from VS Code. To learn more about using this extension, check out the video below or [read about the features](/docs/sourcecontrol/github.md).

<iframe src="https://www.youtube-nocookie.com/embed/T6sW1Dk9B4E" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="What every GitHub user should know about VS Code"></iframe>

---

[GitLens](https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens)

The GitLens extension enhances the built-in Git features in VS Code, like showing you when lines of code were written, by whom, and in what commit. To learn more, check out this helpful [video](https://www.youtube.com/watch?v=C6wMNoe78oc).

<iframe src="https://www.youtube-nocookie.com/embed/bRdQw4-sGIY" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="VS Code tips: Viewing file history with GitLens"></iframe>
