---
ContentId: 52eaec33-21c6-410c-8e10-1ee3658a854f
MetaDescription: devcontainer.json reference
DateApproved: 05/08/2025
---
# devcontainer.json reference

**TL;DR - This document has moved to the [Development Containers Specification](https://containers.dev/implementors/json_reference) site! This page now exists as a redirect to the specification site, which contains the latest information.**

## Dev container specification

As mentioned in the main [Dev Containers documentation](/docs/devcontainers/containers.md), we're creating the **Development Containers Specification** to empower anyone in any tool to configure a consistent dev environment.

The `devcontainer.json` reference is [hosted on the specification site](https://containers.dev/implementors/json_reference). Here you can also review the [devcontainer.json schema](https://containers.dev/implementors/json_schema).

You can also review the specification documents and latest proposals in the [devcontainers/spec](https://github.com/devcontainers/spec/tree/main/docs/specs) repository.

## Additional resources

[Create a development container](/docs/devcontainers/create-dev-container.md) has more information on configuring a dev container, or you can use the **Dev Containers: Add Dev Container Configuration Files...** or **Codespaces: Add Dev Container Configuration Files...** commands from the Command Palette (`kbstyle(F1)`) to add a wide variety of base configurations.

> **Tip:** If you've already built a container and connected to it, be sure to run **Dev Containers: Rebuild Container** or **Codespaces: Rebuild Container** from the Command Palette (`kbstyle(F1)`) to pick up any changes you make.
