# February 2020 (version 1.43)

## Visual Studio Code Remote Core

### Open remote workspaces via application links

VS Code can now open a remote workspace via protocol links.

The syntax is as follows:

```bash
vscode://vscode-remote/<remoteName>+<resolverArguments>/<path>
vscode-insiders://vscode-remote/<remoteName>+<resolverArguments>/<path>
```

### Recommendations for remote extensions

When opening a link to a remote workspace, but the corresponding remote extension is not yet installed, a recommendation makes it easy to do so:

![Remote extension recommendation](images/1_43/remote-recommendation.png)

## SSH

### Stabilized support for Windows hosts

We have had experimental support for Windows hosts in VS Code Insiders for a few months, but we are now ready to add this support to the VS Code Stable release!

### Support for macOS hosts

There is now support for connecting to macOS hosts. You don't need to do anything - when you connect to the host, VS Code will automatically detect which platform you are connecting to. And it is already available in the Stable version of VS Code.

### Connection sharing mode enabled by default

[A few months ago](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_39.md#ssh-connection-sharing), we added a new connection mode for Remote - SSH that creates a single SSH connection per host and can be shared between windows and across window reloads. Connection sharing reduces the time it takes to open a new window and the number of times you need to enter your password. It has been available behind a setting but this mode is now enabled by default.

## Containers

### New configuration properties

The `devContainer.json` file supports additional properties:

* `initializeCommand` can be used to run a command before building or connecting to a container. The property can be set to a string, which is run in a shell, or to an array of strings, which are run without a shell.

* `build` allows you to define the Docker [build target](https://docs.docker.com/engine/reference/commandline/build/#specifying-target-build-stage---target) and [build arguments](https://docs.docker.com/engine/reference/builder/#arg). It is now also possible to specify the `dockerfile` (note: all lowercase) and `context` properties inside the `build` section (the old location is still supported).

Here is an example:

```json
{
    "build": {
        "dockerfile": "../Dockerfile",
        "context": "..",
        "target": "development",
        "args": {
            "ARG1": "FOO",
            "ARG2": "BAR"
        }
    }
}
```

### Dotfiles repository settings

In the last iteration, we added support for [dotfiles repositories](https://github.com/microsoft/vscode-docs/blob/vnext/remote-release-notes/v1_42.md#dotfiles-repository-support). Dotfiles are a generally useful mechanism, and are not only used by VS Code Dev Containers. With that in mind, there are now general settings named `dotfiles.*`, which will eventually allow you to define `dotfiles` preferences with a single setting.

![General dotfiles settings](images/1_43/general-dotfiles-settings.png)

## WSL

### Docker in WSL

The Windows team has been busy adding Docker support to the Windows Subsystem for Linux (WSL) and you can learn how to work with Docker, VS Code, and WSL 2 from the [Using Docker in WSL 2](https://code.visualstudio.com/blogs/2020/03/02/docker-in-wsl2) blog post.
