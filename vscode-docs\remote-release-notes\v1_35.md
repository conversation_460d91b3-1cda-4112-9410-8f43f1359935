# May 2019 (version 1.35)

## Remote Development extensions available in VS Code Stable

Until now, the remote extensions have only been available when using the Insiders build of Visual Studio Code. Now all three remote extensions (SSH, WSL, and Containers) can be used with the Stable 1.35 build of VS Code.

## VS Code Remote Core

### Machine Scoped Settings

In a remote context, [machine scoped settings](https://code.visualstudio.com/updates/v1_34#_machinespecific-settings) can no longer be configured in the User Settings. They can now only be configured in the Remote/Workspace/Folder Settings. See also issue [#2](https://github.com/microsoft/vscode-remote-release/issues/2).

## Containers

### "settings" option

Container-specific settings can be added with the new `devcontainer.json` property `"settings"`. The object value of this property will be copied to the container after it is created.

```json
{
	"settings": {
		"python.pythonPath": "/usr/local/bin/python"
	}
}
```

### Support "docker.host" Setting

The [Docker extension](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker)  `"docker.host"` setting is now supported. When set to a Docker `host:port`, new containers will be created with that Docker host. Attaching to a container from the Docker explorer also works.

!["docker.host" Setting](images/1_35/docker-host-attach.png)

### "workspaceMount" option

The automatic mounting of a local project folder into the container can now be overridden with the `"workspaceMount"` option in `devcontainer.json`. It uses the same syntax as `docker`'s `--mount` option. An empty string as its value turns the automatic mount off. See the [Change the default source code mount](https://code.visualstudio.com/remote/advancedcontainers/change-default-source-mount) article for examples.

(This does not support the Docker Compose case where all mounts are controlled by the `docker-compose.yml` and no automatic mounting is done.)

### "workspaceFolder" option

The folder opened in a container by default can now be overridden with the `"workspaceFolder"` option in `devcontainer.json`.

### "Open Folder" Command Parameters

The `'remote-containers.openFolder'` command adds support for URI and options parameters:

```ts
interface IOpenFolderOptions {
	forceNewWindow?: boolean;
	forceReuseWindow?: boolean;
	noRecentEntry?: boolean;
}

async function openFolder(uri?: vscode.Uri, options?: IOpenFolderOptions);
```

## Notable Fixes
- [#11](https://github.com/microsoft/vscode-remote-release/issues/11): Extension packs are not installed on correct location
- [#17](https://github.com/microsoft/vscode-remote-release/issues/17): SSH can't connect to a machine with `/tmp` mounted with `noexec`
- [#19](https://github.com/microsoft/vscode-remote-release/issues/19), [#33](https://github.com/microsoft/vscode-remote-release/issues/33): SSH can't connect when some non-Bash shells are set as the default
- [#38](https://github.com/microsoft/vscode-remote-release/issues/38): Default shell not launched in remote
- [#437](https://github.com/microsoft/vscode-remote-release/issues/437): Remote server should discover the user's environment using their configured shell
