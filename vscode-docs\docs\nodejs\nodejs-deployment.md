---
ContentId: 856a4a73-a4b4-4418-b88d-1f65d0ba7824
MetaDescription: Node.js Deployment to Azure with Visual Studio Code
DateApproved: 05/08/2025
---
# Deploy Node.js Web Apps

The [Azure Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-node-azure-pack) extensions for Visual Studio Code make it easy to deploy Node.js applications.

![Azure Tools extension](images/azure/azure-tools.png)

## Deployment tutorials

These tutorials from Microsoft Learn describe different ways of creating and deploying Node.js apps to Azure via Visual Studio Code:

Tutorial | Description | Related Tools
--- | --- | ---
[Deploy Azure Functions](https://learn.microsoft.com/azure/developer/javascript/tutorial/azure-function-cosmos-db-mongo-api) | Build and manage Azure Functions serverless apps <br> directly in VS Code | [Azure Resources](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureresourcegroups) <br> [Azure Functions](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurefunctions) <br> [Azure Databases](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-cosmosdb)
[Deploy using Azure Portal <br> and Azure App Service](https://learn.microsoft.com/azure/app-service/tutorial-nodejs-mongodb-app) | Manage Azure resources directly in VS Code <br> with Azure App Service | [MongoDB](https://www.mongodb.com/docs/manual/installation/) <br> [Azure Portal](https://portal.azure.com/)
[Deploy using Docker](https://learn.microsoft.com/azure/developer/javascript/tutorial/tutorial-vscode-docker-node/tutorial-vscode-docker-node-01) | Deploy your website using a container | [Container Tools](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-containers) <br> [Azure CLI](https://learn.microsoft.com/cli/azure/install-azure-cli) <br> [Azure App Service](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureappservice) <br> [Azure Resources](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azureresourcegroups)
