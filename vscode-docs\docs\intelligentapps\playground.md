---
ContentId: e919aee8-fd2e-401b-9d83-0ff6f98b23ba
DateApproved: 12/11/2024
MetaDescription: Chat with selected generative AI model in playground. Change system prompt and parameters. Add attachment for Multi-Modal models. Keep chat history.
---
# AI Toolkit playground

The AI Toolkit playground enables you to interact with your AI models and try different prompts with different model parameter settings. You can also use the playground to interact with multi-modal models that support attachment of different input formats.

![Playground view](./images/playground/playground.png)

## Test a model in the playground

To access the playground:

- In AI Toolkit view, select **Playground**

- Select **Load in Playground** or **Try in Playground** from a model card in the model catalog

To test a model in the playground, follow these steps:

1. In **Model Preferences**, select a model from the dropdown list

    ![Select a model and configure context instructions in the playground.](./images/playground/parameters.png)

1. Optionally, add context instructions to guide the model response

1. Optionally, configure the model parameters

1. Enter a chat prompt in the chat input box

    From the chat input box, you can also clear chat history or add attachments for the prompt.

## Add attachments for multi-modal models

Multi-modal models are models that can process multiple types of input, such as text, images, audio, or video. By attaching files to your chat prompt, you can ask questions about the contents of these files.

For the models that support attachments, the attachment icon (paperclip) will show in the chat input box. Select the icon, and follow the instructions to attach one or more local files and use them with your prompt.

![Adding attachments](./images/playground/attachment.png)
