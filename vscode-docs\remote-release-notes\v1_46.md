# May 2020 (version 1.46)

## VS Code Remote Core

With this release, we allow you to install an already installed multi-kind (that can run locally or remotely) extension in its preferred location using  **Install in Remote** or **Install Locally** action. For example, the [Docker extension](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker) prefers to run remotely but it is currently installed and running locally. In this case we show the **Install in Remote** action for the Docker extension. This will install and run the Docker extension remotely:

![Install in Remote for Docker Extension](images/1_46/extensions-install-preferred.png)

*Theme: GitHub Sharp with Customizations*

The command **Remote: Install Local Extensions in Remote** now also includes the multi-kind extensions.

## Containers (version 0.121.0)

### Inspect a Docker volume

You can now inspect the contents of a Docker volume using Visual Studio Code. The command **Remote: Inspect a Volume in a Container...** allows you to select a volume which is then opened in a dev container. The command is also available in the Docker viewlet in the **Volumes** section.

![Inspect Volume in VS Code](images/1_46/inspect-volume.png)

### Improved iterative development of a devcontainer's Dockerfile for Repository Containers

When **Dev Containers: Clone Repository in Container Volume...** fails to start the [Repository Container](https://code.visualstudio.com/docs/devcontainers/containers#_quick-start-open-a-git-repository-or-github-pr-in-an-isolated-container-volume), for example, due to an error when building the Docker image, you can now open the cloned repository in a recovery container. The error dialog shown now provides an action **Reopen in Recovery Container**. This action opens the cloned repository in a minimal container that comes with Git and Docker so that you can correct the error. Once you have fixed the error you can reopen the container with **Dev Containers: Reopen in Container**.

### GPG forwarding

If you have [GPG](https://www.gnupg.org) installed locally and in the Dev Container, we will now forward the GPG socket for signing and encryption and copy your `pubring.kbx` and `trustdb.gpg` (if they do not exist already) into the container. This also allows for signing Git commits.

### Additional devcontainer.json command properties

The `devcontainer.json` now supports `postStartCommand` and `postAttachCommand` properties. Their values can be either a string to be run in the `/bin/sh` shell or an array of strings to be run as an executable with arguments inside the container.

`postAttachCommand` can also be configured in an attached container's configuration.

### Extension caching

Extension downloads are now cached locally and copied to the container for installation to improve startup time.

### Docker executable path setting

We have added a setting for configuring the Docker CLI's executable path:

![Docker executable path](images/1_46/docker-path.png)

Set this to `podman` if you are using the Podman container engine.
