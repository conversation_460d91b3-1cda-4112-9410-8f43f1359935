<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="554px" height="362px" viewBox="-0.5 -0.5 554 362" content="&lt;mxfile scale=&quot;1.2&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;UYQDFGXKUwBN9YeYD3QO&quot; name=&quot;Page-1&quot;&gt;7VtNc+MmGP41OvRgjQAhy8c4yW4PaWdn0plujopFZLqycDFe2/vriyywjJATpcbRaseXxLx8CD3P+wUID90utp95spz/wVKSezBItx668yAMYyz/loJdJYiiuBJknKaVCNSCR/qDKGGgpGuakpXRUDCWC7o0hTNWFGQmDFnCOduYzV5Ybj51mWTEEjzOktyW/k1TMa+kMQ5q+e+EZnP9ZBComkWiGyvBap6kbHMkQvceuuWMierXYntL8hI7jUvV79OJ2sPEOClElw4RrHp8T/K1ejk1MbHTb8vZukhJ2SHw0HQzp4I8LpNZWbuR9ErZXCxyWQLy50pw9o3cspxxKSlYIZtN1TMIF2R7cqLg8PpSbQhbEMF3sonqgBRgO7O4qeEPIyWbH0GPNPKJojw7jFyjIn8oYNpBQjZG46kFk3w3YWKR5DQr5O+ZfEci4ZiWCFCpRjeqYkHTtOw+5WRFfyTP+6FKkJeMFmI/Yzz18F051lqwVWUIJ2F+YYVQxgIjVf6ULGhegnbL1pzKWcDgT7LRjdXsA1XW43kQ3WCIQHzgziKqhc6T3AGIDfJgbLOHWsgLHXAXtnB3d+WuM3cw6I87bHG3PcVcO6Y0zxui7rS2+TnTE/6slIHI9JVw0uIsWyiDDiiLrq7yPHML+zO38dVVnsUdisLeuNOZcRdfeaXONru4P7MD4ErdOVbXY3IJ7JXBlCezb0R4MMpLwp65wWP077pc2e3xGFWI38gGACy3daX8le3/4yny/fA3SxnetSDjTCSCslIjRpNG2gLKBinlco1ctSDJSjhKQibApAWF/mRsMdO+ZnPAjJ33/1WakBtaoO+jgdKC+6XFTundGgzwfThQZkC/zFwz9/P4i3tM/q6Z+3nchUGP5MUXdomB74NhusRxYx/jgz3i5MLEhL6Ph0mMtLw+mYH2YtctM2Pfj4fJDMK92gy017JumYl9fzJQZib9MgMtZhwuiLDvj4dJC2yuU8G4Ky16u++sIw5koUbSjOi8h3ExZxkrkvy+ljaOIeo2D4wtFZr/ECF2Cr0yMzOxJlsqvpbdfYhV8emo6m6rht4XdrpQyHf7qpuVhaf9EFgX6277ku7X5PEkaSuZAM6Ito5KJhKeEd1MLR5LgF6llpNcqtJ3Yox+Fkthvyx1J8kd2GFvYON+wR4PySRQXyy1fBRxiPTBl4Ty48hSxYlHOXOhAokH5ROC+yKtyrZ9vSt2OIgDQB9d7rSzQVYQAOBCa0AcXaNAq8qHLSo/6c0xjX/tKNACts4aewA7vkaBziwFvUUB+5jlHVEgbEQBm/KPjgIw7BAF8IWiQGgvnmdzmqdVvBxafDjS/ydD/V0aA24xhq4pkaJ8ZK7/9ClMZ1NRA38pN8TrUcfGoCNo9mcvL6vSRBq6cZhcN3WxV/RaXeDwfGdv6tJ1nXNCXbATdQHQ1JfoAvrS8jnlA12Jyge/kpv375Wt3Fz7wyOvfDgscL1xFtqbzcrMhpELfqyFRS0W1nXbRrEd+EEQmISP4Jle2akh4eFEY2ck6lNKdynmvusN58nuqIE6Vj7tJwNzEz0OGlxWA/5vZu0jP7YkBS0yO4991nluiyo8JM8k9y521P42q/iUjz3ciVIP8I6vHbVbY51SaWtETmKeGfIae+luDHU4eZA7bwt+CkMN4UUNNWoJyjlbDdBQdRLvwlCtD9Cd2OnINNRGfzepqf3xWXMboZGkBo0kdWLR+9FJajwxYMItXxDpux2uU9TI3jiwtb1Ib8qbr97hY6yj12/qacGUYRxqGx9y5WX1VDKU7WFu1L5yB7QF4jc91RGEbVsvWnbuit0Mcqh5G6pyvKpTzc1b4+DGRCpfbQ3zttHIYn0tuGpe361G9/8B&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="552" height="360" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="114" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 110px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="210" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 190px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="156" y="312" width="48" height="24" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 270px; margin-left: 131px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="150" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="258" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 230px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="230" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="400.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 349px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="349" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="306" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 270px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="270" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="354" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 310px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="310" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="193.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,229.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 191 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 162px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [3..4)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="191" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="145.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,181.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 151 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 122px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Text
                                <br style="font-size: 11px"/>
                                [2..3)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="151" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Text...
                </text>
            </switch>
        </g>
        <rect x="97.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,133.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 111 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 82px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [1..2)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="111" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="64.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 69px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="69" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="448.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 389px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="389" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="48" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,84,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 70 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 41px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [0..1)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="241.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,277.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 231 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 202px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [4..5)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="231" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="384" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,420,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 350 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 321px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [7..8)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="432" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,468,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 390 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 361px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                [8..9)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="313.2" y="225.56" width="72" height="76.8" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,349.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 291 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 262px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Text
                                <br style="font-size: 11px"/>
                                [5..7)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="291" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Text...
                </text>
            </switch>
        </g>
        <path d="M 148.2 195.6 L 133.2 195.6 L 133.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 133.2 226.62 L 129 218.22 L 133.2 220.32 L 137.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 181.2 195.6 L 181.2 219.6 L 181.2 204 L 181.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 181.2 226.62 L 177 218.22 L 181.2 220.32 L 185.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 214.2 195.6 L 229.2 195.6 L 229.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 229.2 226.62 L 225 218.22 L 229.2 220.32 L 233.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="115.2" y="147.6" width="132" height="48" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 143px; margin-left: 97px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                                <br/>
                                Start: 1, End: 4
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="151" y="147" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair...
                </text>
            </switch>
        </g>
        <path d="M 304.2 195.6 L 277.2 195.6 L 277.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 277.2 226.62 L 273 218.22 L 277.2 220.32 L 281.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 349.2 195.6 L 349.2 219.6 L 349.2 204 L 349.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 349.2 226.62 L 345 218.22 L 349.2 220.32 L 353.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 394.2 195.6 L 420 195.6 L 420 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 226.62 L 415.8 218.22 L 420 220.32 L 424.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="259.2" y="147.6" width="180" height="48" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 143px; margin-left: 217px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                                <br/>
                                Start: 4, End: 8
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="291" y="147" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair...
                </text>
            </switch>
        </g>
        <path d="M 196.2 121.2 L 181.2 121.2 L 181.2 139.96" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 181.2 146.26 L 177 137.86 L 181.2 139.96 L 185.4 137.86 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 171px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="171" y="113" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child 1
                </text>
            </switch>
        </g>
        <path d="M 358.2 121.2 L 349.2 121.2 L 349.2 139.96" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 349.2 146.26 L 345 137.86 L 349.2 139.96 L 353.4 137.86 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 311px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="311" y="113" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child 2
                </text>
            </switch>
        </g>
        <rect x="115.2" y="85.2" width="324" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 86px; margin-left: 97px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List, Start: 1, End: 8
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="231" y="90" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List, Start: 1, End: 8
                </text>
            </switch>
        </g>
        <path d="M 277.8 60 L 277.38 77.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 277.23 83.86 L 273.23 75.36 L 277.38 77.56 L 281.63 75.56 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 210px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="64" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child
                </text>
            </switch>
        </g>
        <path d="M 174.3 60 L 84 60 L 84 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 84 226.62 L 79.8 218.22 L 84 220.32 L 88.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 70px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                opening
                                <br/>
                                bracket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="115" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    opening...
                </text>
            </switch>
        </g>
        <path d="M 381.3 60 L 468 60 L 468 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 468 226.62 L 463.8 218.22 L 468 220.32 L 472.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 391px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                closing
                                <br/>
                                bracket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="391" y="114" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    closing...
                </text>
            </switch>
        </g>
        <rect x="70.8" y="24" width="414" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 343px; height: 1px; padding-top: 35px; margin-left: 60px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Start: 0, End: 9
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="39" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Start: 0, End: 9
                </text>
            </switch>
        </g>
        <path d="M 51.6 348 L 51.6 25.2" fill="none" stroke="none" pointer-events="stroke"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>