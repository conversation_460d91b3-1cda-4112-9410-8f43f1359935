pr: none

resources:
  repositories:
  - repository: self
    type: github
    ref: main
    trigger:
    - main
    - vnext
  - repository: website
    type: github
    endpoint: Monaco
    name: microsoft/vscode-website
    ref: release/prod
  - repository: dist
    type: github
    endpoint: Monaco
    name: microsoft/vscode-website-dist
    ref: $(Build.SourceBranchName)
jobs:
- job:
  displayName: Build
  variables:
    skipComponentGovernanceDetection: true
  cancelTimeoutInMinutes: 1
  pool:
    vmImage: macOS-latest
  steps:
  - checkout: website
    clean: true
    path: vscode-website
    persistCredentials: true
    fetchDepth: 1
  - checkout: self
    path: vscode-website/vscode-docs
    lfs: true
    fetchDepth: 1
    persistCredentials: true
  - checkout: dist
    path: vscode-website/dist
    persistCredentials: true
  - task: NodeTool@0
    displayName: Use Node 20.x
    inputs:
      versionSpec: 20.x
      checkLatest: true
  - task: Bash@3
    displayName: Build Dist Setup
    inputs:
      filePath: ../vscode-website/scripts/ci-build-dist-setup.sh
      workingDirectory: ../vscode-website
    env:
      WEBSITE_BRANCH: $(Build.SourceBranchName)
  - task: Bash@3
    displayName: Rebuild Dist From Website
    condition: eq(variables['Build.Repository.Name'], 'microsoft/vscode-website')
    inputs:
      filePath: ../vscode-website/scripts/ci-build-dist-from-website.sh
      workingDirectory: ../vscode-website
    env:
      WEBSITE_BRANCH: $(Build.SourceBranchName)
  - task: Bash@3
    displayName: Rebuild Dist From Docs
    condition: eq(variables['Build.Repository.Name'], 'microsoft/vscode-docs')
    inputs:
      filePath: ../vscode-website/scripts/ci-build-dist-from-docs.sh
      workingDirectory: ../vscode-website
    env:
      DOCS_BRANCH: $(Build.SourceBranchName)