# April 2020 (version 1.45)

## Containers (version 0.117.0)

### Container Configuration recommendation

When adding a development container configuration for a workspace using the **Dev Containers: Add Dev Container Configuration Files**, we now recommend the container configurations that could be a good fit your workspace. The recommendation is done based on your current platform and programming languages.

![Select a container configuration definition dropdown](images/1_45/add-configuration.png)

### WSL 2 Support

Dev Containers now supports the WSL 2 based engine of Docker Desktop for Windows. You can author your `devcontainer.json` in WSL 2 using the WSL extension and then reopen the WSL 2 folder in a container:

![Reopen in Container command for WSL 2](images/1_45/containers-wsl2-support.png)

This currently requires Windows Insiders build 18917 or later (slow ring will do), [WSL 2](https://learn.microsoft.com/windows/wsl/install), and Docker Desktop for Windows' Edge version for its [WSL 2 based engine](https://docs.docker.com/docker-for-windows/wsl-tech-preview/).

### Podman Support

On Linux, we now support <PERSON><PERSON> as the container engine. Simply create a symlink `docker` to the <PERSON><PERSON> executable on the `PATH`.

Make sure you have `conmon` >= 2.0.14 installed to get the fix for [containers/libpod#5046](https://github.com/containers/libpod/issues/5046).

### Additional devcontainer.json variables

There are three additional variables for the `devcontainer.json`:

| Variable | Description |
|----------|----------------------|
| `${localWorkspaceFolderBasename}` | Name of the local folder that was opened in VS Code (that contains `.devcontainer/devcontainer.json`). |
| `${containerWorkspaceFolder}` | The path where the workspaces files can be found in the container. |
| `${containerWorkspaceFolderBasename}` | Name of the folder where the workspace files can be found in the container. |
