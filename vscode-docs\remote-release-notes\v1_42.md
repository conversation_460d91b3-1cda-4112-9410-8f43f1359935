# January 2020 (version 1.42)

## Visual Studio Code Remote Core

### Piping to VS Code

Piping content to the `code` launch command now also works in the Integrated Terminal of remote windows:

```bash
    `ps aux | code -`
```

### Unified port forwarding experience

The Remote Explorer has a single port forwarding view that the SSH and Containers extensions have adopted. From the **Forwarded Ports** section, you can see the ports you have currently forwarded through VS Code, forward additional ports, label forwarded ports, and copy the local address. You can also see suggested ports to forward based on what processes are currently running.

If you want to have your forwarded ports restored when you open the same folder or workspace again, set the `remote.restoreForwardedPorts` setting to `true`.

![Forwarded Ports view](images/1_42/forwarded-ports.png)

## SSH

### Auto-detect remote OS

In order to improve our support for Windows remotes, the Remote-SSH extension is now capable of detecting the OS of the remote as it connects to it. Previously, it was required to use the `remote.SSH.windowsRemotes` setting to declare a particular remote as Windows. Now, this is not needed, as long as you have set `remote.SSH.useLocalServer` to opt-in to the new "connection sharing mode" (see [the v1.39 release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_39.md#ssh-connection-sharing) for more details).

The remote machine must be Windows 10, running an OpenSSH server, and you must use VS Code [Insiders](https://code.visualstudio.com/insiders/).

## WSL

### Reveal in Explorer

The **Reveal in Explorer** command is now available in a WSL window. It will open the Windows Explorer on the WSL resource. For files not located in `/mnt/c/..`, this will use the `wsl$` network share:

![Reveal In Explorer command](images/1_42/reveal-in-explorer.png)

![Folder in Windows Explorer](images/1_42/reveal-in-explorer-2.png)

## Containers

### Pre-built container images

You can now take advantage of an initial set of pre-built development container images that include common dependencies, such as [Git](https://git-scm.com/), [zsh](https://en.wikipedia.org/wiki/Z_shell), and [Oh My Zsh!](https://ohmyz.sh/). Their sources are available in the [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers) repository and we will be adding more in the future.

These same images are also used when you select the related language / platform by using the **Dev Containers: Add Dev Container Configuration Files** or **Dev Containers: Open Folder in Container** / **Dev Containers: Reopen in Container** commands from the Command Palette (`kbstyle(F1)`) on an empty folder.

Check out our [Docker Hub page](https://hub.docker.com/_/microsoft-vscode-devcontainers) for a list of available images.

### Dotfiles repository support

We have added support for [dotfiles repositories](https://dotfiles.github.io/). When you configure your dotfile repository in User Settings, that repository will be cloned and its install command will be run the first time you connect to a container.

![Dotfiles repository settings](images/1_42/dotfiles.png)

*Theme: Dark+*

### Improved iterative development of a devcontainer's Dockerfile

The error dialog displayed when a development container fails to start (for example, due to an error when building the Docker image), now has an **Open Folder Locally** action that will reuse the current window and automatically open the log file from the failed startup. When you are ready to try starting your dev container again, the **Dev Containers: Rebuild and Reopen in Container** command will remove any existing container and start a new one after rebuilding the Docker image.

There is also a new **Dev Containers: Open Log File...** command for when you want to look at the log of a previous Dev Containers session.

### forwardPorts configuration property

The `devcontainer.json` file has a new `forwardPorts` property that lists the container's ports (as numbers) that should be forwarded to the local machine. This has the advantage over the existing `appPort` property, in that it will also forward to the local machine when the Docker daemon runs on a remote machine, and it will first try to allocate the same port locally and if that port is already in use, fall back to a random port.

### Open private repositories in containers

The command **Dev Containers: Clone Repository in Container Volume** now supports private repositories. One remaining limitation is that the credentials need to be available in the local Git credentials manager when using HTTPS and the key needs to be available in the ssh-agent when using SSH to connect to the Git server.

### Simplified adding extensions to the devcontainer.json

The Extensions view has a context menu action on each extension to add it to `devcontainer.json`. Additionally, there are IntelliSense suggestions when editing the `extensions` property in `devcontainer.json` and a simple linter checking the syntax of the extension IDs.

![Add to devcontainer.json in context menu](images/1_42/addtodevcontainerjson.png)

*Theme: Dark+*
