---
ContentId: a7a9837e-f515-4644-8dae-b5d44715e63f
DateApproved: 4/13/2021
MetaDescription: Get started with C++ in Visual Studio Code by watching these introductory videos
---
# Introductory Videos for C++

Get started with C++ in Visual Studio Code by watching these introductory videos! These videos are designed to help you set up C++ IntelliSense and build and debug C++ projects in VS Code. After watching these quick tutorials, you'll be able to enjoy VS Code's rich C++ feature set.

## Getting started with C++ in 5 minutes

This video shows you how to install the C/C++ extension and a C++ compiler. You then learn how to run and debug your code by using the run or debug play button.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/qeEcV6u1kV4" title="Getting Started with C++ in 5 minutes" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>

## Configure C++ IntelliSense

This video walks you through the steps to configure C++ IntelliSense for your project.

<iframe src="https://channel9.msdn.com/Blogs/One-Dev-Minute/Configure-C-IntelliSense-in-Visual-Studio-Code/player" width="960" height="540" allowFullScreen frameBorder="0" title="Configure C++ IntelliSense in Visual Studio Code - Microsoft Channel 9 Video"></iframe>

## Build a C++ project

Learn how to build C++ projects in VS Code by customizing your C++ build tasks.

<iframe src="https://channel9.msdn.com/Blogs/One-Dev-Minute/Build-a-C-project-in-VS-Code/player" width="960" height="540" allowFullScreen frameBorder="0" title="Build a C++ project in VS Code - Microsoft Channel 9 Video"></iframe>

## Debug a C++ project

This video shows you how to customize debug configurations for your C++ project and start a C++ debugging session in VS Code.

<iframe src="https://channel9.msdn.com/Shows/Docs-Dev-Tools/Debug-a-C-project-in-VS-Code/player" width="960" height="540" allowFullScreen frameBorder="0" title="Debug a C++ project in VS Code - Microsoft Channel 9 Video"></iframe>

## Getting started using Copilot Chat for C++ in VS Code

This video covers some use cases for using Copilot Chat in VS Code with C++ Code.

<iframe src="https://www.youtube-nocookie.com/embed/ZfT2CXY5-Dc?si=0F2qRniRhN-sg3cR" width="960" height="540" allowFullScreen frameBorder="0" title="Getting Started using Copilot Chat for C++ in VS Code"></iframe>

## Next steps

- Learn about all the [C++ editing features](/docs/cpp/cpp-ide.md) available in VS Code.
- Check out the Hello World tutorials for getting started with C++ for your toolset and platform:
  - [GCC on Windows](/docs/cpp/config-mingw.md)
  - [Microsoft C++ on Windows](/docs/cpp/config-msvc.md)
  - [GCC on Linux](/docs/cpp/config-linux.md)
  - [GCC on Windows Subsystem For Linux](/docs/cpp/config-wsl.md)
  - [Clang/LLVM on macOS](/docs/cpp/config-clang-mac.md)
