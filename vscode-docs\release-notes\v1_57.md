---
Order:
TOCTitle: May 2021
PageTitle: Visual Studio Code May 2021
MetaDescription: Learn what is new in the Visual Studio Code May 2021 Release (1.57)
MetaSocialImage: 1_57/release-highlights.png
Date: 2021-6-10
DownloadVersion: 1.57.1
---
# May 2021 (version 1.57)

**Update 1.57.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22May+2021+Recovery%22+is%3Aclosed).

The [Workspace Trust](#workspace-trust) feature addresses [CVE-2021-34529](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-34529).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the May 2021 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Workspace Trust](#workspace-trust)** - Extra security against code execution when browsing unfamiliar source code.
* **[New Getting Started experience](#new-getting-started-experience)** - Helps you quickly set up and learn about VS Code.
* **[Remote Repositories](#remote-repositories)** - Browse and edit code without locally cloning repositories.
* **[Terminal tabs](#terminal-tabs)** - Tabs let you easily create, manage, and group multiple open terminals.
* **[Edge browser debugging](#javascript-debugging)** - Integrates the Microsoft Edge Developer Tools directly into VS Code.
* **[JSDoc @link support](#jsdoc-link-support)** - Add @link tags in your comments for fast symbol navigation.
* **[Go to Definition for non-code files](#go-to-definition-for-non-jsts-files)** - Quickly jump to images and stylesheets.
* **[Notebook API finalized](#notebook-api)** - Notebook API for native notebook support in VS Code.
* **[VS Code at Build 2021 blog post](#documentation)** - Catch up with on-demand sessions featuring VS Code.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Watch a highlight** of the new features in this version in the VS Code team's release party. You can find the [recording of the event](https://youtu.be/g_DrpYwcRF4) on our YouTube channel.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Workbench

### Workspace Trust

Visual Studio Code takes security seriously and wants to help you safely browse and edit code no matter the source or authors. The [Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust) feature lets you decide whether your project folders should allow or restrict automatic code execution.

![Trust this folder dialog](images/1_57/workspace-trust-dialog.png)

>**Note**: When in doubt, leave a folder in **Restricted Mode**. You can always **enable trust** later.

**Safe code browsing**

It's great that there is so much source code available on public repositories and file shares. No matter the coding task or problem, there is probably already a good solution available somewhere. However, using open-source code and tools does have risks and you can leave yourself open to malicious code execution and exploits.

Workspace Trust provides an extra layer of security when working with unfamiliar code by preventing automatic code execution when a workspace is open in [Restricted Mode](https://code.visualstudio.com/docs/editor/workspace-trust#_restricted-mode).

>**Note**: If you want to disable the Workspace Trust feature, you can set `security.workspace.trust.enabled` to `false`. Only disable Workspace Trust if you are confident that you know the source and integrity of the files you are working with.

**Restricted Mode**

When prompted by the Workspace Trust dialog, if you choose **No, I don't trust the authors**, VS Code will go into Restricted Mode to prevent code execution. The workbench will display a banner at the top with links to **Manage** your folder via the Workspace Trust editor and **Learn More** taking you to the [Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust) user guide.

![Workspace Trust Restricted Mode banner](images/1_57/restricted-mode-banner.png)

You will also see a Restricted Mode badge in the Status bar.

![Workspace Trust Restricted Mode Status bar badge](images/1_57/restricted-mode-status-bar.png)

Restricted Mode tries to prevent automatic code execution by disabling or limiting the operation of several VS Code features: tasks, debugging, workspace settings, and extensions.

To see the full list of features disabled in Restricted Mode, you can open the Workspace Trust editor via the **Manage** link in the banner or by clicking the Restricted Mode badge in the Status bar.

![Workspace Trust editor](images/1_57/workspace-trust-editor.png)

**Trusting a workspace**

If you trust the authors and maintainers of a project, you can trust the project's folder on your local machine. For example, it is usually safe to trust repositories from well-known GitHub organizations such as github.com/microsoft or github.com/docker. The initial Workspace Trust prompt when you open a new folder allows you to trust that folder and its subfolders.

You can also bring up the Workspace Editor and quickly toggle a folder's trusted state. There are several ways to bring up the Workspace Editor dialog.

When in Restricted Mode:

* Restricted Mode banner **Manage** link
* Restricted Mode Status bar item

You can also at any time use:

* **Workspaces: Manage Workspace Trust** command from the Command Palette (`kb(workbench.action.showCommands)`)
* **Manage Workspace Trust** from the **Manage** gear in the Activity bar

![Manage Workspace Trust command in the Manage gear context menu](images/1_57/gear-manage-workspace-trust.png)

**Selecting folders**

When you trust a folder, it is added to the **Trusted Folders & Workspaces** list displayed in the Workspace Trust editor.

![Workspace Trust editor Trusted Folders and Workspaces list](images/1_57/trusted-folders-workspaces-list.png)

You can manually add, edit, and remove folders from this list and the active folder enabling trust is highlighted in bold.

You also have the option to trust the parent folder. This will apply trust to the parent folder and all subfolders. This can be helpful if you have many folders with trusted content collocated under one folder.

**Enabling extensions**

What happens if you want to use Restricted Mode but your favorite extension doesn't support Workspace Trust? This can happen if an extension, while useful and functional, isn't being actively maintained and hasn't declared their Workspace Trust support. To handle this scenario, you can override the extension's trust state with the `extensions.supportUntrustedWorkspaces` setting.

>**Note**: Be careful overriding an extension's Workspace Trust support. It may be that the extension author has a good reason for disabling their extension in Restricted Mode.

Below you can see the user `settings.json` entry for the Prettier extension.

```json
  "extensions.supportUntrustedWorkspaces": {
    "esbenp.prettier-vscode": {
      "supported": true,
      "version": "6.4.0"
    },
  },
```

You can either enable or disable Workspace Trust support with the `supported` attribute. The `version` attribute specifies the exact extension version applicable and you can remove the version field if you want to set the state for all versions.

If you'd like to learn more about how extension authors evaluate and determine which features to limit in Restricted Mode, you can read the [Workspace Trust Extension Guide](https://code.visualstudio.com/api/extension-guides/workspace-trust).

**Settings**

Below are the available Workspace Trust settings:

* `security.workspace.trust.enabled` - Enable Workspace Trust feature. Default is true.
* `security.workspace.trust.startupPrompt` - Whether to show the Workspace Trust dialog on startup. Default is to only show once per distinct folder or workspace.
* `security.workspace.trust.emptyWindow` - Whether to always trust an empty window (no open folder). Default is true.
* `security.workspace.trust.untrustedFiles` - Controls how to handle loose files in a workspace. Default is to prompt.
* `extensions.supportUntrustedWorkspaces` - Override extension Workspace Trust declarations. Either true or false.

To learn more about Workspace Trust, you can read the [Workspace Trust](https://code.visualstudio.com/docs/editor/workspace-trust) user guide.

### New Getting Started experience

Over the past couple iterations, we have been working on refreshing the welcome page for VS Code, and we're happy to announce that starting this iteration our new welcome page (**Getting Started**) is enabled by default for all users. In this new experience, both the core of VS Code and installed extensions can contribute "walkthroughs" to the welcome page, which are designed to help you get started in either VS Code itself or with a new extension. For example, the [Luna Paint](https://marketplace.visualstudio.com/items?itemName=Tyriar.luna-paint) extension contributes a Walkthrough with guides to help understand each component of the image editor.

![New welcome page, showing some of the walkthroughs core and extensions can contribute](images/1_57/welcomePageWalkthroughs.gif)

Additionally, once you've completed (or dismissed) all walkthroughs, VS Code rearranges the contents to double the number of recent workspaces accessible with one click.

![New welcome page with all walkthroughs dismissed, showing extra recent workspaces entries](images/1_57/welcomePageNoWalkthroughs.png)

**Settings**

* `workbench.startupEditor` - Controls the default editor to show on startup. The new value `gettingStarted` replaces the old default `welcome`. Some of the other available options include `readme`, `gettingStartedInEmptyWorkbench`, `newUntitledFile`, or `none`.
* `workbench.welcomePage.walkthroughs.openOnInstall` - Whether to automatically open extension-contributed walkthroughs upon install.

The new page will be called **Getting Started** until we phase the existing Welcome page out entirely.

### Remote Repositories

The **Remote Repositories** extension was introduced as a [built-in preview feature in 1.56](https://code.visualstudio.com/updates/v1_56#_remote-repositories-remotehub) and is now publicly available, but it is no longer built-in to VS Code so you'll need install it from the [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=github.remotehub).

As a quick recap, the [Remote Repositories extension](https://marketplace.visualstudio.com/items?itemName=github.remotehub) allows you to instantly browse, search, edit, and commit to any GitHub repository directly from within VS Code without having to clone or have the repository locally.

In this iteration, while we were mainly focused on bug fixing and polishing the extension for release, we did add a few new features. You can now see your remote repositories in the **Remote Explorer** to quickly open or compare any uncommitted changes you may have. Additionally, you can apply changes, from the same repository, to the current remote repository allowing you to quickly and easily move uncommitted changes from one branch to another.

![Remote Repositories Remote Explorer view](images/1_57/remote-repositories-remote-explorer.png)

*Theme: [Amethyst Dark Theme](https://marketplace.visualstudio.com/items?itemName=eamodio.amethyst-theme)*

To learn more about Remote Repositories, you can check out the [Remote Repositories blog post](https://code.visualstudio.com/blogs/2021/06/10/remote-repositories) and [YouTube video](https://www.youtube.com/watch?v=wHsmaXoGIXI).

### Improved editor drag and drop across windows

Support for dragging editors to other windows was improved in this release. You can now drag diff editors, custom editors, and the entire editor group to another window to open the editors there.

![Improved editor drag and drop across windows](images/1_57/editor-dnd.gif)

*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

**Note:** If you frequently use this feature, make sure to have Auto Save enabled (`files.autoSave`) so that changes in the one window are automatically reflected in the other window.

### Removed Cmd+W / Ctrl+W keybinding to close window when no editor is opened

We have gotten [feedback](https://github.com/microsoft/vscode/issues/54492) that users are frustrated when the VS Code window (instance) suddenly closes after having closed the last editor via rapid `Cmd+W` / `Ctrl+W` usages. A quick poll in the team also revealed that many people had unassigned this keybinding for closing the window, so we went ahead and removed `Cmd+W` / `Ctrl+W` as a keybinding to close the window when no editor is opened.

You can easily bring back the keybinding by configuring it as follows in your user `keybindings.json` file:

**macOS**

```json
{
  "key": "cmd+w",
  "command": "workbench.action.closeWindow",
  "when": "!editorIsOpen && !multipleEditorGroups"
}
```

**Windows/Linux**

```json
{
  "key": "ctrl+w",
  "command": "workbench.action.closeWindow",
  "when": "!editorIsOpen && !multipleEditorGroups"
}
```

**Note:** On all platforms, there is a dedicated assigned keybinding to close the window:

* macOS: `Cmd+Shift+W`
* Linux: `Alt+F4`
* Windows: `Alt+F4`

### Notebook layout customization

We added several notebook layout settings this month to allow users to customize the notebook editor for their workflow. The customizations that are turned on by default are:

**Notebook Toolbar**

We added a notebook toolbar in the editor, in which you easily access actions like insert cell, run all cells or switch kernels. The toolbar can be disabled by setting `notebook.globalToolbar` to `false`.

![Notebook toolbar](images/1_57/notebook-toolbar.png)

**Output toolbar**

The **Clear Cell Outputs** action and the button to pick a different output renderer or mimetype are now combined into a single **...** ellipsis menu next to cell outputs. This can be disabled by setting `notebook.consolidatedOutputButton` to `false`

![Notebook output toolbar](images/1_57/notebook-output-toolbar.png)

**Focused cell highlight on gutter**

VS Code now supports rendering focused cell highlight in the gutter area of a cell, which helps identify which cell is focused when a notebook contains many cells. The setting `notebook.cellFocusIndicator` can be either `gutter`, the new default, or `border` to highlight the entire cell border.

![Notebook focus indicator on gutter](images/1_57/notebook-focus-indicator.png)

**Show folding icon on mouse over**

The folding icon on Markdown cells is now visible on mouse over rather than visible all the time. You can customize the behavior by setting `notebook.showFoldingControls` to either `mouseover` (default) or `always`.

![Notebook show focus icon on mouse over](images/1_57/notebook-show-folding-icon-on-mouse-over.gif)

The full list of notebook layout settings implemented this month are:

* `notebook.cellFocusIndicator`: Adds the option for cells to indicate their focused state with a colored bar on the side of the cell (`gutter`), similar to Jupyter.
* `notebook.cellToolbarVisibility`: Determines whether the cell toolbar should appear when the cell is focused, or hovered. The default is to only show up when a cell is focused.
* `notebook.compactView`: When enabled, cells are rendered in a more compact style with less empty space. It is enabled by default.
* `notebook.consolidatedOutputButton`: The **Clear Cell Outputs** action and the button to pick a different output renderer or mimetype have been combined into a single **...** ellipsis menu next to cell outputs. The new menu can be disabled with this setting.
* `notebook.consolidatedRunButton`: There are two new actions, **Execute Above Cells** and **Execute Cell and Below**. They will appear in the cell toolbar by default, but enabling this setting moves them to a new context menu next to the execute button.
* `notebook.dragAndDropEnabled`: Disables drag and drop for cells. You can still rearrange cells using the commands (`kbstyle(Alt+Up)`/`kbstyle(Alt+Down)` by default).
* `notebook.globalToolbar`: Adds a toolbar to the top of the notebook editor.
* `notebook.insertToolbarLocation`: Controls whether the buttons to insert new cells appear between cells, in the toolbar, both, or are hidden.
* `notebook.showCellStatusBar`: This setting has a new option, `visibleAfterExecute`, which will hide the cell Status bar to save space until a cell is executed. Once it's executed, it will become visible so the user can review the execution details.
* `notebook.showFoldingControls`: Controls whether the folding chevron that appears on Markdown headers is always visible, or only visible on mouseover.
* `notebook.editorOptionsCustomizations`: Lets the user customize the cell editor settings in the notebook.

The image below shows the effects of several of these settings (toolbar at the top, gutter highlight, output **...** button):

![Notebook layout changes](images/1_57/notebook-layout.png)

### Updated Quick Pick & suggest widget colors

We've update the focus state in the Quick Pick and suggest widget to better align with our tree widget styles. This introduces a few new color tokens that control focus foreground:

* `list.focusHighlightForeground`
* `quickInputList.focusForeground`
* `editorSuggestWidget.selectedForeground`

![Quick Pick style in light theme](images/1_57/quick-pick-light.png)

![Suggest style in light theme](images/1_57/suggest-light.png)

*Icon Theme: [Fluent Icons](https://marketplace.visualstudio.com/items?itemName=miguelsolorio.fluent-icons)*

### Updated Touch Bar icons for macOS

We've updated our macOS Touch Bar icons so they align with our updated iconography style.

![macOS Touch Bar icons](images/1_57/tb-default.png)
_Default view_

![macOS Touch Bar icons while debugging with more controls](images/1_57/tb-debugging.png)
_While debugging_

### Context menus for webviews

VS Code now supports showing a simple context menu inside webviews, such as the Markdown preview, release notes, and Extension Details pages:

![A context menu in a webview](images/1_57/webview-context-menu.png)

This context menu currently contains actions for copy and pasting text. We may add additional actions to the menu in the future.

If this context menu is interfering with a webview in your extension, you can prevent it from showing up by calling `preventDefault` on the `contextmenu` event:

```js
document.body.addEventListener('contextmenu', e => {
  e.preventDefault(); // cancel the built-in context menu
});
```

## Editor

### Suggestion preview

With the new setting `editor.suggest.preview` enabled, a preview of the selected suggestion or snippet is shown at the cursor position.

![Suggestion preview showing smart completions for the letter 'i'](images/1_57/suggest-preview.gif)

*Theme: Dark+ (default dark)*

### Hide deprecated suggestions

There is a new setting `editor.suggest.showDeprecated`, which defaults to `on`, but when turned `off`, suggestions will not show items that are marked as deprecated.

## Integrated Terminal

### Terminal tabs

Terminal tabs were introduced as a [preview feature in 1.56](https://code.visualstudio.com/updates/v1_56#_terminal-tabs) and are now enabled by default. The new tabs view will only show by default when there are at least two terminals. For single terminals, the tab is "inlined" into the panel title. This also introduces the concept of terminal statuses such as whether a task is running, succeeded, or failed:

![Single inlined terminal tab, terminal tabs with status, and collapsed terminal tabs](images/1_57/tabs-overview.png)

Below are some of the highlights in this release:

* A tab's color can be changed to enable quick navigation and differentiation between them.
* Tabs can be dragged and dropped to allow for rearranging.<br>![drag and drop](images/1_57/tabs-drag-and-drop.gif)
* Dragging a tab in a terminal group into the empty area will remove it from the group (unsplit the terminal, also available via the context menu).<br>![unsplit](images/1_57/tabs-unsplit.gif)
* Dragging a tab into the main terminal area allows joining a group (also available via the context menu).<br>![dragging a terminal tab to join a tab group](images/1_57/tabs-joining.gif)
* `kbstyle(Alt)` now splits the terminal when clicking on a tab, the **+** button, or the single tab. The same works for middle-click to kill a terminal.<br>![split terminal buttons](images/1_57/tabs-split.gif)
* The inline actions won't be shown unless the tabs list is sufficiently large to avoid accidentally splitting/killing terminals.
* When killing a terminal such that there will only be one, the tabs list won't hide until the mouse leaves the view.

If you're not a fan of the new UX, you can go back to the dropdown by setting `"terminal.integrated.tabs.enabled": false`, the plan is to continue to support this as an option going forward.

### Terminal profile improvements

The terminal profile system has many improvements in this release, here are some highlights:

* `defaultProfile` will be respected if the terminal is restored upon launching VS Code and will be used for tasks and debugging.
* Intellisense now works in `settings.json` for `icon` and `defaultProfile`.
* `overrideName` now works for the default profile.
* `defaultProfile` can now be set in workspace settings. This is also now controlled by the new Workspace Trust feature.
* If `defaultProfile` and `shell` or `shellArgs` settings are used, you will now be asked to migrate the deprecated settings to the profiles system.
* `color` can now be set on a profile.

### Title sequence support

The setting `terminal.integrated.experimentalUseTitleEvent` setting has been promoted to stable as `terminal.integrated.titleMode`. By default, this setting will be `executable`, which names terminals based on the detected foreground process:

![Running 'git show' will result in the terminal's title changing to 'git'](images/1_57/terminal-title-executable.png)

When this is set to `sequence`, the title will be based on what the shell sets it to. This needs shell support to work, an example of this in action is oh-my-zsh's default prompt, which shows additional information about the session:

![The oh-my-zsh default prompt will show the computer's name and current working directory](images/1_57/terminal-title-sequence.png)

### Improved launching with clean environment

The setting `terminal.integrated.inheritEnv` has been available since the [v1.36](https://code.visualstudio.com/updates/v1_36#_launch-terminals-with-clean-environments) but the implementation had some flaws where it could fail due to permissions issues on Linux and may not work as expected on macOS.

Due to improvements to how VS Code sets up its underlying environment, it's now possible to access the initial environment it was launched with, which is how most terminals work. When `inheritEnv` is set to `true`, VS Code will now use the initial environment and, when `false`, use the sourced "shell environment". This setting can help the terminal act more like a regular terminal. However, you may run into problems if your profile/rc configuration is not set up correctly.

### Native line wrapping support on Windows

Line wrapping in the terminal drives task problem matchers and link detection. Historically, the Windows terminal has used a heuristic to flag lines as wrapped since the emulation technology that simulates a Unix pty didn't support wrapping. This changed on recent versions of Windows, and when updating to Windows 10 21376+ (currently Insiders only), this heuristic will be disabled and wrapped lines should work correctly as in Windows Terminal.

## Tasks

### Task status on terminal tab

The status of a task is now shown in its terminal tab. For background tasks, the status is only shown when there is an associated problem matcher.

![Task status on a terminal tab](images/1_57/terminal-task-status.png)

### Automatically close task terminals

The task `presentation` property has a new `close` property. Setting `close` to `true` will cause the terminal to close when the task exits.

```json
{
  "type": "shell",
  "command": "node build/lib/preLaunch.js",
  "label": "Ensure Prelaunch Dependencies",
  "presentation": {
    "reveal": "silent",
    "close": true
  }
}
```

## Debugging

### JavaScript debugging

As usual, the complete list of changes can be found in the [vscode-js-debug changelog](https://github.com/microsoft/vscode-js-debug/blob/main/CHANGELOG.md#v157-may-2021).

**Edge Developer Tools integration**

The JavaScript debugger integrates with the Microsoft Edge Developer Tools to provide a DOM, style, and network inspector for browser debugging, accessible via the new "Inspect" icon in the debug toolbar.

![Debugger inspect button](images/1_57/debug-inspect-button.png)

The short video below demonstrates selecting the "Inspect" button and navigating through elements in Edge DevTools.

![Demonstration of clicking the "Inspect" icon and clicking through elements in Edge](images/1_57/js-debug-edge.gif)
*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

If you aren't already using [Microsoft Edge](https://aka.ms/getedge) for browser debugging, you can switch to it by replacing `"type": "chrome"` or `"type": "pwa-chrome"` with `"type": "pwa-msedge"` in your `launch.json`. This is also supported in [Blazor](https://dotnet.microsoft.com/apps/aspnet/web-apps/blazor) applications by setting `"browser": "edge"` in its launch config.

**Support for renamed identifiers in sourcemaps**

A long-requested feature, the JavaScript debugger now supports renamed identifiers in sourcemaps. This is particularly useful when debugging minified code:

![A screenshot paused at a breakpoint. Minified code is being run, but the variables, watch, and debug console views show that variables can be accessed by the original names.](images/1_57/js-debug-renames.png)
*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

Renamed variables are supported in the Variables view, Watch view, and in the Debug Console REPL. You can check whether your tooling emits renames by looking for a non-empty "names" array in the sourcemap files it generates.

### Reveal hit breakpoint in the BREAKPOINTS view

When a breakpoint gets hit, VS Code now selects and reveals it in the BREAKPOINTS view. This should make it easier to understand which exact breakpoint has caused the program to break.

Currently only the JS-debug extension supports this feature, but we expect other debug extensions to follow soon.

![As the user steps in the program, a breakpoint that got hit is selected and revealed in the BREAKPOINTS view](images/1_57/reveal-breakpoint.gif)

## Languages

### TypeScript 4.3.2

VS Code now ships with TypeScript 4.3.2. This major update includes many TypeScript language improvements, along with many improvements and bug fixes for JavaScript and TypeScript tooling.

You can read more about TypeScript 4.3 on the [TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-3/).

### JSDoc @link support

VS Code now supports [JSDoc @link tags](https://jsdoc.app/tags-inline-link.html) in JavaScript and TypeScript comments. These let you create clickable links to a symbol in your documentation:

![Navigating code using JSDoc @link tags](images/1_57/js-link.gif)

JSDoc `@link` tags are written as: `{@link symbolName}`. You can also optionally specify text to be render in place of the symbol name: `{@link class.property Alt text}`.

`@link` is supported in hovers, suggestions, and signature help. We have also updated the VS Code extensibility type declaration file `vscode.d.ts` to use `@link`.

### Completions in import statements

Auto import in JavaScript and TypeScript automatically add imports when you accept a suggestion. With VS Code 1.57, they now also work when writing an import statement itself:

![Auto imports in an import statement](images/1_57/ts-import.gif)

This can be a time saver if you ever need to manually add an import.

### Go to Definition for non JS/TS files

Many modern JavaScript bundlers and frameworks use `import` statements to import assets such as images and stylesheets. We now support navigating through these imports with **Go to Definition**:

![Navigating to a non-JS/TS import](images/1_57/js-go-def.gif)

This is probably most useful when using `Ctrl`/`Cmd` click to navigate through your code.

### Sort imports source action

The **Sort imports** Code Action for JavaScript and TypeScript sorts your imports. Unlike **Organize imports** however, it does not remove any unused imports.

You can also enable sorting of imports on save by setting:

```json
"editor.codeActionsOnSave": {
    "source.sortImports": true
}
```

### Infer function return type Quick Fix

The **Infer function return type** refactoring for TypeScript adds explicit return type annotations to functions:

![The Infer function return type refactoring in action](images/1_57/ts-infer-return.gif)

This is useful if you want to add more explicit typings. This refactoring can also save time when you want to extract the return type to a named type/interface, or if you need to modify a function's return type.

### Prompt to disable TS Server logging

VS Code now prompts you if you have TypeScript server logging enabled for a prolonged period of time:

![TS server logging enabled notification](images/1_57/ts-log-notification.png)

Logging can significantly impact performance so it's best to keep it disabled unless you are actively trying to diagnose an issue.

## Contributions to extensions

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.57 include:

* Control desired protocol for forwarded ports.
* Optional watch for forwarded port collision.
* Dev Containers supports Workspace Trust security feature.
* Better environment variable detection in dev containers.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_57.md).

## Extension authoring

### Notebook API

We have finalized most of the notebooks API. The API is structured into three separate parts:

1. The `NotebookSerializer` API that, in combination with the `notebooks` contribution point, enables VS Code to open, modify, and save notebook documents.
1. Notebook renderers that present cell output. They are added via the `notebookRenderer` contribution point of your extensions `package.json` file.
1. The `NotebookController` API to generate output for code cells.

Sample of a simple notebook serializer and an echo controller. The serializer uses `JSON.parse` and `stringify` with special handling for empty files. The controller returns the upper-case variant of the cell text - once as plain text and once as html.

```ts
// serialize/deserialize notebook
vscode.workspace.registerNotebookSerializer(
  'echobook',
  new class implements vscode.NotebookSerializer {
    // serializer is relying on JSON.parse/stringify
    deserializeNotebook(content: Uint8Array) {
        return JSON.parse(new TextDecoder().decode(content) || '{"cells":[]}');
    }
    serializeNotebook(data: vscode.NotebookData) {
        return new TextEncoder().encode(JSON.stringify(data));
    }
})

// notebook controller
vscode.notebooks.createNotebookController(
  'myId',
  'echobook',
  'Echo',
  (cells, _notebook, controller) => {
    for (let cell of cells) {
        // execution means to echo in upper-case, once as plain text and once as html
        const execution = controller.createNotebookCellExecution(cell);
        execution.start();
        const echo = cell.document.getText().toUpperCase();
        const textItem = vscode.NotebookCellOutputItem.text(echo, 'text/plain');
        const htmlItem = vscode.NotebookCellOutputItem.text(`<b>${echo}</b>`, 'text/html');
        const output = new vscode.NotebookCellOutput([textItem, htmlItem]);
        execution.replaceOutput(output)
        execution.end(true);
    }
});
```

The example statically registers the notebook type for `*.echobook` files. Also, VS Code will emit an `onNotebook:echobook` activation event whenever such notebooks are opened.

```json
"contributes": {
  "notebooks": [{
      "type": "echobook",
      "displayName": "Echobook",
      "selector": [{
        "filenamePattern": "*.echobook"
      }]
  }]
}
```

### Status bar item 'id' and 'name' properties

We finalized the API for `StatusBarItem` identifier and name properties. The identifier can be passed from the new overload of the `createStatusBarItem` method while the `name` property can change dynamically on the `StatusBarItem` itself.

These new properties are used for the context menu on the Status bar to give individual entries a name and for allowing a user to show and hide entries. If you do not provide these properties, the menu will aggregate all status entries from one extension under a single entry.

![Status bar item menu](images/1_57/status-menu.png)

If your extension contributes to the Status bar, consider updating to the new API.

### Print a message in new terminals

When creating a terminal using `window.createTerminal`, it's now possible to set a message via `TerminalOptions.message` that is printed at the top of the terminal. This message supports ANSI escape sequences to enable text styles:

```ts
vscode.window.createTerminal({
  name: `My terminal`,
  message: '\x1b[3;1mSome custom message\x1b[0m'
});
```

![Terminal displaying a custom message](images/1_57/terminal-api-message.png)

### New theme colors for inline values

New colors were added to theme the debugger inline values:

* `editor.inlineValuesBackground`: color for the debug inline value foreground text
* `editor.inlineValuesForeground`: color for the debug inline value background.

As a reminder, inline values are shown during debugging for debug extensions that have inline value providers registered or if the setting `debug.inlineValues` is `true`.

![Debug inline values are shown with the foreground and background color customized](images/1_57/inline-values.png)

### vscode-webview.d.ts

The newly published `@types/vscode-webview` package adds typings for the API [that VS Code exposes inside of webviews](https://code.visualstudio.com/api/extension-guides/webview#passing-messages-from-a-webview-to-an-extension).

You can install these types with `npm install --save-dev @types/vscode-webview`. If the webview types are not being picked up in your project, try adding them to the `typeAcquisition` section of your `jsconfig` / `tsconfig`:

```json
"typeAcquisition": {
  "include": [
    "@types/vscode-webview"
  ]
}
```

Or add a triple-slash reference in your code:

```js
///<reference types="@types/vscode-webview"/>
```

### Improved webview array buffer transfers

In previous versions of VS Code, sending typed arrays to or from a webview had a few quirks:

* Typed arrays, such as `UInt8Array`, serialize inefficiently. This can cause performance issues when you need to transfer many megabytes of data.
* Sent typed arrays are not recreated as the correct type on the receiving side. If you send a `UInt8Array`, the receiver instead gets a generic object that has the data values of the `UInt8Array`.

While both of these issues are bugs, we also can't fix them without potentially breaking extensions that rely on the existing behavior. At the same time, there's zero reason a newly written extension would want the current confusing and inefficient behavior.

Therefore, we've decided to leave the existing behavior in place for existing extensions but move new extensions onto the more correct behavior. This is done by looking at the `engines` value in your extension's `package.json`.

```json
"engines": {
  "vscode": "^1.57.0",
}
```

If the extension targets VS Code 1.57+, then typed arrays should be recreated on the receiver side and the transfer of large typed arrays to and from webviews should be much more efficient.

### parentSession on Debug Sessions

Some time ago, VS Code added support for hierarchical debug sessions, but no information about the hierarchy was exposed in the extension APIs. To address this, there is a new property on the `DebugSession` interface that references the parent session, if any. A session's parent will never change.

```ts
export interface DebugSession {
  /**
    * The parent session of this debug session, if it was created as a child.
    * @see DebugSessionOptions.parentSession
    */
  readonly parentSession?: DebugSession;

  // ...
```

### Improved VS Code Insiders version targeting

When working on extensions that use proposed APIs, it's possible that a new Insiders build is released with breaking changes. In order to provide a more seamless transition for users, you can now target Insiders versions precisely with a date tag. For example, setting `engines.vscode` to `^1.56.0-20210428` will target any VS Code 1.56 (or newer) build that was created on or after 0:00 UTC, April 28, 2020. This allows you to safely release post-dated extension updates before an upcoming Insiders version is released.

```json
"engines": {
  "vscode": "^1.56.0-20210428",
}
```

### Tree hovers support command URIs

When a `TreeItem` has a trusted Markdown tooltip, that tooltip can contain commands of the format `[this is a link](command:workbench.action.quickOpenView)`. This follows the same format as all other places where command links are supported in Markdown.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Testing

We intended to finalize the first parts of the testing APIs this iteration, but it was deferred due to the number of other in-flight features. While there were no breaking changes to the testing APIs this iteration, we continued to make progress on its editor experience. For example, the Peek view gained a split view that allows you to browse the results of current and past test runs.

![Image of a Peek view with a references-style tree shown on the right side of a split view](images/1_57/testing-peek.png)
*Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

Testing gained a new set of keybindings as chords starting with `kbstyle(Ctrl+;)`, the Peek view now has better support for Markdown messages, and many bugs were fixed.

If you use the [Test Explorer UI](https://marketplace.visualstudio.com/items?itemName=hbenl.vscode-test-explorer) extension for tests, you can toggle on the setting `testExplorer.useNativeTesting` to switch to the new, native UI. In the next iteration, we plan to increase socialization of this setting and begin adoption with partner extension teams.

### Terminal profile contributions

This API will enable extensions to contribute to the terminal profile system. An extension needs to do a few things to get this to work. First contribute the profile in the `package.json`:

```json
"contributes": {
  "terminal": {
    "profiles": [
      {
        "title": "Profile from extension",
        "id": "my-ext.terminal-profile",
        "icon": "lightbulb"
      }
    ]
  },
}
```

Then activate the extension on the new `onTerminalProfile` activation event:

```json
"activationEvents": [
  "onTerminalProfile:my-ext.terminal-profile"
]
```

Lastly, register the provider in the activation event:

```ts
vscode.window.registerTerminalProfileProvider('my-ext.terminal-profile', {
  provideProfileOptions(token) {
    return {
      name: 'Profile from extension',
      shellPath: 'cmd.exe'
    };
  }
});
```

If the proposed API is enabled, the extension contributed profile should show up in the profiles list:

![Extension contributed profile is displayed in the profiles list](images/1_57/terminal-api-profile.png)

### iconPath support in terminal options

The standard `iconPath` type is supported on `TerminalOptions` and `TerminalExtensionOptions` to set the terminal's icon, which is displayed on the terminal's tab:

```ts
readonly iconPath?: Uri | { light: Uri; dark: Uri } | ThemeIcon;
```

### Change title of Pseudoterminal

The new event `Pseudoterminal.onDidChangeName` enabled control of Pseudoterminal-based terminals:

```ts
const writeEmitter = new EventEmitter<string>();
const nameEmitter = new EventEmitter<string>();
const pty = {
    onDidWrite: writeEmitter.event,
    open: () => writeEmitter.fire('Type to change the title of the terminal'),
    close: () => { /* noop*/ },
    handleInput: (data: string) => nameEmitter.fire(data)
};
const terminal = vscode.window.createTerminal({
    name: `My Terminal`,
    pty
});
terminal.show();
```

### Enable file system providers to declare a file as readonly

File system providers can now mark individual files as readonly via a new `permissions` property on the `FileStat` by setting the value of the property to `FilePermission.Readonly`. Readonly files are not editable.

**Note:** If all files should be treated readonly, you can use the existing `isReadonly` option when calling `registerFileSystemProvider`.

### Support workspace URIs in asExternalUri

The pre-existing `vscode.env.asExternalUri` API now supports [handling workspaces URIs](https://github.com/microsoft/vscode/issues/124263). This allows extension authors to create system-wide URIs that directly open the given workspace.

For example:

```ts
const uri = await vscode.env.asExternalUri('file:///Users/<USER>/work/code');
```

The resulting `uri` can be opened by the operating system and VS Code will open that workspace.

## Engineering

### Progress for Electron sandbox support

This milestone we continued to make the VS Code window ready for enabling Electron's [sandbox](https://www.electronjs.org/docs/tutorial/sandbox) and [context isolation](https://www.electronjs.org/docs/tutorial/context-isolation) features.

Specifically:

* We removed Node.js dependencies from more code that is used in the renderer.
* We continued investigating dropping `webview` usage entirely and switching to `iframe` for our custom views and editors.

## Documentation

### VS Code at Build 2021

If you missed the [Microsoft Build 2021](https://mybuild.microsoft.com/) developer event, you can still watch the sessions on-demand. Check out the [Visual Studio Code at Build 2021](https://code.visualstudio.com/blogs/2021/06/02/build-2021) blog post, where we've put together a list of sessions we think will be of interest to VS Code users.

[![What's New in VS Code Build 2021 session on YouTube](images/1_57/whats-new-in-vs-code.png)](https://youtu.be/eKJftIqv8Uo)

### Browser debugging

A new topic [Browser debugging in VS Code](https://code.visualstudio.com/docs/nodejs/browser-debugging) describes the built-in debugging support for the Edge and Chrome browsers. You can configure VS Code to either launch a new browser debugging session (via the **Debug: Open Link** command) or attach to a running browser.

### PyTorch

There is a new topic on how to use the [PyTorch](https://pytorch.org) machine learning framework within VS Code. In the [PyTorch support in VS Code](https://code.visualstudio.com/docs/datascience/pytorch-support) article, you'll learn how Jupyter Notebooks can be extended with PyTorch and [TensorFlow](https://www.tensorflow.org) data types.

[![PyTorch logo](images/1_57/pytorch-logo-dark.png)](https://pytorch.org)

## Notable fixes

* [71966](https://github.com/microsoft/vscode/issues/71966): Often getting full window hangs
* [85332](https://github.com/microsoft/vscode/issues/85332): Windows: freeze on file delete
* [95077](https://github.com/microsoft/vscode/issues/95077): Terminal "word" link provider does not support wrapping
* [108804](https://github.com/microsoft/vscode/issues/108804): Do not wait for shell environment resolution before opening window
* [120004](https://github.com/microsoft/vscode/issues/120004): Weird letter spacing in Integrated Terminal for VS Code
* [125035](https://github.com/microsoft/vscode/issues/125035): Debug Console filter text should be preserved across reloads
* [iOS/iPadOS](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22May+2021%22+is%3Aclosed+label%3Aios-ipados+): Several fixes were made for the iOS/iPadOS platform

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Nicholas Rayburn (@nrayburn-tech)](https://github.com/nrayburn-tech)
* [Alessandro Fragnani (@alefragnani)](https://github.com/alefragnani)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)

Contributions to `vscode`:

* [@Ashray123 (Ashray Jha)](https://github.com/Ashray123): Modified Markdown preview nested list styling [PR #124445](https://github.com/microsoft/vscode/pull/124445)
* [@barbaravaldez (Barbara Valdez)](https://github.com/barbaravaldez): Initial implementation of drag and drop api [PR #122239](https://github.com/microsoft/vscode/pull/122239)
* [@CommaNet (Melvin )](https://github.com/CommaNet): Update README.md [PR #124654](https://github.com/microsoft/vscode/pull/124654)
* [@danielgary (Daniel Gary)](https://github.com/danielgary): Replaced wmic call with windows-process-tree [PR #123895](https://github.com/microsoft/vscode/pull/123895)
* [@DonJayamanne (Don Jayamanne)](https://github.com/DonJayamanne): Preserve execution summary when converting dto [PR #124549](https://github.com/microsoft/vscode/pull/124549)
* [@eltociear (Ikko Ashimine)](https://github.com/eltociear): fix typo in files.ts [PR #124693](https://github.com/microsoft/vscode/pull/124693)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Allow FileSystemProvider to stat a file as readonly (#73122) [PR #111237](https://github.com/microsoft/vscode/pull/111237)
  * fix #123831 handle command URL in ErrorResponse Message from debug adapter [PR #123833](https://github.com/microsoft/vscode/pull/123833)
* [@habibkarim (Habib Karim)](https://github.com/habibkarim): Implement Pseudoterminal.onDidChangeName [PR #115276](https://github.com/microsoft/vscode/pull/115276)
* [@hantatsang (Sang)](https://github.com/hantatsang): Fix welcome view links missing hover color [PR #121835](https://github.com/microsoft/vscode/pull/121835)
* [@huszkacs (Csaba Huszka)](https://github.com/huszkacs): Fixes android screen-keyboard backspace issue. [PR #124288](https://github.com/microsoft/vscode/pull/124288)
* [@inidaname (Hassan Sani)](https://github.com/inidaname): added bottom spacing for table in Markdown preview [PR #124385](https://github.com/microsoft/vscode/pull/124385)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes pascalCase snippet formatter fails to process numbers [PR #122796](https://github.com/microsoft/vscode/pull/122796)
  * Add support for file drop on a terminal tab [PR #123429](https://github.com/microsoft/vscode/pull/123429)
  * Fixes incorrect decoding of percent encoded markdown links [PR #123584](https://github.com/microsoft/vscode/pull/123584)
  * Fixes process explorer does not show twistie [PR #123910](https://github.com/microsoft/vscode/pull/123910)
  * Fixes process explorer list styles are not working [PR #123919](https://github.com/microsoft/vscode/pull/123919)
  * Fixes terminal find widget should not cover tabs [PR #124317](https://github.com/microsoft/vscode/pull/124317)
  * Fixes listener leak while updating terminal pane actions [PR #124741](https://github.com/microsoft/vscode/pull/124741)
  * Preserve repl filter value through reloads [PR #125221](https://github.com/microsoft/vscode/pull/125221)
* [@kabel (Kevin Abel)](https://github.com/kabel): Implement GitHub Enterprise authn provider [PR #115940](https://github.com/microsoft/vscode/pull/115940)
* [@leilapearson (Leila Pearson)](https://github.com/leilapearson): New Sort Order Lexicographic Options setting for Explorer [PR #97272](https://github.com/microsoft/vscode/pull/97272)
* [@mzainuddin51 (Mohammad Zainuddin)](https://github.com/mzainuddin51): fix incorrect links for issue #123772 [PR #124203](https://github.com/microsoft/vscode/pull/124203)
* [@nrayburn-tech (Nicholas Rayburn)](https://github.com/nrayburn-tech)
  * add color customizations for inline debug values [PR #123065](https://github.com/microsoft/vscode/pull/123065)
  * Fix issue reporter not closing on submit and debounce submissions [PR #123340](https://github.com/microsoft/vscode/pull/123340)
  * Add showDeprecated option to suggest, filters out deprecated options [PR #123351](https://github.com/microsoft/vscode/pull/123351)
  * add notification if tsserver logs are left on for more than 7 days [PR #124149](https://github.com/microsoft/vscode/pull/124149)
  * option to hide terminal tabs if there is only a single group [PR #124480](https://github.com/microsoft/vscode/pull/124480)
* [@plankp (Paul Teng)](https://github.com/plankp): Add pattern matching 'in' [PR #121941](https://github.com/microsoft/vscode/pull/121941)
* [@rtzoeller (Ryan Zoeller)](https://github.com/rtzoeller): Register .mak extension for the Make language [PR #122614](https://github.com/microsoft/vscode/pull/122614)
* [@shskwmt (Shunsuke Iwamoto)](https://github.com/shskwmt): Fixes #116335: Send a file path text to the dropped terminal area [PR #116621](https://github.com/microsoft/vscode/pull/116621)
* [@suzmue (Suzy Mueller)](https://github.com/suzmue): Fix setting the debug call stack display for exceptions [PR #123726](https://github.com/microsoft/vscode/pull/123726)
* [@tejasvi (Tejasvi S. Tomar)](https://github.com/tejasvi): Improve docs for TextEditor.setDecorations [PR #122785](https://github.com/microsoft/vscode/pull/122785)
* [@ValeraS (Valeriy)](https://github.com/ValeraS): fix: use addListener instead of addEventListener to to observe media query lists [PR #121557](https://github.com/microsoft/vscode/pull/121557)
* [@vibhavsarraf (Vibhav)](https://github.com/vibhavsarraf): Normalize path in terminal word links [PR #123246](https://github.com/microsoft/vscode/pull/123246)
* [@wendellhu95 (Wendell)](https://github.com/wendellhu95): test: add test for event Relay [PR #119070](https://github.com/microsoft/vscode/pull/119070)

Contributions to `vscode-css-languageservice`:

* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fixes link resolution for filename with a suffix containing a dot [PR #241](https://github.com/microsoft/vscode-css-languageservice/pull/241)

Contributions to `vscode-eslint`:

* [@edupsousa (Eduardo Pereira de Sousa)](https://github.com/edupsousa): Add severity "off" to eslint.rules.customizations [PR #1243](https://github.com/microsoft/vscode-eslint/pull/1243)
* [@karlhorky (Karl Horky)](https://github.com/karlhorky): Document "node" for system version of Node.js [PR #1247](https://github.com/microsoft/vscode-eslint/pull/1247)

Contributions to `vscode-js-debug`:

* [@danielgary (Daniel Gary)](https://github.com/danielgary): 990 - Support using launch configuration name as terminal title [PR #991](https://github.com/microsoft/vscode-js-debug/pull/991)
* [@lhchavez](https://github.com/lhchavez): feat: Allow callers of debugServerMain.ts to specify the host [PR #1006](https://github.com/microsoft/vscode-js-debug/pull/1006)

Contributions to `vscode-languageserver-node`:

* [@yanmofeixi](https://github.com/yanmofeixi): Add more info to client not ready error [PR #764](https://github.com/microsoft/vscode-languageserver-node/pull/764)

Contributions to `vscode-pull-request-github`:

* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH): Removed TSLint recommendation and mentions left [PR #2758](https://github.com/microsoft/vscode-pull-request-github/pull/2758)

Contributions to `vscode-vsce`:

* [@jedwards1211 (Andy Edwards)](https://github.com/jedwards1211): use ignore npm package instead of custom code for .vscodeignore [PR #418](https://github.com/microsoft/vscode-vsce/pull/418)
* [@oliversalzburg (Oliver Salzburg)](https://github.com/oliversalzburg): fix: Missing error response handling [PR #564](https://github.com/microsoft/vscode-vsce/pull/564)

Contributions to `language-server-protocol`:

* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Clarify how textDocument/selectionRange can handle some missing results [PR #1272](https://github.com/microsoft/language-server-protocol/pull/1272)
* [@HO-COOH](https://github.com/HO-COOH): Update specification-3-16.md [PR #1259](https://github.com/microsoft/language-server-protocol/pull/1259)
* [@narnaud (Nicolas Arnaud-Cormos)](https://github.com/narnaud)
  * Fix missing semicolon in specification 3.16 and 3.17 [PR #1265](https://github.com/microsoft/language-server-protocol/pull/1265)
  * Fix missing semi-colon in SemanticTokensClientCapabilities [PR #1268](https://github.com/microsoft/language-server-protocol/pull/1268)
* [@NTaylorMullen (N. Taylor Mullen)](https://github.com/NTaylorMullen)
  * Add ability to link various portions of the LSP site. [PR #1274](https://github.com/microsoft/language-server-protocol/pull/1274)
  * Clarify isIncomplete feature behavior. [PR #1282](https://github.com/microsoft/language-server-protocol/pull/1282)

Contributions to `monaco-languages`:

* [@akonatala (apoorva konatala)](https://github.com/akonatala): Update cameligo language support [PR #137](https://github.com/microsoft/monaco-languages/pull/137)
* [@anark](https://github.com/anark): Add aliases and mimetypes to liquid language [PR #136](https://github.com/microsoft/monaco-languages/pull/136)
* [@mattvague (Matt Vague)](https://github.com/mattvague): Fix incorrect filenames for liquid [PR #135](https://github.com/microsoft/monaco-languages/pull/135)

Contributions to `monaco-typescript`:

* [@paranoidjk (paranoidjk)](https://github.com/paranoidjk): fix: support go to definition for extraLib file [PR #79](https://github.com/microsoft/monaco-typescript/pull/79)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
