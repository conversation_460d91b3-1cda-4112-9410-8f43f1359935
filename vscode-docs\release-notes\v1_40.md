---
Order:
TOCTitle: October 2019
PageTitle: Visual Studio Code October 2019
MetaDescription: See what is new in the Visual Studio Code October 2019 Release (1.40)
MetaSocialImage: 1_40/release-highlights.png
Date: 2019-11-07
DownloadVersion: 1.40.2
---
# October 2019 (version 1.40)

**Update 1.40.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2019+Recovery%22+is%3Aclosed).

**Update 1.40.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2019+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the October 2019 release of Visual Studio Code. As announced in the [October iteration plan](https://github.com/microsoft/vscode/issues/82194), we focused on housekeeping GitHub issues and pull requests as documented in our [issue grooming guide](https://github.com/microsoft/vscode/wiki/Issue-Grooming). Across all of our VS Code repositories, we closed (either triaged or fixed) **4622** issues, which is even more than during our last housekeeping iteration in September 2018, where we closed **3918** issues. While we closed issues, you created **2195** new issues. This resulted in a net reduction of **2427** issues. The main [vscode repository](https://github.com/microsoft/vscode/issues) now has 2162 open feature requests and 725 open bugs. In addition, we closed **287** pull requests. As part of this effort, we have also tuned our process and updated the [issue triaging workflow](https://github.com/microsoft/vscode/wiki/Issues-Triaging).

Same as last year, we used the [live tracker](https://vscode-issue-tracker.netlify.com/) from [Benjamin Lannon](https://github.com/lannonbr) to track our progress:

![Burn down chart of VS Code issues](images/1_40/october-issue-grooming.png)

During this housekeeping milestone, we also addressed several feature requests and community [pull requests](#thank-you). Read on to learn about new features and settings.

## Workbench

### Activity Bar indicator

We've introduced a new indicator for the active item in Activity Bar to make it stand out better and increase readability. We also tuned the inactive foreground colors for a stronger contrast with the active element.

![Active Activity Bar indicator](images/1_40/activity-bar-indicator.gif)

You can control the active indicator via the new color token, `activityBar.activeBorder`. We also introduced an optional background color for the active element, `activityBar.activeBackground`, and when configured can look like so:

![Active Activity Bar indicator custom background](images/1_40/activity-bar-indicator-2.png)

### Themable window border

We've introduced two new theme colors, `window.activeBorder` and `window.inactiveBorder`, for providing a border around the VS Code window. The `window.activeBorder` applies to the active (focused) window, while the `window.inactiveBorder` applies to inactive (unfocused) windows. These new colors do not inherit from any color, so they must either be provided by the theme or by the `workbench.colorCustomizations` setting. If only one color is specified, a border with that color will be applied to both active and inactive windows.

![Animated image showing the use of the new window border colors](images/1_40/window-border.gif)

### List and tree keyboard scrolling

You can now press `kb(list.scrollUp)` and `kb(list.scrollDown)` to scroll lists and trees using the keyboard.

### Fewer notifications in Zen mode

While using Zen mode, notification toasts will no longer distract you unless they inform about errors. You can control this behavior with the new `zenMode.silentNotifications` setting. You can always access all notifications from the status bar by clicking on the notification icon in the bottom-right corner.

### Type filters for outline and breadcrumbs

There are new settings to configure what symbols are displayed in the Outline view and breadcrumb navigation. The settings are called `outline.show{symbol type}` and `breadcrumbs.show{symbol type}`. For example, `outline.showVariables`, `outline.showFunctions`, etc.

![Filtered Outline view](images/1_40/outline_filtered.png)

The screen shot above shows the Outline view configured to not show local variables and you can see that the variable `hidden` isn't shown.

Note that it's up to the language extension to assign the type of a symbol. You can hover over the symbol label in the Outline view to see which symbol type is being used (shown in parentheses).

### Control the sizing behavior when splitting editors

A new setting `workbench.editor.splitSizing` controls the layout of editors when splitting them. By default, the size will be distributed evenly among all editors as shown below:

![splitSizing with distribute value](images/1_40/split-distribute.gif)

Change this setting to `split` for the current editor to be split in half and not affect the size of other editors:

![splitSizing with split value](images/1_40/split-split.gif)

### Disable GPU acceleration

We have heard issue reports from users that seem related to how the GPU is used to render VS Code's UI. These users have a much better experience when running VS Code with the additional `--disable-gpu` command-line argument. Running with this argument will disable the GPU hardware acceleration and fall back to a software renderer.

To make life easier, you can add this flag as a setting so that it does not have to be passed on the command line each time.

To add this flag:

* Open the Command Palette (`kb(workbench.action.showCommands)`).
* Run the **Preferences: Configure Runtime Arguments** command.
* This command will open a `argv.json` file to configure runtime arguments. You might see some default arguments there already.
* Add `"disable-hardware-acceleration": true`.
* Restart VS Code.

**Note:** Do not use this setting unless you are seeing issues!

## Editor

### files.eol per language

The `files.eol` setting can now be set per language. For example, you could define that all files of 'mylanguage' language identifier use `\n` as the default end-of-line sequence for all new files.

```jsonc
"[mylanguage]": {
   "files.eol": "\n"
}
```

The end-of-line sequence is used for new files. For existing files, the current end-of-line sequence is always preserved. To change the end-of-line sequence for an existing file, use the **Change End Of Line Sequence** command.

Using a `files.eol` language setting, the default end-of-line sequence for 'shellscript' is now defined as `\n`.

### Minimap scaling and improved font rendering

A new setting `editor.minimap.scale` configures the minimap's size to one of several constant values, particularly useful for high DPI or ultra-wide displays. Also, the rendering of the minimap's font is now smoother and clearer.

![Animated image showing the ability to resize the minimap](images/1_40/minimap-scaling.gif)

### Definition Preview Hover from the keyboard

There is a new command **Show Definition Preview Hover** for better accessibility of the definition preview hover widget, which can be triggered by hovering a symbol with the mouse and pressing a modifier key dependent on the platform and configuration. Previously, only the command **Show Hover** was provided, which is equivalent to hovering the mouse cursor over a symbol. Now, with **Show Definition Preview Hover**, the detailed hover information can be shown via the keyboard.

![Animated image showing the use of the new Show Definition Preview Hover command](images/1_40/definition-preview-hover.gif)

### Improvements to bracket matching

Previously, VS Code would only highlight matching brackets when the cursor was next to a bracket character. Now VS Code will always highlight enclosing brackets if they exist.

![Image showing highlighted enclosing brackets](images/1_40/bracket-matching-enclosing.png)

 We have also adjusted our **Go to Bracket** and **Select to Bracket** actions to use enclosing brackets.

### Duplicate selection

We have added a new action named **Duplicate Selection**. When executed, the current selection will be duplicated and the result will be selected. When there is no selection, the current line will be duplicated, all without writing to the system clipboard.

### Font feature settings

There is now more fine grained control over the font features. When configuring `"editor.fontLigatures": true`, VS Code would turn on `liga` and `calt`. But some fonts have more settings, such as stylistic sets used by [Fira Code](https://github.com/tonsky/FiraCode/releases).

We now allow these font features to be explicitly controlled, for example:

```js
"editor.fontFamily": "Fira Code",
"editor.fontLigatures": true,
"[javascript]": {
  "editor.fontLigatures": "'ss02', 'ss19'",
},
```

The above settings configure the editor to use the **Fira Code** font family, turn on font ligatures for all languages, and in JavaScript files enable the 'Horizontal Bar' ('ss02') and the 'Dotted zero' ('ss19') font feature settings. These make the `>=` ligature render with a horizontal bar and the `0` render with a dot inside it in JavaScript:

![Image showing font feature settings in the editor](images/1_40/font-feature-settings.png)

### Better handling for Unicode combining characters

We have made improvements around handling of [Unicode combining characters](https://en.wikipedia.org/wiki/Combining_character) and we treat such sequences as atomic characters (in cursor movement, editing operations, or in block cursor rendering). This is best demonstrated using the block cursor style and a before/after animation:

Before:<br/>
![Animation showing cursor movement before](images/1_40/unicode-before.gif)

After:<br/>
![Animation showing cursor movement after](images/1_40/unicode-after.gif)

In a related change, the status bar now shows the Unicode point count in the `Col` section.

### Cursor Redo

The **Soft Undo** command has been renamed to **Cursor Undo**. This action is useful, for example when using `kb(editor.action.addSelectionToNextFindMatch)` to select the next match and going one time too far, as it undoes only the last cursor state change. Additionally, we now have added **Cursor Redo**, which redoes the previously undone cursor state change.

### Diff editor improvements

The built-in diff computation used to have a hard-coded timeout of 5 seconds, after which it would give up and return a non-minimal diff. You can now configure this timeout using `"diffEditor.maxComputationTime"` and setting the timeout to `0` will always compute a minimal diff, no matter how long that would take. Additionally, the diff editor now renders a progress bar when the computation takes longer than one second.

A source of confusion when using the diff editor was when comparing files with only leading or trailing whitespace differences and the diff editor was configured to ignore trim whitespace. This would result in Source Control showing the files as modified, while the diff editor would render no diffs. VS Code now displays a **Show Whitespace Differences** button when it detects this case to help you quickly show diffs in whitespace. This can be toggled using the **Show/Ignore Trim Trailing Whitespace Difference** command at the top of the diff editor:

![Animation for button that shows when comparing two files that contain only whitespace diffs but the diff editor is configured to ignore trim whitespace](images/1_40/diff-editor-show-whitespace-button.gif)

## Integrated Terminal

### Chords are now supported by default

The new setting `terminal.integrated.allowChords` (default `true`) allows keybindings that are part of a chord keybinding to skip the terminal for evaluation and instead get handled by VS Code (bypassing the `terminal.integrated.commandsToSkipShell` allow list).

Note that the same limitations around keybindings in the terminal still apply; the chord keybinding with the highest priority will be considered when making this evaluation.

For example, if an extension contributes keybinding `kbstyle(Ctrl+M A)` and you have the following defined in your `keybindings.json` file:

```json
[
  {"key": "ctrl+m a", "command": "workbench.action.terminal.toggleTerminal" },
  {"key": "ctrl+m a", "command": "workbench.action.focusLastEditorGroup" }
]
```

`kbstyle(Ctrl+M A)` will result in the `workbench.action.focusLastEditorGroup` command executing since it's the final keybinding and [user keybindings are of higher priority](https://code.visualstudio.com/docs/getstarted/keybindings#_keyboard-rules) than extension contributed keybindings.

### Fast scroll support

The terminal now supports `kbstyle(Alt)` with mouse wheel to scroll faster based on the multiplier defined in the `editor.fastScrollSensitivity` setting.

### New "paste" option for right-click behavior

A new option has been added to the `rightClickBehavior` setting to always paste in the terminal on right-click:

```json
"terminal.integrated.rightClickBehavior": "paste"
```

### Double-click to select word uses more separator characters

The characters `,`, `:`, and `;` are now counted as word separators when double-clicking to select a word(s) in the terminal.

### Symlink cwds are no longer resolved

If a terminal's initial working directory is set to a symlink, the symlink will now be preserved in the terminal.

### Command navigation now works from the scroll position

Command navigation enables navigating between or selecting between commands that have been run in the terminal. This now takes the scroll position of the terminal into account so you can scroll, find the previous command, and the terminal will scroll up from the terminal's viewport (previously it would scroll from the bottom).

![Command navigation jumps to nearest command after scrolling](images/1_40/command-navigation.gif)

Command navigation is only bound on macOS by default as `Cmd+Up/Down` and command selection as `Cmd+Shift+Up/Down`.

Here are some example keybindings if you want to use this feature on Windows or Linux:

```jsonc
{
  // Terminal command tracking on Windows and Linux
  { "key": "ctrl+up",          "command": "workbench.action.terminal.scrollToPreviousCommand", "when": "terminalFocus" },
  { "key": "ctrl+down",        "command": "workbench.action.terminal.scrollToNextCommand", "when": "terminalFocus" },
  { "key": "ctrl+shift+up",    "command": "workbench.action.terminal.selectToPreviousCommand", "when": "terminalFocus" },
  { "key": "ctrl+shift+down",  "command": "workbench.action.terminal.selectToNextCommand", "when": "terminalFocus" },
}
```

## Tasks

### Task Quick Pick lists

The **Tasks: Configure Task** command now always skips the Quick Pick list of tasks if you only have one task defined. To extend this behavior to **Tasks: Run Task** and other task Quick Picks, you can set the `task.quickOpen.skip` setting to `true`.

The new setting `task.quickOpen.history` controls how many tasks are shown in the task Quick Pick as **recently used** tasks. Set `task.quickOpen.history` to `0` to disable the recent tasks section of the tasks Quick Pick.

Tasks that have a `detail` property will now show it in some task lists. You can turn off details in the task Quick Pick by setting `task.quickOpen.detail` to `false`.

### Option to run NPM scripts for a folder

When the setting `npm.enableRunFromFolder` is enabled, the File Explorer's context menu shows the command **Run NPM Script in Folder...** when a folder is selected. The command shows a Quick Pick list of the NPM scripts contained in this folder and you can select the script to be executed as a task.

### Slow task provider warning

When a task provider is running slowly, we now show a warning offering suggestions for how to avoid the slowdown. The warning can be disabled for individual task types, or it can be disabled for all tasks by setting `task.slowProviderWarning` to `false`.

![Slow task provider warning](images/1_40/slow-taskprovider.png)

### Disable task autodetection

If all the tasks you care about are defined in a `tasks.json` file, then you might want to disable all extension task providers to improve performance when you run tasks. It is good practice for individual extensions to provide a setting to disable their task providers, but you can now disable all task providers setting  `task.autoDetect` to `off`.

### Problem matcher prompt

In the past, any task without a problem matcher would need to have an empty problem matcher set in `tasks.json` to skip the problem matcher prompt. Now, you can use `task.problemMatchers.neverPrompt` to disable the problem matcher prompt for all tasks or for specific task types. There's also a new option in the problem matcher prompt to disable the prompt.

![Problem matcher prompt dropdown](images/1_40/task-problemmatcher-prompt.png)

## Source Control

### Highlight file on active editor change

The Source Control view will now automatically highlight the active editor file in its tree/list view:

![SCM view highlighting the active file](images/1_40/scm-highlight.gif)

### Git: Improved untracked files management

You can now manage untracked files separately by using the **Git: Untracked Changes** setting. Choose the `separate` option, if you'd like to see untracked files in a separate group in the Source Control view. Choose `hidden` if you'd like to never see them. The default commit action will adjust itself to this setting and will only include untracked changes in a commit when using the `mixed` setting value.

![Git untracked files](images/1_40/untracked.gif)

### Git: Automatic commit message on merge commits

Whenever VS Code detects you're in a middle of a git merge commit, it will automatically populate the commit input box with Git's default commit message.

![Git merge commit message](images/1_40/conflict.png)

### Git: Reveal in Explorer

There's now a context menu action **Reveal in Explorer** to reveal files in the File Explorer, from a Git repository in the Source Control view.

![Git Reveal in Explorer](images/1_40/reveal-in-explorer.png)

### Git: Clone progress

The clone operation now supports displaying progress in the bottom-right corner notification:

![Git clone progress](images/1_40/clone.gif)

## Debugging

### Breakpoints can be shown in overview ruler

To make life easier when working with breakpoints in large files, you can now show breakpoints in the editor's overview ruler by enabling the new setting `debug.showBreakpointsInOverviewRuler`.

![breakpoints in overview ruler](images/1_40/bpts-in-overview.png)

### PreLaunch tasks for compound debug configurations

Today every debug configuration can specify a task to be run before the debug session is started via the `preLaunchTask` attribute. This works well when debugging a single program, but it gets unwieldy when using compound configurations where multiple programs are involved.

In this milestone, we've added support for a `preLaunchTask` per compound debug configuration. With this addition, a single task can build all debug targets before the individual debug sessions are started. In case of a build error, no session is started.

![prelaunch task for compound debug configurations](images/1_40/prelaunch-compound-config.png)

## Languages

### CSS media query symbols

In CSS/SCSS/Less files, media query symbols are now shown in the Outline view, the breadcrumb path, and the **Go to Symbols in File** list:

![CSS media query symbols](images/1_40/css-media-query-symbols.png)

### typescript.tsserver.maxTsServerMemory

If you are working with a large TypeScript project containing thousands of files, the new `typescript.tsserver.maxTsServerMemory` setting lets you increase the maximum memory usage for the TypeScript server that powers IntelliSense.

## Extension authoring

### vscode.env.uiKind

A new API `vscode.env.uiKind` was added so that an extension can know in which kind of UI is it running. To support running VS Code in a browser, the possible values are `UIKind.Web` and `UIKind.Desktop`.

### Call Hierarchy Provider API

We have finalized the call hierarchy provider API: [CallHierarchyProvider](https://github.com/microsoft/vscode/blob/3a5209ec6474137f69078727f7fe255c97fb8795/src/vs/vscode.d.ts#L3983-L4021). It supports the **Peek Call Hierarchy** feature, which finds callers to and calls from functions, methods, etc.

### Task CustomExecution

The `CustomExecution` task type API has been finalized. If a shell or process task isn't suitable for your task, you can use a `CustomExecution` task to execute a callback in your extension. Learn more in the [task provider extension guide](https://code.visualstudio.com/api/extension-guides/task-provider#customexecution).

### Debug API: "consoleMode" option for "startDebugging" request

In the last milestone, we had introduced a [new proposed API](https://code.visualstudio.com/updates/v1_39#_startdebugging-consolemode-option-to-merge-debug-consoles)
for controlling whether a hierarchy of debug sessions shares a single Debug Console or use individual consoles. This API is now official so you can use it in extensions published on the Marketplace.

### Icon color tokens

We're excited to finally introduce new color tokens for our icons. We're slowly adding these for certain areas, below is the first batch.

**Symbols**

These icons appear in the Outline view, breadcrumb navigation, and suggest widget.

* `symbolIcon.arrayForeground`
* `symbolIcon.booleanForeground`
* `symbolIcon.classForeground`
* `symbolIcon.colorForeground`
* `symbolIcon.constructorForeground`
* `symbolIcon.constantForeground`
* `symbolIcon.enumeratorForeground`
* `symbolIcon.enumeratorMemberForeground`
* `symbolIcon.eventForeground`
* `symbolIcon.fieldForeground`
* `symbolIcon.fileForeground`
* `symbolIcon.folderForeground`
* `symbolIcon.functionForeground`
* `symbolIcon.interfaceForeground`
* `symbolIcon.keyForeground`
* `symbolIcon.keywordForeground`
* `symbolIcon.methodForeground`
* `symbolIcon.moduleForeground`
* `symbolIcon.namespaceForeground`
* `symbolIcon.nullForeground`
* `symbolIcon.numberForeground`
* `symbolIcon.objectForeground`
* `symbolIcon.operatorForeground`
* `symbolIcon.packageForeground`
* `symbolIcon.propertyForeground`
* `symbolIcon.referenceForeground`
* `symbolIcon.snippetForeground`
* `symbolIcon.stringForeground`
* `symbolIcon.structForeground`
* `symbolIcon.textForeground`
* `symbolIcon.typeParameterForeground`
* `symbolIcon.unitForeground`
* `symbolIcon.variableForeground`

![Symbol icon colors](images/1_40/icon-symbols.png)

**Problems and Notifications**

We've set the default colors for the problems and notifications icons to inherit from the editor foreground colors, so if you change those colors, it will inherit to the other areas:

![Problem icon colors](images/1_40/icon-editor.png)

You can also explicitly set the icon colors for the Problems panel and Notifications:

* `problemsErrorIcon.foreground`
* `problemsWarningIcon.foreground`
* `problemsInfoIcon.foreground`
* `notificationsErrorIcon.foreground`
* `notificationsInfoIcon.foreground`
* `notificationsWarningIcon.foreground`

**Lightbulb**

The lightbulb icons are also themable:

* `editorLightBulb.foreground`
* `editorLightBulbAutoFix.foreground`

![Lightbulb icon colors](images/1_40/icon-lightbulb.png)

### vscode.env.asExternalUri

The `vscode.env.asExternalUri` API allows an extension to resolve an **external** URI - such as a `http:` or `https:` URI - from where the extension is running to a URI for the same resource on the client machine. This is a companion to the `vscode.env.openExternal` API, except instead of opening the resolved URI using an external program, it returns the result to extensions.

```ts
import * as vscode from 'vscode';
import * as http from 'http';

const PORT = 3000;

export function activate(context: vscode.ExtensionContext) {
  startLocalServer(PORT);

  context.subscriptions.push(vscode.commands.registerCommand('example.command', async () => {
      const resolved = vscode.env.asExternalUri(vscode.Uri.parse(`http://localhost:${PORT}`));
      vscode.window.showInformationMessage(`Resolved to: ${resolved} on client and copied to clipboard`);
      vscode.env.clipboard.writeText(resolved.toString());
  }));
}

function startLocalServer(port: number) {
  const server = http.createServer((req, res) => {
    res.end('Hello world!');
  });
  server.on('clientError', (err, socket) => {
    socket.end('HTTP/1.1 400 Bad Request\r\n\r\n');
  });
  server.listen(port);
  vscode.window.showInformationMessage(`Started local server on port: ${port}`);
}
```

For `http:` and `https:` URIs, `asExternalUri` is a no-op if the extension is running on the client machine. However, if the extension is running remotely, `vscode.env.asExternalUri` automatically establishes a port forwarding tunnel from the local machine to `target` on the remote and returns a local URI to the tunnel.

If `asExternalUri` is called with a `vscode.env.uriScheme` URI, then it returns a URI that - if opened in a browser (for example via `openExternal`) - will result in the registered URI handler being triggered.

### Removal of experimental Custom Data settings and Contribution Points

`[html/css].experimental.customData` settings and `contributes.[html/css].experimental.customData` Contribution Points were [deprecated in 1.38](https://code.visualstudio.com/updates/v1_38#_custom-data-marked-as-stable) and now removed. You can use the stable Custom Data settings and Contribution Points instead. To read more about the Custom Data Format, see the [microsoft/vscode-custom-data](https://github.com/microsoft/vscode-custom-data) repository.

### extensionKind can be an array

In `package.json`, the `extensionKind` property could have the value `"ui"` or `"workspace"` to indicate where an extension should run in the remote case. With this release, `extensionKind` can now be an array. For example, `["ui", "workspace"]` would indicate that an extension can run both on the `"ui"` and `"workspace"` side, with a preference to running on the `"ui"` side, if installed there.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Support IntelliSense replace mode

We are working on a feature that allows users to decide if suggestions should replace existing text or just be inserted. For example, accepting `console` inside `con|st` can result in `console` (replace) or `consolest` (insert). Both modes have merit and it eventually depends on the current context and user preference. For this feature to work best, we need extensions to tell us what to do. Therefore we are proposing to add a new type to the `CompletionItem#range` property, changing it like this:

```ts
interface CompletionItem {
  range?: Range | { insert: Range; replace: Range; };
}
```

The range continues to be optional or be a simple range but it can also be two ranges: one for insert and one for replace. See [Issue #10266](https://github.com/microsoft/vscode/issues/10266) for more details.

### Task detail

There is a new property on `Task` that task providers can use to add a `detail` to the task when it is shown in UI. You can also use the detail property in `tasks.json`. The built-in npm extension uses the task detail to show which npm script will run.

![tasks with detail](images/1_40/task-detail.png)

### Support for passing WebviewPanelOptions to Custom Editors

We continued our work on the [Custom Editor API proposal](https://github.com/microsoft/vscode/issues/82115) this iteration. Now the proposed `registerWebviewEditorProvider` function takes a set of `options` that lets you enable [`retainContextWhenHidden`](https://code.visualstudio.com/api/extension-guides/webview#retaincontextwhenhidden) and other features in custom editor webviews:

```ts
export function registerWebviewEditorProvider(
  viewType: string,
  provider: WebviewEditorProvider,
  options?: WebviewPanelOptions
): Disposable;
```

## Engineering

### Test VS Code running in a browser

There is now a minimal setup where VS Code can run in a browser that is available for development and testing. The browser instance is still missing some features and is under active development.

In your local fork of the [vscode](https://github.com/microsoft/vscode) repository, execute `yarn web` from the command line and access `http://localhost:8080/`. For more details about cloning and building the vscode repo, see the [setup instructions](https://github.com/microsoft/vscode/wiki/How-to-Contribute).

![VS Code running in a browser](images/1_40/yarn-web.png)

**Note:** Do not use this setup for any serious development. The intent is to allow the community to contribute changes back to VS Code to improve the overall experience in the browser.

### Building VS Code with TypeScript 3.7

VS Code is now built using TypeScript 3.7. This is an exciting update as it lets us use [optional chaining (`?.`)](https://devblogs.microsoft.com/typescript/announcing-typescript-3-7-beta/) in our codebase (as well as bringing many other improvements).

You can start using TypeScript 3.7 features in VS Code today by installing the [JavaScript and TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

### Electron 6.0 Update

In this milestone, we finished the exploration of bundling Electron 6 into VS Code, making it the first time this Electron version ships with stable. This is a major Electron release and comes with Chromium 76.0.3809.146 and Node.js 12.4.0 (a major leap forward from our current version with Chromium 69 and Node.js 10.11.0).

We have started to explore updating to Electron 7, which we hope to push to Insiders soon.

## Notable fixes

* [78634](https://github.com/microsoft/vscode/issues/78634): Terminal content goes missing when resizing (powershell/conpty)
* [25854](https://github.com/microsoft/vscode/issues/25854): Can't drag Markdown preview to start a new editor group
* [81824](https://github.com/microsoft/vscode/issues/81824): Webview editor doesn't appear in Open Previous Editor From History command

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)

Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues.

Contributions to `vscode`:

* [Adam Burgess (@adamburgess)](https://github.com/adamburgess): Add paste option on right click for terminal [PR #81448](https://github.com/microsoft/vscode/pull/81448)
* [André Casal (@andrecasal)](https://github.com/andrecasal): Clarified git.confirmEmptyCommits description [PR #83257](https://github.com/microsoft/vscode/pull/83257)
* [Anirudh Rayabharam (@anirudhrb)](https://github.com/anirudhrb)
  * Use the editor font size for the breakpoint widget [PR #83153](https://github.com/microsoft/vscode/pull/83153)
  * Show breakpoints in overview ruler [PR #82787](https://github.com/microsoft/vscode/pull/82787)
* [Anthony Dresser (@anthonydresser)](https://github.com/anthonydresser): Update sql language syntax [PR #77601](https://github.com/microsoft/vscode/pull/77601)
* [Brian Malehorn (@bmalehorn)](https://github.com/bmalehorn): search: remove unused config settings [PR #82019](https://github.com/microsoft/vscode/pull/82019)
* [Callum Mellor-Reed (@callummr)](https://github.com/callummr): Focus correct SCM repo when restoring editors [PR #79566](https://github.com/microsoft/vscode/pull/79566)
* [Dave Alongi (@dalongi)](https://github.com/dalongi): Hide Help > Report Issue menu item when issue reporter is disabled in product.json [PR #83561](https://github.com/microsoft/vscode/pull/83561)
* [Dhairya Nadapara (@dhairyanadapara)](https://github.com/dhairyanadapara): added split pane options to settings [PR #82888](https://github.com/microsoft/vscode/pull/82888)
* [DiamondYuan (@DiamondYuan)](https://github.com/DiamondYuan): Support Update VS Code from Command Palette [PR #77515](https://github.com/microsoft/vscode/pull/77515)
* [Drew Cross (@drew212)](https://github.com/drew212): Adding git stash drop for issue 76195 [PR #76342](https://github.com/microsoft/vscode/pull/76342)
* [Edward Thomson (@ethomson)](https://github.com/ethomson)
  * GitHub Actions: only run on main branch [PR #83158](https://github.com/microsoft/vscode/pull/83158)
  * CI Builds with GitHub Actions [PR #82992](https://github.com/microsoft/vscode/pull/82992)
* [Alexandr Fadeev (@fadeevab)](https://github.com/fadeevab): Makefile testcase for updated scheme to support the following: var:=$(val:.c=.o) [PR #83121](https://github.com/microsoft/vscode/pull/83121)
* [Andrew Wong (@featherbear)](https://github.com/featherbear): Change checks for present command line arguments [PR #83311](https://github.com/microsoft/vscode/pull/83311)
* [Freddy Robinson (@frobinsonj)](https://github.com/frobinsonj)
  * Filter out dead keys in tree keyboard navigation [PR #82972](https://github.com/microsoft/vscode/pull/82972)
  * Filter out Media keys in tree keyboard navigation [PR #83218](https://github.com/microsoft/vscode/pull/83218)
* [@gawicks](https://github.com/gawicks): When resolving a merge conflict allow accepting the default message [PR #66522](https://github.com/microsoft/vscode/pull/66522)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray): Remove hyphen in formatter-related messages [PR #83538](https://github.com/microsoft/vscode/pull/83538)
* [@HonkingGoose](https://github.com/HonkingGoose): Use HTTPS for link to Emmet cheat sheet. [PR #82754](https://github.com/microsoft/vscode/pull/82754)
* [Hung-Wei Hung (@hunghw)](https://github.com/hunghw): Fix #79428 - add "$WORKSPACE_FOLDER" for snippet [PR #79764](https://github.com/microsoft/vscode/pull/79764)
* [Andrew Liu (@hypercubestart)](https://github.com/hypercubestart): Fix 30419 and 80649 - Allow Setting the Number of Recent Tasks  [PR #82757](https://github.com/microsoft/vscode/pull/82757)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH): Fallback to PCRE2 if match whole word used with regexp [PR #82072](https://github.com/microsoft/vscode/pull/82072)
* [@Jakobeha](https://github.com/Jakobeha): Config option to separate or hide untracked files (addresses #35480) [PR #80083](https://github.com/microsoft/vscode/pull/80083)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fix git repository not detected if root folder ends in [space] [PR #82038](https://github.com/microsoft/vscode/pull/82038)
  * Fixes debug console stops autoscrolling when a line wraps around [PR #82945](https://github.com/microsoft/vscode/pull/82945)
  * Fix scrolling behavior in menu while zoomed in [PR #80965](https://github.com/microsoft/vscode/pull/80965)
* [Jakob Fahr (@jfhr)](https://github.com/jfhr): Fix #82199, numbers hard to read in light theme markdown preview [PR #82450](https://github.com/microsoft/vscode/pull/82450)
* [Liming Jin (@jinliming2)](https://github.com/jinliming2): fix: error when filling in the HTTP proxy address in IPv6 format [PR #77260](https://github.com/microsoft/vscode/pull/77260)
* [Jon Bockhorst (@jmbockhorst)](https://github.com/jmbockhorst)
  * Change terminal link hover widget position to be consistent [PR #83175](https://github.com/microsoft/vscode/pull/83175)
  * Confirm "Undo last commit" if its a merge commit [PR #71525](https://github.com/microsoft/vscode/pull/71525)
  * Add new remote option when publishing branch [PR #71434](https://github.com/microsoft/vscode/pull/71434)
  * Show git clone progress bar and percentage complete [PR #71341](https://github.com/microsoft/vscode/pull/71341)
* [Jonas Platte (@jplatte)](https://github.com/jplatte): linux/bin/code.sh: use command -v instead of which [PR #82097](https://github.com/microsoft/vscode/pull/82097)
* [JavaScript Joe (@jsjoeio)](https://github.com/jsjoeio): fix: remove TypeScript from ExtensionSuggestions [PR #82125](https://github.com/microsoft/vscode/pull/82125)
* [Jesús Alonso Abad (@Kencho)](https://github.com/Kencho): Perl5 support to fold POD blocks [PR #71448](https://github.com/microsoft/vscode/pull/71448)
* [Nikolay Kondratyev (@kondratyev-nv)](https://github.com/kondratyev-nv)
  * Render Octicons in CodeLens [PR #82062](https://github.com/microsoft/vscode/pull/82062)
  * Use yarn compile for start scripts [PR #81840](https://github.com/microsoft/vscode/pull/81840)
* [Kryštof Řeháček (@Krystofee)](https://github.com/Krystofee): Added RANDOM, RANDOM_HEX snippet variables [PR #82529](https://github.com/microsoft/vscode/pull/82529)
* [Kumar Harsh (@kumarharsh)](https://github.com/kumarharsh): fix(workbench): add preference to disable recently used tasks [PR #61994](https://github.com/microsoft/vscode/pull/61994)
* [Lio李欧 (@lionello)](https://github.com/lionello): StartFindWithSelectionAction should not focus the find widget [PR #63176](https://github.com/microsoft/vscode/pull/63176)
* [@MartinBrathen](https://github.com/MartinBrathen): Fixed: Image preview should not zoom on first click if unfocused [PR #82074](https://github.com/microsoft/vscode/pull/82074)
* [Per Persson (@md2perpe)](https://github.com/md2perpe): Add missing preposition [PR #82613](https://github.com/microsoft/vscode/pull/82613)
* [Niklas Mollenhauer (@nikeee)](https://github.com/nikeee): Remove executable bits from non-executable files [PR #82103](https://github.com/microsoft/vscode/pull/82103)
* [@NotWearingPants](https://github.com/NotWearingPants)
  * Add `cursorRedo` command (Ctrl+Shift+J) [PR #82620](https://github.com/microsoft/vscode/pull/82620)
  * Rename `cursorUndo/Redo` commands from "Soft Undo/Redo" to "Cursor Undo/Redo" [PR #82930](https://github.com/microsoft/vscode/pull/82930)
* [Nate Rauh (@NRauh)](https://github.com/NRauh): Feature/pin close others [PR #82545](https://github.com/microsoft/vscode/pull/82545)
* [Oxirt (@oxirt)](https://github.com/oxirt): allow `Git: fetch` command to show password prompt [PR #72615](https://github.com/microsoft/vscode/pull/72615)
* [@simaosilva](https://github.com/simaosilva): support editorHoverWidget.foreground [PR #65170](https://github.com/microsoft/vscode/pull/65170)
* [Prabhanjan S Koushik (@skprabhanjan)](https://github.com/skprabhanjan)
  * Fix #82200 - "Preserve Case" button in search viewlet is not tabbable [PR #82485](https://github.com/microsoft/vscode/pull/82485)
  * Fix-81729 Preserve case in Search and Replace isn't working when I use group substitution [PR #81858](https://github.com/microsoft/vscode/pull/81858)
* [saif (@sksaifuddin)](https://github.com/sksaifuddin): Fix #78014 Added Capability to escape |, < and >in addition to & and ^ [PR #82704](https://github.com/microsoft/vscode/pull/82704)
* [@smilegodly](https://github.com/smilegodly): Add a clear button to settings editor search bar [PR #82904](https://github.com/microsoft/vscode/pull/82904)
* [Sohail Rajdev (@sohailrajdev97)](https://github.com/sohailrajdev97): add support for command line arguments in grunt task runner [PR #82819](https://github.com/microsoft/vscode/pull/82819)
* [Konstantin Solomatov (@solomatov)](https://github.com/solomatov)
  * Replace Disposable[] with DisposableStore [PR #80684](https://github.com/microsoft/vscode/pull/80684)
  * Open New Terminal -> Open New External Terminal [PR #82380](https://github.com/microsoft/vscode/pull/82380)
  * Fix async race condition in the tree [PR #82881](https://github.com/microsoft/vscode/pull/82881)
* [Charles Milette (@sylveon)](https://github.com/sylveon): Skip quick picker when there is only one task to select from [PR #47853](https://github.com/microsoft/vscode/pull/47853)
* [U-ways (@U-ways)](https://github.com/U-ways): fix image preview to show correct canvas size [PR #82027](https://github.com/microsoft/vscode/pull/82027)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
  * Add default styles for <kbd> tags [PR #83300](https://github.com/microsoft/vscode/pull/83300)
  * Add branch name to commit input box [PR #80335](https://github.com/microsoft/vscode/pull/80335)
* [Jeremy Shore (@w9jds)](https://github.com/w9jds)
  * #70254 - Set astrisk as a delimiter for URIs [PR #82816](https://github.com/microsoft/vscode/pull/82816)
  * #75938 - Added in setting for diff gutter visibility [PR #82209](https://github.com/microsoft/vscode/pull/82209)
* [Ԝеѕ (@wesinator)](https://github.com/wesinator): Add .har extension to JSON language [PR #77300](https://github.com/microsoft/vscode/pull/77300)
* [Xhulio Hasani (@xuhas)](https://github.com/xuhas): added deleteTag [PR #74345](https://github.com/microsoft/vscode/pull/74345)
* [Yusuke Yamada (@yamachu)](https://github.com/yamachu): Fixed the problem of overwriting many characters in suggestion [PR #82349](https://github.com/microsoft/vscode/pull/82349)
* [Keyon You (@youngyou)](https://github.com/youngyou): Press `space` key on a disabled button should not trig click event. [PR #81975](https://github.com/microsoft/vscode/pull/81975)
* [Helen3141 (@helen3141)](https://github.com/helen3141): Enable running individual npm scripts in the containing folder [PR #79960](https://github.com/microsoft/vscode/pull/79960)

Contributions to `language-server-protocol`:

* [Adedayo Adetoye (aka Dayo) (@adedayo)](https://github.com/adedayo): Removed duplicate foldingRangeProvider field in ServerCapabilities [PR 848](https://github.com/microsoft/language-server-protocol/pull/848)

Contributions to `vscode-eslint`:

* [Christian Batchelor (@CSBatchelor)](https://github.com/CSBatchelor): README.md Typo [PR #777](https://github.com/microsoft/vscode-eslint/pull/777)

Contributions to `vscode-json-languageservice`:

* [Ben Hutton (@Relequestual)](https://github.com/Relequestual): Updated error message for draft 2019-09 [PR #45](https://github.com/microsoft/vscode-json-languageservice/pull/45)

Contributions to `vscode-css-languageservice`:

* [Sergey Zolotarev (@sryze)](https://github.com/sryze): Wrap URI.parse() call in try ... catch [PR #180](https://github.com/microsoft/vscode-css-languageservice/pull/180)

Contributions to `node-jsonc-parser`:

* [KoyamaSohei (@KoyamaSohei)](https://github.com/KoyamaSohei): Fix typo in README [PR #25](https://github.com/microsoft/node-jsonc-parser/pull/25)
* [Jakub Rożek (@P0lip)](https://github.com/P0lip): parse function should include properties with empty string as their keys [PR #24](https://github.com/microsoft/node-jsonc-parser/pull/24)

Contributions to `debug-adapter-protocol`:

* [Peter Thomas (@ptrthomas)](https://github.com/ptrthomas): adding karate as a dap implementation [PR #78](https://github.com/microsoft/debug-adapter-protocol/pull/78)

Contributions to `vscode-vsce`:

* [Ritwick Dey (@ritwickdey)](https://github.com/ritwickdey): [Added] Relative sources in <img> tags in README.md are not rewritten to absolute URLs [PR #208](https://github.com/microsoft/vscode-vsce/pull/208)
* [Theo Tzaferis (@tzfrs)](https://github.com/tzfrs): Improve error message when packaging an extension with an unchanged README.md [PR #392](https://github.com/microsoft/vscode-vsce/pull/392)

Contributions to `inno-updater`:

* [Adam Coyne (@bhank)](https://github.com/bhank): Increase control width to avoid truncating text [PR #13](https://github.com/microsoft/inno-updater/pull/13)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Bosnian (Latin, Bosnia and Herzegovina):** Adnan Rizvan.
* **Czech:** Michal Franc, Jan Hruby.
* **Danish:** Lasse Stilvang, Frederik bruun.
* **Dutch:** Laurens Kwanten, Lemuel Gomez, Niels ter Haar.
* **English (United Kingdom):** Graham Smith, Martin Littlecott, Alexander McLean.
* **French:** Antoine Griffard, Rodolphe NOEL, Bruno Lewin, Maxime Bouveron, DJ Dakta.
* **German:** Simon Haag, Adrian Bähr, Julian Tomsik.
* **Hebrew:** Ariel Bachar.
* **Hindi:** Kishan K, Rohit Raj.
* **Chinese Simplified:** Yizhi Gu, paul cheung, 斌 项, 顺 谭, 一斤瓜子, Charles Lee, Peng Zeng, Charles Dong, 楠 姜, yungkei fan, ztluo, David Huang, 普鲁文, Zhen-Qi Liu, zhichen zhao, Phil Wang.
* **Chinese Traditional:** Jimmy Hu, 船長, E L, Winnie Lin.
* **Indonesian:** Jakka Prihatna, Eriawan Kusumawardhono, Nicko Satria Utama, Christian Elbrianno, Septian Adi.
* **Italian:** Alessandro Alpi, Luigi Bruno.
* **Japanese:** Yuta Ojima, Michihito Kumamoto, Aya Tokura, TENMYO Masakazu, Takayuki Fuwa, Koichi Makino.
* **Korean:** Hongju, Sungjin Jeong.
* **Polish:** Andrzej Poblocki, Kacper Łakomski.
* **Portuguese (Brazil):** Marcelo Fernandes, Marcondes Alexandre, Roberto Fonseca, Alessandro Fragnani, Arthur Renato, Jota Freitas Jr, Caio Felippe Curitiba Marcellos.
* **Portuguese(Portugal):** Pedro Filipe.
* **Romanian:** Andrei Tudor, Bogdan Mateescu, Stefan Gabos.
* **Russian:** Kazakov Lex, Vadim Svitkin, TJS.
* **Spanish:** Aleks M, José María Aguilar, Jerónimo Milea, Ricardo Estrada Rdez, Abdón Rodríguez P..
* **Swedish:** Sven Axelsson, Patric Högman.
* **Tamil:** Pradeep Krishnan.
* **Turkish:** Ahmetcan Aksu, Cansu Gürler, Mehmet Yönügül, Umut Can Alparslan.
* **Ukrainian:** Arthur Murauskas, Oleh Hatsenko, Oleksandr Krasnokutskyi, Did Kokos.
* **Vietnamese:** huy dk, Việt Anh Nguyễn, Ha Xuan Anh Nguyen, Quoc Han Dinh.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
