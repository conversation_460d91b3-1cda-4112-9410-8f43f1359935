# November 2022 (version 1.74)

## Dev Containers

### GPU host requirement

In the `devcontainer.json` file, you can now specify that a GPU is required:

```json
{
    "image": "mcr.microsoft.com/devcontainers/base:bullseye",
    "hostRequirements": {
        "gpu": true
    }
}
```

The container will be started with GPU enabled (if one is detected).

### Improved `ssh-agent` and `gpg-agent` forwarding

We now also support forwarding to local SSH and GPG agents listening on MINGW and Cygwin socket paths.

### Bundled `@devcontainer/cli` NPM package

The JavaScript in the [@devcontainer/cli](https://www.npmjs.com/package/@devcontainers/cli) NPM package is now bundled in a single minified JavaScript file.

### Improved Create Dev Container command UI

The **Dev Containers: Create Dev Container** command UI now let you pick a name for the Dev Container and offers a shortcut avoiding additional options:

![Create Dev Container command name input](images/1_74/create-dev-container.png)
