---
Order: 63
TOCTitle: Build 2021
PageTitle: Visual Studio Code at Microsoft Build 2021
MetaDescription: A hand-picked collection of Visual Studio Code content from Build 2021.
Date: 2021-06-02
Author: <PERSON>
---

# Visual Studio Code at Build 2021

June 2, 2021 by <PERSON>, [@ItalyPaleAle](https://twitter.com/ItalyPaleAle)

> "That's a wrap! Now catch up with content on-demand."

[Build 2021](https://mybuild.microsoft.com/), Microsoft's main event for developers, ended last week. The Visual Studio Code team, alongside all our colleagues from Developer Division, worked really hard to deliver a lot of exciting content over the span of this two-day virtual event!

Even though Build is over, all the sessions are now available to watch on-demand. You can rewatch a live session, catch up with content you may have missed, or just learn something new; all conveniently at your own pace.

Among all the sessions that were presented, here is a hand-picked selection for you, VS Code users and developers, that we hope you'll enjoy!

## Find out what's new

### What's new in Visual Studio Code

Join <PERSON> and <PERSON> for some of the newest features of VS Code, including new debugging and testing features, custom notebooks, better support for Python, PyTorch, and Jupyter Notebooks.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/eKJftIqv8Uo" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### Application development with Scott Hanselman & friends

Why did this "not a keynote" keynote get so many rave reviews? Find out for yourself, and learn about what's new for developers from Microsoft, including with VS Code: custom notebooks, GitHub integration, GitHub Codespaces, and more.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/EWYYgEkGJfs" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### What's new with TypeScript

Check out some of the latest innovations in TypeScript with Daniel Rosenwasser.

[![Video cover - click to play](typescript.jpg)](https://mybuild.microsoft.com/sessions/45814e35-b4f7-4dc5-8ee1-6f5d103c09c2?WT.mc_id=devcloud-00000-cxa)

## Build something cool

### Container-based development with Visual Studio Code

VS Code developer Christof Marti gives a brief demo of developing in a container using Visual Studio Code.

[![Video cover - click to play](remote.jpg)](https://mybuild.microsoft.com/sessions/2b9941c4-a3f5-4c2d-8d0e-8a71912bd6d1?WT.mc_id=devcloud-00000-cxa)

### Microservices made easy with Dapr

[Dapr](https://dapr.io/) is a distributed application runtime to build microservice-based apps and works with any programming language. Check out Dapr and how it is used with VS Code.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/DyKYB76SDx0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### Build cloud-native applications that run anywhere

Use VS Code and Azure to build, debug, and run serverless or containerized apps.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/jN2INtCLRTo" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## Learn new skills

This year's Build had a large "student zone", for beginners and perennial learners. Here are a few of our favorite sessions:

### Core tools for a dev career: an introduction to Visual Studio Code and GitHub

Learn how VS Code and GitHub can help student and beginning programmers learn to code. Hosted by Sana Ajani, from the VS Code team, and Meaghan Lewis.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/Jk708pQJSyk" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### The art of artificial intelligence: creating images with Python and Azure

Learn how to use AI to generate images and photos, with Python and Jupyter Notebooks in VS Code.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/WyAikmxLlhs" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### Get started with popular programming languages: Intro to Python and C#

James Montemagno and Christopher Harrison offer a beginner-friendly introduction to programming in C# and Python, and of course using VS Code.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/wiUzCG_mEGo" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## Reach out

Even though the live event is over, you can stay in touch with our team via social media ([@code](https://twitter.com/code) on Twitter or [@vscode](https://www.tiktok.com/@vscode) on TikTok). Feel free to contact us if you have questions or feedback.

Happy watching… and coding!

Alessandro Segala, VS Code Product Manager [@ItalyPaleAle](https://twitter.com/ItalyPaleAle)