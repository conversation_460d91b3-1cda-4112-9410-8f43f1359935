// For format details, see https://aka.ms/vscode-remote/devcontainer.json
{
	"name": "VS Code Docs",
	"dockerFile": "Dockerfile",
	"settings": {
		"terminal.integrated.defaultProfile.linux": "zsh"
	},
	"extensions": [
		"yzhang.markdown-all-in-one",
		"streetsidesoftware.code-spell-checker",
		"DavidAnson.vscode-markdownlint",
		"bierner.github-markdown-preview",
		"github.vscode-pull-request-github"
	],
	"remoteUser": "vscode"
}