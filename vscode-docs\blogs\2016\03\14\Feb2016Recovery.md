---
Order:
TOCTitle: February 2016 Recovery Release
PageTitle: Visual Studio Code February 2016 Recovery Release
MetaDescription: Visual Studio Code February 2016 Recovery Release
Date: 2016-03-14
ShortDescription: The February 2016 Recovery Release of VS Code
Author: <PERSON>
---

# February 2016 Recovery Release

March 14, 2016 by The VS Code Team, [@code](https://twitter.com/code)

If we find critical issues after a major update, we do what we call a "recovery" release. Today we're updating the February 2016 build with fixes for the following four issues:

- [3903](https://github.com/microsoft/vscode/issues/3903): [js] syntax highlight for 'var' and 'function' not working in Default VS theme
- [3899](https://github.com/microsoft/vscode/issues/3899): [folding] sections are still folded when disabled in preferences
- [3509](https://github.com/microsoft/vscode/issues/3509): Smoke Test: Don't get cross file IntelliSense in JS
- [3894](https://github.com/microsoft/vscode/issues/3894): [Handlebars] Curly braces edit issues

The latest version is now `0.10.11` and if you have automatic updates turned on (OS X and Windows), you'll soon be prompted to install. Otherwise, please [download now](https://code.visualstudio.com).

Thanks to everyone who submitted these issues.

The VS Code Team