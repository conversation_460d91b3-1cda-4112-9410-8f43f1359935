---
Order: 104
TOCTitle: October 2024
PageTitle: Visual Studio Code October 2024
MetaDescription: Learn what is new in the Visual Studio Code October 2024 Release (1.95)
MetaSocialImage: 1_95/release-highlights.png
Date: 2024-10-29
DownloadVersion: 1.95.3
---
# October 2024 (version 1.95)

**Security update**: The following extensions have security updates: [ms-python.python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) and [ms-vscode-remote.remote-ssh](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh).

**Update 1.95.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2024+Recovery+1%22+is%3Aclosed).

**Update 1.95.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2024+Recovery+2%22+is%3Aclosed).

**Update 1.95.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2024+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the October 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* [Copilot Edits](#start-a-code-editing-session-with-copilot-edits) - Iterate quickly on large changes across multiple files
* [Copilot Chat in Secondary Side Bar](#chat-in-the-secondary-side-bar) - Keep Copilot Chat open and ready to-go while you work
* [Multiple GitHub accounts](#multiple-github-accounts) - Log in to multiple GitHub accounts in VS Code simultaneously
* [Copilot code reviews](#copilot-code-reviews) - Get a quick review pass or a deeper review of uncommitted changes
* [Docstrings with Pylance](#generate-docstrings-with-pylance) - Generate docstring templates for classes or methods
* [Preview settings indicator](#settings-editor-indicator-for-experimental-and-preview-settings) - View experimental and preview settings in the Settings editor
* [Copilot extensibility](#copilot-extensions-showcase) - Showcasing Copilot extensibility in VS Code

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).
**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## GitHub Copilot

Copilot features might go through different early access stages, which are typically enabled and configured through settings.

* **Experimental** - view the [experimental features](command:workbench.action.openSettings?%5B%22%40tag%3Aexperimental%20%40ext%3Agithub.copilot-chat%22%5D) (`@tag:experimental`)

	This setting controls a new feature that is actively being developed and may be unstable. It is subject to change or removal.

* **Preview** - view the [preview features](command:workbench.action.openSettings?%5B%22%40tag%3Apreview%20%40ext%3Agithub.copilot-chat%22%5D) (`@tag:preview`)

	This setting controls a new feature that is still under refinement yet ready to use. Feedback is welcome.

### Start a code editing session with Copilot Edits

> Copilot Edits is currently in preview

**Setting**: `setting(github.copilot.chat.edits.enabled)`

With Copilot Edits, you can start an AI-powered code editing session where you can quickly iterate on code changes. Based on your prompts, Copilot Edits proposes code changes across multiple files in your workspace. These edits are applied directly in the editor, so you can quickly review them in-place, with the full context of the surrounding code.

Copilot Edits is great for iterating on large changes across multiple files. It brings the conversational flow of Copilot Chat and fast feedback from Inline Chat together in one experience. Have an ongoing, multi-turn chat conversation on the side, while benefiting from inline code suggestions.

<video src="images/1_95/copilot-edits-hero.mp4" title="Use Copilot Edits to modify an Express app." autoplay loop controls muted></video>

Get started with Copilot Edits in just three steps:

1. Start an edit session by selecting **Open Copilot Edits** from the Chat menu, or press `kb(workbench.action.chat.openEditSession)`.

    ![Screenshot showing the Copilot menu in the Command Center, highlighting the Open Edit Session item](images/1_95/copilot-command-center-open-edit-session.png)

1. Add relevant files to the _working set_ to indicate to Copilot which files you want to work on.

1. Enter a prompt to tell Copilot about the edit you want to make! For example, `Add a simple navigation bar to all pages` or `Use vitest instead of jest`.

Get more details about [Copilot Edits](https://code.visualstudio.com/docs/copilot/copilot-edits) in our documentation. Try it out now and provide your feedback through [our issues](https://github.com/microsoft/vscode-copilot-release/issues)!

### Chat in the Secondary Side Bar

The new default location for the Chat view is the [Secondary Side Bar](https://aka.ms/vscode-secondary-sidebar). By using the Secondary Side Bar, you can have chat open at any time, while you still have other views available to you like the File Explorer or Source Control. This provides you with a more integrated AI experience in VS Code. You can quickly get to chat by using the `kb(workbench.action.chat.open)` keyboard shortcut.

![Chat view in its new location after having moved.](<images/1_95/chat-new-location.png>)

With the introduction of the new Chat menu next to the Command Center, bringing up the Secondary Side Bar with chat is just a click away:

<video src="images/1_95/chat-video.mp4" title="Chat in Secondary Side Bar." autoplay loop controls muted></video>

The chat menu gives you access to the most common tasks for Copilot Chat. If you wish to hide this menu, a new setting `setting(chat.commandCenter.enabled)` is provided.

![Chat menu in the Command Center.](<images/1_95/chat-menu.png>)

**Note:** If you had previously installed GitHub Copilot, a view will show up at the location you had Chat before that enables you to restore the Chat view to the old location, if that works better for you.

![Chat view in its old location showing that Chat has moved to the Secondary Side Bar.](<images/1_95/chat-old-location.png>)

### Copilot code reviews

> Copilot code reviews are currently in preview

With GitHub Copilot code review in Visual Studio Code, you can now get fast, AI-powered feedback on your code as you write it, or request a review of all your changes before you push. GitHub Copilot code review in Visual Studio Code is currently in preview. Try it out and provide feedback through [our issues](https://github.com/microsoft/vscode-copilot-release/issues).

There are two ways to use Copilot code review in VS Code:

* **Review selection**: for a quick review pass, select code in the editor and either select **Copilot** > **Review and Comment** from the editor context menu, or use the **GitHub Copilot: Review and Comment** command from the Command Palette. _(This feature is in preview.)_

* **Review changes**: for a deeper review of all uncommitted changes, select the **Copilot Code Review** button in the **Source Control** view, which you can also do in your pull request on GitHub.com. _(Join the [waitlist](https://gh.io/copilot-code-review-waitlist), open to all Copilot subscribers)_

    ![Request review of uncommitted changes](images/1_95/review_diff.png)

Copilot's feedback shows up as comments in the editor, attached to lines of your code. Where possible, the comments include actionable code suggestions, which you can apply in one action.

![Screenshot showing a comment reviewing a code selection](./images/1_95/reviewing_selection.png)

To learn more about Copilot code review, head to the [GitHub code review documentation](https://gh.io/copilot-code-review-docs).

Copilot's quick review on code selection can provide feedback that match the specific practices of your team or project, provided you give the right context. When reviewing selections with custom review instructions, you can define those specific requirements via the `setting(github.copilot.chat.reviewSelection.instructions)` setting. Similar to [code-generation and test-generation instructions](https://code.visualstudio.com/docs/copilot/copilot-customization), you can either define the instructions directly in the setting, or you can store them in a separate file and reference it in the setting.

The following code snippet shows an example of review instructions:

```json
  "github.copilot.chat.reviewSelection.instructions": [
    {
      "text": "Logging should be done with the Log4j ."
    },
    {
      "text": "Always use the Polly library for fault-handling."
    },
    {
      "file": "code-style.md" // import instructions from file `code-style.md`
    }
  ],
```

An example of the contents of the `code-style.md` file:

```markdown
Private fields should start with an underscore.

A file can only contain one class declaration.
```

### Automatic chat participant detection

**Setting**: `setting(chat.experimental.detectParticipant.enabled)`

GitHub Copilot has several built-in chat participants, such as `@workspace`, and you may have installed other extensions that contribute chat participants too.

To make it easier to use chat participants with natural language, Copilot Chat will automatically route your question to a suitable participant or chat command, when possible.

If the automatically selected participant is not appropriate for your question, you can still select the **rerun without** link at the top of the chat response to resend your question to Copilot.

![Screenshot of Chat view that shows how the '@workspace' participant is automatically detected.](images/1_93/participant-detection.png)

This month, we also added an action to let you skip this detection behavior on a per-request basis. The default action when you enter a chat prompt is **Send and dispatch**, which includes participant detection. If you choose **Send**, the request is sent directly to Copilot Chat and it won't be automatically dispatched to a chat participant.

![The list of available "send" commands in the chat view.](<images/1_95/chat-send-commands.png>)

You can also disable automatic participant detection entirely with the `setting(chat.experimental.detectParticipant.enabled)` setting.

### Control current editor context

Copilot Chat has always automatically included your current selection or the currently visible code as context with your chat request. Large Language Models (LLMs) are generally good at understanding whether a piece of context is relevant. But sometimes, when you ask a question that is not about your current editor, including this context might affect how the model interprets your question.

We now show a special attachment control in the chat input that gives a hint about the editor context and which enables you to toggle whether or not to include the editor context.

![The current editor context control in the chat input, which shows that the context is not included.](<images/1_95/implicit-context.png>)

There are no changes to the behavior of the editor context. When the active editor has a selection, then just the selection is included. Otherwise, just the code that is scrolled into view is included. You can still attach other files or the full file by using the paperclip button or by typing `#` in the chat prompt.

### Interactive workspace symbol links

A common use case of Copilot Chat is asking questions about the code in your workspace, such as using `/tests` to generate new unit tests for the selected code or asking `@workspace` to find some specific class or function in your project. This milestone, we added enhanced links for any workspace symbols that Copilot mentions in chat responses. These symbol links can help you better understand Copilot responses and learn more about the symbols used in them.

Symbol links are rendered as little pills in the response, just like the [file links](https://code.visualstudio.com/updates/v1_94#_improved-file-links-in-chat-responses) we added last milestone. To start learn more about a symbol, just select the symbol link to jump to that symbol's definition:

<video src="images/1_95/copilot-symbol-links-overview.mp4" title="Symbols links being rendered in a Copilot response. Clicking on then navigates to the symbol definition." autoplay loop controls muted></video>

You can also hover over the symbol link to see which file the symbol is defined in:

![Hovering over a symbol link to see the file it's defined in.](images/1_95/copilot-symbol-link-hover.png)

To start exploring a symbol in more detail, just right-click on the symbol link to bring up a context menu with options, such as **Go to Implementations** and **Go to References**:

![Using the context menu on a symbol link to learn more about a symbol.](images/1_95/copilot-symbol-link-context-menu.png)

Basic symbol links should work for any language that supports Go to Definition. More advanced IntelliSense options, such Go to Implementations, also require support for that language. Make sure to install language extensions to get the best symbol support for any programming languages used in Copilot responses.

### Fix using Copilot action in the Problem hover

When you hover over a problem in the editor, it now includes an action to fix the problem using Copilot. This action is available for problems that have a fix available, and the fix is generated by Copilot.

![The Problem hover showing a Fix using Copilot action.](images/1_95/copilot-fix-problem-hover.png)

### Workspace indexing

[`@workspace`](https://code.visualstudio.com/docs/copilot/copilot-chat#_chat-participants) lets you ask questions about code in your current project. This is implemented using either [GitHub's code search](https://github.blog/2023-02-06-the-technology-behind-githubs-new-code-search) or a smart local index that VS Code constructs. This milestone, we added a few more UI elements that let you understand how this workspace index is being used.

First up, the new **GitHub Copilot: Build Local Workspace index** command lets you explicitly start indexing the current workspace. This indexing is normally kicked off automatically the first time you ask a `@workspace` question. With the new command, you can instead start indexing at any time. The command also enables indexing of larger workspaces, currently up to 2000 files (not including ignored files, such as the `node_modules` or `out` directories).

While the index is being built, we now also show a progress item in the status bar:

![A status bar item showing the progress of indexing the current workspace.](images/1_95/copilot-workspace-ui-progress.png)

Indexing workspaces with many hundreds of files can take a little time. If you try to ask an `@workspace` question while indexing is being constructed, instead of waiting, Copilot will try to respond quickly by using a simpler local index that can be built up more quickly. We now show a warning in the response when this happens:

![A warning showing on a response telling the user the Copilot index is being constructed.](images/1_95/copilot-workspace-ui-warning.png)

Notice that Copilot was still able to answer the question in this case, even though it used the simpler local index instead of the more advanced one. That's often the case, although more ambiguous or complex questions might only be answerable once the smarter index has been constructed. Also keep in mind that if your workspace is backed by a GitHub repository, we can instead use [GitHub's code search](https://github.blog/2023-02-06-the-technology-behind-githubs-new-code-search) to answer questions. That means that code search is used instead of the simpler local index.

### Chat follow-up improvements

**Setting**: `setting(github.copilot.chat.followUps)`

To make more room for chat conversations in the Chat view, we've made follow-up prompts more concise and, by default, they only appear on the first turn. Configure the `setting(github.copilot.chat.followUps)` setting to change when follow-up prompts appear:

* `firstOnly` (default) - Follow-up prompts only appear on the first turn
* `always` - Follow-up prompts always appear
* `never` - Disable follow-up prompts

### Sort by relevance in Semantic Search (Experimental)

**Setting**: `setting(github.copilot.chat.search.semanticTextResults)`

Last milestone, we introduced the ability to perform a semantic search using Copilot to get search results that are semantically relevant to your query. We have now improved the search results by sorting them by their relevance. Keyword matches from more relevant snippets are deemed more relevant overall.

## Workbench

### Multiple GitHub accounts

Graduating last month's feature to the default behavior, it's now possible to be logged in to multiple GitHub accounts in VS Code at the same time.

![Multiple GitHub Accounts in the Account menu.](images/1_95/multi-github-accounts.png)

Here are a couple of scenarios in which you might need multiple accounts:

* Use *Account1* for Settings Sync and *Account2* for the GitHub Pull Request extension
* Use *Account1* for the GitHub extension (to push) and *Account2* for GitHub Copilot

To use this functionality, simply trigger a log in action (either with a built-in feature like Settings Sync or with an extension), and you'll be given the option to log in to a different account. This feature also pairs nicely with the [Account Preference Quick Pick](#add-additional-accounts-when-changing-account-preferences), should you need to change the selected account at a later stage.

While most things should just continue to work with your existing extensions, some behaviors might not play perfectly nice with this multi-account world just yet. If you think there's room for improvement, do open an issue on those extensions. With the help of the relatively new `vscode.authentication.getAccounts('github')` API, extensions have a lot of power to handle multiple accounts.

### Add additional accounts when changing account preferences

Last month, we introduced the [Account Preference Quick Pick](https://code.visualstudio.com/updates/v1_94#_change-an-extensions-account-preference), which is useful for changing the preferred account to use for an extension should you need to change that for any reason.

One of the pieces of feedback we received was around wanting an easy way to add an account that is not yet logged in. This milestone, we have introduced a new item in the Quick Pick that enables you to do just that. Use the **Use a new account...** item to start an authentication flow and set the account preference to that account in one go.

![Use a new account option in the account preference Quick Pick.](images/1_95/use-new-account.png)

### Settings editor indicator for Experimental and Preview settings

Previously, it wasn't always clear which settings were experimental or preview from looking at the Settings editor. To highlight experimental and upcoming features, the Settings editor now displays indicators next to experimental and preview settings. You can type `@tag:experimental` or `@tag:preview` in the Settings editor search box to filter to settings accordingly.

Extension authors can add "experimental" or "preview" tags to their settings to show the corresponding indicator in the Settings editor.

<video src="images/1_95/settings-editor-preview-search.mp4" title="Searching for preview settings in Settings editor" autoplay loop controls muted></video>

_Theme: [Light Pink](https://marketplace.visualstudio.com/items?itemName=mgwg.light-pink-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/mgwg.light-pink-theme))_

### More icons for profiles

In this milestone, we have added more icons for profiles. You can now choose from a wider range of icons to customize your profile.

![Image showing all available profile icons, highlighting the newly added icons.](images/1_95/profile-icons.png)

### View icons in Panel

In the workbench Panel area, views are typically displayed as labels in the title bar (for example, _TERMINAL_ or _OUTPUT_). However, on smaller screens, these labels can exceed the available space, causing some views to overflow into a dropdown menu.

To address this, we've added a new setting: `setting(workbench.panel.showLabels:false)`. When disabled, views are displayed as icons instead of labels, conserving horizontal space and reducing overflow.

**`workbench.panel.showLabels: true`**

![Panel area showing the labels for each panel.](images/1_95/panel-showLabels-on.png)

**`workbench.panel.showLabels: false`**

![Panel area showing an icon for each panel and no label.](images/1_95/panel-showLabels-off.png)

## Editor

### Occurrences Highlight Delay

This milestone, we have introduced the setting `setting(editor.occurrencesHighlightDelay)` to give you control over the delay before occurrences are highlighted in the editor. Lowering this delay value can lead to an editor experience that feels more responsive when working with semantic highlighting.

## VS Code for the Web

### VS Code for the Web supports local file events

When using Chrome or Edge as of version 129, opening <https://insiders.vscode.dev> with a local folder now supports file events. If you make changes to files and folders of the opened workspace outside the browser, these changes are reflected immediately inside the browser.

This feature leverages the new [`FileSystemObserver`](https://chromestatus.com/feature/4622243656630272) interface that is proposed as new API for the web.

## Contributions to extensions

### Copilot extensions showcase

This milestone, the team worked on building several extensions that showcase [Copilot extensibility in VS Code](https://code.visualstudio.com/docs/copilot/copilot-extensibility-overview). These extensions demonstrate the following capabilities:

* [Chat Participant & Tool APIs](https://code.visualstudio.com/api/references/vscode-api)
* [prompt-tsx](https://github.com/microsoft/vscode-prompt-tsx)
* How to leverage the language models provided by GitHub Copilot

Try these extensions and see how you can extend Copilot in your own extensions.

| Extension |Links |
|-----------|------|
| GitHub Pull Requests               | [Marketplace](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github)  |
| Web Search for Copilot             | [Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-websearchforcopilot), [source code](https://github.com/microsoft/vscode-websearchforcopilot)  |
| MermAId diagram generation with Copilot | [Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.copilot-mermaid-diagram)  |
| Data Analysis for Copilot          | [Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-copilot-data-analysis), [source code](https://github.com/microsoft/vscode-data-analysis-for-copilot)  |
| VS Code Commander                  | [Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-commander)  |
| Vision for Copilot Preview         | [Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-copilot-vision)  |

#### GitHub Pull Requests

Version [0.100.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#01000) of the [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension adds Copilot integration:

* Use the `@githubpr` chat participant in the Chat view to search for issues, summarize issues/prs, and suggest fixes for issues. `@githubpr` uses a number of Language Model tools to accomplish this.
* There's also a new **Notifications** view that shows GitHub notifications, with an action to prioritize them with Copilot.

To try everything out, you can set the following settings:

* `setting(githubPullRequests.experimental.chat:true)`
* `setting(githubPullRequests.experimental.notificationsView:true)`

##### Issue Search with Copilot

The new `@githubpr` Chat participant can search for issues on GitHub.

![Copilot issue search for most open bugs.](images/1_95/copilot-issue-search-most-bugs.png)

When displaying issues, `@githubpr` shows a Markdown table and tries to pick the best columns to show, based on the search.

![Copilot issue search for closed October issues.](images/1_95/copilot-issue-search.png)

##### Summarizing and Fixing with Copilot

Each issue listed in the **Issues** view now has a new action, **Summarize With Copilot**, that opens the Chat panel and summarizes the selected issue. We also added another action, **Fix With Copilot**, that summarizes the selected issue and uses the workspace context to suggest a fix for it.

<video src="images/1_95/fix-summarize-issue.mp4" title="Summarize and Fix GitHub issue" autoplay loop controls muted></video>

##### Notification Prioritization with Copilot (Experimental)

This milestone, we added an experimental **Notifications** view that lists your unread notifications across repositories. By default, the notifications are sorted by most recently updated descending, but you can use the **Sort by Priority using Copilot** action from the view title's `...` menu to have Copilot prioritize the notifications. Selecting each notification triggers an action to summarize the notification using Copilot. The view also contains easily accessible actions to mark a notification as read, or to open the notification on GitHub.com.

![Notifications View](images/1_95/notifications-view.png)

#### Web Search for Copilot

This extension showcases:

* Chat Participant & Tool APIs
* [prompt-tsx](https://github.com/microsoft/vscode-prompt-tsx)
* How to leverage the language models provided by GitHub Copilot

The source code is [available on GitHub here](https://github.com/microsoft/vscode-websearchforcopilot).

##### Description

Get the most up-to-date and relevant information from the web right in Copilot.

This is powered by one of two different search engines, configured by `setting(websearch.preferredEngine)`:

* [Tavily](http://tavily.com) (default)
* [Bing](https://bing.com)

As a user, you'll need to acquire an API key from one of these services to use this extension. Upon first use, it asks you for that key and stores it using VS Code's built-in secret storage, and can be managed via VS Code's authentication stack as well just as you would for your GitHub account.

##### Chat participant

This extension contributes the `@websearch` chat participant, which is capable of handling questions that likely need live information from the internet.
You can invoke it manually using `@websearch when did Workspace Trust ship in vscode?`

![The question "when did Workspace Trust ship in vscode" and the answer showing the references and details.](images/1_95/websearch-participant.png)

##### Chat tool

This extension contributes the `#websearch` chat language model tool as well, which is similar to the participant but is useful for providing context from the web in other chat participants. For example:

* `@workspace /new #websearch create a new web app written in Python using the most popular framework`

![The question "create a new web app written in Python using the most popular framework" using the websearch variable with /new. The result is a project using Django.](images/1_95/websearch-tool.png)

Additionally, if you are working on your own Chat particpant or tool, you can consume this Chat tool via the `vscode.lm.invokeTool` API.

#### MermAId diagram generation with Copilot

The vscode-mermAId (`vscode:extension/ms-vscode.copilot-mermaid-diagram`) extension contributes a new chat participant to GitHub Copilot to build and modify visualizations for your code with Mermaid, a Markdown-inspired diagraming and charting tool.

##### Create and render diagrams

Create any type of Mermaid-supported diagrams through chat conversations and use the `/iterate` slash command to refine the diagram. Slash commands are available for specific diagrams to provide extra guidance to the model.

<video src="images/1_95/iterate-on-sequence-diagram.mp4" title="Create a sequence diagram then iterate to remove a participant" autoplay loop controls muted></video>

Links are added for certain diagram types, like flow, to point back to the references that were gathered to build the diagram.

<video src="images/1_95/diagram-with-links-short.mp4" title="Generate a diagram then click a link back to a referenced piece of code" autoplay loop controls muted></video>

##### Mermaid Visual Outline view

Open the **Visual Outline** view to dynamically generate diagrams from the active editor. You can break out into chat for finer control.

<video src="images/1_95/mermaid-outline-view-iterate.mp4" title="Uses the mermaid visual outline view to generate a diagram and then iterate on it with chat" autoplay loop controls muted></video>

##### Chat tool

The extension contributes a tool to gather symbol information within files or the entire workspace and that can be consumed by other chat participants when this extension is installed.

#### Data Analysis for Copilot

The Data Analysis for Copilot extension empowers people in the data science field. From cleaning up a `.csv` file, to performing higher-level data analysis by leveraging different statistics measures, graphs, and predictive models, the `@data` chat participant helps make more advanced and informed decisions by offering tailored insights and interactivity for data tasks.

The extension contributes a tool where the LLM can ask to execute Python code by using [Pyodide](https://pyodide.org/en/stable/) and get the result of the relevant Python code execution. It is also able to smartly retry for better or more appropriate execution results in case of errors. You can also export the code that is used to perform the analysis (or generate visualizations) into a Jupyter Notebook or a Python file.

You can download the extension from [the marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-copilot-data-analysis) and the source is [available on GitHub here](https://github.com/microsoft/vscode-data-analysis-for-copilot).

##### Data analysis and visualizations

* Given a CSV file, enter a prompt such as `Analyze the file #<file name>` or write a more specific prompt (see below recording)
* Provide follow-up prompts to request the generation of visualizations, such as charts, plots, and more

<video src="images/1_95/data-analysis-chat-participant.mp4" title="Analyzing CSV file using data analysis chat participant in Copilot." autoplay loop controls muted></video>

##### Exporting the code used to perform the data analysis

* The Python code used to perform the analysis and generate visualizations can be viewed
* The Code can be exported in a Jupyter notebook or a Python file

<video src="images/1_95/data-analysis-export-chat-participant.mp4" title="Exporting code used to perform data analysis and generate visualizations." autoplay loop controls muted></video>

##### Editor and explorer integrations for CSV files

* Right-click on a CSV file to analyze it
* Open a CSV file and use the Copilot icon to analyze the file

<video src="images/1_95/data-analysis-csv-chat-participant.mp4" title="Exporting code used to perform data analysis and generate visualizations." autoplay loop controls muted></video>

#### VS Code Commander extension

The VS Code Commander extension (`vscode:extension/ms-vscode.vscode-commander`) acts as your personal assistant within VS Code. This powerful tool enables you to configure your VS Code environment by using conversational, free-form text. With the VS Code Commander, you can:

* Discover and explore various settings and commands
* Tailor your development environment to your needs

These actions can be performed through a simple and intuitive chat interface, making it easier than ever to manage your VS Code configuration.

<video src="images/1_95/vscode-commander-demo.mp4" title="Showing off VS Code Commander capabilities in the copilot chat view" autoplay loop controls muted></video>

#### Vision for Copilot Preview extension

The Vision for Copilot Preview extension (`vscode:extension/ms-vscode.vscode-copilot-vision`) enables you to attach images directly as contextual input, enriching conversations and enabling more dynamic, visually-supported responses. This extension will be eventually deprecated in favor of built-in image flow in Github Copilot Chat.

##### Vision in Chat

For now, you can experience the image attachment flow in the Chat view by using your own OpenAI, Azure OpenAI, Anthropic, or Gemini keys. Get started by easily attaching images from the clipboard or dragging them directly into the chat.

![Screenshot of a chat exchange. A user asks for HTML and CSS for a landing page. The response provides a basic HTML structure with a header, navigation links (Home, About, Contact), and a link to an external CSS file.](images/1_95/demo.gif)

_Theme: [Sapphire](https://marketplace.visualstudio.com/items?itemName=Tyriar.theme-sapphire) (preview on [vscode.dev](https://vscode.dev/editor/theme/Tyriar.theme-sapphire))_

##### Vision with Quick Fixes

Additionally, you can generate or refine alt text for images in markdown, HTML, JSX, or TSX documents with the provided code actions, simplifying the process of incorporating descriptive text for better context and accessibility. Alt text quick fixes work for images in the workspace and image URLs.

![An example markdown document displays a quick fix feature for generating alt text, resulting in the automatic insertion of an alt tag and a value. The user is then prompted with a different quick fix to refine the alt text using an input box. After the user enters and submits their refined description, the alt text is updated accordingly.](images/1_95/demo-alt-text.gif)

This extension uses the `ChatReferenceBinaryData` proposed API. Feel free to check out an example of how it's used in the source code, [available on GitHub here](https://github.com/microsoft/vscode-copilot-vision).

### Python

#### Native REPL Variables view

The Native Python REPL now provides up-to-date variables for the built-in Variables view. This lets you dig into the state of the interpreter as you execute code from files or through the REPL input box.

<video src="images/1_95/python-variable-view.mp4" title="Opening the variable view within the debug panel after executing code in the native REPL" autoplay loop controls muted></video>

#### Generate docstrings with Pylance

You can now more conveniently generate documentation for your Python code with [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance)'s docstrings template generation feature! You can generate a docstring template for classes or methods by typing `"""` or `'''`, pressing `kbstyle(Ctrl+Space)`, or selecting the lightbulb to invoke the **Generate Docstring** code action. The generated docstring includes fields for the function's description, parameters, parameter types, return and return types.

<video src="images/1_95/pylance-generate-docstring.mp4" title="Generating docstring templates for a function with Pylance by invoking Ctrl+Space inside a pair of triple quotes." autoplay loop controls muted></video>

This feature is currently behind an experimental setting, but we look forward to making it the default experience soon. You can try it out today by enabling the `setting(python.analysis.supportDocstringTemplate:true)` setting.

#### Fold all docstrings

Documentation strings are great for providing context and explanations for your code, but sometimes you might want to fold them to focus on the code itself. You can now more easily do so by folding docstrings with the new **Pylance: Fold All Docstrings** command, which can also be bound to a keybinding of your choice. To unfold them, use the **Pylance: Unfold All Docstrings** command.

<video src="images/1_95/pylance-fold-docstrings.mp4" title="Folding and unfolding docstrings with Pylance's new commands." autoplay loop controls muted></video>

#### Improved import suggestions

One of Pylance's powerful features is its ability to provide auto-import suggestions. By default, Pylance offers the import suggestion from where the symbol is defined, but you might want it to import it from a file where the symbol is imported (i.e. aliased). With the new `setting(python.analysis.includeAliasesFromUserFiles:true)` setting, you can now control whether Pylance includes alias symbols from user files in its auto-import suggestions or in the add import Quick Fix.

Note that enabling this setting can negatively impact performance, especially in large codebases, as Pylance may need to index more symbols and monitor more files for changes, which can increase resource usage.

#### Experimental AI Code Action: Implement Abstract Classes

You can now get the best of both worlds with AI and static analysis with the new experimental Code Action to implement abstract classes! This feature requires both [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) and the [GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extensions. To try it out, you can select the **Implement all inherited abstract classes with Copilot** Code Action when defining a class that inherits from an abstract one.

<video src="images/1_95/pylance-ai-code-action.mp4" title="Implementing methods from an inherited abstract class with code action from Pylance and Copilot." autoplay loop controls muted></video>

You can disable this feature by setting `"python.analysis.aiCodeActions": {"implementAbstractClasses": false}` in your User settings.

## Extension Authoring

### Tools for language models

We have finalized our [`LanguageModelTool` API](https://code.visualstudio.com/api/references/vscode-api#lm.tools)! This API enables chat extensions to build more powerful experiences by connecting language models to external data sources, or take actions. The API comes with two major parts:

1. The ability for extensions to register a *tool*. A tool is a piece of functionality that is meant to be used by language models. For example, reading the Git history of a file. When a tool is registered using the `lm.registerTool` method, it's accessible to other extensions as well, in the `lm.tools` list. This will enable chat extensions to seamlessly integrate with other extensions via an ecosystem of shared tools.

2. The mechanics for language models to support tools, such as extensions passing tools when making a request, language models requesting a tool invocation, and extensions communicating back the result of a tool invocation.

The use of language model tools is complex, and this API does not hide that complexity. If you want to register a tool or make use of tools in your chat participant, we recommend starting with the [extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/chat-sample).

### Chat participant detection

We have finalized our API for chat participant detection, which allows GitHub Copilot to automatically select your chat participant or participant command to handle a user's question. Please check out [our documentation](/api/extension-guides/chat.md#implement-participant-detection) for a detailed tutorial and recommendations.

### VS Code Speech

The [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension is updated to the August version of the [Azure Speech SDK](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/releasenotes?tabs=speech-sdk#speech-sdk-140-2024-august-release) and comes with newer models for speech-to-text recognition. You should see improved results with this update for the [speech-to-text integrations in VS Code](https://code.visualstudio.com/docs/configure/accessibility/voice), such as Copilot Chat.

### Comment Thread `collapsibleState`

The expand/collapse state of a `CommentThread` can be changed using the new `CommentThread.collapsibleState` property, even once the thread has already been shown. Previously, this property would only be respected the first time the comment thread was shown.

### Codicons in welcome views

Welcome views now support the ability to render codicons. You can do so using the usual `$(icon-name)` in your welcome view.

![A sample welcome view showing the use of text, links, buttons, and codicons.](images/1_95/welcome-view-codicons.png)

### Chat participant access to model picker

You may have noticed the model picker in the Chat view, which lets you select the model that is used for a chat request.

![Copilot model picker control in the Chat view enables switching to another language model.](./images/1_94/copilot-model-picker.png)

Your chat participant extension needs to adopt a new API in order to use this model picker. We just finalized a new `model` property on the `ChatRequest` object, which will be set to the `LanguageModelChat` instance for the model in the picker. You can use this instead of the `lm.selectChatModels` method. If your extension wants to use a particular model besides the selected one, you can still use `lm.selectChatModels` instead.

## Preview Features

### TypeScript 5.7

We've continued improving our support for the upcoming TypeScript 5.7 release. Check out the [TypeScript 5.7 beta blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-5-7-beta/) and the [TypeScript 5.7 plan](https://github.com/microsoft/TypeScript/issues/59905) for details.

To start using preview builds of TypeScript 5.7, install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next).

### Update imports on paste for JavaScript and TypeScript

Tired of having to add imports after moving code between files? Try out our experimental support for updating imports on paste! When you copy and paste code between editors, VS Code automatically adds imports when the code is pasted:

<video src="images/1_95/jsts-update-imports-paste.mp4" title="Title" autoplay loop controls muted></video>

Notice how it not only added imports, it even added a new export for a local variable that was used in the pasted code!

To try this out today, make sure you are using TypeScript 5.7+. Then enable `javascript.experimental.updateImportsOnPaste`/`typescript.experimental.updateImportsOnPaste`. Currently this is only supported when pasting between text editors in the same VS Code window.

## Proposed APIs

### Chat Reference Binary Data for image attachments

We now allow images (`png`, `jpeg`, `bmp`, `gif`, and `tiff`) to be pasted in chat if there is an extension that uses the `ChatReferencebinaryData` proposed API.

```typescript
export class ChatReferenceBinaryData {
	/**
	 * The MIME type of the binary data.
	 */
	readonly mimeType: string;

	/**
	 * Retrieves the binary data of the reference.
	 * @returns A promise that resolves to the binary data as a Uint8Array.
	 */
	data(): Thenable<Uint8Array>;

	/**
	 * @param mimeType The MIME type of the binary data.
	 * @param data The binary data of the reference.
	 */
	constructor(mimeType: string, data: () => Thenable<Uint8Array>);
}
```

Extension authors can access this after creating a chat handler via `request.references`, which can be a `URI` when images are attached via drag and drop or from the quick pick, or will be `ChatReferenceBinaryData` for pasted images.

## Engineering

### Prompt building library for LLMs

This month, we open sourced our [@vscode/prompt-tsx](https://www.npmjs.com/package/@vscode/prompt-tsx) library, which we've developed and used in Copilot Chat over the past year for crafting language model prompts. The library enables developers to create their prompts using TSX/JSX syntax, similar to React, and includes a variety of tools to make the best use of prompts' token budget.

### AMD code removal and more ESM use in web

We removed the last traces of AMD (Asynchronous Module Definition) from our sources, mainly from the build scripts that we still kept for supporting AMD in case needed for a recovery release.

In addition, <https://vscode.dev> is now also running 100% with ESM (ECMAScript Modules) only.

### Migration to ESLint 9

We've updated both the main VS Code repo and all of our [extension samples](https://github.com/microsoft/vscode-extension-samples) to use ESLint 9. This included migrating all of our ESLint configuration to use modern [flat configs](https://eslint.org/blog/2023/10/flat-config-rollout-plans/).

### Electron 32 update

In this milestone, we are promoting the Electron 32 update to users on our stable release. This update comes with Chromium 128.0.6613.186 and Node.js 20.18.0. We want to thank everyone who self-hosted on Insiders builds and provided early feedback.

## Notable fixes

* [177046](https://github.com/microsoft/vscode/issues/177046) will crash after searching at extension panel

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)

### Pull requests

Contributions to `vscode`:

* [@Abrifq (Arda Aydın)](https://github.com/Abrifq): Change `window.experimentalControlOverlay`'s scope to application [PR #230593](https://github.com/microsoft/vscode/pull/230593)
* [@asemdreibati (Asem Dreibati)](https://github.com/asemdreibati): handle edge case in slice function in Iterable namespace (#230683) [PR #232134](https://github.com/microsoft/vscode/pull/232134)
* [@BABA983 (BABA)](https://github.com/BABA983)
  * Add developer action to show gpu status [PR #222291](https://github.com/microsoft/vscode/pull/222291)
  * Fix debug console is cleared on style changed [PR #224694](https://github.com/microsoft/vscode/pull/224694)
  * Support open in editor in git editor [PR #226967](https://github.com/microsoft/vscode/pull/226967)
* [@Bistard (SIHAN LI)](https://github.com/Bistard): Fix typo: context view anchor option might be dismissed when using `||` [PR #228896](https://github.com/microsoft/vscode/pull/228896)
* [@cobey (Cody Beyer)](https://github.com/cobey): added mistral ai npm package [PR #229865](https://github.com/microsoft/vscode/pull/229865)
* [@elias-pap (Elias Papavasileiou)](https://github.com/elias-pap): fix: improve settings descriptions for actions triggered on save [PR #230052](https://github.com/microsoft/vscode/pull/230052)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Correct Menu Contexts info on extension's Commands page (fix #229258) [PR #229260](https://github.com/microsoft/vscode/pull/229260)
* [@hamirmahal (Hamir Mahal)](https://github.com/hamirmahal): style: simplify string formatting for readability [PR #231763](https://github.com/microsoft/vscode/pull/231763)
* [@injust (Justin Su)](https://github.com/injust): Fix "in none full screen mode" typo [PR #229914](https://github.com/microsoft/vscode/pull/229914)
* [@jamesharris-garmin (James Harris)](https://github.com/jamesharris-garmin): Fix missing __dirname in --locate-shell-integration-path [PR #231423](https://github.com/microsoft/vscode/pull/231423)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fix if logOutputChannel is created again after being disposed, it will disappear [PR #225709](https://github.com/microsoft/vscode/pull/225709)
* [@Kaidesuyoo (Kaidesuyo)](https://github.com/Kaidesuyoo): Performance optimization [PR #230804](https://github.com/microsoft/vscode/pull/230804)
* [@kkshinkai (Kk Shinkai)](https://github.com/kkshinkai): Correctly trigger the `onDidAddListener` event in emitter options [PR #230259](https://github.com/microsoft/vscode/pull/230259)
* [@Parasaran-Python (Parasaran)](https://github.com/Parasaran-Python): 228640: Hiding prelaunch task popup if the setting to hide it is enabled [PR #231225](https://github.com/microsoft/vscode/pull/231225)
* [@quiple (Minseo Lee)](https://github.com/quiple): Change Korean font priority [PR #230195](https://github.com/microsoft/vscode/pull/230195)
* [@r3m0t (Tomer Chachamu)](https://github.com/r3m0t): Fix scrolling of Test Results when a new test starts (fixes #229531) [PR #229532](https://github.com/microsoft/vscode/pull/229532)
* [@sandersn (Nathan Shively-Sanders)](https://github.com/sandersn): TS extension: register call to CopilotRelated with copilot extension [PR #228610](https://github.com/microsoft/vscode/pull/228610)
* [@ShadowRZ (夜坂雅)](https://github.com/ShadowRZ): fix: Use a proper desktop name in package.json [PR #231472](https://github.com/microsoft/vscode/pull/231472)
* [@trevor-scheer (Trevor Scheer)](https://github.com/trevor-scheer): Marker message white-space `nowrap` -> `pre` [PR #229454](https://github.com/microsoft/vscode/pull/229454)
* [@vietanhtwdk](https://github.com/vietanhtwdk): rerender on resize stickyscroll [PR #227400](https://github.com/microsoft/vscode/pull/227400)
* [@yanglb (Yanblb)](https://github.com/yanglb): add type checking to decorators [PR #230626](https://github.com/microsoft/vscode/pull/230626)

Contributions to `vscode-docs`:

* [@Cecil0o0 (hj)](https://github.com/Cecil0o0)
  * doesn't provide built-in language support in the core editor [PR #7679](https://github.com/microsoft/vscode-docs/pull/7679)
  * location is restricted in a limited area for debug toolbar as `floating` [PR #7704](https://github.com/microsoft/vscode-docs/pull/7704)
  * Outdated command title [PR #7705](https://github.com/microsoft/vscode-docs/pull/7705)
* [@echofly](https://github.com/echofly): Update v1_94.md [PR #7677](https://github.com/microsoft/vscode-docs/pull/7677)
* [@ghosted-sound](https://github.com/ghosted-sound)
  * Update aksextensions.md [PR #7693](https://github.com/microsoft/vscode-docs/pull/7693)
  * Update package-management.md [PR #7694](https://github.com/microsoft/vscode-docs/pull/7694)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Document the `hide` property [PR #7643](https://github.com/microsoft/vscode-docs/pull/7643)
  * Fix typo [PR #7687](https://github.com/microsoft/vscode-docs/pull/7687)
* [@oleschri](https://github.com/oleschri): add argument --update-extensions [PR #7681](https://github.com/microsoft/vscode-docs/pull/7681)
* [@partev](https://github.com/partev): fix URL redirect [PR #7640](https://github.com/microsoft/vscode-docs/pull/7640)
* [@ptrptrd](https://github.com/ptrptrd): docs: remove double entries in theme color references [PR #7639](https://github.com/microsoft/vscode-docs/pull/7639)
* [@RonakRahane](https://github.com/RonakRahane): Added documentation for new Code Coverage in C# Fixes #7635 [PR #7664](https://github.com/microsoft/vscode-docs/pull/7664)

Contributions to `vscode-js-debug`:

* [@marcusball (Marcus Ball)](https://github.com/marcusball): feat: use `remoteHostHeader` option when looking up websocket address [PR #2111](https://github.com/microsoft/vscode-js-debug/pull/2111)

Contributions to `vscode-languageserver-node`:

* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Add support for CompletionList "applyKind" to control how defaults and per-item commitCharacters/data are combined [PR #1558](https://github.com/microsoft/vscode-languageserver-node/pull/1558)

Contributions to `vscode-mypy`:

* [@jwhitaker-gridcog (Jarrad)](https://github.com/jwhitaker-gridcog): run mypy in the directory of the nearest pyproject.toml or mypy.ini [PR #316](https://github.com/microsoft/vscode-mypy/pull/316)

Contributions to `vscode-vsce`:

* [@andrewlayer](https://github.com/andrewlayer): Added unpublish to api.ts [PR #1061](https://github.com/microsoft/vscode-vsce/pull/1061)
* [@deribaucourt (Enguerrand de Ribaucourt)](https://github.com/deribaucourt): Fix regression with workdir symlinks [PR #1053](https://github.com/microsoft/vscode-vsce/pull/1053)
* [@dtivel (Damon Tivel)](https://github.com/dtivel): Quote `filename` value in `Content-Disposition` header [PR #1060](https://github.com/microsoft/vscode-vsce/pull/1060)

Contributions to `language-server-protocol`:

* [@asukaminato0721 (Asuka Minato)](https://github.com/asukaminato0721)
  * add systemd [PR #2034](https://github.com/microsoft/language-server-protocol/pull/2034)
  * add 3 lsp [PR #2046](https://github.com/microsoft/language-server-protocol/pull/2046)
* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Add support for `completionList.applyKind` to determine how values from `completionList.itemDefaults` and `completion` are combined. [PR #2018](https://github.com/microsoft/language-server-protocol/pull/2018)
* [@DavyLandman (Davy Landman)](https://github.com/DavyLandman): Added Rascal to relevant LSP implementor sections [PR #2029](https://github.com/microsoft/language-server-protocol/pull/2029)
* [@g-plane (Pig Fang)](https://github.com/g-plane): fix punctuation typo [PR #2048](https://github.com/microsoft/language-server-protocol/pull/2048)
* [@nthykier (Niels Thykier)](https://github.com/nthykier): Add the `debputy` language server [PR #2044](https://github.com/microsoft/language-server-protocol/pull/2044)
* [@RainCmd (渴望蓝天)](https://github.com/RainCmd): Add Rain language server to  LSP [PR #2039](https://github.com/microsoft/language-server-protocol/pull/2039)
* [@WilsonZiweiWang (ziweiwang)](https://github.com/WilsonZiweiWang): Add BitBake language server [PR #2049](https://github.com/microsoft/language-server-protocol/pull/2049)
* [@yasmewad (Yash Mewada)](https://github.com/yasmewad): Add Smithy language server links to LSP [PR #2036](https://github.com/microsoft/language-server-protocol/pull/2036)

Contributions to `lsprotocol`:

* [@nobodywasishere (Margret Riegert)](https://github.com/nobodywasishere): Add Crystal plugin to README [PR #403](https://github.com/microsoft/lsprotocol/pull/403)

Contributions to `tolerant-php-parser`:

* [@TysonAndre (Tyson Andre)](https://github.com/TysonAndre): Fix php 8.4 notices about implicitly nullable parameters [PR #410](https://github.com/microsoft/tolerant-php-parser/pull/410)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
