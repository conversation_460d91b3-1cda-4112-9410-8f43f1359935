<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="554px" height="362px" viewBox="-0.5 -0.5 554 362" content="&lt;mxfile scale=&quot;1.2&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;UYQDFGXKUwBN9YeYD3QO&quot; name=&quot;Page-1&quot;&gt;7VtLc+MoEP41Ojol0MPyMU4yu4fM1lRlq3ZyVCwisyMLD8Zje379ggWWEHisjOUQb+mSiOZh9H3dTTcIL7hbbP+g6XL+mWSo8KCfbb3g3oMwTCL+Vwh2lSCOk0qQU5xVIlALnvBPJIW+lK5xhlZaQ0ZIwfBSF85IWaIZ02QppWSjN3slhf6ryzRHhuBplham9B+csXklTSK/lv+JcD5Xvwx8WbNIVWMpWM3TjGwaouDBC+4oIax6WmzvUCGwU7hU/T4dqT1MjKKSdekQw6rHj7RYy5eTE2M79baUrMsMiQ6+F0w3c8zQ0zKdidoNp5fL5mxR8BLgjytGyTd0RwpCuaQkJW82lb+BKEPboxMFh9fnaoPIAjG6401kh0ACttOLmxr+MJayeQP6QCGfSsrzw8g1KvxBAmMHKTAxGk8NmPi7MR2LtMB5yZ9n/B0Rh2MqEMBcjW5lxQJnmeg+pWiFf6Yv+6EEyEuCS7afcTT1onsx1pqRVWUIR2F+JSWTxgJjWf6ULnAhQLsja4r5LKD/F9qoxnL2viyr8TwY3EYwAMmBO4MoC51HuQMw0siDicleYCEv7IG70MLd/cBdZ+6g7467yOBue4w5O6a4KFqi7rTa/JzuCT8qZSDWfSWcWJylhTLYA2Xx4CrPM7fQnbmNB1d5FndBHDrjTkXGXXzlQJ1pdok7swNgoO4cq3MYXAIzM5jSdPYNMQ/GhSDshWo8xt/XIrPb4zGqEL/lDQBYbvdoqHr+lIv/j6jMxUvsGxlK8abEjBKWMkyEZowmrfAFiAYZpjxXrlqgdMV6CkYmQKcnCG8mY4Mhe+7WA0Nm/P+3MKWBHlkbuaXHDPEHA2oxBNwyNET05/GXOAwKh4j+PO5C3yF5yeAaT9Azbu1zvLNnnAwEnUqrAqdrFzST4oGhVvYUObUhaOa+A0MthiZuGYIGQxdKoOBV0gPb+S0Yd6VHbReedUQSGKihLEcqPiKUzUlOyrR4qKWtY4y6zSMhS4nmv4ixnURPRHA61miL2VfR/QZGsvjcqLrfyqH3hZ0qlPzdvqpmovC8HyJSxbrbvqT6tXk8StqKB4ozpKykkrGU5kg1k8mmAOiX1FJUcFX6gbTRz2IpdMtSd5L6Azt0BnbkFuzxNZlE4Ioly0cVh5Xf/5Ji2lxhzMXCdHtvWix6cPxAnXXulHcJDK8PwIWSwyge3L5Vx0OLjk+ceaLx/9vtW8BW4aIDsJPB7XdmyXfm9s3zmLe4fTOOem+3D8MObj+6kNsPzXR5NsdFZk1uP/yC0FD4Z03f+9T+yKL9XYMeSflIz/DUeUxn25ADfxFb4/WoY23QEdT7k9fXlbCJlm4cJtdNXczcXamLmWx/eGfpTF26ZjJH1CXqRV0A1PUlvoC+WD64fMQrPirv6TfdsBnXOI++lQNsuOHD+UDfe2Khua8s7eo6or33NanYYlJdd2Ik2/6N7/s64SN4phvu1XKi61l+eyNRHVD2F0Tuu95Smu4aDeSJ8nHH6Ov744nf4rIa8LeZNU/5yBKVuMzNSPVFRbIWVXhMX1DhXeyU/TSr0TEfe7gmJX/Aa95EsltjHUMpawx6WeT0Na61Pd6PoV5P4NOftwUfwlBDeFFDjS2LckFWV2ioKmrvw1CNb9J7sdORbqit/v3EouZ3Z+2NgnZUOjH4fO+oNJlouESWr4XU/Y6+Y9LY3Bow1bvMbsXtV+/w4VXj9duKWRJpCYfa1kdbhaieckryPcyt2l/cA7VAfNI1NSC0ba4o2bk5ub6qBe0bUZWnlZ1qbk6NE7UmUjlnY5jTVsKL9dXgqnl9vzp4+A8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="552" height="360" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="114" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 110px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="210" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 190px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="156" y="312" width="48" height="24" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 270px; margin-left: 131px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="150" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="258" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 230px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="230" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="400.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 349px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="349" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="306" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 270px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="270" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="354" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 310px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="310" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="193.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,229.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 191 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 162px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="191" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="145.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,181.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 151 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 122px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Text
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="151" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Text...
                </text>
            </switch>
        </g>
        <rect x="97.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,133.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 111 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 82px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="111" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="64.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 69px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="69" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="448.8" y="300" width="36" height="48" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 389px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="389" y="278" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="48" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,84,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 70 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 41px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="241.2" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,277.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 231 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 202px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="231" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="384" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,420,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 350 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 321px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="432" y="245.96" width="72" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,468,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 390 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 361px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket
                                <br style="font-size: 11px"/>
                                Length: 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Bracket...
                </text>
            </switch>
        </g>
        <rect x="313.2" y="225.56" width="72" height="76.8" fill="#ffffff" stroke="#000000" stroke-width="1.2" transform="rotate(-90,349.2,263.96)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)rotate(-90 291 219.97000000000003)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 220px; margin-left: 262px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Text
                                <br style="font-size: 11px"/>
                                Length: 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="291" y="223" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Text...
                </text>
            </switch>
        </g>
        <path d="M 148.2 195.6 L 133.2 195.6 L 133.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 133.2 226.62 L 129 218.22 L 133.2 220.32 L 137.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 181.2 195.6 L 181.2 219.6 L 181.2 204 L 181.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 181.2 226.62 L 177 218.22 L 181.2 220.32 L 185.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 214.2 195.6 L 229.2 195.6 L 229.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 229.2 226.62 L 225 218.22 L 229.2 220.32 L 233.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="115.2" y="147.6" width="132" height="48" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 143px; margin-left: 97px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                                <br/>
                                Length: 3
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="151" y="147" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair...
                </text>
            </switch>
        </g>
        <path d="M 304.2 195.6 L 277.2 195.6 L 277.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 277.2 226.62 L 273 218.22 L 277.2 220.32 L 281.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 349.2 195.6 L 349.2 219.6 L 349.2 204 L 349.2 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 349.2 226.62 L 345 218.22 L 349.2 220.32 L 353.4 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 394.2 195.6 L 420 195.6 L 420 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 226.62 L 415.8 218.22 L 420 220.32 L 424.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="259.2" y="147.6" width="180" height="48" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 143px; margin-left: 217px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                                <br/>
                                Length: 4
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="291" y="147" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair...
                </text>
            </switch>
        </g>
        <path d="M 196.2 121.2 L 181.2 121.2 L 181.2 139.96" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 181.2 146.26 L 177 137.86 L 181.2 139.96 L 185.4 137.86 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 171px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="171" y="113" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child 1
                </text>
            </switch>
        </g>
        <path d="M 358.2 121.2 L 349.2 121.2 L 349.2 139.96" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 349.2 146.26 L 345 137.86 L 349.2 139.96 L 353.4 137.86 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 311px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="311" y="113" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child 2
                </text>
            </switch>
        </g>
        <rect x="115.2" y="85.2" width="324" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 86px; margin-left: 97px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List, Length: 7
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="231" y="90" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List, Length: 7
                </text>
            </switch>
        </g>
        <path d="M 277.8 60 L 277.38 77.56" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 277.23 83.86 L 273.23 75.36 L 277.38 77.56 L 281.63 75.56 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 210px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                child
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="64" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    child
                </text>
            </switch>
        </g>
        <path d="M 174.3 60 L 84 60 L 84 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 84 226.62 L 79.8 218.22 L 84 220.32 L 88.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 70px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                opening
                                <br/>
                                bracket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="115" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    opening...
                </text>
            </switch>
        </g>
        <path d="M 381.3 60 L 468 60 L 468 220.32" fill="none" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 468 226.62 L 463.8 218.22 L 468 220.32 L 472.2 218.22 Z" fill="#000000" stroke="#000000" stroke-width="1.2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 391px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                closing
                                <br/>
                                bracket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="391" y="114" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    closing...
                </text>
            </switch>
        </g>
        <rect x="70.8" y="24" width="414" height="36" fill="#ffffff" stroke="#000000" stroke-width="1.2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(1.2)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 343px; height: 1px; padding-top: 35px; margin-left: 60px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair, Length: 9
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="39" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair, Length: 9
                </text>
            </switch>
        </g>
        <path d="M 51.6 348 L 51.6 25.2" fill="none" stroke="none" pointer-events="stroke"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>