---
Order:
TOCTitle: June 2019
PageTitle: Visual Studio Code June 2019
MetaDescription: See what is new in the Visual Studio Code June 2019 Release (1.36)
MetaSocialImage: 1_36/release-highlights.png
Date: 2019-07-03
DownloadVersion: 1.36.1
---
# June 2019 (version 1.36)

**Update 1.36.1**: The update addresses these [issues](https://github.com/microsoft/vscode/milestone/99?closed=1).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the June 2019 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Hide/show status bar items](#hide-individual-status-bar-items)** - Only display your preferred status bar items.
* **[Indent guides in explorers](#tree-indent-guides)** - Clearly highlights your project's folder structure.
* **[Better terminal shell selector](#better-default-shell-selector)** - Easily select the default shell to use in the Integrated Terminal.
* **[Sequential task execution](#sequential-dependency-execution)** - Control the order of task and subtask execution.
* **[Jump to cursor debugging](#jump-to-cursor)** - Skip code execution as you jump to a new location.
* **[Disable debug console word wrap](#disable-console-word-wrap)** - Lets you keep debugging output to one line.
* **[JavaScript/TypeScript nightly builds](#javascript-and-typescript-nightly-extension)** - New extension integrates JS/TS nightly builds.
* **[New Java installer](#installer-for-java-developers)** - Installs VS Code, the Java extension pack, and required Java dependencies.
* **[Remote Development (Preview) improvements](#remote-development-preview)** - Save to local file system, drag and drop files to remotes, and more.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to see new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Workbench

### Hide individual status bar items

There is now a context menu for the status bar to hide and show individual entries.

![Hide entries in the status bar](images/1_36/status-bar.gif)

The configuration is persisted globally across all workspaces.

**Note**: The setting `workbench.statusBar.feedback.visible` was removed in favor of this new approach. You can hide the feedback smiley using the status bar context menu.

### Tree indent guides

The tree widget now supports indent guides. This means indent guides are now available in the File Explorer, Search view, Debug views, etc.

![Explorer with indentation guides](images/1_36/tree-indent-guides.png)

As before, you can control the tree's indentation level using the `workbench.tree.indent` setting and now you can also control how indent guides behave with the `workbench.tree.renderIndentGuides` setting.

The possible values for `workbench.tree.renderIndentGuides` are:

* `onHover` - Show indent guide lines when hovering on a tree. The default behavior.
* `always` - Always show indent guide lines in a tree.
* `none` - Don't show indent guides.

### Drag and drop a folder to copy

It is now possible to drag and drop a folder from outside VS Code into the File Explorer to copy it. Previously, when dropping a folder into the VS Code Explorer, we would always open a workspace containing that folder. Now it is possible to just copy the folder content.

![Explorer drag and drop to copy](images/1_36/dnd-copy.png)

### Copy paste filename incrementor change

When copy pasting files and folders that are duplicates inside the VS Code Explorer, VS Code increments the name of the pasted file. The way we were doing the incremental naming sometimes led to unexpected results.

To try to simplify naming, we now increment the filename the following way:

"hello.txt" -> "hello copy.txt" -> "hello copy 2.txt" -> "hello copy 3.txt"

### Disable Alt key focus of the custom menu bar

Many users have asked to disable the behavior of focusing the custom menu bar when the Alt-key is pressed. To prevent this behavior, set the new setting, `window.customMenuBarAltFocus`, to `false`.

### Minimap search decorations

When searching within a file, the resulting matches will now be highlighted both within the file and in the minimap:

![Show search results in the minimap](images/1_36/minimap_search.png)

### Updated warning colors

We've updated `list.warningForeground` and `editorWarning.foreground` to better match the rest of the editor warning colors. You'll see this updated color in the File Explorer, Peek error view, and editor squiggles:

![Warning color update](images/1_36/warning-color.png)

We also updated `editorOverviewRuler.findMatchForeground` to better stand out with the updated warning colors in the ruler:

![Find match color update](images/1_36/find-match-ruler-color.png)

### Online services settings

VS Code uses online services for various purposes such as downloading product updates, finding, installing and updating extensions, or providing Natural Language Search within the Settings editor. You can choose to turn on/off features that use these services through your user settings, which you can filter with the tag `@tag:usesOnlineServices`. There is now a command **File** > **Preferences** > **Online Services Settings** that applies the tag in the Settings editor.

![online services settings](images/1_36/online-services-settings.png)

For more information, see our [telemetry documentation](https://code.visualstudio.com/docs/getstarted/telemetry).

## Integrated Terminal

### Better default shell selector

Windows has had the **Select Default Shell** command for some time and last release it was added to the Integrated Terminal dropdown menu. This command is now also available on macOS and Linux and exposes the shells registered on the system by reading the `/etc/shells` file.

![Default shell selector on macOS](images/1_36/terminal-mac-shell-selector.png)

### Launch terminals with clean environments

The Integrated Terminal in VS Code has always acted a little differently to normal terminals, particularly on Linux and macOS. The reason is that the environment was always inherited from VS Code's window (instance) and VS Code/Electron-related environment variables were removed, whereas a normal terminal would typically be launched from the Dock/Start menu and use the system environment. This could cause issues in certain scenarios, for example Python virtual environments were broken because of how they use the `$PATH` variable.

There's a new preview option, `terminal.integrated.inheritEnv`, that when `false` causes the terminal to not use VS Code's environment.

Instead, depending on the platform, it will do the following:

* **Linux**: Fetch and use the environment of the parent process of VS Code's "main process".
* **macOS**: Pull a handful of important environment variables off the current environment and only include them. Eventually we would like macOS to behave the same as Linux but there are currently issues with fetching environments.
* **Windows**: Currently this setting does not affect Windows.

The main visible result of setting `inheritEnv` to `false` is that `$SHLVL` (shell level) should now be 1 and `$PATH` should not include duplicate paths, provided your launch scripts don't intentionally include them.

The default value for `terminal.integrated.inheritEnv` is `true`, which is the previous behavior, but we will probably switch the value to `false` in the future.

### Change to Ctrl+\

Previously, `Ctrl+\` was mapped to the command to split the terminal on Linux and Windows but this has been changed to pass `SIGQUIT` through to the shell, as most people would expect a terminal to do. If you want the old behavior, you can add this keyboard shortcut to your `keybindings.json` file.

```json
{
    "key": "ctrl+\\", "command": "workbench.action.terminal.split", "when": "terminalFocus"
}
```

## Tasks

### Sequential dependency execution

The `dependsOn` task attribute still defaults to running all dependencies in parallel, but now you can specify `"dependsOrder": "sequence"` and have your task dependencies executed in the order they are listed in `dependsOn`. Any background/watch tasks used in `dependsOn` must have a problem matcher that tracks when they are "done".

The example task below runs task Two, task Three, and then task One.

```json
{
    "label": "One",
    "type": "shell",
    "command": "echo Hello ",
    "dependsOrder": "sequence",
    "dependsOn":[
        "Two",
        "Three"
    ]
}
```

### Problem matcher path detection

When task output is scanned for problems, information about the path is provided using the `fileLocation` problem matcher attribute. In addition to the existing `relative` and `absolute` options, you can now specify `autoDetect`. When using `autoDetect`, the task system will automatically try to determine whether the paths in the problems are relative or absolute.

## Languages

### TypeScript 3.5.2

This release includes TypeScript 3.5.2, a small update that [fixes a few important bugs](https://github.com/microsoft/TypeScript/milestone/97?closed=1).

### Use syntax only feature of JavaScript and TypeScript more quickly

VS Code's JavaScript and TypeScript language features is powered by a [TypeScript server](https://github.com/microsoft/TypeScript/wiki/Standalone-Server-%28tsserver%29). This server powers complex features such as IntelliSense and error reporting, along with simpler features such as code folding and document outlines.

Features such as IntelliSense require that the TypeScript server evaluate the entire JavaScript or TypeScript project before it can return any results, and this can take time for larger projects. While this processing is happening, the server is not able to handle any other requests, including requests for simple features like code folding that only require a basic semantic understanding of the current file. You may have seen this issue if you've ever noticed a delay before code folding or the document outline become available.

In order to let you start working with your code faster, we've added a new experimental option whereby VS Code uses two TypeScript servers: one that only handles simple syntax-based operations, and a complete one that handles project processing, IntelliSense, error reporting, and other advanced language features. To enable this behavior, set `"typescript.experimental.useSeparateSyntaxServer": true`. This setting requires using TypeScript 3.4 or newer in your workspace.

## Debugging

### Jump to cursor

We have added a new debug command **Jump to Cursor**, which lets you move program execution to a new location without executing any of the code in between. If **Jump to Cursor** is supported by a debugger, the new command appears in the editor context menu and Command Palette while debugging. Currently this command is only available from the C# extension, but other debug extensions should follow soon.

![Jump to cursor debugger command](images/1_36/jump-to-cursor.gif)

### Disable console word wrap

A new setting, `debug.console.wordWrap`, controls whether word wrap is enabled in the Debug Console. By default, all lines are wrapped. If the setting is turned off, the lines will no longer break in the Debug Console and there is a horizontal scrollbar.

### Node.js debug configuration attribute useWSL is deprecated

With the [WSL](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl) extension, universal [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) support has arrived in VS Code. Now all extensions can be used in WSL in a seamless way.

Consequently, we are planning to remove the now obsolete WSL support we added to VS Code's Node.js debugger two years ago. As a first step toward this goal, we are deprecating the `useWSL` debug configuration attribute. Starting with this milestone, the attribute will show up with a squiggly when opening a `launch.json` file in the editor. In addition, a notification appears when a debug session is launched that contains a `useWSL`.

Here are the steps for migrating an existing project that uses the `useWSL` flag to debugging with the [WSL](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-wsl) extension:

* Make sure that you have the WSL extension installed.
* Open the folder of your project in VS Code.
* Reopen the project in WSL by using the **WSL: Reopen Folder in WSL** command.
* Press `kb(workbench.action.debug.start)`.
* Remove the `useWSL` flag from the debug configuration.

For more information, see our [Developing in WSL](https://code.visualstudio.com/docs/remote/wsl) documentation.

## Contributions to extensions

### JavaScript and TypeScript nightly extension

The new [JavaScript and TypeScript nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next) uses the nightly build of TypeScript (typescript@next) for VS Code's built-in TypeScript version, which powers JavaScript and TypeScript IntelliSense. This makes it easy to test the latest TypeScript features and provide feedback about them!

### TSLint 1.2

We've published a new version of the [TSLint extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-tslint-plugin) that fixes a few important bugs. This release also adds a warning indicator to the VS Code status bar if there is a `tslint.json` file in a workspace but TSLint itself is not properly installed.

### Installer for Java developers

There is a new [Visual Studio Code Installer for Java](https://devblogs.microsoft.com/visualstudio/announcing-visual-studio-code-java-installer/) to help first-time developers set up their Java environment. When you [run the Java Pack Installer](https://aka.ms/vscode-java-installer-win), it automatically detects whether the JDK (Java Development Kit), Visual Studio Code, and required extensions are already installed. If not, the installer can download and configure the missing dependencies for you. You can also use the installer to add the Java-related components to your existing Visual Studio Code installation.

![The Java installer UI](images/1_36/java-installer.png)

With the Java extensions installed, Visual Studio Code provides comprehensive Java development features such as smart code completion, refactoring, debugging, and testing as well as project management and application server integration.

### Remote Development (Preview)

Work has continued on the [Remote Development](https://aka.ms/vscode-remote/download/extension) extensions, which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment. You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_36.md).

And to learn more about developing Linux applications on Windows, see this [Linux development with WSL and Visual Studio Code Remote](https://devblogs.microsoft.com/commandline/take-your-linux-development-experience-in-windows-to-the-next-level-with-wsl-and-visual-studio-code-remote/) blog post from the Windows Developer Platform team.

### Debugger for Chrome as remote UI extension

If you are working on a web project in a remote window, you can use the [Debugger for Chrome extension](https://marketplace.visualstudio.com/items?itemName=msjsdiag.debugger-for-chrome) to debug it in a local Chrome window. Just install the extension, start your dev server on the remote, forward the server's port, and start your launch configuration. See the [extension README](https://github.com/microsoft/vscode-chrome-debug#usage-with-remote-vs-code-extensions) for details.

## Extension authoring

### Splitting vscode package into @types/vscode and vscode-test

During the [event-stream incident](https://code.visualstudio.com/blogs/2018/11/26/event-stream) last year, we found that the `vscode` package was affected as its 223 transitive dependencies included `event-stream`. These dependencies also cause GitHub security alerts from time to time for many VS Code extensions. To address dependency complexity, we started slimming down the `vscode` package.

The `vscode` package had served two purposes:

* Pull down `vscode.d.ts` for extension development.
* Run integration tests by downloading and launching a local copy of VS Code.

Now we are splitting `vscode` into `@types/vscode` and `vscode-test`, two packages with more focused functionality.

* [`@types/vscode`](https://www.npmjs.com/package/@types/vscode) contains `vscode.d.ts` for each release. For example, `npm i @types/vscode@1.34.0` installs the VS Code 1.34 Extension API. Unlike `vscode`, which pulls down `vscode.d.ts` through a `postinstall` script, this package can be fully cached by package managers.
* [`vscode-test`](https://github.com/microsoft/vscode-test) provides a set of APIs to run integration tests with VS Code. The old `vscode` package will continue to work, but new features will only go to `vscode-test`. We suggest that you switch over to `vscode-test`, which features a slimmer dependency graph and a more flexible, explicitly documented API. You can learn more about using `vscode-test` in the [Testing Extensions](https://code.visualstudio.com/api/working-with-extensions/testing-extension) article.

Additionally:

* [`vscode-dts`](https://github.com/microsoft/vscode-dts) allows you to quickly download any version of the VS Code API via CLI.
* [`vsce`](https://github.com/microsoft/vscode-vsce) now checks `@types/vscode` version against `engines.vscode` to prevent you from using a new API incompatible with older versions of VS Code.
* The [`helloworld-test-sample`](https://github.com/microsoft/vscode-extension-samples/tree/main/helloworld-test-sample), [Testing Extensions](https://code.visualstudio.com/api/working-with-extensions/testing-extension) page, and [Continuous Integration](https://code.visualstudio.com/api/working-with-extensions/continuous-integration) page are updated to use `vscode-test`.
* The [Testing Extensions](https://code.visualstudio.com/api/working-with-extensions/testing-extension) page contains a [migration guide](https://code.visualstudio.com/api/working-with-extensions/testing-extension#migrating-from-vscode) to help you transition from `vscode` to `@types/vscode` and `vscode-test`.
* All VS Code [sample extensions](https://github.com/microsoft/vscode-extension-samples) now use `@types/vscode`.
* The VS Code [extension generator](https://github.com/microsoft/vscode-generator-code) scaffolds extensions using the `@types/vscode` and `vscode-test` packages.

### Node.js update

The Electron version that VS Code runs on has been updated and brings with it an update to Node.js from `10.2.0` to `10.11.0`. All extensions will now run on this newer version of Node.js.

### APIs for remote

There is a new property `vscode.env.remoteName` that is defined whenever a remote extension host runs. Its value is defined by the extension bootstrapping the remote extension host and the value is available on the local and remote extension hosts.

Extensions that need to know whether they run on the remote or local extension host can use `Extension#extensionKind`, which is either `ExtensionKind.UI` or `ExtensionKind.Workspace`. The value represents what's defined in the extension's `package.json` file or overridden by the user. When no remote extension host exists, the value is always `ExtensionKind.UI`.

### DocumentLink.tooltip

The new `DocumentLink.tooltip` property allows a `DocumentLinkProvider` to customize the text displayed when a user hovers over a document link:

![Custom hover text displayed for a Markdown link](images/1_36/api-documentlink-tooltip.png)

VS Code includes instructions on how to activate the link (`cmd + click` in the example above) along with the `tooltip` text.

### Port forwarding and port mapping now support '127.0.0.1' in addition to 'localhost'

The `vscode.env.openExternal` API opens an URI using the default external application. When `openExternal` is called by a remote extension on a local URI, such as `http://localhost:8080`, VS Code automatically opens a tunnel that connects a port on the local machine to the opened port on the remote machine. This automatic tunneling previously was only enabled for 'localhost' URIs but it is now enabled for '127.0.0.1' as well.

Additionally, the [webview](https://code.visualstudio.com/api/extension-guides/webview) port mapping API now handles '127.0.0.1' in addition to 'localhost' URIs.

### More properties marked as readonly or ReadonlyArray

More properties in the VS Code API are now marked as readonly in `vscode.d.ts` to better express their intent to extensions.

Notable changes include:

* All fields on event interfaces are now readonly. Event objects should never be mutated as the same object can be dispatched to multiple listeners.
* The methods on `DiagnosticCollection` now take readonly arrays. This change was made because you can only update a `DiagnosticCollection` through its methods, not by mutating an array previously passed to it.
* `Extensions.all` is now a readonly array since it cannot be mutated.
* `TextEditor.insertSnippet` now takes readonly arrays since it does not mutate its parameters.

These new `readonly` modifiers may cause compile errors for extension code that explicitly typed out the non-readonly types that the VS Code API was previously using:

```ts
vscode.window.onDidChangeTextEditorSelection(e => {
    // Error: `e.selections` is now a readonly array but
    // our `updateForSelections` function takes a mutable array
    updateForSelections(e.selections);
});

function updateForSelections(selections: vscode.Selection[]) {
    ...
}
```

To fix this, propagate the `readonly` modifier through your extension's source code as well:

```ts
vscode.window.onDidChangeTextEditorSelection(e => {
    updateForSelections(e.selections);
});

function updateForSelections(selections: readonly vscode.Selection[]) {
    ...
}
```

### TerminalOptions.hideFromUser

The `runInBackground` terminal `hideFromUser` option is now in the stable API. Use this option to completely hide a terminal from the user until `Terminal.show()` is called:

```ts
const term = window.createTerminal({ hideFromUser: true });
term.sendText('do something')
```

Combined with the `sendText` and `onDidWriteData` APIs, it's possible for an extension to interact with an interactive terminal, for example, to set up a connection, and only call `Terminal.show()` if something goes wrong.

### Comment reactions

The Comments API now supports displaying and managing user reactions on comments. When `Comment.reactions` is present, reactions will be rendered under the comment body.

If an extension registers a reaction handler with `CommentController.reactionHandler`, users will be able to respond to the existing reactions or use the reactions picker to create a new reaction.

![Comment reactions UI](images/1_36/comment-reaction.png)

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always we are keen on your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project.

Note that you cannot publish an extension that uses a proposed API. We may likely make breaking changes in the next release and we never want to break existing extensions.

### vscode.workspace.fs

There is proposed API to allow extensions to interact with [file system providers](https://github.com/microsoft/vscode/blob/b1dd5ab40e08315fcc8f58310556479738a5e7b2/src/vs/vscode.d.ts#L5642). The API lets extensions create, read, write, and delete files and folders from arbitrary file systems. For example, a language extension can now load source files that are provided from an ftp-server or another remote source.

The API is accessed via a new property on the workspace-object: `vscode.workspace.fs`. Give it a try and feel free to leave feedback on [issue #48034](https://github.com/microsoft/vscode/issues/48034).

### Updated API for code insets

We have refactored and simplified the code insets proposal. Instead of using the provider-pattern, it is now more like the text decorations API.

```ts
export function createWebviewTextEditorInset(editor: TextEditor, line: number, height: number, options?: WebviewOptions): WebviewEditorInset;
```

Given an editor, a line, and a height, you can create insets. Insets then use the [Webview](https://github.com/microsoft/vscode/blob/b1dd5ab40e08315fcc8f58310556479738a5e7b2/src/vs/vscode.d.ts#L5817) that's already known from the `WebviewPanel`. As with decorations, insets are being disposed once their containing editor closes.

### Webview.resourceRoot

The proposed `resourceRoot` constant on webviews exposes the root from which local resources are loaded in webviews.

```ts
const panel = vscode.window.createWebviewPanel(CatCodingPanel.viewType, 'Cat Coding', vscode.ViewColumn.One, {
    // Restrict the webview to only loading local content from our extension's `media` directory.
    localResourceRoots: [vscode.Uri.file(path.join(extensionPath, 'media'))]
});

const resourceRoot = await panel.resourceRoot;
panel.html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${resourceRoot} https:;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cat Coding</title>
</head>
<body>
    <img src="${resourceRoot}/${path.join(extensionPath, 'media')}/cat.gif" width="300" />
</body>
</html>`
```

When VS Code is run on the desktop, `resourceRoot` will be `vscode-resource:`. However, the value may be different when VS Code is run in other environments, such a browser.

### API to get the default shell

Since the `terminal.integrated.shell.<platform>` settings now default to `null`, extensions can no longer use that setting to detect the default shell. There is now a proposed API `vscode.env.shell` that will return the default shell for the terminal:

```ts
const shell = vscode.env.shell;
if (shell.search(/(powershell|pwsh)/i) !== -1) {
    // Do something special that PowerShell needs
}
```

### Language Server Protocol

A new version of the [Language Server Index Format](https://github.com/microsoft/language-server-protocol/blob/main/indexFormat/specification.md) tools for TypeScript have been implemented. The new versions have an improved format for easier importing of large dumps into databases without making it database specific. See the instructions in the [lsif-node](https://github.com/microsoft/lsif-node/blob/main/README.md) repository for details on running these tools.

## Engineering

### Electron 4.0 update and Electron 6.0 exploration

In this milestone, we finished the exploration of bundling Electron 4 into VS Code, making it the first time this Electron version ships with stable. This is a major Electron release and comes with Chrome 69 and Node.js 10.11.0 (a major leap forward from our current version with Chrome 66 and Node.js 10.2.0).

Unfortunately, we had to disable macOS simple fullscreen support (`"window.nativeFullScreen": false`) for one release due to [issue #75054](https://github.com/microsoft/vscode/issues/75054). We expect to include a fix to our Insiders build early next month.

We have started to explore updating to Electron 6, which we hope to push to Insiders soon.

### Better code loading

Electron 4 gave us access to new script caching APIs. With the new caching APIs, [cached data](https://v8.dev/blog/code-caching) can be created without affecting the very first start-up and can be created repeatedly to cover lazily parsed functions. That and improvements in our loader resulted in ~15% faster code loading.

### Linux 32-bit support ends

VS Code is using the [Electron framework](https://electronjs.org/) to run on multiple platforms. With this release, we move to Electron version 4.x, which means that VS Code will no longer run on Linux 32-bit. Please [update](https://code.visualstudio.com/download) to a 64-bit version of VS Code. All your settings and extensions will work as before, without the need to migrate anything. You can read the related [blog post from Electron](https://electronjs.org/blog/linux-32bit-support) for more information.

### iframe-based webview exploration

This iteration, we explored using normal iframes instead of Electron's `<webview>` tags to implement [VS Code's webviews](https://code.visualstudio.com/api/extension-guides/webview). This work is primarily being done to support running VS Code in the browser, but `<webview>` is also fairly complex and has caused issues for us in the past. Our hope is that we eventually will be able to replace our use of `<webview>` with normal iframes.

We've made significant progress on iframe-based webviews and can now successfully run many webviews from extensions in browsers, but there's still lots of work to be done. We will continue this exploration in July.

## Notable fixes

* [41356](https://github.com/microsoft/vscode/issues/41356): hotkey to move to next/previous error should not move NVDA's focus outside the editor
* [45515](https://github.com/microsoft/vscode/issues/45515): Uri#parse can break path-component
* [54084](https://github.com/microsoft/vscode/issues/54084): Do not change debug touchbar buttons position when stepping
* [70248](https://github.com/microsoft/vscode/issues/70248): The cls command doesn't clear the terminal (Windows 10 1903, ConPTY)
* [74710](https://github.com/microsoft/vscode/issues/74710): Image preview should only draw "transparent" checkered background for the size of the image
* [75359](https://github.com/microsoft/vscode/issues/75359): Variables pane does not update after changing value via debug console
* [41085](https://github.com/microsoft/vscode/issues/41085): Git: File events don't work if .git is outside the opened folder

## Thank you

Last but certainly not least, a big *__Thank You!__* to the following folks that helped to make VS Code even better:

Contributions to `vscode`:

* [Abby (@abbychau)](https://github.com/abbychau): Add an option to allow to open in new window after git initializing or cloning  [PR #69763](https://github.com/microsoft/vscode/pull/69763)
* [Andrius (@andrius-pra)](https://github.com/andrius-pra): Sync languages provided by Typescript plugins [PR #75371](https://github.com/microsoft/vscode/pull/75371)
* [Aurélien Pupier (@apupier)](https://github.com/apupier): Add License field to package.jsons #68423 [PR #68771](https://github.com/microsoft/vscode/pull/68771)
* [Jakub Čábera (@Ash258)](https://github.com/Ash258): shellscript: Add folding markers [PR #75828](https://github.com/microsoft/vscode/pull/75828)
* [Lee Houghton (@asztal)](https://github.com/asztal): Handle multiple users with /tmp/vscode-typescript [PR #75547](https://github.com/microsoft/vscode/pull/75547)
* [Babak K. Shandiz (@babakks)](https://github.com/babakks): Added `cachedScrollTop` to SCM panel [PR #74723](https://github.com/microsoft/vscode/pull/74723)
* [Patrick Burke (@burknator)](https://github.com/burknator): Makes status zoom button a IStatusbarEntry #74454 [PR #75618](https://github.com/microsoft/vscode/pull/75618)
* [Chase Adams (@chaseadamsio)](https://github.com/chaseadamsio): Support theming of Quick Input / Quick Open  [PR #74041](https://github.com/microsoft/vscode/pull/74041)
* [Christian Oliff (@coliff)](https://github.com/coliff): Recommend users install EditorConfig extension [PR #75391](https://github.com/microsoft/vscode/pull/75391)
* [Hung-Wei Hung (@hwhung0111)](https://github.com/hwhung0111): Fix some typos in comment [PR #75565](https://github.com/microsoft/vscode/pull/75565)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Delete breadcrumbs.filterOnType unused setting. [PR #75971](https://github.com/microsoft/vscode/pull/75971)
  * Always focus explorer viewlet while creating new file/folder [PR #75040](https://github.com/microsoft/vscode/pull/75040)
  * Call revealLastElement within runAtThisOrScheduleAtNextAnimationFrame in repl [PR #75043](https://github.com/microsoft/vscode/pull/75043)
  * Simplify incrementFileName [PR #75398](https://github.com/microsoft/vscode/pull/75398)
* [Jonas Kemper (@jk21)](https://github.com/jk21): Package json info [PR #72763](https://github.com/microsoft/vscode/pull/72763)
* [@malingyan2017](https://github.com/malingyan2017): Fix Recent list in dock does not show recent files/folders #74788 [PR #75108](https://github.com/microsoft/vscode/pull/75108)
* [Denis Stepanov (@meduzik)](https://github.com/meduzik): Empty arguments in launch commands are now properly escaped [PR #25098](https://github.com/microsoft/vscode/pull/25098)
* [Micah Smith (@Olovan)](https://github.com/Olovan): Fix for issue #35245 [PR #75357](https://github.com/microsoft/vscode/pull/75357)
* [@orange4glace](https://github.com/orange4glace)
  * fix: #75693 [PR #75695](https://github.com/microsoft/vscode/pull/75695)
  * fix: #72626 [PR #75826](https://github.com/microsoft/vscode/pull/75826)
* [Jesse Mazzella (@ozyx)](https://github.com/ozyx): remove trailing '/' from repo url for baseFolderName [PR #75822](https://github.com/microsoft/vscode/pull/75822)
* [Alasdair McLeay (@penx)](https://github.com/penx): Node module resolution for CSS import [PR #70693](https://github.com/microsoft/vscode/pull/70693)
* [Raul Piraces Alastuey (@piraces)](https://github.com/piraces): Suppress MsgBox when Wizard is running in silent mode [PR #76215](https://github.com/microsoft/vscode/pull/76215)
* [Remco Haszing (@remcohaszing)](https://github.com/remcohaszing)
  * mark .config/git/config as properties file [PR #74802](https://github.com/microsoft/vscode/pull/74802)
  * Add syntax highlighting for .gitmodules [PR #75332](https://github.com/microsoft/vscode/pull/75332)
* [Evgeny Zakharov (@risenforces)](https://github.com/risenforces): add window.disableMenuBarAltBehavior option [PR #73258](https://github.com/microsoft/vscode/pull/73258)
* [Samuel Bronson (@SamB)](https://github.com/SamB): Fix typo: timemout -> timeout [PR #75162](https://github.com/microsoft/vscode/pull/75162)
* [TBK (@TBK)](https://github.com/TBK): Add support for APKBUILD [PR #75706](https://github.com/microsoft/vscode/pull/75706)
* [Tomáš Chaloupka (@tchaloupka)](https://github.com/tchaloupka): Fix LANG env var for Czech [PR #75519](https://github.com/microsoft/vscode/pull/75519)
* [Tony Xia (@tony-xia)](https://github.com/tony-xia)
  * Fixed typos [PR #74545](https://github.com/microsoft/vscode/pull/74545)
  * identfier -> identifier, Servuce -> Service [PR #74544](https://github.com/microsoft/vscode/pull/74544)
* [Waldir Pimenta (@waldyrious)](https://github.com/waldyrious): Fix spelling of the example abbreviation (e.g.) [PR #74785](https://github.com/microsoft/vscode/pull/74785)

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Alexander (@usernamehw)](https://github.com/usernamehw)
* [Eric Amodio (@eamodio)](https://github.com/eamodio)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
* [Danny Tuppeny (@DanTup)](https://github.com/DanTup)

Please see our [Community Issue Tracking](https://github.com/microsoft/vscode/wiki/Community-Issue-Tracking) page, if you want to help us manage incoming issues.

Contributions to `vscode-css-languageservice`:

* [Dmitry Parzhitsky (@parzh)](https://github.com/parzh): Fix pseudo-element specificity [PR #154](https://github.com/microsoft/vscode-css-languageservice/pull/154)

Contributions to `vscode-html-languageservice`:

* [Liam Newman (@bitwiseman)](https://github.com/bitwiseman): Update js-beautify to 1.10.0 [PR #61](https://github.com/microsoft/vscode-html-languageservice/pull/61)
* [Javey (@Javey)](https://github.com/Javey): Make it can be compressed by uglify-js [PR #64](https://github.com/microsoft/vscode-html-languageservice/pull/64)

Contributions to `language-server-protocol`:

* [Danny Tuppeny (@DanTup)](https://github.com/DanTup): Fix typo "must not sent" -> "must not send" [PR #747](https://github.com/microsoft/language-server-protocol/pull/747)

Contributions to `debug-adapter-protocol`:

* [Joel Day (@joelday)](https://github.com/joelday): Add Papyrus implementation [PR #59](https://github.com/microsoft/debug-adapter-protocol/pull/59)

Contributions to `vscode-azurecli`:

* [Matthew Burleigh (@mburleigh)](https://github.com/mburleigh): Open results in new editor [PR #55](https://github.com/microsoft/vscode-azurecli/pull/55)
* [Matthew Burleigh (@mburleigh)](https://github.com/mburleigh): Add status bar item to indicate progress [PR #56](https://github.com/microsoft/vscode-azurecli/pull/56)

Contributions to `vscode-vsce`:

* [@atsutton](https://github.com/atsutton): Changed log level to info. [PR #369](https://github.com/microsoft/vscode-vsce/pull/369)
* [James George (@jamesgeorge007)](https://github.com/jamesgeorge007)
  * fix(chore): Minor refactor [PR #366](https://github.com/microsoft/vscode-vsce/pull/366)
  * Remove stub type definition for commander [PR #359](https://github.com/microsoft/vscode-vsce/pull/359)
  * Suggest matching commands if the user makes a typo [PR #358](https://github.com/microsoft/vscode-vsce/pull/358)
  * Show up usage information the right way [PR #357](https://github.com/microsoft/vscode-vsce/pull/357)
* [Jonathan Nagy (@nagytech)](https://github.com/nagytech): Allow commit message to be specified [PR #365](https://github.com/microsoft/vscode-vsce/pull/365)

Contributions to `vscode-recipes`:

* [Mladen Mihajlović (@mika76)](https://github.com/mika76): Vue - Updated readme with info gathered from issues [PR #213](https://github.com/microsoft/vscode-recipes/pull/213)
* [Ephraim Khantsis (@doom777)](https://github.com/doom777): update ng-test launch configuration [PR #212](https://github.com/microsoft/vscode-recipes/pull/212)

Contributions to `localization`:

There are over 800 [Cloud + AI Localization](https://github.com/microsoft/Localization/wiki) community members using the Microsoft Localization Community Platform (MLCP), with over about 100 active contributors to Visual Studio Code. We appreciate your contributions, either by providing new translations, voting on translations, or suggesting process improvements.

Here is a snapshot of [contributors](https://microsoftl10n.github.io/VSCode/). For details about the project including the contributor name list, visit the project site at [https://aka.ms/vscodeloc](https://aka.ms/vscodeloc).

* **Bosnian:** Ismar Bašanović, Ernad Husremovic.
* **Bulgarian:** Иван Иванов, Gheorghi Penkov.
* **Czech:** Tadeáš Cvrček, Michal Franc, Jan Kos, Radim Hampl.
* **Danish:** René Pape, Lars Vange Jørgensen, Martin Liversage, Lasse Stilvang, Anders Lund, Allan Kimmer Jensen, Anton Ariens.
* **Dutch:** Leroy Witteveen, Luc Sieben, Maxim Janssens, Damien van Gageldonk, Tom Meulemans.
* **English (United Kingdom):** Martin Littlecott, Alexander Ogilvie, Fabio Zuin, Mohit Nain, Sulkhan Ninidze, alshyab wa3ed, Tejas kale.
* **Finnish:** Lasse Leppänen, Petri Niinimäki, Sebastian de Mel.
* **French:** Antoine Griffard, Thierry DEMAN-BARCELÒ.
* **German:** Julian Pritzi, Patrick Burke, Ettore Atalan, Meghana Garise.
* **Greek:** Θοδωρής Τσιρπάνης, Charalampos Fanoulis, Vassilis Vouvonikos.
* **Hebrew:** חיים לבוב, Eyal Ellenbogen.
* **Hindi:** Sanyam Jain, Kishan K.
* **Hungarian:** Boldi Kemény.
* **Chinese Simplified:** 斌 项, paul cheung, 张锐, Yizhi Gu, Yiting Zhu, Justin Liu, Shi Liu, Pluwen, Joel Yang, Jieting Xu, Chen Yang, 涛 罗, 立飞 李, 雨齐 刘, cuibty wong, 建 周, XIANG ZUO.
* **Chinese Traditional:** LikKee 沥祺 Richie, Winnie Lin, Jeremy.
* **Indonesian:** Jakka Prihatna, Arif Fahmi, Septian Adi, Heston Sinuraya, Hendra Widjaja, Don Nisnoni, Eriawan Kusumawardhono, Bervianto Leo Pratama, Laurensius Dede Suhardiman, Rifani, rsyad, Christian Elbrianno.
* **Italian:** andrea falco, Aldo Donetti.
* **Japanese:** Michihito Kumamoto, Yoshihisa Ozaki, Aya Tokura, TENMYO Masakazu, 太郎 西岡.
* **Korean:** Hongju, 우현 조, Hoyeon Han, Hong Kwon.
* **Latvian:** Kaspars Bergs, Andris Vilde.
* **Lithuanian:** Andrius Svylas, Tautvydas Derzinskas, Karolis Kundrotas, Martynas J..
* **Norwegian:** Dag H. Baardsen, Ole Kristian Losvik.
* **Polish:** Rafał Całka, Marcin Weksznejder, Jakub Żmidziński, Rafał Wolak, Szymon Seliga, Grzegorz Miros.
* **Portuguese (Brazil):** Alessandro Trovato, Thiago Dupin Ugeda, Weslei A. de T. Marinho, Rafael Lima Teixeira, Gerardo Magela Machado da Silva, Marcos Albuquerque, Loiane Groner, Alessandro Fragnani, Judson Santiago, Andrei Bosco, Fábio Corrêa, Roberto Fonseca, Fabio Lux, Emmanuel Gomes Brandão, Guilherme Pais, Rodrigo Vieira, André Gama.
* **Portuguese(Portugal):** Nuno Carapito, Pedro Daniel, José Rodrigues, Diogo Barros.
* **Romanian:** Stefan Gabos.
* **Russian:** Дмитрий Кирьянов, Анатолий Калужин.
* **Spanish:** David Fernández Aldana, Ricardo Rubio, Thierry DEMAN, José María Aguilar.
* **Swedish:** Johan Spånberg, Notetur Nomen.
* **Tamil:** Merbin J Anselm, Jeyanthinath Muthuram, Boopesh Kumar, Nithun Harikrishnan, Vignesh Rajendran.
* **Turkish:** Meryem Aytek, Fıratcan Sucu, Ahmetcan Aksu, Mehmet Yönügül, Ömer Sert, Anıl MISIRLIOĞLU, Misir Jafarov, Bruh Moment.
* **Ukrainian:** Arthur Murauskas, Alexander Varchenko, Вадим Шашков, Евгений Коростылёв.
* **Vietnamese:** Van-Tien Hoang, Vuong Bui, Chủ Tất.

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
