---
ContentId: 90f8dc30-1e71-4ea7-8230-2bf09bfb97d4
MetaDescription: Visual Studio Code Azure Tutorials
DateApproved: 02/1/2024
---
# Deploying Applications to Azure

Visual Studio Code makes it easy to deploy your applications to the cloud with Azure and we've created walkthroughs to help you get started.

Whether you're a fullstack, backend, API developer, or DevOps engineer,  you'll find the deployment steps you need.

## Deployment tutorials

The tutorials and topics below describe different ways of creating and deploying apps to Azure via Visual Studio Code:

Tutorial(s) | Description | Framework / Language
--- | --- | ---
[Deploy a static website](https://learn.microsoft.com/azure/static-web-apps/getting-started) | Create, deploy, and update a static website | Angular, Blazor, React, or Vue
[Deploy Node.js apps](/docs/nodejs/nodejs-deployment.md) | Deploy web apps, containerized apps, or serverless code | Node.js
[Deploy Python apps](/docs/python/python-on-azure.md) | Deploy web apps, containerized apps, or serverless code | Python
[Deploy Java apps](/docs/java/java-on-azure.md) | Deploy web apps, Spring Boot apps, or serverless code | Java

You can find additional tutorials and walkthroughs on the
[Azure Developer Center](https://learn.microsoft.com/azure/developer), including language-specific articles for JavaScript and Node.js, Python, Java, and .NET.

## Next steps

* [Deploy to Azure Container Apps](https://learn.microsoft.com/en-us/azure/container-apps/deploy-visual-studio-code) - Run microservices and containerized applications on a serverless platform.
* [Visual Studio Code Azure Extensions](/docs/azure/overview.md) - The Azure Tools extension pack is designed to deploy your application to Azure within minutes.
* [Working with Docker](/docs/azure/docker.md) - Put your application in a Docker container for easy reuse and deployment.
