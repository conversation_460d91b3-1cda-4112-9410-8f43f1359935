# April 2022 (version 1.67)

## Core

### Extending support for Enterprise Linux distros

Linux x64 and ARM64 servers are now compatible with glibc >= 2.17 and libstdc++ >= 6.0.20 (GLIBCXX_3.4.20) allowing them to run natively on RHEL 7, CentOS 7 distros.

## Containers (version 0.234.x)

### Move settings and extensions properties to "vscode" namespace

We are moving the VS Code-specific properties to a `vscode` property under `customizations` in preparation for the open specification for `devcontainer.json`.

```jsonc
{
    "customizations": {
        "vscode": {
            "settings": { /*...*/ },
            "extensions": [ /*...*/ ]
        }
    }
}
```

### Refactoring to extract reference implementation

Work is being done behind the scenes to extract a reference implementation for the dev container configuration. This will be released as a CLI that can build and start a dev container from a `devcontainer.json`. The CLI will be independent of VS Code and also support running in continuous integration (CI) builds.

Expect more details on this effort to become available as we make progress with our open specification effort.
