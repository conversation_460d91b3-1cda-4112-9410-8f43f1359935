---
ContentId: d9583ed0-aaf6-4ce1-9a27-bbfb0017b6be
DateApproved: 10/5/2021
MetaDescription: Learn how to personalize Visual Studio Code with themes.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Personalize Visual Studio Code

In this Visual Studio Code tutorial, we show you how to personalize Visual Studio Code with themes.

<iframe src="https://www.youtube-nocookie.com/embed/HOShAQzOy4Q" width="640" height="320" allowFullScreen="true" frameBorder="0" title="Personalize Visual Studio Code"></iframe>

Here's the next video we recommend: [Extensions in Visual Studio Code](/docs/introvideos/extend.md)

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Change your Color Theme.
* Install a new Color Theme from the VS Code Extension Marketplace.
* Change your File Icon Theme.

## Next video

* [Extensions in Visual Studio Code](/docs/introvideos/extend.md) - Add features to VS Code through Extensions.
* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [User and Workspace Settings](/docs/configure/settings.md) - Configure user and workspace settings.
* [Key Bindings](/docs/configure/keybindings.md) - See the default keyboard shortcuts (key bindings) for editing, navigation, and more.
* [Snippets](/docs/editing/userdefinedsnippets.md) - Create your own custom snippets.
* [Themes](/docs/configure/themes.md) - Change your Color Theme to customize VS Code's background text and language syntax colorization.
