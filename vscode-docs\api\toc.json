[
  {
    "name": "Get Started",
    "area": "get-started",
    "topics": [
      ["Your First Extension", "/api/get-started/your-first-extension"],
      ["Extension Anatomy", "/api/get-started/extension-anatomy"],
      ["Wrapping Up", "/api/get-started/wrapping-up"]
    ]
  },
  {
    "name": "Extension Capabilities",
    "area": "extension-capabilities",
    "topics": [
      ["Overview", "/api/extension-capabilities/overview"],
      ["Common Capabilities", "/api/extension-capabilities/common-capabilities"],
      ["Theming", "/api/extension-capabilities/theming"],
      ["Extending Workbench", "/api/extension-capabilities/extending-workbench"]
    ]
  },
  {
    "name": "Extension Guides",
    "area": "extension-guides",
    "topics": [
      ["Overview", "/api/extension-guides/overview"],
      ["Command", "/api/extension-guides/command"],
      ["Color Theme", "/api/extension-guides/color-theme"],
      ["File Icon Theme", "/api/extension-guides/file-icon-theme"],
      ["Product Icon Theme", "/api/extension-guides/product-icon-theme"],
      ["Chat", "/api/extension-guides/chat"],
      ["Chat Tutorial", "/api/extension-guides/chat-tutorial"],
      ["Language Model", "/api/extension-guides/language-model"],
      ["Language Model Tutorial", "/api/extension-guides/language-model-tutorial"],
      ["Language Model Tools", "/api/extension-guides/tools"],
      ["MCP", "/api/extension-guides/mcp"],
      ["Prompt TSX", "/api/extension-guides/prompt-tsx"],
      ["Tree View", "/api/extension-guides/tree-view"],
      ["Webview", "/api/extension-guides/webview"],
      ["Notebook", "/api/extension-guides/notebook"],
      ["Custom Editors", "/api/extension-guides/custom-editors"],
      ["Virtual Documents", "/api/extension-guides/virtual-documents"],
      ["Virtual Workspaces", "/api/extension-guides/virtual-workspaces"],
      ["Web Extensions", "/api/extension-guides/web-extensions"],
      ["Workspace Trust", "/api/extension-guides/workspace-trust"],
      ["Task Provider", "/api/extension-guides/task-provider"],
      ["Source Control", "/api/extension-guides/scm-provider"],
      ["Debugger Extension", "/api/extension-guides/debugger-extension"],
      ["Markdown Extension", "/api/extension-guides/markdown-extension"],
      ["Test Extension", "/api/extension-guides/testing"],
      ["Custom Data Extension", "/api/extension-guides/custom-data-extension"],
      ["Telemetry", "/api/extension-guides/telemetry"]
    ]
  },
  {
    "name": "UX Guidelines",
    "area": "ux-guidelines",
    "topics": [
      ["Overview", "/api/ux-guidelines/overview"],
      ["Activity Bar", "/api/ux-guidelines/activity-bar"],
      ["Sidebars", "/api/ux-guidelines/sidebars"],
      ["Panel", "/api/ux-guidelines/panel"],
      ["Status Bar", "/api/ux-guidelines/status-bar"],
      ["Views", "/api/ux-guidelines/views"],
      ["Editor Actions", "/api/ux-guidelines/editor-actions"],
      ["Quick Picks", "/api/ux-guidelines/quick-picks"],
      ["Command Palette", "/api/ux-guidelines/command-palette"],
      ["Notifications", "/api/ux-guidelines/notifications"],
      ["Webviews", "/api/ux-guidelines/webviews"],
      ["Context Menus", "/api/ux-guidelines/context-menus"],
      ["Walkthroughs", "/api/ux-guidelines/walkthroughs"],
      ["Settings", "/api/ux-guidelines/settings"]
    ]
  },
  {
    "name": "Language Extensions",
    "area": "language-extensions",
    "topics": [
      ["Overview", "/api/language-extensions/overview"],
      ["Syntax Highlight Guide", "/api/language-extensions/syntax-highlight-guide"],
      ["Semantic Highlight Guide", "/api/language-extensions/semantic-highlight-guide"],
      ["Snippet Guide", "/api/language-extensions/snippet-guide"],
      ["Language Configuration Guide", "/api/language-extensions/language-configuration-guide"],
      ["Programmatic Language Features", "/api/language-extensions/programmatic-language-features"],
      // ["Smart Editing Guide", "/api/language-extensions/smart-editing-guide"],
      ["Language Server Extension Guide", "/api/language-extensions/language-server-extension-guide"],
      ["Embedded Languages", "/api/language-extensions/embedded-languages"]
    ]
  },
  {
    "name": "Testing and Publishing",
    "area": "working-with-extensions",
    "topics": [
      ["Testing Extensions", "/api/working-with-extensions/testing-extension"],
      ["Publishing Extensions", "/api/working-with-extensions/publishing-extension"],
      ["Bundling Extensions", "/api/working-with-extensions/bundling-extension"],
      ["Continuous Integration", "/api/working-with-extensions/continuous-integration"]
      // ["Profiling Extension", "/api/advanced-topics/profiling-extension"],
    ]
  },
  {
    "name": "Advanced Topics",
    "area": "advanced-topics",
    "topics": [
      ["Extension Host", "/api/advanced-topics/extension-host"],
      ["Remote Development and Codespaces", "/api/advanced-topics/remote-extensions"],
      // ["Extension Dependencies", "/api/advanced-topics/extension-dependencies"],
      ["Using Proposed API", "/api/advanced-topics/using-proposed-api"],
      ["Migrate from TSLint to ESLint", "/api/advanced-topics/tslint-eslint-migration"],
      ["Python Extension Template", "/api/advanced-topics/python-extension-template"]
      // ["Exposing API", "/api/advanced-topics/exposing-api"],
      // ["Native Modules", "/api/advanced-topics/native-modules"],
      // ["Web Assembly", "/api/advanced-topics/web-assembly"],
      // ["Internationalization", "/api/advanced-topics/internationalization"]
    ]
  },
  {
    "name": "References",
    "area": "references",
    "topics": [
      ["VS Code API ", "/api/references/vscode-api"],
      ["Contribution Points", "/api/references/contribution-points"],
      ["Activation Events", "/api/references/activation-events"],
      ["Extension Manifest", "/api/references/extension-manifest"],
      ["Built-in Commands", "/api/references/commands"],
      ["When clause contexts", "/api/references/when-clause-contexts"],
      ["Theme Color", "/api/references/theme-color"],
      ["Product Icon Reference", "/api/references/icons-in-labels"],
      ["Document Selector", "/api/references/document-selector"]
    ]
  }
]
