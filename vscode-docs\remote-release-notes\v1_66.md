# March 2022 (version 1.66)

## Core

### Remote extension host process crashes

Previously, when the remote extension host process would crash, this would be presented incorrectly as a reconnection error and the only way to recover was to reload the window. Now, the remote extension host process is restarted automatically and a notification is shown only if it crashes 3 times within the past 5 minutes.

## Containers (version 0.231.x)

You can now add a badge or link to your repository so that users can easily open your project in Dev Containers. It will install the Dev Containers extension if necessary, clone the repo into a container [volume](https://code.visualstudio.com/remote/advancedcontainers/improve-performance#_use-clone-repository-in-container-volume), and start up the dev container.

As an example, a badge to open the [vscode-remote-try-java](https://github.com/microsoft/vscode-remote-try-java) repo would look like:

[![Open in Dev Containers](https://img.shields.io/static/v1?label=Dev%20-%20Containers&message=Open&color=blue&logo=visualstudiocode)](https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/microsoft/vscode-remote-try-java)

You would author the badge in Markdown as:

```markdown
[![Open in Dev Containers](https://img.shields.io/static/v1?label=Dev%20-%20Containers&message=Open&color=blue&logo=visualstudiocode)](https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/microsoft/vscode-remote-try-java)

```

You may also include an [open in dev container](https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/microsoft/vscode-remote-try-java) link directly.

## SSH (version 0.78.x)

You can now connect to remote Apple Silicon/M1/ARM64 machines with the Remote - SSH extension.
