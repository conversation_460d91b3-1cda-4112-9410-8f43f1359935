parameters:
  - name: jobDisplayName
    type: string
  - name: artifactName
    type: string

jobs:
- job:
  displayName: ${{ parameters.jobDisplayName }}
  pool:
    name: 1es-windows-2022-x64
    os: Windows
  templateContext:
    outputs:
      - output: pipelineArtifact
        targetPath: $(Build.ArtifactStagingDirectory)/out
        artifactName: drop_Build_${{ parameters.jobDisplayName }}

  steps:
  - checkout: self
    path: vscode-docs

  - download: ${{ parameters.artifactName }}
    artifact: drop_Build_Linux
    displayName: Download ${{ parameters.artifactName }}

  - task: CopyFiles@2
    inputs:
      contents: '**'
      sourceFolder: $(Pipeline.Workspace)/${{ parameters.artifactName }}/drop_Build_Linux
      targetFolder: $(Pipeline.Workspace)/vscode-website
    displayName: Copy ${{ parameters.artifactName }} to vscode-website

  - task: CopyFiles@2
    inputs:
      contents: '**'
      sourceFolder: $(Pipeline.Workspace)/vscode-docs
      targetFolder: $(Pipeline.Workspace)/vscode-website/vscode-docs
    displayName: Copy vscode-docs into vscode-website

  - task: NodeTool@0
    displayName: Install Node via .nvmrc
    inputs:
      versionSource: 'fromFile'
      versionFilePath: $(Pipeline.Workspace)/vscode-website/.nvmrc
      checkLatest: true

  - script: npm config list
    displayName: List npm properties

  - task: npmAuthenticate@0
    displayName: Setup NPM Authentication
    inputs:
      workingFile: $(Pipeline.Workspace)/vscode-website/server/.npmrc

  - task: npmAuthenticate@0
    displayName: Setup NPM Authentication
    inputs:
      workingFile: $(Pipeline.Workspace)/vscode-website/server/.npmrc
      customEndpoint: website npm feed

  - script: npm install
    displayName: Install dependencies
    workingDirectory: $(Pipeline.Workspace)/vscode-website

  - script: |
      npm run lint-build
      npm run lint-client
      npm run lint-server
    displayName: Lint
    workingDirectory: $(Pipeline.Workspace)/vscode-website

  - powershell: $(Pipeline.Workspace)/vscode-website/scripts/ci-build-dist-setup.ps1
    displayName: Build Dist Setup
    workingDirectory: $(Pipeline.Workspace)/vscode-website

  - powershell: $(Pipeline.Workspace)/vscode-website/scripts/ci-build-artifact-from-website.ps1
    displayName: Rebuild Dist From Website
    workingDirectory: $(Pipeline.Workspace)/vscode-website

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: $(Pipeline.Workspace)/vscode-website/dist
      archiveType: 'zip'
      includeRootFolder: false
      archiveFile: $(Build.ArtifactStagingDirectory)/out/website.zip
    displayName: Archive complete website
