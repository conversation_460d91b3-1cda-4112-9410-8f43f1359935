# September 2024 (version 1.94)

## Dev Containers

### Attach to a Kubernetes container over SSH/Tunnel

Dev Containers now supports attaching to a Kubernetes container when connected via Remote-SSH or Remote-Tunnels. Simply select **Dev Containers: Attach to Running Kubernetes Container...** from the **Command Palette** (`F1`) and pick the container to attach to.

### User setting for GPU availability

When the automatic GPU detection doesn't work as expected, you can now use the new user setting, <a href="vscode://settings/dev.containers.gpuAvailability" codesetting="true">`dev.containers.gpuAvailability`</a>, to specify whether or not GPUs are available.

This makes sure that you will get the expected behavior when a `devcontainer.json` configures GPU as an optional resource (`"hostRequirements": { "gpu": "optional" }`).

## Remote - SSH

### Baseline Test Configurations

This iteration we began developing a collection of public [remote configurations](https://github.com/microsoft/vscode-remote-release/tree/main/ssh).  Our internal release pipeline has been configured to utilize these configurations as a baseline regression testing gate.

In the next iterations we will continue to add configurations with the goal of increased compatibility and reliability of the Remote - SSH extension.

### Honor `ConnectTimeout` SSH configuration setting

Previously the Remote - SSH extension would not honor the `ConnectTimeout` SSH configuration option.

Now, the `ConnectTimeout` option is preferred per host, followed by the existing <a href="vscode://settings/remote.SSH.connectTimeout" codesetting="15">`remote.SSH.connectTimeout`</a> setting. A `ConnectTimeout` of `0` indicates a maximum timeout internal.
