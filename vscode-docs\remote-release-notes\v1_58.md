# June 2021 (version 1.58)

## Core

### New openBrowserOnce option for onAutoForward setting

The `remote.portsAttributes` setting now allows a new option for `onAutoForward`: `openBrowserOnce`. This option will open the browser only the first time the port is detected during a "session", where a "session" is reset by reloading or closing the folder. This option is useful if you have a watch task that frequently causes the process that your port is listening on to be killed and restarted.

```json
{
  "remote.portsAttributes": {
    "4000": {
      "onAutoForward": "openBrowserOnce"
    }
  }
}
```

## Containers (version 0.187.x)

### devcontainer.json support for --cache-from

When using a Dockerfile, you can now specify one or more images to use as caches when building the image. Cached image identifiers, specified with `cacheFrom` in `devcontainer.json`, are passed to the `docker build` command with `--cache-from`.

```json
{
  ...

  "build": {
    "dockerfile": "Dockerfile",
    "cacheFrom": "<image identifier>"
  }
}
```

[<PERSON> (@stuartleeks)](https://github.com/stuartleeks) contributed this option as a PR.

### Host requirements

Dev Containers now checks if the `"hostRequirements"` in the `devcontainer.json` are met by the Docker daemon's host machine:

```json
{
  ...

  "hostRequirements": {
    "memory": "10gb",
    "storage": "20gb",
    "cpus": 3
  }
}
```

![Dialog when host requirements are not met.](images/1_58/host-requirements.png)

### Update UID/GID with Docker Compose on Linux

We now update UID/GID of the container user also when using Docker Compose. The automatic update was previously only available when using a single container. This feature can be turned off with `"updateRemoteUserUID": false` in `devcontainer.json`.

### overrideCommand support with Docker Compose

We now support overriding the command and entrypoint with Docker Compose. The default with Docker Compose is to not override. This feature can be turned on with `"overrideCommand": true` in `devcontainer.json`.
