# May 2023 (version 1.79)

## Core

All remotes now provide a choice of where to open a remote from the Remote Explorer: either in the current window or a new window. These are available in both the inline or context menu.

In the video below, both the [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels) and [Dev Containers](https://code.visualstudio.com/docs/devcontainers/containers) views in the Remote Explorer display the **Connect in Current Window** and **Connect in New Window** commands.

![Open remotes in current or new window](images/1_79/remote-window.gif)

### Easier "Make Public"

You can quickly make ports public with the **Make Public** button on the port-forwarded notification. Auto forwarded ports that are made public will be remembered as public for an entire session. User forwarded ports that are made public and restored upon reconnection to the same remote will be restored as public.

![Make port public button](images/1_79/make-public-button.png)

## Tunnels

This releases adds preliminary support to connect to WSL remotes from vscode.dev. To use this feature, navigate to vscode.dev, then install the WSL extension. Once installed, open a tunnel to a Windows machine, and then use the **WSL Targets** view to connect to a WSL distribution.

![The WSL Targets view displayed in the Remote Explorer](images/1_79/wsl-tunnel.png)

This coming iteration we will continue to polish the experience and add support for connect to WSL over a tunnel from VS Code desktop.
