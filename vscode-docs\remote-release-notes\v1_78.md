# April 2023 (version 1.78)

## Core

### Hybrid port source

The new `"hybrid"` value for the setting `"remote.autoForwardPortsSource"` discovers ports by watching terminal and debug output, in the same way that the existing `"output"` setting value does. However, process watching is still used so that when a process that listens on a port exits, the port is no longer forwarded. This allows you to narrow the number of ports discovered for forwarding while still getting the additional features of process-based port forwarding.

## Dev Containers (version 0.292.x)

### Experimental lockfile for Features

We have added support for a lockfile for Dev Container Features to:

* Remember the actual version a semver version resolves to, ensuring reproducibility. For example, `ghcr.io/devcontainers/features/github-cli:1` might resolve to `1.0.10` of the feature.
* Improve cache hits when building the container image by using the same sources as with previous builds.
* Detect when the content of a specific version of a feature unexpectedly changes to catch manipulation attempts.

```json
{
  "features": {
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "1.0.10",
      "resolved": "ghcr.io/devcontainers/features/github-cli@sha256:8a1705f62d358565394a868685047e57a8729cd88462a665967bea79a550b7c7",
      "integrity": "sha256:8a1705f62d358565394a868685047e57a8729cd88462a665967bea79a550b7c7"
    }
  }
}
```

We are making the lockfile available to collect feedback. The support is experimental and can change in the future. You can enable it in the user settings:

![Experimental Lockfile User Setting](images/1_78/devcontainers-lockfile.png)

See [Dev Container Feature Lockfile](https://github.com/devcontainers/spec/blob/main/proposals/devcontainer-lockfile.md) for more information.
