---
Order:
TOCTitle: Intro Videos
PageTitle: Visual Studio Code Introductory Videos
MetaDescription: We want to help people get going with VS Code. Watch our new introductory videos to jump start your work with VS Code.
Date: 2016-08-15
ShortDescription: Watch our new intro videos to jump start your work with VS Code.
Author: <PERSON>
---

# Announcing Intro Videos

August 15, 2016 by <PERSON>, [@waderyan_](https://twitter.com/waderyan_)

On the product team, we're constantly trying to make Visual Studio Code better. We spend hours in interviews, usability studies, and interacting with you online.

One theme we hear from users new to VS Code is that they would like more on-boarding material. They want to be able to get up and running with VS Code quickly and be able to take advantage of its powerful features right away.

To help new users, today we are announcing a set of introductory videos. These videos give basic introductions to downloading and installing VS Code and using features like IntelliSense, debugging, version control and more.

## Take a Look

Navigate to [Intro Videos - Overview](/docs/getstarted/introvideos.md) to get started.

Below is the first video covering downloading, installing, and the basics of using VS Code:

<iframe src="https://www.youtube-nocookie.com/embed/LUl_WXt8ohA?rel=0&amp;disablekb=0&amp;modestbranding=1&amp;showinfo=0" frameborder="0" allowfullscreen></iframe>

You can see the video outline [here](/docs/introvideos/basics.md) as well as additional introductory learning resources.

Wade Anderson, VS Code Team Member <br>
[@waderyan_](https://twitter.com/waderyan_)
