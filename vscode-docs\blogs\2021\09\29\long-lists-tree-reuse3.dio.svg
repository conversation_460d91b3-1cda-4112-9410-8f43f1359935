<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="703px" height="891px" viewBox="-0.5 -0.5 703 891" content="&lt;mxfile scale=&quot;1&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;dw6CfWT_FkV9XQvZtEv5&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="189.5" y="589" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 209.5 639)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 639px; margin-left: 161px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="643" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="249.5" y="589" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 269.5 639)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 639px; margin-left: 221px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="270" y="643" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="309.5" y="589" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 329.5 639)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 639px; margin-left: 281px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="330" y="643" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="369.5" y="589" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 389.5 639)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 639px; margin-left: 341px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="643" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="519.5" y="750" width="40" height="90" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 539.5 795)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 795px; margin-left: 496px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair H
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="799" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair H
                </text>
            </switch>
        </g>
        <rect x="579.5" y="760" width="40" height="79" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 599.5 799.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 800px; margin-left: 561px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="803" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="639.5" y="760" width="40" height="79" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 659.5 799.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 800px; margin-left: 621px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="803" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="180.5" y="860" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 875px; margin-left: 196px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="196" y="881" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <path d="M 402 569 L 329.57 569 L 329.57 582.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 329.57 587.88 L 326.07 580.88 L 329.57 582.63 L 333.07 580.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 587 569 L 389.57 569 L 389.57 582.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 389.57 587.88 L 386.07 580.88 L 389.57 582.63 L 393.07 580.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 586 569 L 569.57 569 L 569.52 583.44" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 569.5 588.69 L 566.03 581.68 L 569.52 583.44 L 573.03 581.7 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="309.5" y="529" width="370" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 368px; height: 1px; padding-top: 549px; margin-left: 311px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List E, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="495" y="553" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List E, Height 1
                </text>
            </switch>
        </g>
        <path d="M 604.5 730 L 604.57 709 L 599.57 709 L 599.57 753.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 599.57 758.88 L 596.07 751.88 L 599.57 753.63 L 603.07 751.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 654.5 730 L 654.57 709 L 659.57 709 L 659.57 753.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 659.57 758.88 L 656.07 751.88 L 659.57 753.63 L 663.07 751.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="579.5" y="699" width="100" height="31" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 714px; margin-left: 581px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List G, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="630" y="718" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List G, Height 1
                </text>
            </switch>
        </g>
        <path d="M 214.5 569 L 209.57 569 L 209.57 582.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 209.57 587.88 L 206.07 580.88 L 209.57 582.63 L 213.07 580.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 264.5 569 L 269.57 569 L 269.57 582.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 269.57 587.88 L 266.07 580.88 L 269.57 582.63 L 273.07 580.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="189.5" y="529" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 549px; margin-left: 191px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List D, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="553" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List D, Height 1
                </text>
            </switch>
        </g>
        <path d="M 312 499 L 239.57 499 L 239.57 522.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 239.57 527.88 L 236.07 520.88 L 239.57 522.63 L 243.07 520.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 557 499 L 509.57 499 L 509.57 522.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 509.57 527.88 L 506.07 520.88 L 509.57 522.63 L 513.07 520.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="189.5" y="459" width="490" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 488px; height: 1px; padding-top: 479px; margin-left: 191px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List B, Height 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="435" y="483" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List B, Height 2
                </text>
            </switch>
        </g>
        <rect x="208.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 224px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="224" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="240.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 256px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="256" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="268.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 284px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="284" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="300.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 316px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="316" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="328.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 344px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="344" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="360.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 376px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="376" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="388.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 404px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="404" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="420.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 436px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="436" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="481.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 497px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="497" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="510.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 526px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="526" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="538.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 554px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="554" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="570.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 586px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="586" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="598.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 614px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="614" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="630.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 646px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="646" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="658.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 674px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="674" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="451.5" y="859" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 874px; margin-left: 467px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="467" y="880" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <path d="M 554.5 629 L 570 629 L 570 648.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 570 653.88 L 566.5 646.88 L 570 648.63 L 573.5 646.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="429.5" y="590" width="250" height="39" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 610px; margin-left: 431px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair β, Height 0
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="555" y="613" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair β, Height 0
                </text>
            </switch>
        </g>
        <rect x="460.5" y="750" width="40" height="89" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 480.5 794.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 795px; margin-left: 437px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair δ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="481" y="798" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair δ
                </text>
            </switch>
        </g>
        <path d="M 624.75 685 L 629.57 685 L 629.53 692.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 629.51 697.88 L 626.04 690.86 L 629.53 692.63 L 633.04 690.9 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 515.25 685 L 510.57 685 L 510.53 692.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 510.51 697.88 L 507.04 690.86 L 510.53 692.63 L 514.04 690.9 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="460.5" y="655" width="219" height="30" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 217px; height: 1px; padding-top: 670px; margin-left: 462px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List X, Height 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="570" y="674" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List X, Height 2
                </text>
            </switch>
        </g>
        <path d="M 485.5 730 L 480.57 730 L 480.52 743.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 480.5 748.88 L 477.03 741.87 L 480.52 743.63 L 484.03 741.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 535.5 730 L 539.57 730 L 539.52 743.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 539.5 748.88 L 536.03 741.87 L 539.52 743.63 L 543.03 741.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="460.5" y="699" width="100" height="31" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 714px; margin-left: 462px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List Y, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="511" y="718" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List Y, Height 1
                </text>
            </switch>
        </g>
        <path d="M 142.5 59 L 130 59 L 130 82.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 130 87.88 L 126.5 80.88 L 130 82.63 L 133.5 80.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 387.5 59 L 400 59 L 400 82.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 400 87.88 L 396.5 80.88 L 400 82.63 L 403.5 80.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="19" width="490" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 488px; height: 1px; padding-top: 39px; margin-left: 21px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List α, Height 3
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="43" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List α, Height 3
                </text>
            </switch>
        </g>
        <rect x="20" y="229" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 40 279)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 279px; margin-left: -9px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="283" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="80" y="229" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 100.00000000000023 279)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 279px; margin-left: 51px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="283" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="140" y="229" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 159.99999999999977 279)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 279px; margin-left: 111px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="283" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="200" y="229" width="40" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 219.99999999999977 279)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 279px; margin-left: 171px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="220" y="283" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="350" y="200" width="40" height="100" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 369.9999999999998 250)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 250px; margin-left: 321px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair H
                                <br/>
                                Height 0 ↯
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="370" y="254" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair H...
                </text>
            </switch>
        </g>
        <rect x="410" y="250" width="40" height="79" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 429.9999999999998 289.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 290px; margin-left: 391px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="293" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="470" y="250" width="40" height="79" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 489.9999999999998 289.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 290px; margin-left: 451px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="293" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair
                </text>
            </switch>
        </g>
        <rect x="11" y="350" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 365px; margin-left: 26px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="26" y="371" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <path d="M 165 199 L 160 199 L 160 222.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 160 227.88 L 156.5 220.88 L 160 222.63 L 163.5 220.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 215 199 L 220 199 L 220 222.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 220 227.88 L 216.5 220.88 L 220 222.63 L 223.5 220.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="140" y="159" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 179px; margin-left: 141px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List E, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="183" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List E, Height 1
                </text>
            </switch>
        </g>
        <path d="M 435 230 L 435 199 L 430 199 L 430 243.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 430 248.88 L 426.5 241.88 L 430 243.63 L 433.5 241.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 485 230 L 485 199 L 490 199 L 490 243.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 490 248.88 L 486.5 241.88 L 490 243.63 L 493.5 241.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="410" y="199" width="100" height="31" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 215px; margin-left: 411px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List G, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="460" y="218" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List G, Height 1
                </text>
            </switch>
        </g>
        <path d="M 45 199 L 40 199 L 40 222.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 40 227.88 L 36.5 220.88 L 40 222.63 L 43.5 220.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 95 199 L 100 199 L 100 222.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 100 227.88 L 96.5 220.88 L 100 222.63 L 103.5 220.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="159" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 179px; margin-left: 21px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List D, Height 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="183" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List D, Height 1
                </text>
            </switch>
        </g>
        <path d="M 75 129 L 70 129 L 70 152.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 70 157.88 L 66.5 150.88 L 70 152.63 L 73.5 150.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 185 129 L 190 129 L 190 152.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 190 157.88 L 186.5 150.88 L 190 152.63 L 193.5 150.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="89" width="220" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 109px; margin-left: 21px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List B, Height 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="113" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List B, Height 2
                </text>
            </switch>
        </g>
        <rect x="39" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 54px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="54" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="71" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 86px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="86" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="99" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 114px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="114" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="131" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 146px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="146" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="159" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 174px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="174" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="191" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 206px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="206" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="219" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 234px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="234" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="251" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 266px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="266" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="312" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 327px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="327" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="341" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 356px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="356" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="369" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 384px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="384" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="401" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 416px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="416" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="429" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 444px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="444" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="461" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 476px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="476" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="489" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 504px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="504" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="282" y="349" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 364px; margin-left: 297px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 21px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="297" y="370" fill="#A52318" font-family="Courier New" font-size="21px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <path d="M 385 129 L 400.57 129 L 400.51 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 400.5 158.88 L 397.02 151.87 L 400.51 153.63 L 404.02 151.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="260" y="89" width="250" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 109px; margin-left: 261px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair β, Height 0 ↯
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="385" y="113" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair β, Height 0 ↯
                </text>
            </switch>
        </g>
        <rect x="291" y="200" width="40" height="100" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)rotate(-90 311 250)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 250px; margin-left: 262px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Bracket Pair δ
                                <br/>
                                Height 0 ↯
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="311" y="254" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bracket Pair δ...
                </text>
            </switch>
        </g>
        <path d="M 345.75 180 L 311 180 L 311 193.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 311 198.88 L 307.5 191.88 L 311 193.63 L 314.5 191.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 400.5 180 L 370 180 L 370 193.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 370 198.88 L 366.5 191.88 L 370 193.63 L 373.5 191.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 455.25 180 L 460 180 L 460 192.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 460 197.88 L 456.5 190.88 L 460 192.63 L 463.5 190.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="291" y="160" width="219" height="20" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 217px; height: 1px; padding-top: 170px; margin-left: 292px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                List γ, Height 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="401" y="174" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    List γ, Height 2
                </text>
            </switch>
        </g>
        <path d="M 55 425 Q 55 454 98.63 454" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 103.88 454 L 96.88 457.5 L 98.63 454 L 96.88 450.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="74" y="425" width="70" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 435px; margin-left: 109px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Balancing
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="109" y="439" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Balancing
                </text>
            </switch>
        </g>
        <path d="M 0 60 L 0 10 Q 0 0 10 0 L 520 0 Q 530 0 530 10 L 530 130 Q 530 140 520 140 L 270 140 Q 260 140 260 150 L 260 330 Q 260 340 250 340 L 10 340 Q 0 340 0 330 L 0 60" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 270 200 L 270 330 Q 270 340 280 340 L 520 340 Q 530 340 530 330 L 530 160 Q 530 150 520 150 L 280 150 Q 270 150 270 160 L 270 200" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 440 699 L 440.47 840 Q 440.5 850 450.5 850 L 690.5 850 Q 700.5 850 700.48 840 L 700.02 659 Q 700 649 690 649 L 450 649 Q 440 649 440 659 L 440 699" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 169.5 509 L 169.5 700 Q 169.5 710 179.5 710 L 409.5 710 Q 419.5 710 419.5 700 L 419.5 650 Q 419.5 640 429.5 640 L 689.5 640 Q 699.5 640 699.5 630 L 699.5 450 Q 699.5 440 689.5 440 L 179.5 440 Q 169.5 440 169.5 450 L 169.5 509" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>