# July 2020 (version 1.48)

## SSH

### Notable Fixes

* [1760](https://github.com/microsoft/vscode-remote-release/issues/1760): Panel "check remote" keeps popping out and interrupting my work
* [2518](https://github.com/microsoft/vscode-remote-release/issues/2518): Remote SSH password keeps asking password (Installation already in progress)

## Containers (version 0.134.0)

### Environment variables probe

The `devcontainer.json` file has a new property, `userEnvProbe`, to specify if environment variables should be extracted from the container user's startup scripts.

Supported values are:

* `none` - No environment variables are pulled in. (default)
* `loginInteractiveShell` - Use the login script. For example, sourcing `.bash_profile` for Bash.
* `interactiveShell` - Use the shell script. For example, sourcing `.bashrc` for Bash.

The scripts that run depend on the container user's default shell.

### Notable Fixes

- [2925](https://github.com/microsoft/vscode-remote-release/issues/2925): SSH und GPG agent forwarding for WSL2
