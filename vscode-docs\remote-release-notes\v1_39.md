# September 2019 (version 1.39)

## VS Code Remote Core

### Opening a local file

The default behavior when opening a local file while in a remote window is now to open that file in the same window (instance). To return to the previous behavior, where opening a local file would open a new window, you can set `"window.openFilesInNewWindow": "on"`.

### Auto expand/collapse Remote Explorer sections

If you have all of the VS Code Remote extensions installed, the Remote Explorer can be very busy. Now, when you open the Remote Explorer in a remote window, only the sections that are related to the current remote session will be expanded, and other sections will be collapsed by default.

### Remote UI extensions

It is now possible to run [UI extensions](https://code.visualstudio.com/api/advanced-topics/remote-extensions#architecture-and-extension-kinds) remotely. When a UI extension is installed on both the local and on the remote machine, the local extension will execute.

### Automatic tunneling of localhost links in remote Integrated Terminals

When connected to [remote workspaces](https://code.visualstudio.com/docs/remote), clicking `http` or `https` localhost links in the Integrated Terminal will now transparently open a tunnel so that your local machine can view resources from the remote machine. The video below shows this behavior running the `http-server` utility in a remote SSH workspace.

![Tunneling when clicking on a localhost link in a remote terminal](images/1_39/terminal-tunnel.gif)

Notice how when the link to `http://127.0.0.1:8080` is clicked, we open the local browser to `http://127.0.0.1:53507`. Behind the scenes, VS Code has automatically established a secure tunnel that connects port 53507 on the local machine to port 8080 on the remote.

## WSL

### Remote Explorer

The WSL extension has joined Containers and SSH in the Remote Explorer. You can see all your installed Linux distributions, connect to a specific distro, set your default distro, and see the folders you've opened on a Linux distro in the Remote Explorer.

![WSL Remote Explorer](images/1_39/wsl-remote-explorer.png)

### Environment setup script

You can now make changes to the environment where the VS Code Remote is started and run additional services. To do so, create a script `~/.vscode-server/server-env-setup` (Insiders: `~/.vscode-server-insiders/server-env-setup`). If present, the script is processed before the server is started.

The script needs to be a valid Bourne shell (sh) script. Be aware that an invalid script will prevent the server from starting up.

Check the WSL log (**WSL: Show Log**) for output and errors.

## Containers

### Save configuration for images when attaching to a container

When attaching to a container, we now save the last workspace folder, the installed extensions, and the dynamically forwarded ports. The next time you attach to the same container or a container of the same image (and tag), this configuration will be reapplied.

The **Dev Containers: Open Container Configuration File** command will open the current configuration while being attached to a container and the **Dev Containers: Open Attached Container Configuration File...** command will show a list of all configurations.

```json
{
    "workspaceFolder": "/scripts",
    "extensions": [
        "ms-vscode.azurecli"
    ],
    "forwardPorts": [
        3000
    ]
}
```

### Clone repository in container volume

There is a new command **Dev Containers: Clone Repository in Container Volume...** to allow you to work on a repository in a container without locally cloning it first. This lets you work with an isolated copy of a repository, useful for a PR review or to investigate another branch, without impacting what you have currently checked out. The command requires that the repository has a `devcontainer.json` file and, in the current version, the repository must be be public.

Repository Containers use isolated, local Docker volumes instead binding to the local filesystem. In addition to not polluting your file tree, local volumes have the added benefit of improved performance on Windows and macOS.

To try out the feature, you can run **Dev Containers: Clone Repository in Container Volume...** and enter `microsoft/vscode-remote-try-node`, for example.

The **Container** section of the Remote Explorer shows the details of a Repository Container, such as the source repository and the local Docker volume. Removing a Repository Container from the explorer will prompt you whether you want to delete the volume.

### Prompt to rebuild container

You now will be asked if you want to rebuild a container when the `devcontainer.json`, `Dockerfile`, or `docker-compose.yml` files have changed.

![Prompt rebuild container](images/1_39/prompt-rebuild-container.png)

### UI extensions in 'devcontainer.json'

With the new support to run UI extensions remotely, you can now include UI extensions in the `extensions` section of the `devcontainer.json` file. This enables you to define a development container to run UI extensions in isolation.

### Remote Explorer

The Remote Explorer now shows the repository containers (created by [Opening a repository in a container](#open-repository-in-a-container)) and has improved icons. You can also see and inspect your mount points.

![Remote Explorer repository containers](images/1_39/repository-containers-explorer.png)

Other improvements include a **Rebuild Container** command in the context menu of your currently connected container, **Inspect** in the context menu for containers, images, and volumes, and a better title that reflects the name of the container for the details view.

### Additional Docker extension settings

The [Docker extension](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker) comes with three settings for configuring the connection to a remote Docker daemon and all three are supported now by the Dev Containers extension:

- Docker hostname (same as DOCKER_HOST environment variable).
- Docker certificate path (same as DOCKER_CERT_PATH environment variable).
- Docker TLS verification (same as DOCKER_TLS_VERIFY environment variable).

## SSH

### SSH connection sharing

Currently, every window attached to a remote machine via SSH uses its own SSH connection. This adds a little extra time to load the window, and if you are using password authentication, you will have to enter your password again every time you open a folder or reload the window. We are developing a new mode that creates one SSH connection per connected remote, managed by a local server, and lets multiple windows share that connection. This makes opening windows faster and reduces the number of times that you will have to type your password. If you want to try this experimental mode, you can set `"remote.SSH.useLocalServer": true`.

### Remote - SSH for Windows remotes

We now have experimental support for connecting to Windows remote machines. The remote must be Windows 10, running an OpenSSH server, and you must use [VS Code Insiders](https://code.visualstudio.com/insiders/). You can try it out with the setting `"remote.SSH.windowsRemotes": ["my-windows-host-name"]`.

### Friendlier error message for remotes that don't meet Linux prerequisites

If you connect to a remote machine that doesn't have the minimum required versions of `glibc` or `libstdc++`, VS Code used to fail to connect while displaying a confusing error message. We have added a new warning popup that describes the missing requirement.

### Eliminating the Remote - SSH: Explorer extension

There had been three separate Remote - SSH extensions. One extension provides language support for SSH configuration files, one powers the SSH Explorer, and another handles connecting to SSH remotes. Now that the Remote Explorer is built into VS Code, we have merged the second two extensions and you will be prompted to uninstall the earlier Explorer extension.
