---
Order: 
TOCTitle: March 2023
PageTitle: Visual Studio Code March 2023
MetaDescription: Learn what is new in the Visual Studio Code March 2023 Release (1.77)
MetaSocialImage: 1_77/release-highlights.png
Date: 2023-3-30
DownloadVersion: 1.77.3
---
# March 2023 (version 1.77)

**Update 1.77.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22March+2023+Recovery+1%22+is%3Aclosed).

**Update 1.77.2**: The update addresses this security [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22March+2023+Recovery+2%22+is%3Aclosed).

**Update 1.77.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22March+2023+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the March 2023 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[Accessibility improvements](#accessibility)** - New keyboard shortcuts for hovers, notifications, and Sticky Scroll.
* **[Copy GitHub deep links](#copy-github-deep-links-from-editor-gutter)** - Create permalinks and HEAD links from within the editor.
* **[Notebook Format on Save](#format-on-save)** - Automatically format notebooks cells on save.
* **[TS/JS switch case completions](#switch-case-completions-for-javascript-and-typescript)** - Quickly fill in TypeScript/JavaScript switch statements.
* **[Python move symbol refactoring](#move-symbol-refactoring)** - Move Python symbols to an existing or new file.
* **[Remote Tunnels update](#remote-development)** - Reuse existing tunnel and quickly transition from remote to desktop.
* **[Ruby documentation](#new-programming-language-topics)** - Learn about Ruby language support for VS Code.
* **[Preview: expanded GitHub Copilot integration](#github-copilot)** - New inline chat and full AI chat view.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Terminal accessible buffer improvements

The terminal accessible buffer, which provides screen reader users access to the terminal contents via **Terminal: Focus Accessible Buffer** (`kb(workbench.action.terminal.focusAccessibleBuffer)`), now dynamically updates and remains active until `kbstyle(Escape)` or `kbstyle(Tab)` are used to end the session.

When the accessible buffer is focused in a terminal with shell integration, **Go to Symbol in Accessible View** (`kb(editor.action.accessibleViewGoToSymbol)`) enables navigation between terminal commands similar to how editors can be navigated with **Go to Symbol in Editor...**.

### Hover control navigation

It is now possible to focus on the hover control and scroll horizontally and vertically with the `up`, `down`, `home`, `end`, `page up` and `page down` keys. The keyboard shortcut to focus the hover control (`kb(editor.action.showHover)`) is the same as used to show the hover at the primary cursor position.

### Accept a notification's primary action

To accept a notification's primary action, users have historically needed to navigate to the Notification Center. **Notifications: Accept Notification Primary Action** (`kb(notification.acceptPrimaryAction)`) runs the primary action without leaving the current context.

### Sticky Scroll navigation

It is now possible to focus on the Sticky Scroll lines and navigate with the `up` and `down` keys. You can use the `Enter` key to go to a selected line. To focus on the Sticky Scroll, you can run **Focus Sticky Scroll** from the Command Palette. Enable the Sticky Scroll UI via the **View: Toggle Stick Scroll** command.

## Workbench

### Copy GitHub deep links from editor gutter

You can now copy deep links for an editor line or range from the editor gutter when working in a GitHub repository.

In VS Code Desktop, to be able to create GitHub permalinks and HEAD links from the editor gutter, you need to install the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension. In [vscode.dev](https://vscode.dev), deep links for GitHub repositories are available out of the box.

<video src="images/1_77/github-permalink.mp4" placeholder="images/1_77/github-permalink.mp4" autoplay loop controls muted title="Copy GitHub permalinks">
    Sorry, your browser doesn't support HTML 5 video.
</video>

These actions have also been added to the **Share** submenu in the editor tab context menu and Explorer context menu.

### Recommend extensions by file content

VS Code can now recommend extensions based on the file content. For example, if you open a Python file that has notebook cells syntax, VS Code recommends the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension to render and run these cells appropriately.

### Select default provider for Sticky Scroll

Sticky Scroll (**View: Toggle Sticky Scroll**) uses several different content models to create its headings. It is now possible to choose between the outline provider model, the folding provider model, and the indentation model to determine which lines to display in the Sticky Scroll UI. If a model is not available for the current language, VS Code falls back to the next model in the aforementioned order. The default model initially used can be changed with **Editor > Sticky Scroll: Default Model** (`editor.stickyScroll.defaultModel`).

## Terminal

### Improved tab hover

The terminal tab hover now shows the shell's process ID, the full command line, and an improved view of extension environment variable contributions.

![Hovering the terminal tab will reveal details about the terminal, this can be triggered with the keyboard via Ctrl/Cmd+K, Ctrl/Cmd+I](images/1_77/terminal-tab-hover.png)

## Source Control

### Git LFS commit support in Remote Repositories

You can now commit LFS-tracked files without installing [Git LFS](https://git-lfs.github.com) when editing GitHub and Azure Repos repositories. To get started, install the [GitHub Repositories](https://marketplace.visualstudio.com/items?itemName=GitHub.remotehub) or [Azure Repos](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-repos) extensions on VS Code Desktop, run the **Open Remote Repository** command to open your repository, and commit your changes using the source control view. The extensions parse your `.gitattributes` configuration to determine whether your changed files should be committed to the repository provider's Git LFS storage.

To disable this behavior, you can set `"githubRepositories.experimental.lfs.write.enabled": false` and `"azureRepos.experimental.lfs.write.enabled": false`.

> **Note**: Support for VS Code for the Web ([vscode.dev](https://vscode.dev)) is not yet available.

### 3-way merge editor documentation

If you haven't already tried using the 3-way merge editor to help you resolve merge conflicts, check out the [3-way merge editor](https://code.visualstudio.com/docs/sourcecontrol/overview#_3way-merge-editor) section in the Source Control documentation. The 3-way merge editor lets you simultaneously view and select incoming and current changes, preview the results, and even review the base version of the file before any changes.

![3-way merge editor](images/1_77/merge-editor-overview.png)

And don't miss [The EXTREMELY Helpful Guide to Merge Conflicts](https://youtu.be/HosPml1qkrg) YouTube video, which stars the 3-way merge editor.

## Notebooks

### Format on Save

You can now enable format on save for notebooks. This formats the entire notebook when you save it. You can enable this by setting `notebook.formatOnSave.enabled` to `true`.

<video src="images/1_77/notebook-format-on-save.mp4" placeholder="images/1_77/notebook-format-on-save.mp4" autoplay loop controls muted title="Notebook format on save demo">
    Sorry, your browser doesn't support HTML 5 video.
</video>

### Notebook Find in output enabled by default

When you open Find control in a notebook, it will now search for text in cell inputs and outputs by default. You can turn this off by changing the options in the filter dropdown.

<video src="images/1_77/notebook-find-in-output.mp4" placeholder="images/1_77/notebook-find-in-output.mp4" autoplay loop controls muted title="Find in output by default demo">
    Sorry, your browser doesn't support HTML 5 video.
</video>

### Scrollable output regions

With `notebook.output.scrolling` set to true, outputs and errors that exceed the `notebook.output.textLineLimit` will render within a scrollable region.

<video src="images/1_77/notebook-scrollable-output.mp4" placeholder="images/1_77/notebook-scrollable-output.mp4" autoplay loop controls muted title="Notebook scrollable output">
    Sorry, your browser doesn't support HTML 5 video.
</video>

## Languages

### TypeScript 5.0

VS Code now ships with TypeScript 5.0.2. This major update brings new TypeScript language features, improved performance, and many important improvements and bug fixes. You can read about TypeScript 5.0 on the [TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-5-0).

### Switch case completions for JavaScript and TypeScript

VS Code can now help scaffold out switch statements over literal types in JavaScript or TypeScript:

<video src="images/1_77/ts-case-completions.mp4" placeholder="images/1_77/ts-case-completions.mp4" autoplay loop controls muted title="Completing the cases of a switch statement">
    Sorry, your browser doesn't support HTML 5 video.
</video>

Type `case` inside of the switch statement and accept the suggestion for `case ...`. This will automatically insert cases for all values of this type. Note that this only works when the value being switched over is a union or literal type.

## VS Code for the Web

### .gitignore support

This milestone we've added support for `.gitignore` files in GitHub and Azure Repos repositories on VS Code for the Web. In the video below, files that are untracked via `.gitignore` are greyed out in the Explorer view and do not appear in the Source Control view. This also works when using the [GitHub Repositories](https://marketplace.visualstudio.com/items?itemName=GitHub.remotehub) or [Azure Repos](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-repos) extensions to remotely browse repositories on VS Code Desktop.

The short video below adds the `.pxt` folder to `.gitignore` to hide the contained files from the Source Control view.

<video src="images/1_77/gitignore.mp4" placeholder="images/1_77/gitignore.mp4" autoplay loop controls muted title="Gitignore in VS Code for the Web">
    Sorry, your browser doesn't support HTML 5 video.
</video>

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

A Dev Container lets you use a container as a full-featured development environment. The [Dev Container Specification](https://containers.dev/) seeks ways to enrich existing formats with common development settings, tools, and configurations while still providing a simplified, unorchestrated single container option. You can learn more about Dev Containers and the specification in [episode #529 of the Changelog podcast](https://changelog.com/podcast/529).

Other Remote Development highlights include:

* Remote Tunnels - Better reuse of existing tunnels.
* Remote Tunnels - **Continue Working in VS Code Desktop** command to quickly transition to local development.
* Dev Containers - Easier clean up of unused Dev Containers and volumes.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_77.md).

## Extensions

### Extension installation not blocked by signature verification failures

Extension signature verification is incorrectly reporting errors for a small number of valid extensions and preventing them from being installed. These failures are caused by bugs in the VS Marketplace and VS Code and we are actively working on fixes. In order to unblock extension use, VS Code will install extensions even if signing verification fails. We will re-enable signature verification checking once [VS Marketplace issue #619](https://github.com/microsoft/vsmarketplace/issues/619) is fixed, most likely in our next Stable release.

## Contributions to extensions

### Python

#### Move symbol refactoring

You can now more conveniently refactor your Python code with [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) thanks to the new **Move symbol to** Code Actions!

To try it out, select a symbol on a Python file and click on the light bulb that is presented (`kb(editor.action.quickFix)`). You can either move the symbol to an existing file or to a new file. If the location is a new file, a Python file is created with the same name as your symbol. All the applicable import references are automatically updated with the symbol move.

<video src="images/1_77/pylance-move-symbol.mp4" placeholder="images/1_77/pylance-move-symbol.mp4" autoplay loop controls muted title="Move symbol refactoring with Pylance">
    Sorry, your browser doesn't support HTML 5 video.
</video>

#### Create environment from dependency files

When you open a `requirements.txt` or a `pyproject.toml` file, there's a new **Create Environment...** button in the editor that runs the **Python: Create Environment** command, allowing you to create a new virtual environment and install the listed dependencies.

![A pyproject.toml file open with dependencies listed, and a Create Environment button displayed on the bottom right corner for the editor](images/1_77/create-env-python-dependencies-files.png)

#### Finalized environments API for extension authors

The Python extension's API for working with available Python environments on the user's machine is finalized. Extensions can also use the API to access the selected environment path used by the Python extension to run scripts, or update the path to their preferred one.

Examples of API usage are in the [Python Environment APIs](https://github.com/microsoft/vscode-python/wiki/Python-Environment-APIs#extension-api-usage) wiki page. We are also planning to release an npm types package in the future so it's easier to keep track of any changes in the Python extension API.

### Jupyter

#### Kernel picker improvements for Python environments

The kernel picker now lists **conda environments** even if the Python runtime is not installed in them.

For example, if a new conda environment is created using a CLI such as `conda create -n envML`, this new environment is displayed in the list of [Python Environments](https://code.visualstudio.com/docs/datascience/jupyter-kernel-management#_python-environments) under a section **Conda Env Without Python**.

When you select such an environment, the Python runtime and necessary dependencies are automatically installed into the environment.

![Notebook kernel picker with ability to select empty conda environments](images/1_77/notebook-kernel-empty-conda-env.png)

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. Highlights include:

* Pull requests can be opened on vscode.dev from the **Pull Requests** view.
* There's a new setting to check the **Auto-merge** option in the **Create Pull Request** view: `githubPullRequests.setAutoMerge`.

Review the [changelog for the 0.62.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0620) release of the extension to see the other highlights.

## GitHub Copilot

[GitHub Copilot](https://copilot.github.com) is an AI pair programmer tool that helps you write code faster and smarter. You can use the [Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot) extension in VS Code to generate code, learn from the code it generates, and even configure your editor.

![GitHub Copilot extension](images/1_77/copilot-extension.png)

We are excited to announce the preview of deeper Copilot integration into VS Code. By using the [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension, you'll be able to try out new features such as:

* **Inline suggestions:** Copilot suggestions appear inline as you work in your code.
* **Chat view:** Ask Copilot for help with any task or question in the GitHub Copilot Chat view.
* **Inline chat:** Talk with Copilot while writing code, inline in your files.

You can ask Copilot to look for bugs, explain tricky code, create tests, and even ask questions about VS Code.

![Copilot chat example asking how to change VS Code colors](images/1_77/slash-commands-example.png)

You can learn more about the VS Code team's experience and future with Copilot in the [VS Code and GitHub Copilot](https://code.visualstudio.com/blogs/2023/03/30/vscode-copilot) blog post. You can also read the [GitHub Copilot in VS Code](https://code.visualstudio.com/docs/editor/github-copilot) article for more details about using Copilot in VS Code.

## Preview features

### Notebook search support for outputs

Last iteration, we introduced global search for formatted notebook content in open notebooks, but only for cell and Markdown inputs. This iteration, we have added support for searching notebook outputs. When you have a notebook open, there is a toggle that allows you to customize where you would like to search in a notebook. Enable `search.experimental.notebookSearch` to try this out!

<video src="images/1_77/notebook-search.mp4" placeholder="images/1_77/notebook-search.mp4" autoplay loop controls muted title="Notebook rich content search demo">
    Sorry, your browser doesn't support HTML 5 video.
</video>

### Remote connection picker on the Welcome page

The Welcome page now supports an experimental remote connection picker **Connect to..** in the start list to help you get started with remote connections (SSH, Remote Tunnels, GitHub Codespaces, etc.). This feature is disabled by default and can be enabled by setting `workbench.remote.experimental.showStartListEntry` to `true`.

## Extension authoring

### Upcoming Electron 22 update may require changes to native modules

We plan to update to [Electron 22](https://www.electronjs.org/blog/electron-22-0) in our next Stable release. This comes with implications for extensions that use native modules due to enabling the [V8 memory cage](https://www.electronjs.org/blog/v8-memory-cage): `ArrayBuffers` that point to external ("off-heap") memory are no longer allowed.

We plan to provide a custom memory allocator so that native modules that are not updated will still continue to work, but nevertheless, it is recommended to review your usages of allocated external memory. Please follow the advice in the [Electron blog post](https://www.electronjs.org/blog/v8-memory-cage#i-want-to-refactor-a-node-native-module-to-support-electron-21-how-do-i-do-that) for how to adopt this change.

### Finalized support for continuous test runs

Support for [continuous test runs](https://github.com/microsoft/vscode/issues/134941) has been finalized. This API allows users to indicate to test extensions that they want to "watch" tests and continue to run them as changes are made.

```diff
const profile = ctrl.createRunProfile(/* ... */);
+profile.supportsContinuousRun = true;

function runHandler(request: vscode.TestRunRequest, cancellation: vscode.CancellationToken) {
+  if (request.continuous) {
+    startWatchingAndRunningTests(request);
+  }

  // ...
}
```

### New when clause parser

This VS Code release includes a new parser for [when clauses](https://code.visualstudio.com/api/references/when-clause-contexts) offering new features (for example, support for parentheses) and better correctness but also stricter rules. The release also includes a linter for when clauses in extension manifest files (`package.json`) to make sure they are syntactically correct. We encourage you to check your when clauses for possible breakage with the new parser. Also, if you use parentheses in a when clause, your extension is compatible with VS Code version 1.77 and later. There is more information about the changes in [issue #175540](https://github.com/microsoft/vscode/issues/175540).

### Inline completions in Source Control input

The Source Control input box now supports [inline completions](https://github.com/microsoft/vscode-extension-samples/tree/main/inline-completions):

![The inline suggestions extension sample running in the Source Control input](images/1_77/scm-inline.png)

The Source Control input box's document [language identifier](https://code.visualstudio.com/docs/languages/identifiers) is `scminput`.

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [vscode-dts](https://www.npmjs.com/package/vscode-dts) and run `vscode-dts dev`. The command downloads the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Editor gutter proposed context menu

The `editor/lineNumber/context` proposed menu is anchored to the editor gutter and editor line numbers and provides a way to display extension actions that are contextual to a particular line.

To try this out in development, enable the `contribEditorLineNumberMenu` API proposal. Actions contributed to this menu receive the line number in command arguments and can reference the `editorLineNumber` context key in their when clauses. You can leave feedback in the [API proposal issue #175945](https://github.com/microsoft/vscode/issues/175945).

### Notebook lifecycle event: onWillSaveNotebookDocument

The `onWillSaveNotebookDocument` event is fired before a notebook document is saved. This event is useful for extensions that want to perform some action before a notebook document is saved. For example, an extension that wants to clean up notebook cell outputs before saving it can register a listener for this event and return the cell output edits to be applied.

Here is how an extension can register a listener for this event:

```ts
vscode.workspace.onWillSaveNotebookDocument(e => {
    if (event.reason == vscode.NotebookDocumentSaveReason.Manual) {
      event.waitUntil(new Promise((resolve) => {
        const notebookEdit = new vscode.NotebookEdit(...);
        const edit = new vscode.WorkspaceEdit();
        edit.set(event.document.uri, [notebookEdit]);
        resolve([edit]);
      }));
    }
});
```

## Engineering

### EOL warning for windows 8 and 8.1

VS Code Desktop will be updating to Electron >=23 in the next couple of milestones. With the Electron 23 update, VS Code desktop will no longer run on Windows 8 / Windows Server 2012 and Windows 8.1 / Windows Server 2012 R2. In this milestone, we have added deprecation notices for users on these affected platforms to prepare them for migration. If you are running one of these Windows versions, take a look at our [FAQ](https://aka.ms/vscode-faq-old-windows) for additional information.

### Base image updated for Snap package

In this iteration, we have updated our Snap package to use Core20 (built from Ubuntu 20.04) as the base image from our previous Core (built from Ubuntu 16.04). This change addresses missing GLIBC symbols for our CLI and fixes wayland support among other issues. You can find more context in [pull request #127320](https://github.com/microsoft/vscode/pull/127320).

### Exploring custom memory allocator for the extension host

To adopt Electron 22, which comes with V8 sandbox enabled, and minimize the impact for extensions that rely on native modules that specifically use external array buffers, we have customized the existing memory allocator in the extension host. Specifically, the extension host now accommodates allocations from these native modules to be inside the V8 sandbox as a workaround to avoid fatal crashes. If you are interested in how/why we ended up with this solution, you can review [issue #177338](https://github.com/microsoft/vscode/issues/177338) for details.

## Documentation

### New programming language topics

* [Ruby in VS Code](https://code.visualstudio.com/docs/languages/ruby) - Learn about Ruby language support (snippets, linting, debugging) using the [Ruby LSP](https://marketplace.visualstudio.com/items?itemName=Shopify.ruby-lsp) extension.
* [Polyglot Notebooks](https://code.visualstudio.com/docs/languages/polyglot) - The [Polyglot Notebooks](https://marketplace.visualstudio.com/items?itemName=ms-dotnettools.dotnet-interactive-vscode) extension enables polyglot programming for C#, PowerShell, JavaScript, and more.

## Notable fixes

* [174690](https://github.com/microsoft/vscode/issues/174690) Comments API - How can author.iconPath reference a file bundled with the extension?
* [175805](https://github.com/microsoft/vscode/issues/175805) VS Code does not auto-detect IPv6 Processes for port forwarding

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@starball5 (starball)](https://github.com/starball5)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@andschwa (Andy Jordan)](https://github.com/andschwa): Handle `ParameterBindingException` for PowerShell 5.1 [PR #176004](https://github.com/microsoft/vscode/pull/176004)
* [@azdavis (Ariel Davis)](https://github.com/azdavis): Put marker hovers on top [PR #166560](https://github.com/microsoft/vscode/pull/166560)
* [@babakks (Babak K. Shandiz)](https://github.com/babakks): 💬 Add PID and executable/arguments to terminal tab hover [PR #171258](https://github.com/microsoft/vscode/pull/171258)
* [@billti (Bill Ticehurst)](https://github.com/billti): Fixes failure to load first installed web extension on desktop [PR #174175](https://github.com/microsoft/vscode/pull/174175)
* [@brice-gros (Brice Gros)](https://github.com/brice-gros): support Git for Windows SDK [PR #177443](https://github.com/microsoft/vscode/pull/177443)
* [@dkniffin (Derek Kniffin)](https://github.com/dkniffin): Add vscode.getEditorLayout command [PR #171224](https://github.com/microsoft/vscode/pull/171224)
* [@dyedgreen (Tilman Roeder)](https://github.com/dyedgreen): Add support for three or more chord keyboard shortcuts [PR #175253](https://github.com/microsoft/vscode/pull/175253)
* [@EliiseS (Eliise)](https://github.com/EliiseS): Fix issues with inconsistent line wrap application [PR #174688](https://github.com/microsoft/vscode/pull/174688)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Handle file uri in CommentAuthorInformation.iconPath (fix #174690) [PR #175361](https://github.com/microsoft/vscode/pull/175361)
  * Allow `--vscode-XXX` color variables in `<span>` style in Markdown (#176064) [PR #176312](https://github.com/microsoft/vscode/pull/176312)
  * Add color to icons in tabular view of Problems (fix #176621) [PR #176625](https://github.com/microsoft/vscode/pull/176625)
* [@harbin1053020115 (ermin.zem)](https://github.com/harbin1053020115): chore: update IPCClient class comment [PR #175198](https://github.com/microsoft/vscode/pull/175198)
* [@hermannloose (Hermann Loose)](https://github.com/hermannloose)
  * Add themeable color for comment glyphs on lines with unresolved threads [PR #174915](https://github.com/microsoft/vscode/pull/174915)
  * Consistently expand or collapse all comments on a line [PR #176967](https://github.com/microsoft/vscode/pull/176967)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fixes #174838 [PR #175646](https://github.com/microsoft/vscode/pull/175646)
* [@jessebluemr (Marko Reiprecht)](https://github.com/jessebluemr): monaco-editor-3626: Ensure brackets are highlighted in languages like 'json' [PR #176084](https://github.com/microsoft/vscode/pull/176084)
* [@lawvs (Whitewater)](https://github.com/lawvs): composition enter should not accept rename [PR #174829](https://github.com/microsoft/vscode/pull/174829)
* [@mickaelistria (Mickael Istria)](https://github.com/mickaelistria): onEnterRules add prefix on newline inside Javadoc [PR #176800](https://github.com/microsoft/vscode/pull/176800)
* [@nikdmello (Nikhil D'Mello)](https://github.com/nikdmello): fix screencast mode: only keyboard shortcuts no longer show command names [PR #176149](https://github.com/microsoft/vscode/pull/176149)
* [@NWilson (Nicholas Wilson)](https://github.com/NWilson): Move DOM manipulation in RenameInputField to render-only [PR #174684](https://github.com/microsoft/vscode/pull/174684)
* [@ohah (ohah)](https://github.com/ohah): Screencast ime bug fix(#176331) [PR #176150](https://github.com/microsoft/vscode/pull/176150)
* [@orgads (Orgad Shaneh)](https://github.com/orgads): LinkParsing: Support "foo line 123" [PR #175906](https://github.com/microsoft/vscode/pull/175906)
* [@poeck (Paul Köck)](https://github.com/poeck): Oh, it's already 2023 [PR #172649](https://github.com/microsoft/vscode/pull/172649)
* [@pouyakary (Pouya Kary ✨)](https://github.com/pouyakary): Fixes #177580 [PR #177581](https://github.com/microsoft/vscode/pull/177581)
* [@spahnke (Sebastian Pahnke)](https://github.com/spahnke): \[Monaco\] Add `monaco.editor.registerLinkOpener` method to be able to intercept opening links from the editor [PR #177055](https://github.com/microsoft/vscode/pull/177055)
* [@SvetozarMateev (Svetozar Mateev)](https://github.com/SvetozarMateev): Add accelerator to process explorer's kill process action [PR #172755](https://github.com/microsoft/vscode/pull/172755)
* [@tisilent (xie jialong 努力鸭)](https://github.com/tisilent): Add z-index for .integrated-terminal [PR #175997](https://github.com/microsoft/vscode/pull/175997)
* [@webarthur (Arthur Ronconi)](https://github.com/webarthur): Added features and fixes to screencast mode [PR #171845](https://github.com/microsoft/vscode/pull/171845)
* [@yiliang114 (易良)](https://github.com/yiliang114)
  * fix typos [PR #176764](https://github.com/microsoft/vscode/pull/176764)
  * fix: close #176789 supplement find widget border-bottom-radius [PR #176791](https://github.com/microsoft/vscode/pull/176791)
* [@zardoy (Vitaly)](https://github.com/zardoy): Explorer: auto create folder when file path ends with / [PR #173901](https://github.com/microsoft/vscode/pull/173901)

Contributions to `vscode-languageserver-node`:

* [@EhabY (Ehab Younes)](https://github.com/EhabY): Added registerCapability and unregisterCapability to the client middleware [PR #1179](https://github.com/microsoft/vscode-languageserver-node/pull/1179)

Contributions to `vscode-pull-request-github`:

* [@Balastrong (Leonardo Montini)](https://github.com/Balastrong)
  * Allow empty labels array to be pushed to set-labels to remove all of them [PR #4637](https://github.com/microsoft/vscode-pull-request-github/pull/4637)
  * Allow empty array to be pushed to remove the last label [PR #4648](https://github.com/microsoft/vscode-pull-request-github/pull/4648)

Contributions to `vscode-docs`:

* [@vinistock (Vinicius Stock)](https://github.com/vinistock): Add languages guide for Ruby [PR #6152](https://github.com/microsoft/vscode-docs/pull/6152)

Contributions to `monaco-editor`:

* [@danboo (Dan Boorstein)](https://github.com/danboo): add perl module (.pm) extension [PR #3258](https://github.com/microsoft/monaco-editor/pull/3258)
* [@nnnnoel (Noel Kim (김민혁))](https://github.com/nnnnoel): Add CommonJS, ESM extension for TS [PR #3264](https://github.com/microsoft/monaco-editor/pull/3264)
* [@PmcFizz (Fizz)](https://github.com/PmcFizz): opt example  [PR #3726](https://github.com/microsoft/monaco-editor/pull/3726)
* [@tamayika](https://github.com/tamayika): Fix playground samples type errors and add CI test [PR #3722](https://github.com/microsoft/monaco-editor/pull/3722)

Contributions to `devcontainers/cli`:

* [@jarrodcolburn (jarrodcolburn)](https://github.com/jarrodcolburn): Doc: add shell highlighting to markdown [PR #436](https://github.com/devcontainers/cli/pull/436)
* [@stuartleeks (Stuart Leeks)](https://github.com/stuartleeks): Fix typo (userEnvProb -> userEnvProbe) [PR #426](https://github.com/devcontainers/cli/pull/426)
* [@trxcllnt (Paul Taylor)](https://github.com/trxcllnt): Incrementally copy features near the layer they're installed [PR #382](https://github.com/devcontainers/cli/pull/382)
* [@ShauryaAg (Shaurya Agarwal)](https://github.com/ShauryaAg)
  * modify argument regex to only allow certain set of values [PR #361](https://github.com/devcontainers/cli/pull/361)
  * fixed fromStatement parsing to parse quotes in variable expressions [PR #356](https://github.com/devcontainers/cli/pull/356)
  * handle parsing variable expression in dockerfile [PR #337](https://github.com/devcontainers/cli/pull/337)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
