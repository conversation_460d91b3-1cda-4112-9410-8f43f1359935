---
ContentId: baf150cd-6daf-4604-87db-a7c70a6706a7
DateApproved: 5/4/2022
MetaDescription: Download and learn the basics of Visual Studio Code.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Getting started with Visual Studio Code

In this tutorial, we walk you through setting up Visual Studio Code and give an overview of the basic features.

<iframe src="https://www.youtube-nocookie.com/embed/B-s71n0dHUk?autoplay=true" width="640" height="320" allowFullScreen="true" frameBorder="0" title="Getting Started with Visual Studio Code"></iframe>

Here's the next video we recommend: [Code Editing in Visual Studio Code](/docs/introvideos/codeediting.md)

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Download and install VS Code.
  * From [https://code.visualstudio.com/download](https://code.visualstudio.com/download)
* Open a folder
  * **File** > **Open Folder** (`kb(workbench.action.files.openFolder)`)
* File Explorer
  * **View** > **Explorer** (`kb(workbench.view.explorer)`)
* Search view
  * **View** > **Search** (`kb(workbench.view.search)`)
* Source Control
  * **View** > **Source Control (SCM)** (`kb(workbench.view.scm)`)
* Run and Debug
  * **View** > **Run** (`kb(workbench.view.debug)`)
* Extensions view
  * **View** > **Extensions** (`kb(workbench.view.extensions)`)
* Open the Command Palette.
  * **View** > **Command Palette...** (`kb(workbench.action.showCommands)`)
* Output panel
  * **View** > **Output** (`kb(workbench.action.output.toggleOutput)`)
* Debug Console
  * **View** > **Debug Console** (`kb(workbench.debug.action.toggleRepl)`)
* Problems panel
  * **View** > **Problems** (`kb(workbench.actions.view.problems)`)
* Integrated Terminal
  * **View** > **Terminal** (`kb(workbench.action.terminal.toggleTerminal)`)
* Create a new file
  * **File** > **New File** (`kb(workbench.action.files.newUntitledFile)`)
* Save a file
  * **File** > **Save** (`kb(workbench.action.files.save)`)
* Auto Save
  * **File** > **Auto Save**
* Run
  * **Run** > **Start Debugging** (`kb(workbench.action.debug.start)`)
* Programming language extensions
  * [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) - IntelliSense, linting, debugging, code formatting, refactoring, and more.
  * [Live Preview](https://marketplace.visualstudio.com/items?itemName=ms-vscode.live-server) - Hosts a local server to preview your webpages.
* Zoom
  * Zoom out (`kb(workbench.action.zoomOut)`)
  * Zoom in (`kb(workbench.action.zoomIn)`)
* Customize your editor with color themes.
  * **File** > **Preferences** > **Theme** > **Color Theme** (`kb(workbench.action.selectTheme)`)

## Next video

* [Code Editing in Visual Studio Code](/docs/introvideos/codeediting.md) - Learn about the advanced code editing features in VS Code.
* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [User Interface](/docs/getstarted/userinterface.md) - View the documentation for VS Code.
* [Setup Overview](/docs/setup/setup-overview.md) - Documentation for getting up and running with VS Code, including platform specific setup.
* [IntelliSense](/docs/editing/intellisense.md) -VS Code brings smart code completions as you type.
