# November 2024 (version 1.96)

## Remote - SSH

### The `@remote-ssh` chat participant

The Remote - SSH extension now includes a Copilot chat participant. This participant has context on your recent SSH connections and can help you configure or troubleshoot elements of your remote environment. The participant can also answer more general SSH or remote development questions.

Enable the participant with `setting(remote.SSH.experimental.chat)`.

![Dialog showing a connection failure now also had a Diagnose with Copilot button.](images/1_96/remote-ssh-participant-06.png)

<video src="images/1_96/remote-ssh-participant-08.mp4" title="Demo diagnosing last connection issue" autoplay loop controls muted></video>

<video src="images/1_96/remote-ssh-participant-01.mp4" title="Asking the Remote - SSH chat participant a question" autoplay loop controls muted></video>

### Enhanced session logging

Connection errors are sometimes complex to diagnose and fix.  For known classes of issues, the Remote - SSH extension will provide a summary of the detected issue and provide possible solutions.

Our troubleshooting database continues to grow with each release and is used by GitHub Copilot to improve its problem solving skills.

Enable enhanced logging with `setting(remote.SSH.experimental.enhancedSessionLogs)`.

![The terminal has a table containing a row that shows the connection status, detailed message, and proposed solutions to resolve the issue.](images/1_96/remote-ssh-participant-02.png)

Combining enhanced logging with GitHub Copilot provides a powerful troubleshooting experience.

<video src="images/1_96/remote-ssh-participant-03.mp4" title="Demo using enhanced logging to diagnose a real connection issue" autoplay loop controls muted></video>
