<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="646px" height="121px" viewBox="-0.5 -0.5 646 121" content="&lt;mxfile&gt;&lt;diagram id=&quot;JGUCzz65NiplJT813B3b&quot; name=&quot;Page-1&quot;&gt;7Zpdb5swFIZ/DbcT4EDIZZM03aRtmtSLbZceuMEa4Mg4Cemvnx2Ow4epStK0RBo3kX1s/PE+9rF9FAst0uKB4038jUUksVw7Kiy0tFzXC2byVxkOpcGxp25pWXMaga0yPNJnoiuCdUsjkjcqCsYSQTdNY8iyjISiYcOcs32z2hNLmr1u8JoYhscQJ6b1J41EXFoDz67snwldx7pnx4aSFOvKYMhjHLF9zYTuLbTgjIkylRYLkijxtC7ld6sXSk8D4yQTfT5wAcUOJ1uYHAxMHPRsOdtmEVEf2Baa72MqyOMGh6p0L/lKWyzSROYcmXximVjhlCaK7YJtOSVcNved7KEQaDoe5BcsYfzYEVqtbGQjZadJou0Zy2T9uTkxmOuOcEGKmgkm+kBYSgQ/yCpQOkXlF6dVBwz2FUKkbXEdn4aFYdmsT01XysoEiNstNLL/H6EnTjCg0q4p9HRuaC2nIpqC4oSuM5kO5cSllGiuJkzlpr+DgpRGkfp8zklOn/GfY1OK1IbRTBxH7M0tb6na2gqWg/4ymwvO/pKW0DVGrt8LKIzeNoHeeS6Sol+FXuA14bld8Ex2k2tskg50yxFdb3SOPxy7icGu6EGuW17TK/Un3OU2m471YnrOO9PTtDQ9ZNKbdNC7htP0Rqf5JnauPdzO80ev+SZ2SPuuAdhNX78Vkiy6U28WRSrBeU5D8yJYl5UUVPyCMpX+rRT85EFuWYCgx8wBMi+qmEssIQwELnUkajyOTFlrsnkdsmkbJwkWdNd8UnVpCT38UIuuRg3NGtRcu4WjHDp8VX8CvdKQ47caEpiviTAaklDwoVYNNkXvATtB42kmE2WL1bo5adprKQXGUvoiD71MSJFZJgu+kt3xGa7J3vCxfHyzXOU+1DpRp+bOdt7rSHVMIH1uRKNXBgST4U5Ux4yKjOjOQDfgE1J3Nd5kL4Q3QfZw8JwOeONVtj88zxkQ3qWBtzEGUNt97QjOB8YAOsNvo+s8Y/d5A+4+M/42us5z4Pn+bDh4HeG3mw0D6HjTjcQB/KAZvbk4DtBu6L3iAEY/V44DdMQDuwMB5hq7ubP5WoEArx2e/chAQEeQb3xNnsFuNuCpOsZw3nSmftxbUmarf82UXrP67xG6/wc=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="300" height="120" fill="none" stroke="#000000" pointer-events="all"/>
        <rect x="345" y="0" width="300" height="120" fill="none" stroke="#000000" pointer-events="all"/>
        <rect x="12" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 27px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="27" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="92" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 107px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="107" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="47" y="30" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 40px; margin-left: 48px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="67" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle" font-weight="bold">
                    x
                </text>
            </switch>
        </g>
        <rect x="132" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 147px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="147" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="251" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 266px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="266" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 227 80 L 256 80 Q 266 80 266 73.18 L 266 66.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 266 61.12 L 269.5 68.12 L 266 66.37 L 262.5 68.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="87" y="70" width="140" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 80px; margin-left: 88px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Indentation Level 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="157" y="85" fill="#000000" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    Indentation Level 1
                </text>
            </switch>
        </g>
        <rect x="172" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 187px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="187" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="212" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 227px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="227" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="357" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 372px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="372" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="437" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 452px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="452" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <rect x="392" y="30" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 40px; margin-left: 393px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="412" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle" font-weight="bold">
                    {
                </text>
            </switch>
        </g>
        <rect x="477" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 492px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                {
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="492" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    {
                </text>
            </switch>
        </g>
        <rect x="596" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 611px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                }
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="611" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    }
                </text>
            </switch>
        </g>
        <path d="M 572 80 L 601 80 Q 611 80 611 73.18 L 611 66.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 611 61.12 L 614.5 68.12 L 611 66.37 L 607.5 68.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="432" y="70" width="140" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 80px; margin-left: 433px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Indentation Level 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="502" y="85" fill="#000000" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    Indentation Level 2
                </text>
            </switch>
        </g>
        <rect x="517" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 532px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="532" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
        <rect x="557" y="20" width="30" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 572px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 26px; font-family: Courier New; color: #A52318; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                x
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="572" y="48" fill="#A52318" font-family="Courier New" font-size="26px" text-anchor="middle">
                    x
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>