---
Order:
TOCTitle: April 2021
PageTitle: Visual Studio Code April 2021
MetaDescription: Learn what is new in the Visual Studio Code April 2021 Release (1.56)
MetaSocialImage: 1_56/release-highlights.png
Date: 2021-5-5
DownloadVersion: 1.56.2
---
# April 2021 (version 1.56)

**Update 1.56.1**: The update addresses these security [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22April+2021+Recovery%22+is%3Aclosed).

**Update 1.56.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22April+2021+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the April 2021 release of Visual Studio Code. The VS Code team has been busy this month working on several longer lead time updates so check out the [Preview features](#preview-features) section to learn what's upcoming. Here are some of the highlights included in this release:

* **[Improved hover feedback](#improved-action-hover-feedback)** - Helps you quickly find clickable editor actions.
* **[Terminal profile improvements](#terminal)** - Create a custom default terminal profile.
* **[Debugger inline values](#inline-values-by-default-for-some-languages)** - Display variable values inline during debugging sessions.
* **[Notebook KaTeX support](#math-support-in-markdown-cells)** - Math support in notebook Markdown cells.
* **[Dev Containers volumes view](#remote-development)** - Manage mounted volumes within Docker containers.
* **[winget installation](#windows-installers-consolidated-in-windows-package-manager)** - VS Code is available via the Windows Package Manager.
* **[New introductory videos](#documentation)** - Videos for getting started with VS Code as well as working with C++.
* **[Terminal tabs preview](#terminal-tabs)** - Get a first look at managing open terminals with the new tabs view.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/livestream) on Tuesday, May 11 at 8am Pacific (4pm London) to see a demo of what's new in this release, and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Workbench

### Improved action hover feedback

We have changed the mouse hover feedback for actions all around the workbench, to provide a better UX around clickable actions.

![Actions hover feedback is improved with a background color.](images/1_56/actions.gif)

### Untitled editors hint

We have noticed that many new users are not aware that a language has to be set in order to get full VS Code language support. To help with this problem, we have introduced a hint for untitled editors to help users set the correct language mode. The untitled hint might not be helpful to advanced users, so it goes away immediately as you start typing or you can select **don't show** to never display the hint again.

![Untitled editor shows an untitled hint](images/1_56/untitled-hint.png)

### Default custom editor and notebook resolution

If you have two editors that both state they should be your default editor for a resource (for example, an image viewer and an image editor), you will receive a prompt to resolve the conflict.

In the short video below, the user opens a PNG file, which is associated with two editors. The notification lets the user keep using the Luna Paint Image Editor or configure a new default, which they do and start using the binary Hex Editor.

![A image editor is opened and the user is prompted with a notification to configure their default editor or keep the current one](images/1_56/conflicting-defaults.gif)

### Updated custom dialogs

We have updated our custom dialog styles, which you can enable via `"window.dialogStyle": "custom"`. VS Code now dims the background so there is better focus on the dialog and also uses the secondary button style when there is more than one action.

![An example of a custom dialog with a dimmed background and secondary buttons](images/1_56/custom-dialogs.png)

*Product Icon Theme: [Fluent Icons](https://marketplace.visualstudio.com/items?itemName=miguelsolorio.fluent-icons)*

You can [theme](https://code.visualstudio.com/api/references/theme-color)  the secondary button style by using the following color tokens:

* `button.secondaryBackground`
* `button.secondaryForeground`
* `button.secondaryHoverBackground`

### Auto update only enabled extensions

You can now configure VS Code to auto update only your extensions that are currently enabled.

![Auto update only enabled extensions](images/1_56/extensions-auto-update.png)

*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

## Terminal

### Profile improvements

Last iteration, we introduced [terminal profiles](https://code.visualstudio.com/updates/v1_55#_terminal-profiles). The terminal now supports setting a default profile with the `terminal.integrated.defaultProfile.<platform>` settings.

Environment and icon support was also added to the profiles system:

```json
"terminal.integrated.profiles.windows": {
  "PowerShell": {
    "source": "PowerShell",
    "overrideName": true,
    "icon": "terminal-powershell",
    "env": {
      "TEST_VAR": "value"
    }
  }
},
"terminal.integrated.defaultProfile.windows": "PowerShell",
```

Going forward, these settings are the recommended way to switch out the default terminal configuration and the `terminal.integrated.shell` and `terminal.integrated.shellArgs` settings have been deprecated.

### New terminal picker

Profiles and setting shortcuts have moved out of the dropdown and into a new **+** button with a dropdown.

![When selected, the dropdown button displays a menu](images/1_56/tabs-dropdown-button.png)

This also supports creating a new split terminal based on a non-default profile.

### New keybindings

The terminal has several new default keybindings this release:

* Move to previous terminal - `Ctrl+PageUp` (macOS `Cmd+Shift+]`)
* Move to next terminal - `Ctrl+PageDown` (macOS `Cmd+shift+[`)
* Focus terminal tabs view - `Ctrl+Shift+\` (macOS `Cmd+Shift+\`) - [Terminal tabs preview](#terminal-tabs)

As always, these default keybindings can be removed or custom keybindings can be added via the [keybindings system](https://code.visualstudio.com/docs/getstarted/keybindings).

### Linux selection paste command

The new command `workbench.action.terminal.pasteSelection` is available on Linux to paste into the terminal from the selection clipboard.

### Terminal workspace shell permissions changed

In order to support incoming changes related to profiles, the prompt that would show if terminal settings were present in `.vscode/settings.json` was removed in favor of a setting to opt into workspace settings use. Note that this can be dangerous in untrusted workspaces.

```json
"terminal.integrated.allowWorkspaceConfiguration": true
```

When Workspace Trust is enabled by default, we will remove this and use that system.

## Tasks

### Removal of Tasks 0.1.0 with upgrade

Tasks 2.0.0 has been available and working well for over three years, and tasks 0.1.0 has been deprecated throughout that time. In light of our [Node.js-free renderer](https://github.com/microsoft/vscode/issues/92164) goal, tasks 0.1.0 has been deleted rather than brought into the Node.js-free world. When you open a folder that contains 0.1.0 tasks, they will be automatically upgraded to version 2.0.0 for you.

![Tasks upgrade notification](images/1_56/tasks-upgrade.png)

### No "Task" prefix in terminal tabs

When using the new [Terminal tabs](#terminal-tabs) feature, the "Task" prefix will no longer be added to the terminal name. Instead, tasks are indicated by the "tools" icon to make better use of the available space.

![Tasks as terminal tabs](images/1_56/tasks-terminal-tabs.png)

### Better matching with eslint-stylish

The `$eslint-stylish` problem matcher matches multiline problems more accurately.

## Debugging

### Breakpoints view improvements

**Access type is shown for data breakpoints**

For data breakpoints, VS Code now shows the access type ("Read", "Write", or "Access") next to its name in the Breakpoints view.

![The Breakpoints view shows the access types "Read", "Write" and "Access" being rendered next to the breakpoint name](images/1_56/data-breakpoints.png)

**Better status/error reporting for exception breakpoints**

For exception breakpoints, VS Code now shows their individual verification status and detailed reason in the Breakpoints view. If an exception breakpoint cannot be verified (for example because its condition contains a syntax error), it will be grayed out and, on hover, the corresponding error message is shown.

![The Breakpoints view shows disabled exception breakpoints that on hover show the error message](images/1_56/exception-breakpoints.png)

### Other UI improvements

**Alternative behavior for Stop and Disconnect commands**

A debug session is typically stopped via the **Debug: Stop** and **Debug: Disconnect** commands. If the debug session is of type `launch`, the **Stop** command does not only stop the session but it also terminates the debuggee. For `attach` type debug sessions, there is a **Disconnect** command, which stops debugging and resumes execution of the debuggee.

With this release, it is now possible to flip this behavior by pressing the `Alt` modifier while triggering the command from the Debug toolbar. Altering the behavior makes it possible to leave the debuggee running for `launch` type debug sessions, and to terminate the debuggee for `attach` type debug sessions. Apart from using `Alt` with the default commands, it is also possible to access both the **Debug: Stop** and **Debug: Disconnect** commands from the Command Palette for both `launch` and `attach` debug sessions.

The alternative behavior is only available for debug extensions that have opted in to this feature.

**Improved flow for installing missing debug extensions**

We have improved the flow if a user wants to start debugging but hasn't installed the necessary language extension that provides debugging support (like Python or Java). When this happens, VS Code now prompts the user to install the appropriate extension.

![VS Code prompts to install the Python extension if it is missing](images/1_56/python-flow.gif)

**Call stack column stopped indicator**

VS Code now renders the Call Stack column indicator every time a debuggee is stopped on a line. This should help identify where on a line the program is currently stopped.

![An code execution is stopped and a column indicator is rendered in the middle of a line](images/1_56/debug-column.gif)

### Inline values by default for some languages

VS Code's debugger UI supports **Inline values** that show variable values **inline** in the editor when stepping through source code. This feature is based on a generic implementation in VS Code core and so may not be a perfect fit for all languages, even sometimes showing incorrect values because the generic approach doesn't understand the underlying source language. For these reasons, the feature was not enabled by default.

With a new debugger extension API, it is now possible for language extensions to provide correct inline value support and we are enabling the **Improved inline values** feature by default.

To enable this feature, the `debug.inlineValues` setting has a new (default) value `auto`. When set to `auto`, inline values are automatically enabled for those languages that have "improved inline value" support.

The [Debugger for Java](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-debug) extension is one of the first debugger extensions to adopt the API. In the screenshot below, the accurate values of the Java variables are displayed right next to their usage.

![Java extension displaying inline values in the editor while debugging](images/1_56/java-inline-values.png)

You can get inline values while debugging PowerShell scripts using the [Inline Values support for PowerShell](https://marketplace.visualstudio.com/items?itemName=TylerLeonhardt.vscode-inline-values-powershell) extension.

### Debug view displayed on break

The default value of the `debug.openDebug` setting is now `openOnDebugBreak` so that on every breakpoint hit, VS Code will open the Debug view. The Debug view is also displayed on first session start.

### JavaScript debugging

As usual, the complete list of changes can be found in the [vscode-js-debug changelog](https://github.com/microsoft/vscode-js-debug/blob/main/CHANGELOG.md#changelog).

**Improved breakpoint diagnostic tool discoverability**

Based on [heuristics](https://github.com/microsoft/vscode/issues/57590#issuecomment-813616178), VS Code will show a notification that suggests opening the breakpoint diagnostic tool if it detects the user may be having trouble getting breakpoints to bind.

![Notification that reads "It looks like you might be having trouble with breakpoints, would you like to open our diagnostic tool?"](images/1_56/js-debug-diagnostic-tool.png)

This prompt will initially be visible only to a subset of users as we experiment with its efficacy and assertiveness.

**Private Class Field Support**

[Private class fields](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Classes/Private_class_fields) are now visible and can be inspected in the debugger.

## Notebooks

### Toggle line numbers

You can now temporarily toggle the line numbers of a cell in the current session from the cell toolbar, or change the visibility of line numbers for all notebooks through the `notebook.lineNumbers` setting.

![Toggle line number of a cell](images/1_56/notebook-toggle-line-numbers.gif)

### Cell toolbar position per file type

The cell toolbar position can be now customized per file type through the `notebook.cellToolbarLocation` setting. For example, you can have the cell toolbar on the right side for [GitHub Issue](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-github-issue-notebooks) notebooks but have it on the left for Jupyter notebooks.

### Math support in Markdown cells

You can now use math equations inside of notebook Markdown cells:

![Math being rendered in a Jupyter notebook](images/1_56/notebook-math.png)

VS Code uses [KaTeX](https://katex.org) for rendering the equations. There are two ways to embed a math equation into a Markdown cell:

* Using single dollar signs: `$...$`. This creates an inline math equation.
* Using double dollar signs:  `$$...$$`. This creates a centered, block math equation.

We implemented math support using an experimental notebook markup renders API, which is still in development. Our eventual goal with this API is to also allow extensions to extend the rendering of Markdown in notebooks.

## Languages

### Markdown preview typographer support

The new `markdown.preview.typographer` setting lets you enabled smart quotes and simple [typographic replacements](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.js) in the built-in Markdown preview.

In the example below, Markdown text such as `(c)` gets automatically replaced by copyright symbol `©` in the preview:

![Smart quotes and text replacement in the Markdown preview](images/1_56/markdown-typographer.png)

The `markdown.preview.typographer` setting is disabled by default.

### More files recognized as shellscript

Files with the `.xsession` and `.xprofile` filename extensions will be automatically recognized as shellscript.

## Preview features

### Terminal tabs

Tabs in the terminal are available as a preview feature and can be enabled with the following setting:

```json
"terminal.integrated.tabs.enabled": true
```

![The tabs view is a split pane to the right of two split terminals. It contains icons and labels for each terminal instance.](images/1_56/tabs.png)

When enabled, the new tabs view will only show by default when there are at least two terminals. For single terminals, the tab is "inlined" into the panel title like so:

![Terminal tab inlined into the panel title for a single terminal](images/1_56/tabs-single.png)

Each tab supports several actions via the context menu.

![Right-clicking on a tab displays a menu](images/1_56/tabs-menu-right-click.png)

Split and kill terminal are available on hover.

![Hovering the tab item shows inline action icons](images/1_56/tabs-inline-actions.png)

We've tried to align how the new tabs behave with how the Explorer works. Here are some of the other behaviors:

* Double-clicking the empty space will create a new terminal.
* Double-clicking the sash will toggle the tabs view width between the "ideal" size that displays all the titles without truncating and a narrow view that shows only the icons.
* The tabs can be moved to the left side using the `terminal.integrated.tabs.location` setting.
* Various other configuration settings are available under `terminal.integrated.tabs`.

### Terminal statuses

Along with tabs, we have introduced the concept of **statuses** to the terminal. A terminal can have many statuses, each of which represents a state the terminal can be in temporarily, with the highest severity one being displayed next to the tab. Status icons appear to the right of the terminal title in the tabs view. On hover, details of the status and any associated actions are presented.

![On a tab associated with a terminal that requires a relaunch, a yellow triangle with an exclamation mark is to the right of the terminal title](images/1_56/status-icon.png)

For now, these states are supported:

* Relaunch needed: A warning icon status is used if an extension wants to change a terminal's environment.
* Disconnected: A plug icon status is used when the terminal loses its connection with its process.
* Bell: A bell icon appears when the bell is enabled via the `terminal.integrated.enableBell` setting and the terminal bell triggers.

We plan to support task states soon so that task run status is available at a glance even without activating the tab.

### Welcome Page walkthroughs

We have expanded the `walkthroughs` contribution for placing content on the Getting Started page to allow for Markdown in both step descriptions and step main content. Extension contributions to the Getting Started page are an experimental feature, and can be enabled with `"workbench.welcomePage.experimental.extensionContributions": true,`.

The short video belows show an example walkthrough to teach users about the [Luna Paint](https://marketplace.visualstudio.com/items?itemName=Tyriar.luna-paint) extension.

![Stepping through the Luna Paint extension's walkthrough contribution](images/1_56/getting-started-markdown.gif)

### Custom hover support in Activity bar and Panel

In this milestone, we added experimental support for custom hovers in the Activity bar and Panel. You can enable custom hovers using the setting `workbench.experimental.useCustomHover`.

![Custom hover in Activity Bar and Panel](images/1_56/custom-hover.gif)

*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*
*Product Icon Theme: [Fluent Icons](https://marketplace.visualstudio.com/items?itemName=miguelsolorio.fluent-icons)*

### Remote Repositories (RemoteHub)

As part of this release, we are previewing a new built-in extension, **Remote Repositories (RemoteHub)**, which allows you to instantly browse, search, edit, and commit to any GitHub repository directly from within VS Code without having to clone or have the repository locally. It is currently only available in the [Insiders](https://code.visualstudio.com/insiders/) edition of VS Code.

**Getting started**

To get started, run the **Open Remote Repository...** command from the Command Palette. From there, you can paste in any GitHub URL, or choose to search for a specific repository or pull request.

![Open Remote Repository picker](images/1_56/open-remote-repository.png)

*Theme: [Amethyst Dark Theme](https://marketplace.visualstudio.com/items?itemName=eamodio.amethyst-theme)*

Once you enter a URL or choose a repository or pull request, VS Code will open a new workspace for that repository. The remote status indicator, on the left side of the Status bar, shows the connected remote provider name, for example GitHub, for the remote repository.

![Remote Repositories Demo showing various source control operations](images/1_56/remote-repositories.gif)

*Theme: [Amethyst Dark Theme](https://marketplace.visualstudio.com/items?itemName=eamodio.amethyst-theme)*

**Features**

* Instantly open any GitHub repository without cloning or having the repository locally.
* Easily edit and contribute to any GitHub repository - directly commit your changes to GitHub, or open a pull request.
* Continue in another environment - via the **Continue on...** command (accessible from the Command Palette or the remote indicator quick pick menu).
  * Clone the repository locally
  * Clone the repository into a container - requires the [Dev Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension
* Provides a familiar user interface similar to working on a local repository (*see "Limitations" below).
  * **Explorer** - Open, copy, move, rename, and delete files and folders
  * **Search** - Fast full-text search<sup>*</sup>
  * **Source Control** - Stage and commit your changes, as well as many other source control actions
  * **Timeline** view - See file history with diff support
  * **Quick Open** - Quickly find files to open
  * **Remote Indicator** - Shows the provider that the remote repository is connected to (for example, GitHub)
* Work on different branches simultaneously - each remote branch is treated like a separate worktree (in Git parlance), meaning that any changes you make are isolated to that branch. You don't need to stash your changes just to switch to a new branch in order to checkout a PR or start on a new work item. And when you go back to the previous branch, your changes will still be there.
* Install the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, and quickly view, explore, and checkout pull requests, view and start working on issues.

**Limitations**

* Limited language intelligence - Many language servers don't yet understand this virtualized environment. TypeScript supports single file intelligence for remote repositories.
* Limited extension support - As with language servers, many extensions don't work with remote repositories. Extensions can opt-out and will not be activated for virtual workspaces. See the [Extension authoring section](#extension-authoring) below for more details.
* Search - Full-text search requires a pre-built index for exact text matching, otherwise it will fallback to GitHub's fuzzy default-branch only native search.
* Terminals - Not supported. Any terminals open will be on your local file system.
* Debugging - Not supported.
* Tasks - Not supported.

**Tell us what you think**

We are very excited for you to play with **Remote Repositories (RemoteHub)** and can't wait for your thoughts and feedback. We are just getting started on this journey, so expect the feature set to grow and the limitations to shrink as we continue development. We will also be expanding the set of supported providers. GitHub is just the first provider we are supporting, with Azure Repos coming soon.

### TypeScript 4.3

This release continues to improve our support for the upcoming TypeScript 4.3 release. You can read more about the new language features and improvements in TypeScript 4.3 [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-3-beta/). Here are some of the editor improvements it enables:

* Support for `override`. There are also Quick Fixes for adding the `override` keyword.
* Import statement completions. This is like auto import, except you are typing in the import statement itself.
* JSDoc `@link` tag support.

To start using the TypeScript 4.3 nightly builds, just install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share your feedback and let us know if you run into any bugs with TypeScript 4.3.

### Workspace Trust

In last milestone's release notes, we shared our work on Workspace Trust specifically for extension authors. We made great progress this milestone for both the extension API and user experiences. That being said, Workspace Trust will remain disabled for this release, but we would love for you to try it out and provide feedback.

You can enable the feature with the following setting `security.workspace.trust.enabled`. Once enabled, you will see the following dialog when opening folders in VS Code.

![Workspace Trust startup dialog](images/1_56/trust-dialog.png)

This dialog is important for allowing the user to make a decision early and understand the impact of their decision. Once you understand the feature, you may want to customize when to display the dialog using the `security.workspace.trust.startupPrompt` setting.

You can follow the development of Workspace Trust and provide feedback in [issue #106488](https://github.com/microsoft/vscode/issues/106488).

## Contributions to extensions

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.56 include:

* New volume view when you **Clone Repository in Container Volume**.
* Local terminal warning when connected to a remote.
* Prompt to install Docker Desktop when starting with the Dev Containers extension.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_56.md).

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

To learn about all the new features and updates, you can see the full [changelog for the 0.26.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0260) release of the extension.

## Extension authoring

### Define whether your extension supports a virtual workspace

The new [Remote Repositories](#remote-repositories-remotehub) extension lets you open a folder with content directly from GitHub. It does so by providing a [virtual file system](https://github.com/microsoft/vscode/blob/dc8bd9cd7e5231745549ac6218266c63271f48cd/src/vs/vscode.d.ts#L7038) and opening a workspace on it. Other extensions do the same. They serve content from ftp-servers, from cloud storage, or from databases and seamlessly provide these as files to the user in VS Code.

The virtual file system feature has existed for some time, however we have observed that not all extensions can support running in a **virtual workspace**, where the workspace files do not exist physically on disk. For this reason, we have added support for an extension to signal whether it supports running in a virtual workspaces or not. When an extension has opted-out, it will not be activated by VS Code for a virtual workspace and the user will not see errors from this extension.

An extension opts out of a virtual workspace setup in the `package.json` as shown below:

```json
{
  "capabilities": {
    "virtualWorkspaces": false
  }
}
```

The goal is that as many extensions as possible support running in a virtual workspace. However, this is not always possible, particularly when an extension is using components that assume that files are physically present.  The [Virtual Workspaces guide](https://github.com/microsoft/vscode/wiki/Virtual-Workspaces) documents how an extension can support a virtual workspace.

**Call to Action**: Please check whether your extension can handle virtual workspaces, and set the `virtualWorkspaces` capability accordingly in your `package.json`.

There will be a transition period until extensions have adopted the new `virtualWorkspaces` property. Until then, we maintain an internal list for extensions that we think should have the `virtualWorkspaces` capability set to `false`. This was done based on an analysis whether the extensions is using the Node.js `fs` module and is therefore accessing the file system directly. However, the extension author is in a much better position to assess whether an extension supports the `virtualWorkspaces` capability. To track the adoption, we have created the following [tracking issue #122836](https://github.com/microsoft/vscode/issues/122836). If your extension is in the list and you have adopted the `virtualWorkspaces` capability, please add a comment in the above issue.

### Remote indicator menu

Extensions can now contribute to the remote indicator menu:

![Remote indicator on the left of the Status bar](images/1_56/remote-indicator.png)

The `statusBar/remoteIndicator` menu contribution point adds a command to the remote indicator menu.

```json
"contributes": {
    "menus": {
        "statusBar/remoteIndicator": [
        {
          "command": "remote-wsl.newWindow",
          "when": "!remoteName && isWindows",
          "group": "remote_10_wsl_0_local@1"
        }
    ]},
    "commands": [
      {
        "command": "remote-wsl.newWindow",
        "title": "New WSL Window",
        "category": "Remote-WSL"
      }
    ]
}
```

To allow the menu to sort entries based on the provider, the `group` needs to follow a specific syntax:

For commands from remotes: `remote_${orderOfGroups}_${remoteName)_${internalGrouping}@${orderInGroup}`

For commands from virtual file systems: `virtualfs_${orderOfGroups}_${fileScheme)_${internalGrouping}@${orderInGroup}`

* `orderOfGroups` is a 2-digit number used to sort the groups
* `remoteName` is first part of the remoteAuthority (wsl, ssh,...)
* `fileScheme` is the URI schema of the virtual file system
* `internalGrouping` is of free use for each contribution
* `orderInGroup` is used to sort entries within your group

Example: `remote_10_wsl_1-open@1`

### iframes now used for most webviews

Ever since the webview API was first introduced, we've implemented webview using [Electron's webview tag](https://www.electronjs.org/docs/api/webview-tag). On the web however, VS Code's webviews are instead implemented using standard `<iframe>` elements since `<webview>` is not available.

We've been exploring migrating desktop versions of VS Code to use `<iframe>` backed webviews for a while, as this change will give extensions a more consistent webview environment between desktop and web. Migrating off of `<webview>` will also help with our Electron sandboxing efforts.

This iteration, we have switched the majority of webviews over to use iframes. The `<webview>` tag is now only used for webviews that enable the Find widget, and we plan on migrating them to use iframes as well after a bit more engineering work.

This change should not cause issues, however there are some corner cases where the behavior of `<iframe>` and `<webview>` elements differ. Be sure to give your extension a quick test pass to verify that everything works as expected.

### Easier inspecting of webviews

One clear benefit of `<iframe>`-backed webviews is that they are now much easier to inspect.

If you've worked with webviews previously, you may remember that you had to use the **Developer: Open Webview Developer Tools** command to inspect the contents of your webview. This would open a new developer tools panel just for your webview.

![Inspecting a webview in its own developer tools window](images/1_56/webview-inspect-open.png)

With `<iframe>`-backed webviews, you can instead use VS Code's standard developer tools (**Developer: Toggle Developer Tools**) to inspect webviews.

![Inspect a webview in the main developer tools window](images/1_56/webview-inspect-in-main.png)

This makes it easy to inspect multiple webviews. The developer tools also no longer close when your webview goes away.

In addition, exceptions and console messages that occur inside of webviews are now printed in the top-level developer tools console.

![Exceptions from a webview printed in the main developer tools](images/1_56/webview-inspect-exceptions.png)

You can also use the developer tools to evaluate expressions in a webview's context. After opening VS Code's dev tools with **Developer: Toggle Developer Tools**, open the console, and select the `active-frame` for your webview from the context selector.

![Selecting the current scope of the debug console](images/1_56/webview-inspect-debug-scope.png)

Overall, being able to use VS Code's standard developer tools should provide a nicer development experience for webviews.

### CodeActionTriggerKind

The new `triggerKind` property on `CodeActionContext` tracks why Code Actions are being requested from a `CodeActionProvider`. The possible values of this property are:

* `Invoke` - Code Actions were requested explicitly, either with a keyboard shortcut or command.
* `Automatic` - Code Actions were requested without explicit user action. This includes requesting Code Actions when the contents of a document change.

Providers can use `triggerKind` to return different sets of results depending on how Code Actions are requested. For example, a refactoring Code Action provider that is triggered automatically may only return refactorings for the exact current selection in order to limit how often the Code Action lightbulb shows up. However when Code Actions are explicitly requested, the same provider may automatically expand the current selection to try to show all the refactorings that a user may be interested in at the current location.

### Updated codicons

We've added the following new icons to our [codicon library](https://code.visualstudio.com/api/references/icons-in-labels):

* `arrow-swap`
* `copy`
* `debug-line-by-line`
* `filter-filled`
* `person-add`
* `terminal-bash`
* `terminal-cmd`
* `terminal-debian`
* `terminal-linux`
* `terminal-powershell`
* `terminal-tmux`
* `terminal-ubuntu`
* `wand`

![List displaying updated codicons with their names](images/1_56/codicons.png)

### Keybinding label colors

Keybinding labels are shown when there is a keybinding associated with a command. Usages of the keybinding label include (but are not limited to):

* The Command Palette
* The Keyboard Shortcuts editor
* The Keyboard Shortcuts recorder modal
* The "feature contribution" section of an extension's marketplace page

The following customizations are available:

* `keybindingLabel.background`: Keybinding label background color. The keybinding label is used to represent a keyboard shortcut.
* `keybindingLabel.foreground`: Keybinding label foreground color. The keybinding label is used to represent a keyboard shortcut.
* `keybindingLabel.border`: Keybinding label border color. The keybinding label is used to represent a keyboard shortcut.
* `keybindingLabel.bottomBorder`: Keybinding label border bottom color. The keybinding label is used to represent a keyboard shortcut.

### Workspace Trust extension API

Last milestone, we mentioned the work we had been doing on a feature called Workspace Trust. We asked extension authors to keep an eye on [issue #120251](https://github.com/microsoft/vscode/issues/120251) for updates and we continue to do so. The following information and updates can be found in that issue as well.

This milestone we moved the Workspace Trust extension API from proposed to stable. This allowed us to release the first cut of our guide for onboarding your extension to Workspace Trust. The API is small, so here is a quick look.

You can declare your extension to provide complete or partial or no support in untrusted workspaces using the `untrustedWorkspaces` capability in `package.json`.

The following example declares that the extension is supported completely in untrusted workspaces. In this case, the extension is enabled in untrusted workspaces.

```json
"capabilities": {
  "untrustedWorkspaces": {
    "supported": true
  }
}
```

The next example declares that the extension is not supported in untrusted workspaces. In this case, the extension is disabled in untrusted workspaces.

```json
"capabilities": {
  "untrustedWorkspaces": {
    "supported": false
  }
}
```

The third option is to declared `limited` support. There are three tools provided to you when you select the `limited` option.

First, if you have a setting that can be configured in the workspace but requires the workspace to be trusted in order to apply the workspace value, then you can include the setting using `restrictedConfigurations` array property in `untrustedWorkspaces` object. Doing so, VS Code will ignore the workspace value of these restricted settings when your extension reads these settings values using the VS Code [Workspace Configuration](https://code.visualstudio.com/api/references/vscode-api#WorkspaceConfiguration) API.

The following example declares the settings that are restricted in untrusted workspaces.

```json
"capabilities": {
  "untrustedWorkspaces": {
    "supported": "limited",
    "restrictedConfigurations": [
      "markdown.styles"
    ]
  }
}
```

Next, you can also check and listen if the current workspace is trusted or not programmatically using the following API:

```ts
export namespace workspace {
  /**
    * When true, the user has explicitly trusted the contents of the workspace.
    */
  export const isTrusted: boolean;
  /**
    * Event that fires when the current workspace has been trusted.
    */
  export const onDidGrantWorkspaceTrust: Event<void>;
}
```

Lastly, you can hide commands or views declaratively with the `isWorkspaceTrusted` context key in your when clauses.

**Call to Action**: Please review the "Workspace Trust Extension Guide" in [issue #120251](https://github.com/microsoft/vscode/issues/120251) and set the `untrustedWorkspaces.supported` value as appropriate for your extension.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Native notebooks

We are preparing large parts of the native notebook API for finalization. We have done numerous little tweaks and also some major changes.

**Notebook serializer**

We added the `NotebookSerializer` API. It provides a simplified way to translate "bytes" into `NotebookData` and vice versa. When you implement this API, your notebook will get features like backup, revert, dirty-state, and others for free. We recommend extension authors adopt this new API and not use the previous content provider-based API.

**Notebook controller**

The notebook controller API replaces the kernel provider API. Notebook controllers provide the execution engine of your notebook, which create the notebook output. Notebooks can provide multiple controllers or none and VS Code allows users to select controllers. In return, extensions are free to create, modify, and remove controllers as their domain model demands.

**Notebook cell Status bar**

The `NotebookCellStatusBarItemProvider` API replaces the `createCellStatusBarItem` method. It enables extensions to contribute items with labels, icons, and commands, to the Status bar at the bottom of every cell editor. It follows the same provider pattern as many other VS Code extension APIs.

### Testing

We intended to finalize a subset of the new testing APIs this month, however we focused on refinement and deferred finalization to next month, tracked in [issue #122208](https://github.com/microsoft/vscode/issues/122208). API changes made this iteration were primarily:

* The `TestProvider` was renamed to the `TestController` and its methods were renamed accordingly.
* The `TestItem`s are now managed objects called by `vscode.test.createTestItem`.
* Test results are now created through a standard `vscode.test.createTestResults` method, which can be called within or outside of `TestController.runTests`.

With these changes came a few additional features, such as the ability to display raw test output and for extensions to indicate errors in loading tests. We believe these changes provide a solid foundation for additional capabilities going forward and align more closely to our existing extension APIs.

We also created a [Test Adapter Converter](https://marketplace.visualstudio.com/items?itemName=ms-vscode.test-adapter-converter) extension that allows any adapter working with the existing [Test Explorer UI](https://marketplace.visualstudio.com/items?itemName=hbenl.vscode-test-explorer) extension to automatically plug into native VS Code testing. The converter extension can be installed manually today, and soon it will integrate with the Test Explorer UI for a seamless migration path for existing users and adapters into native testing.

### Improved transfer of ArrayBuffers to and from webviews

In current versions of VS Code, sending typed arrays to or from a webview has a few quirks:

* Typed arrays, such as `UInt8Array`, serialize very inefficiently. This can cause performance issues when you need to transfer many megabytes of data.
* Sent typed arrays are not recreated as the correct type on the receiving side. If you send a `UInt8Array`, the receiver instead gets a generic object that has the data values of the `UInt8Array`.

While both of these issues are bugs, we also can't fix them without potentially breaking extensions that rely on the existing behavior. At the same time, there's zero reason a newly written extension would want the current confusing and inefficient behavior.

Therefore, we've decided to leave the existing behavior in place for existing extensions but opt new extensions into the more correct behavior. This is done by looking at the `engines` in your extension's `package.json`.

```json
"engines": {
  "vscode": "^1.57.0",
}
```

If the extension targets VS Code 1.57+, then typed arrays should be recreated on the receiver side and the transfer of large typed arrays to and from webviews should be much more efficient.

Please test out this behavior and let us know if it either doesn't work as expect or causes unexpected regressions for your existing code.

### TerminalOptions.message

This new proposal allows an extension to display a message in the terminal before the process is launched.

```ts
vscode.window.createTerminal({
 message: '\x1b[3;1mSome custom message\x1b[0m'
});
```

## Debug Adapter Protocol

### Exception breakpoints are better aligned with regular breakpoints

The `setExceptionBreakpoints` request can now optionally return an array of `Breakpoint`s similar to other `set*Breakpoints` requests. This allows clients to show validation error messages for individual exception breakpoints or filters. Starting with this release, VS Code shows these errors in the Breakpoints view.

### Restart request can now pick up debug configuration changes

The `restart` request now takes a new optional parameter `arguments`, where clients can pass the latest version of a launch or attach configuration. With this addition, a debug adapter can restart the session with up-to-date values from the debug configuration.

### Disconnect debugger and keep debuggee suspended

The `disconnect` request is used to end a debug session and to either continue execution of the debuggee or to terminate it. There is a new option to keep the debuggee in the suspended state after the debugger disconnects. This makes it possible to pick up debugging with a new session.

A debug adapter implementing this feature must announce its support with the `supportSuspendDebuggee` capability. A client can use the feature by passing a new optional argument `suspendDebuggee` to the `disconnect` request.

## Engineering

### Electron 12 update

In this milestone, we finished the exploration to bundle Electron 12 into VS Code, thanks to everyone involved with testing and self-hosting on Insiders. This is a major Electron release and comes with Chromium 89.0.4389.114 and Node.js 14.16.0.

### Progress on Electron sandbox

This milestone we continued to make the VS Code window ready for enabling Electron's [sandbox](https://www.electronjs.org/docs/tutorial/sandbox) and [context isolation](https://www.electronjs.org/docs/tutorial/context-isolation) features.

Specifically:

* We changed all of the environmental properties needed for a VS Code window to be accessible in a sandboxed renderer.
* We are exploring switching to `iframe` elements instead of `webview` for custom editors by selectively enabling the `iframe` in certain cases where possible.
* We were able to move some of the native module or Node.js requirements we have in the renderer to other processes or remove them entirely.
* We made `windows-process-tree` context-aware.

### Service workers now used to load resource in webview

In desktop VS Code, webviews now use service workers to load local resources. Web versions of VS Code have always used service workers for this, but previously desktop VS Code was using an [Electron protocol](https://www.electronjs.org/docs/api/protocol).

Using service workers in the desktop version, lets us better align the behavior of webviews between desktop and web. It also helped us fix a few tricky bugs and let us delete around 1000 lines of code.

While this change should have no impact on the majority of webview extensions, it could cause problems in a few edge cases:

* Extensions that make assumptions about how webviews load resources

  If your extension was using the `vscode-webview-resource:` protocol directly or hard coding other assumptions about resource URIs, it may no longer work.

  Instead, make sure you always use `.asWebviewUri` to create URIs for resources. Also remember that the format of the returned URI may chance in the future.

* Extensions that set the `src` of an iframe to point to an HTML file on disk

  Service workers cannot not see this request so we no longer support this. This was already the case on web, and we don't think this pattern is common.

  The recommended fix is to not use an iframe and to inline the contents of the HTML file into the webview instead.

### No implicit override enabled in codebase

The upcoming TypeScript 4.3 release a new [override keyword](https://github.com/microsoft/TypeScript/issues/2000) which tells the compiler that a method in a subclass overrides a method in its superclass. There's also a new `--noImplicitOverride` flag that enforces that all methods that override a method from a superclass must use the `override` keyword:

```ts
class Foo {
   foo() {...}
}

class SubFoo extends Foo {
    foo() { ... } // Error when noImplicitOverride is enabled: missing override specifier
}
```

The override keyword has a few benefits:

* When reading code, it alerts you that a method is overriding one from the base class

* It is an error to try to override a method that does not exist on the base class. This can help catch errors caused by renaming a method in a base class but forgetting to update the method name in the subclasses.

This iteration we adopted the override keyword in our codebase and also enabled `--noImplicitOverride` for both core VS Code and all builtin extensions. While we automated the majority of this work, the change did help us catch a few cases where our code was incorrectly re-declaring a property or had unclear inheritance patterns.

This new strictness rule should also help us catch some common programming mistakes in the future.

### Windows installers consolidated in Windows Package Manager

We have updated our [Windows Package Manager](https://learn.microsoft.com/windows/package-manager/) publishing pipeline to publish our user and system installers for `arm64`, and to adopt the [v1 package manifest schema](https://github.com/microsoft/winget-cli/blob/master/doc/ManifestSpecv1.0.md), allowing us to consolidate the user and system installers for `x86`, `x64` and `arm64` into a single manifest. Users can pick between the user and system installers using the `--scope` parameter when [installing](https://learn.microsoft.com/windows/package-manager/winget/install) a package or using the [winget CLI settings](https://aka.ms/winget-settings#scope).

## Documentation

### Updated introductory videos

The VS Code [introductory videos](https://code.visualstudio.com/docs/getstarted/introvideos) have been updated. The videos range from [Getting Started](https://code.visualstudio.com/docs/introvideos/basics) and [Using extensions](https://code.visualstudio.com/docs/introvideos/extend) to VS Code features like [Debugging](https://code.visualstudio.com/docs/introvideos/debugging) and [Version Control](https://code.visualstudio.com/docs/introvideos/versioncontrol).

[![Intro to Debugging in VS Code video](images/1_56/intro-video-debugging.png)](https://code.visualstudio.com/docs/introvideos/debugging)

### New C++ videos

The [C++ extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.cpptools) team has created a set of [introductory videos](https://code.visualstudio.com/docs/cpp/introvideos-cpp) explaining how to configure IntelliSense, build, and debug your C++ projects.

### The Story and Technology Behind VS Code

You can read about the history and underlying technology of VS Code in [this interview](https://www.git-tower.com/blog/developing-for-the-desktop-vscode/) with VS Code engineer, [Ben Pasero](https://twitter.com/benjaminpasero). Ben talks about the early days of VS Code and what it's like to use Electron as an application framework enabling VS Code run on macOS, Windows, and Linux.

## Partner extensions

### Azure Machine Learning

The [Azure Machine Learning](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.vscode-ai) extension enables you to create and manage powerful cloud computing resources for your machine learning workflows. With its remote capabilities, you can seamlessly [connect to your compute resources](https://techcommunity.microsoft.com/t5/azure-ai/supercharge-azure-ml-code-development-with-new-vs-code/ba-p/2260129) in a secure, auditable, and compliant manner.

[![Microsoft Azure Machine Learning extension](images/1_56/azure-machine-learning.png)](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.vscode-ai)

## Notable fixes

* [108559](https://github.com/microsoft/vscode/issues/108559): RunInTerminal doesn't use the specified integrated terminal from workspace settings bug
* [118282](https://github.com/microsoft/vscode/issues/118282): Debugger Step Over into skipFiles
* [118731](https://github.com/microsoft/vscode/issues/118731): Investigate how to avoid Safari's popup-blocker when opening browser windows and tabs asynchronously
* [119340](https://github.com/microsoft/vscode/issues/119340): Run Without Debug never terminates if uncaught exceptions breakpoint is enabled
* [121347](https://github.com/microsoft/vscode/issues/121347): File gets garbled characters when restored from backup
* [119059](https://github.com/microsoft/vscode/issues/119059): Custom text editors: backups open 2 editors when restoring
* [120245](https://github.com/microsoft/vscode/issues/120245): CSS: `!important` completion is broken
* [120393](https://github.com/microsoft/vscode/issues/120393): Improve handling of webgl context loss
* [120435](https://github.com/microsoft/vscode/issues/120435): Remove `emmet.extensionsPath` valid path check
* [120882](https://github.com/microsoft/vscode/issues/120882): Pasting file in the explorer no longer opens that file in the editor
* [121148](https://github.com/microsoft/vscode/issues/121148): Duplicate launch configuration are shown in debug dropdown
* [120277](https://github.com/microsoft/vscode/issues/120277): The horizontal scroll bar in the Find in Files pane is too small and scrolls the wrong way

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Don Jayamanne (@DonJayamanne)](https://github.com/DonJayamanne)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [T.Y.M.SAI (@tymsai)](https://github.com/tymsai)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

* [@akosyakov (Anton Kosyakov)](https://github.com/akosyakov): fix race between terminal create and initial resize [PR #121272](https://github.com/microsoft/vscode/pull/121272)
* [@alanrenmsft (Alan Ren)](https://github.com/alanrenmsft): fix issue with finding required node handles [PR #118091](https://github.com/microsoft/vscode/pull/118091)
* [@alexmyczko (Alex Myczko)](https://github.com/alexmyczko): Improve debian control description [PR #119160](https://github.com/microsoft/vscode/pull/119160)
* [@aliasliao (Liao Jinyuan)](https://github.com/aliasliao): fix emmet issue where wrong suggestions is promoted in html file [PR #120480](https://github.com/microsoft/vscode/pull/120480)
* [@andrewbranch (Andrew Branch)](https://github.com/andrewbranch)
  * [typescript-language-features] Support import statement completions for TypeScript 4.3 [PR #119009](https://github.com/microsoft/vscode/pull/119009)
  * [html-language-features] Update TS language service call for 4.3 protocol [PR #120362](https://github.com/microsoft/vscode/pull/120362)
  * [typescript-language-features] Add telemetry for import statement completions [PR #121243](https://github.com/microsoft/vscode/pull/121243)
  * Fix missed Previewer.plain call due to adjacent @ts-expect-error [PR #121975](https://github.com/microsoft/vscode/pull/121975)
* [@anthony-c-martin (Anthony Martin)](https://github.com/anthony-c-martin): Update seti-ui file icons [PR #119468](https://github.com/microsoft/vscode/pull/119468)
* [@arnobl (arno)](https://github.com/arnobl): fix(notebook): redo cell creation does not re-select the cell [PR #120086](https://github.com/microsoft/vscode/pull/120086)
* [@awilkins (Adrian Wilkins)](https://github.com/awilkins): Implement selection paste for integrated terminal on Linux [PR #108397](https://github.com/microsoft/vscode/pull/108397)
* [@benmccann (Ben McCann)](https://github.com/benmccann): Fix issue number in comment [PR #121758](https://github.com/microsoft/vscode/pull/121758)
* [@bourdakos1 (Nick Bourdakos)](https://github.com/bourdakos1): Update seti icons [PR #120938](https://github.com/microsoft/vscode/pull/120938)
* [@CanadaHonk](https://github.com/CanadaHonk)
  * [themes] Fix JSON formatting in High Contrast Black default theme [PR #120761](https://github.com/microsoft/vscode/pull/120761)
  * [themes] Fix JSON formatting in High Contrast Black default theme (2) [PR #120815](https://github.com/microsoft/vscode/pull/120815)
  * [themes] Fix JSON formatting in High Contrast Black default theme (3) [PR #120841](https://github.com/microsoft/vscode/pull/120841)
* [@conwnet (netcon)](https://github.com/conwnet): fix: only add readonly attribute to textarea dom when editor is readonly [PR #120503](https://github.com/microsoft/vscode/pull/120503)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * fix #120485 hide `Open Timeline` from Explorer context menu when there are no timeline providers [PR #120497](https://github.com/microsoft/vscode/pull/120497)
  * fix-120578 don't offer callstack step-in/over/out when not stopped [PR #120581](https://github.com/microsoft/vscode/pull/120581)
  * fix #121509 documentation error for two API command results [PR #121511](https://github.com/microsoft/vscode/pull/121511)
* [@hantatsang (Sang)](https://github.com/hantatsang)
  * markdown-language-features: Fix Windows Preview Link Uri [PR #120090](https://github.com/microsoft/vscode/pull/120090)
  * Add emmet self closing abbreviation support [PR #120311](https://github.com/microsoft/vscode/pull/120311)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fix emmet completions in scss [PR #118666](https://github.com/microsoft/vscode/pull/118666)
  * Fix problems pane ignores `files.exclude` option [PR #119040](https://github.com/microsoft/vscode/pull/119040)
  * Fixes simple browser icons are broken in firefox [PR #120275](https://github.com/microsoft/vscode/pull/120275)
  * Fixes horizontal scrollbar is too narrow in split settings editor JSON [PR #121027](https://github.com/microsoft/vscode/pull/121027)
  * Fix regression cannot preview image with special characters '#', '%' or '?' [PR #121874](https://github.com/microsoft/vscode/pull/121874)
* [@Jolg42 (Joël Galeran)](https://github.com/Jolg42): Refactor npm extension to only use npm [PR #120317](https://github.com/microsoft/vscode/pull/120317)
* [@joyceerhl (Joyce Er)](https://github.com/joyceerhl): Add missing padding-bottom for notebook markdown cells [PR #122505](https://github.com/microsoft/vscode/pull/122505)
* [@KapitanOczywisty](https://github.com/KapitanOczywisty): Update PHP grammar [PR #120339](https://github.com/microsoft/vscode/pull/120339)
* [@Kingwl (Wenlu Wang)](https://github.com/Kingwl): Add next/prev conflicts [PR #97613](https://github.com/microsoft/vscode/pull/97613)
* [@leemun1 (Mike Lee)](https://github.com/leemun1): feat: add support for additional surrounding pairs for markdown (#119982) [PR #121698](https://github.com/microsoft/vscode/pull/121698)
* [@lf- (Jade)](https://github.com/lf-): Improve reporting of extension config schema validation errors [PR #120457](https://github.com/microsoft/vscode/pull/120457)
* [@limitedmage (Juliana Peña)](https://github.com/limitedmage): Fix race condition in showing and hiding suggestWidget [PR #121396](https://github.com/microsoft/vscode/pull/121396)
* [@LuisPeregrinaIBM2](https://github.com/LuisPeregrinaIBM2): wsl -l -q, removes header and default mark [PR #120382](https://github.com/microsoft/vscode/pull/120382)
* [@nan0tube (Yongming Lai)](https://github.com/nan0tube): Recognise commonly sourced files as shellscript [PR #121373](https://github.com/microsoft/vscode/pull/121373)
* [@plainerman (Michael Plainer)](https://github.com/plainerman): Add support to deactivate auto-updating disabled extensions [PR #113155](https://github.com/microsoft/vscode/pull/113155)
* [@PooyaZv (Pooya Vakeely)](https://github.com/PooyaZv): Add cuda-cpp lang id, grammar and problem matchers [PR #119444](https://github.com/microsoft/vscode/pull/119444)
* [@qchateau (Quentin Chateau)](https://github.com/qchateau): format modified: format lines with whitespace changes [PR #119021](https://github.com/microsoft/vscode/pull/119021)
* [@sdegutis (Steven Degutis)](https://github.com/sdegutis): Make error message clearer [PR #121339](https://github.com/microsoft/vscode/pull/121339)
* [@sokolovstas (Stanislav)](https://github.com/sokolovstas): Fix file location pattern in eslint-stylish problem matcher [PR #117576](https://github.com/microsoft/vscode/pull/117576)
* [@solomatov (Konstantin Solomatov)](https://github.com/solomatov): Terminate in case of ext host IPC timeout [PR #122399](https://github.com/microsoft/vscode/pull/122399)
* [@stoyannk (Stoyan Nikolov)](https://github.com/stoyannk): Fixed wrong variable name shadowing that caused Profiles for extensio… [PR #120054](https://github.com/microsoft/vscode/pull/120054)
* [@timmaffett (Tim Maffett)](https://github.com/timmaffett): Fixed and extended Debug Console support for most ANSI SGR codes [PR #120891](https://github.com/microsoft/vscode/pull/120891)
* [@vinku](https://github.com/vinku): Emmet: Select next/previous item does not work with script tag. [PR #118842](https://github.com/microsoft/vscode/pull/118842)
* [@wdesgardin (William Desgardin)](https://github.com/wdesgardin): feat: add typographer option for markdown preview [PR #119641](https://github.com/microsoft/vscode/pull/119641)
* [@yannickowow (Yanis HAMITI)](https://github.com/yannickowow): Show current DataBreakpoint access type in "Breakpoints" view (#119588) [PR #119817](https://github.com/microsoft/vscode/pull/119817)

Contributions to `vscode-eslint`:

* [@JoshuaKGoldberg (Josh Goldberg)](https://github.com/JoshuaKGoldberg): Implement eslint.rules.customizations - with overrides [PR #1164](https://github.com/microsoft/vscode-eslint/pull/1164)

Contributions to `vscode-json-languageservice`:

* [@KapitanOczywisty](https://github.com/KapitanOczywisty)
  * Allow case-insensitive modifier at the very beginning of the pattern [PR #97](https://github.com/microsoft/vscode-json-languageservice/pull/97)
  * Parse escaped refs [PR #98](https://github.com/microsoft/vscode-json-languageservice/pull/98)
* [@mifieldxu (Mifield)](https://github.com/mifieldxu): improved glob matching using minimatch [PR #93](https://github.com/microsoft/vscode-json-languageservice/pull/93)
* [@ssbarnea (Sorin Sbarnea)](https://github.com/ssbarnea): Upgrade dependencies [PR #95](https://github.com/microsoft/vscode-json-languageservice/pull/95)

Contributions to `vscode-vsce`:

* [@felipecrs (Felipe Santos)](https://github.com/felipecrs)
  * Do not ignore README.md [PR #556](https://github.com/microsoft/vscode-vsce/pull/556)
  * Add npm-shrinkwrap and webpack config to ignore list [PR #559](https://github.com/microsoft/vscode-vsce/pull/559)

Contributions to `debug-adapter-protocol`:

* [@jarpoole (Jared Poole)](https://github.com/jarpoole): ESP-IDF debug adapter [PR #190](https://github.com/microsoft/debug-adapter-protocol/pull/190)
* [@vadimcn](https://github.com/vadimcn): Add a direct reference to JSON schema [PR #185](https://github.com/microsoft/debug-adapter-protocol/pull/185)

Contributions to `vscode-js-debug`:

* [@millerds (Darren Miller)](https://github.com/millerds): String support chakracore [PR #979](https://github.com/microsoft/vscode-js-debug/pull/979)
* [@swissmanu (Manuel Alabor)](https://github.com/swissmanu): CDP Proxy: Allows Other Extensions to Reuse CDP Connection [PR #964](https://github.com/microsoft/vscode-js-debug/pull/964)

Contributions to `vscode-generator-code`:

* [@felipecrs (Felipe Santos)](https://github.com/felipecrs): Hide dist folder from search results [PR #260](https://github.com/microsoft/vscode-generator-code/pull/260)
* [@miles-crighton (Miles Crighton)](https://github.com/miles-crighton): fix: Update token inspection command [PR #249](https://github.com/microsoft/vscode-generator-code/pull/249)
* [@noah-vdv (Noah van der Veer)](https://github.com/noah-vdv): fix 'orther' typo on line 162 [PR #255](https://github.com/microsoft/vscode-generator-code/pull/255)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
