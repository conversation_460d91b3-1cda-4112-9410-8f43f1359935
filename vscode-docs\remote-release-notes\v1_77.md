# March 2023 (version 1.77)

## Core

### Install additional built-in extensions via VSIX

VS Code Server now supports installing additional built-in extensions via VSIX. This is useful if the remote setup has VSIX files already available and you want to install them without having to reach out to the Marketplace, thereby improving the startup time. This helped GitHub Codespaces startup to be faster by ~125%.

## Tunnels

### Continue Working on in Desktop

When connected to a tunnel in vscode.dev, you can continue in VS Code Desktop with the **Continue Working in VS Code Desktop** command. The command can be found in the remote menu and the Command Palette.

![Continue Working in VS Code Desktop](images/1_77/tunnel-open-in-desktop.png)

### Singleton tunnel instances

Multiple instances of tunnels running on a machine will be deduplicated. Additional requests to **Turn on Tunnel Access** in the VS Code UI, or running `code tunnel` on the command line, monitor any existing running tunnel. These monitoring processes still allow you to stop or restart the tunnel by entering "x" or "r" respectively.

![Screenshot of terminal showing the output of the "monitoring" process](images/1_77/remote-tunnel-singleton.png)

## WSL (version 0.77.x)

The settings `remote.WSL1.connectionMethod` and `remote.WSL2.connectionMethod` have been removed. The WSL extension now always uses the previous default `wslExeProxy`. We are not aware of any problems with that connection method. Please file issues against [vscode-remote-release](https://github.com/microsoft/vscode-remote-release/issues) if the new method doesn't work for you.

## Dev Containers (version 0.288.x)

A Dev Container lets you use a container as a full-featured development environment. The [Dev Container Specification](https://containers.dev/) seeks to find ways to enrich existing formats with common development specific settings, tools, and configuration while still providing a simplified, un-orchestrated single container option.

You can learn more about dev containers and their spec in a [new episode of the Changelog podcast](https://changelog.com/podcast/529).

### Clean up Dev Containers and volumes

The **Dev Containers: Clean Up Dev Containers...** and **Dev Containers: Clean Up Dev Volumes...** commands let you pick which of the stopped containers and dangling volumes to remove.

![Clean Up Dev Containers and Volumes](images/1_77/clean-up-containers-and-volumes.png)

### Remove legacy Maven, Gradle and JupyterLab features

We keep working towards replacing the built-in legacy features with their updated counterparts in the contributable container features. The legacy `maven`, `gradle` and `jupyterlab` features are now mapped to the corresponding options on the `ghcr.io/devcontainers/features/java` and `ghcr.io/devcontainers/features/python` features.

For more information on contributing container features, see the [Dev Container Features](https://containers.dev/implementors/features) documentation.

### Remove dependence on built-in repository configurations

We have dropped support for the few built-in Dev Container configurations that were sourced from the [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers/tree/main/repository-containers/github.com) repository and remain available there.
