# March 2020 (version 1.44)

## Containers

### Checkout a Pull Request or Branch in an isolated container

[Repository Containers](https://code.visualstudio.com/docs/devcontainers/containers#_quick-start-open-a-git-repository-in-an-isolated-container-volume) have been supported for several releases.
These containers use isolated, local [Docker volumes](https://docs.docker.com/storage/volumes/) instead binding to the local filesystem. In addition to not polluting your file tree, local volumes have the added benefit of improved performance on Windows and macOS. In this release, we have added support to checkout a Pull Request directly into a repository container. You can checkout a Pull Request into a container using:

* The **Dev Containers: Clone Repository in Container Volume** command and entering the corresponding Pull Request, for example `https://github.com/microsoft/vscode-npm-scripts/pull/111`.
* The [GitHub Pull Request Extension](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github), where you can select the PR and use the command **Checkout Pull Request in Dev Container**.

![Checkout Pull Request](images/1_44/checkout-pr.png)

When opening a Pull Request into a development container, the GitHub Pull Request extension is automatically installed into the container and the Pull Request is checked out, ready for you to review. You can switch to the PR description from the Status Bar:

![Checkout Pull Request Status Bar](images/1_44/checkout-pr-status.png)

If the repository does not yet contain a dev container configuration, you will be prompted to select one and it will be added.

There is support to quickly create a unique volume for a PR review. You will be prompted to remove the unique volume when removing the container from the Remote Explorer.

![Use a unique volume](images/1_44/checkout-unique.png)

In addition to entering the GitHub URLs for Pull Requests, you can also enter the URLs for branches and commits. In this case, the corresponding branch or commit will be checked out when creating the container.

>**Note**: Opening a PR from a private repository is currently only supported when using the VS Code Insiders version.

### Kubernetes Support

You can now attach to a container in Kubernetes. When the [Kubernetes extension](https://marketplace.visualstudio.com/items?itemName=ms-kubernetes-tools.vscode-kubernetes-tools) is installed together with the Dev Containers extension, there is a new context menu entry on **Pods** items:

![Attach to Kubernetes Container](images/1_44/k8s-attach.png)

## Python pre-built container images

You can now use a set of pre-built Python 3, 3.6, 3.7, and 3.8 development container images, which include common Python utilities (for example, pylint) and dependencies, such as [Git](https://git-scm.com/), [zsh](https://en.wikipedia.org/wiki/Z_shell), and [Oh My Zsh!](https://ohmyz.sh/). Their README and sources are available in the [vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers/tree/main/containers/python-3) repository.

These same images are used when you select the related language / platform by using the **Dev Containers: Add Dev Container Configuration Files** or **Dev Containers: Open Folder in Container** / **Dev Containers: Reopen in Container** commands from the Command Palette (`kbstyle(F1)`) on an empty folder.

Check out our [Docker Hub page](https://hub.docker.com/_/microsoft-vscode-devcontainers) for a list of other available images.

### Support user-supplied project name

When using Docker-Compose for your devcontainer, you can now specify a project name by environment variable (`COMPOSE_PROJECT_NAME`) or `.env` file in the project folder. This aligns the Dev Containers extension with the existing functionality in Docker-Compose.

### Improved progress indication

Progress indication during startup has been simplified and the link to open the log terminal is more prominent:

![Progress notification](images/1_44/devcontainer-progress.png)

### Progress on WSL and WSL 2 support

Progress has been made towards supporting Docker inside WSL and WSL 2. There is an experimental setting for the adventurous if you want to give it a try. [Stuart Leeks (@stuartleeks)](https://github.com/stuartleeks) submitted the initial PR and has an excellent [blog post](https://stuartleeks.com/posts/vscode-devcontainers-wsl/) about using the experimental setting.

### Engineering

We no longer depend on a port/socket to the Docker daemon being available, but instead run all commands through the Docker CLI.
