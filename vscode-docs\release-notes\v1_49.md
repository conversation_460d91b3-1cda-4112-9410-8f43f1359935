---
Order:
TOCTitle: August 2020
PageTitle: Visual Studio Code August 2020
MetaDescription: Learn what is new in the Visual Studio Code August 2020 Release (1.49)
MetaSocialImage: 1_49/release-highlights.png
Date: 2020-9-10
DownloadVersion: 1.49.3
---
# August 2020 (version 1.49)

**Update 1.49.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2020+Recovery%22+is%3Aclosed+).

**Update 1.49.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2020+Recovery+2%22+is%3Aclosed+).

**Update 1.49.3**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22August+2020+Recovery+3%22+is%3Aclosed+).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the August 2020 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Format modified text](#only-format-modified-text)** - Limit formatting to just the code you've changed.
* **[Change casing on Search and Replace](#case-changing-in-regex-replace)** - Change text casing during global Search and Replace.
* **[Source Control repositories view](#source-control-repositories-view)** - Display pending changes per repository.
* **[Filter Debug Console output](#debug-console-filter)** - Quickly filter and find debugging output.
* **[Improved JS debugger Auto Attach](#javascript-debugger-improvements)** - "Smart" Auto Attach to Node.js scripts or test runners.
* **[TypeScript optional chaining refactoring](#convert-to-optional-chain-refactoring)** - Convert multiple checks to a concise optional chain.
* **[JSDoc @deprecated tag support](#deprecated-tag-support-for-javascript-and-typescript)** - IntelliSense clearly shows APIs marked as deprecated.
* **[Notebook UX updates](#notebooks)** - Cell Status bar contributions, enhanced notebook diff editor.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/events) on Wednesday, September 16 at 9am Pacific (5pm London), to see a demo of what's new in this release and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available. And for the latest Visual Studio Code news, updates, and content, follow us on Twitter [@code](https://twitter.com/code)!

## Editor

### Only format modified text

When contributing to another project, the **Format on Save** and **Format Document** commands can cause unwanted updates because they format lines that you have not changed. This behavior can result in large pull requests, which are hard to review, and touch code you didn't intend to modify. There is now a new feature to help limit unwanted changes:

* We added a new command **Format Modified Lines**, which allows you to only format new and changed lines.
* There is a new setting `editor.formatOnSaveMode`, which makes **Format on Save** only apply to modified lines.

Note that modifications are defined by your source control provider, for example, changes as detected by Git, and that without changes in source control, no modified lines are formatted. The Quick Diff indicator on the left side of the editor is a reliable hint for what lines will be formatted.

![Format only modified text](images/1_49/format_modified.gif)

This also requires that the active formatter supports to formatting only a portion of a document, which most popular formatters do.

### Format multiple selections

The **Format Selection** command is now multi-cursor aware. Select multiple lines or statements, trigger **Format Selection**, and formatting will be applied to all sections.

### Find cursor move on type control

When typing in the Find input box, VS Code automatically searches and moves the cursor to the nearest find result. You can now control if the cursor should jump to the find matches while typing by configuring `editor.find.cursorMoveOnType`. When disabled, VS Code will only move the cursor when you press `Enter/Shift+Enter`.

Below, when searching for the word 'bash', the cursor doesn't jump to `bash_completion` until `kbstyle(Enter)` is pressed:

![Disable editor.find.cursorMoveOnType](images/1_49/editor-find-cursorMoveOnType.gif)

### Only show trailing whitespace

There is a new option `trailing` for the editor setting `editor.renderWhitespace` to only show trailing whitespace.

```json
"editor.renderWhitespace": "trailing"
```

### Numeric font-weight values

Numeric value support was added to the editor setting `editor.fontWeight`, which now accepts values in the range from 1 to 1000, in addition to the string values `"normal"` and `"bold"`.

```json
"editor.fontWeight": 350
```

## Workbench

### Case changing in regex replace

In June, we added support for changing the case of regex matching groups while doing a Find/Replace in the editor. This month, we've added the same feature for Search/Replace across the workspace. This is done with the modifiers `\u\U\l\L`, where `\u` and `\l` will upper/lowercase a single character, and `\U` and `\L` will upper/lowercase the rest of the matching group.

### Improved keybinding diagnostics

To troubleshoot keybinding problems, there is now a command **Developer: Toggle Keyboard Shortcuts Troubleshooting**. This will activate logging of dispatched keyboard shortcuts and will open an output panel with the corresponding log file.

You can then press your desired keybinding and check what keyboard shortcut VS Code detects and what command is invoked.

For example, when pressing `cmd+/` in a code editor on macOS, the logging output would be:

```
[KeybindingService]: / Received  keydown event - modifiers: [meta], code: MetaLeft, keyCode: 91, key: Meta
[KeybindingService]: | Converted keydown event - modifiers: [meta], code: MetaLeft, keyCode: 57 ('Meta')
[KeybindingService]: \ Keyboard event cannot be dispatched.
[KeybindingService]: / Received  keydown event - modifiers: [meta], code: Slash, keyCode: 191, key: /
[KeybindingService]: | Converted keydown event - modifiers: [meta], code: Slash, keyCode: 85 ('/')
[KeybindingService]: | Resolving meta+[Slash]
[KeybindingService]: \ From 2 keybinding entries, matched editor.action.commentLine, when: editorTextFocus && !editorReadonly, source: built-in.
```

The first keydown event is for the `MetaLeft` key (`cmd`) and cannot be dispatched. The second keydown event is for the `Slash` key (`/`) and is dispatched as `meta+[Slash]`. There were two keybinding entries mapped from `meta+[Slash]` and the one that matched was for the command `editor.action.commentLine`, which has the `when` condition `editorTextFocus && !editorReadonly` and is a built-in keybinding entry.

### Screencast mode customization

There are several new settings so you can tune your screencast sessions:

* `screencastMode.keyboardOverlayTimeout` - Change the timeout (milliseconds) for the keyboard shortcut overlay.
* `screencastMode.mouseIndicatorColor` - Set the mouse indicator color (hex #RGB, #RGBA, #RRGGBB or #RRGGBBAA).
* `screencastMode.mouseIndicatorSize` - Control the mouse indicator size (pixels).

## Source Control

### Source Control Repositories view

The Source Control Repositories view (formerly known as Source Control Providers view) was reintroduced to allow finer grain control over which source control repositories should be visible in the Source Control view. As before, this view supports multiple selection so you can have multiple repositories visible at the same time.

![Source Control repository view](images/1_49/scm-repositories.gif)

## Debugging

### Debug Console filter

The Debug Console now supports filtering, making it easier for users to find the output they are looking for, or to hide irrelevant logging output. The filter also supports exclude patterns (for example, patterns starting with an exclamation mark `!`). The filter only applies to program output but not to evaluations run by a user. Like other input boxes in VS Code, you can use the `up` and `down` arrow keys to navigate between old filter inputs.

In the short video below, the output is filtered to entries that include the text 'http', do not include the text 'http' (using the filter `!http`), and then include the word 'Severity':

![Debug Console filtering](images/1_49/filter.gif)

### UX improvements

* When debugging multiple sessions, VS Code now only renders the arrow in the glyph margin for the focused session. This should make it easier for users to distinguish which editor Call Stack decoration belongs to the debug session the user is interested in.
* Large stack frames now show a **Load All Stack Frames** button to load all the remaining stack frames. Previously this was a **Load More Stack Frames** button, which would require multiple clicks for large stack traces.

  ![Load All Stack Frames](images/1_49/load-all.png)

### JavaScript debugger improvements

**Improved Auto Attach**

[Auto Attach](https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_auto-attach-feature) can now debug processes even without manually passing an `--inspect` flag to them. When Auto Attach is turned on, by default VS Code uses its "smart" mode to debug any Node.js script outside of `node_modules` folders, as well as some common 'runner' tools such as mocha and ts-node.

![Image showing VS Code debugging a Node.js process launched from a terminal without an "--inspect" flag](images/1_49/auto-attach-no-inspect.png)

*Theme: [Codesong Theme](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong)*

You can switch back to the old behavior, or choose to debug **all** Node.js scripts, using the **debug.javascript.autoAttachFilter** setting. If you want to change when the "smart" mode attaches, you can configure it in the **debug.javascript.autoAttachSmartPattern** setting.

As part of this, Auto Attach also now works with terminal multiplexers such as `tmux`, provided they inherit the session's environment variables.

**Sourcemap handling in logged errors**

Previously, the stack trace when logging a JavaScript error like `console.log(error)` would show the locations in your compiled code. If sourcemaps are available, VS Code will now process the stack trace to use the source locations. This is useful when debugging web apps that use bundlers like Webpack.

![Image shows a beautified stack trace from a React app](images/1_49/error-stacktraces.png)

Previously, a stack trace like this would have referenced links in the compiled `bundle.js` file.

**Notable improvements**

* Previously, VS Code aggressively cached sourcemaps in a way that interfered with some types of tooling, such as Angular's `ng serve` and Nest.js `nest start --watch`. This is now fixed.
* When working in a remote (SSH, Containers, or WSL), VS Code did not support attaching to browsers already running locally; this is now fixed.
* You can now choose to stop if a conditional breakpoint throws an error by toggling the **debug.javascript.breakOnConditionalError** setting.

## Languages

### TypeScript 4.0

VS Code now ships with TypeScript 4.0.2. This major update brings support for new language features such as [variadic tuple types](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0/#variadic-tuple-types), along with editor tooling enhancements and many bug fixes.

You can read more about TypeScript 4.0 [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0/).

### Convert to optional chain refactoring

Use the new `convert to optional chain` refactoring to quickly convert a sequence of expressions such as `a && a.b && a.b.c()` to instead use optional chaining: `a?.b?.c()`. Just select an expression to use it:

![Converting an expression sequence to an optional chain](images/1_49/ts-convert-chain.gif)

Many older codebases have a lot of repetitive null checks that optional chaining can simplify. Now it's easier than ever to automate some of this cleanup.

### Deprecated tag support for JavaScript and TypeScript

VS Code now supports the `@deprecated` JSDoc tag in JavaScript and TypeScript files. To mark an API as deprecated, simply add a `@deprecated` JSDoc comment:

```ts
/**
 * @deprecated Use `getMode()` instead.
 */
export function mode() { ... }
```

VS Code renders IntelliSense suggestions for deprecated properties with a strike through (see the `show` and `hide` suggestions below for example):

![Example of deprecated suggestions](images/1_49/ts-deprecated-suggest.png)

To help you detect usage of deprecated API in your codebase, VS Code will also add a strike through to any references to deprecated APIs:

![A deprecated API with a strike through](images/1_49/ts-deprecated-warning.png)

Disable these strike throughs by setting `"editor.showDeprecated": false`.

### Refactoring disabled reasons

VS Code can now show why a requested JavaScript/TypeScript refactoring cannot be applied:

![Trying to extract a function with a 'continue' statement](images/1_49/ts-refactor-reason.png)

These reasons are shown when you try to [apply a refactoring with a keybinding](https://code.visualstudio.com/docs/editing/refactoring#_keybindings-for-code-actions).

### Smarter auto imports

We are now better at searching your installed npm packages for potential JavaScript and TypeScript auto imports. Packages that are listed in your `package.json` dependencies are now scanned for imports even if they are not yet referenced anywhere in your codebase. This can be especially helpful after you've installed a new package and want to start using it.

You can read more about the improved auto imports behavior [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-0/#smarter-auto-imports).

### Partial IntelliSense during project loads

With TypeScript 4.0, VS Code can now provide IntelliSense and other more advanced language features while a large project is still loading. This should allow you to start working with your code faster.

To explain how this feature works, you first need to understand how VS Code worked previously. When you open a JavaScript or TypeScript file in VS Code, the TypeScript language service tries to load the project that file belongs to. Sometimes this project can be quite large, such as with the core VS Code codebase, which consists of many thousands of TypeScript files. While a project is being loaded, VS Code is able to handle basic syntax operations—such as getting the document outline and enabling code folding—but it previously could not provide IntelliSense or other more advanced language features because they depend on understanding the entire project.

Thanks to TypeScript 4.0, VS Code can offer limited IntelliSense while a large project is still loading. However the IntelliSense provided is limited to using the current file, instead of the entire project. This means that features such as suggestions and **Go to Definition** will work, but suggestions are only shown from the current file and you can only run **Go to Definition** to jump to another symbol in the current file. You also will not get features such as auto import since that relies on VS Code processing the entire project.

Partial mode IntelliSense will only be used while a project is loading. Once the project as loaded, VS Code will automatically switch over to full IntelliSense. If you work with large JavaScript or TypeScript projects, partial IntelliSense should let you start working with your code almost instantly until more complete, project-wide IntelliSense becomes available.

### HTML tag rename on type

There were several bug and performance fixes for **Rename on Type** in HTML files. When modifying a tag, the feature automatically renames the matching HTML tag. The feature is optional and can be enabled by setting: `"editor.renameOnType": true`.

## Contributions to extensions

### Notebooks

**Improve cell execution order label discoverability**

Previously, the cell execute button would take the place of the execution order label **[#]** when hovering or selecting a cell. This made it impossible to see the execution order label while executing a cell. We have moved it to be below the execute button, aligned with the base of the editor. This should make it easier to find.

In this example, the cell execution order label displays **[6]**:

![Cell execution order](images/1_49/cell_execution_order.png)

**Customize cell appearance**

We've added two settings that let users customize the cell appearance:

* `notebook.cellToolbarLocation`: Whether the toolbar should be shown on the left, the right, or hidden.
* `notebook.showCellStatusBar`: Whether or not the cell Status bar should be visible.

**Cell Status bar item contributions**

It's now possible for extensions to contribute custom items to the cell Status bar, similarly to the VS Code Status bar. These items can be buttons that trigger commands, or plain text items.

As an example, our GitHub issues notebook extension now contributes the **Open X results** command as a Status bar item:

![Cell Status bar item](images/1_49/cell_status_bar_item.png)

**Enhanced text diff editor**

In this release, we added a new type of diff editor for viewing changes in notebook documents. Traditional notebooks are stored in JSON format, which is easy to parse but not effective for diffing since most diff algorithms on text files are line-based. To improve this experience, we built an enhanced diff editor, which is aware of the concept of notebook documents and cells to help users quickly identify what cell content or metadata are changed.

![Enhanced notebook text diff](images/1_49/notebook-enhanced-text-diff.gif)

*Theme: [GitHub Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

You can still switch to regular text diff editor temporarily from the editor action tool bar or disable this feature by turning off the setting `notebook.diff.enablePreview`.

![Switch to regular text diff editor](images/1_49/notebook-switch-to-regular-text-diff.gif)

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.49 include:

* Automatic port forwarding: Ports detected in terminal output are automatically forwarded to the client.
* Welcome views: All Remote extensions include a new Welcome experience to help you get started.
* Dev Containers: You can now directly clone a repository from GitHub into a container volume.

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_49.md).

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. One useful update in this release is being able to track multiple repositories, similar to how the build-in Git extension works.

To learn about all the new features and updates, you can see the full [changelog for the 0.20.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0200) release of the extension.

## Extension authoring

### Terminal link providers

The terminal link provider API is now stable. This API enables language/lint extensions to handle more obscure link formats that are not already supported and even support language-specific behavior.

Take the following Java exception for example:

```
Exception in thread "main" java.lang.ArrayIndexOutOfBoundsException: 9
    at MyProgram.main(MyFile.java:10)
```

Using terminal link providers, it is now possible to have a link for `java.lang.ArrayIndexOutOfBoundsException` that goes to API documentation and create links for the symbols `MyProgram` or `main`.

The following example implementation creates a link for the first instance of the word `"test"` on each line:

```ts
window.registerTerminalLinkProvider({
    provideTerminalLinks: (context, token) => {
        // Detect the first instance of the word "test" if it exists and linkify it
        const startIndex = (context.line as string).indexOf('test');
        if (startIndex === -1) {
            return [];
        }
        // Return an array of link results, this example only returns a single link
        return [
            {
                startIndex,
                length: 'test'.length,
                tooltip: 'Show a notification',
                // You can return data in this object to access inside handleTerminalLink
                data: 'Example data'
            }
        ];
    },
    handleTerminalLink: (link: any) => {
        vscode.window.showInformationMessage(`Link activated (data = ${link.data})`);
    }
});
```

### Debug extension APIs

* **DebugAdapterNamedPipeServer**: VS Code offers three different ways of communication with Debug Adapters: via direct method calls, stdin/stdout, or TCP sockets. In this milestone, we've added a fourth type: via "named pipes". Our [debugger sample "Mock Debug"](https://github.com/microsoft/vscode-mock-debug/blob/f4b0e37cfd0cb1653c82a26bdab4910c87489713/src/extension.ts#L215-L244) shows how to use the `DebugAdapterNamedPipeServer`.

* **Changed timing of 'preLaunchTask'**: Before a debug session starts, the debug configuration's `preLaunchTask` is now launched in between the `resolveDebugConfiguration` and the `resolveDebugConfigurationWithSubstitutedVariables` calls of a `DebugConfigurationProvider`. Previously, it was launched **after** the `resolveDebugConfigurationWithSubstitutedVariables` call. For details, see [issue #95162](https://github.com/microsoft/vscode/issues/95162).

* **Access Debug Adapter Protocol breakpoints**: VS Code breakpoints are independent from debug adapters and they exist outside of debug sessions. When a debug session starts, VS Code breakpoints are registered with the debug adapter of the debug session and VS Code receives Debug Adapter Protocol (DAP) breakpoints in return. That means one VS Code breakpoint maps to zero, one, or more DAP breakpoints (depending on the number of concurrently active debug sessions). VS Code maintains a mapping between its breakpoints and corresponding DAP breakpoints. In this milestone, we've added an API for accessing the mapping: the method `DebugSession.getDebugProtocolBreakpoint` returns a DAP breakpoint for a given VS Code breakpoint.

* **Finalized APIs**: Three proposed extension APIs for debugging have been finalized and can now be used in extensions published on the Marketplace:

  * The function `vscode.debug.stopDebugging` for stopping a single specific or all debug sessions. With this new API, it is no longer necessary to use the less predictable **Debug: Stop** command (command ID: `workbench.action.debug.stop`) which only stops the "active" debug session.
  * The `noDebug` property of `DebugSessionOptions` controls whether the session about to start should run with or without debugging.
  * The `compact` property of `DebugSessionOptions` controls whether the parent of the newly created debug session will be hidden in the CALL STACK view as long as it only has a single child.

### Task custom execution variable resolving

The API to resolve variables for custom execution tasks has been finalized. Variables in a custom execution task's definition are resolved and then passed into the task's callback.

```typescript
export class CustomExecution {
    /**
     * Constructs a CustomExecution task object. The callback will be executed the task is run, at which point the
     * extension should return the Pseudoterminal it will "run in". The task should wait to do further execution until
     * [Pseudoterminal.open](#Pseudoterminal.open) is called. Task cancellation should be handled using
     * [Pseudoterminal.close](#Pseudoterminal.close). When the task is complete fire
     * [Pseudoterminal.onDidClose](#Pseudoterminal.onDidClose).
     * @param process The [Pseudoterminal](#Pseudoterminal) to be used by the task to display output.
     * @param callback The callback that will be called when the task is started by a user.
     */
    constructor(callback: (resolvedDefinition: TaskDefinition) => Thenable<Pseudoterminal>);
}
```

### Task detail

The task `detail` API, which is used to show additional information about a task in the task Quick Pick, has been finalized.

![Task Detail shown](images/1_49/task-detail.png)

### 'in' operator for 'when' clauses

There is a new `in` operator for `when` clauses. This new operator allows for a dynamic lookup of a context key's value within another context key's value. For example, if you wanted to add a context menu command to folders that contain a certain type of file (or something that can't be statically known), you can now use the `in` operator to achieve it.

First, determine which folders should support the command, and the folder name to an array. Then, use the `setContext` command to turn the array into a context key:

```ts
vscode.commands.executeCommand('setContext', 'ext:supportedFolders', [ 'test', 'foo', 'bar' ]);

// or

// Note in this case (using an object), the value doesn't matter, it is based on the existence of the key in the object
vscode.commands.executeCommand('setContext', 'ext:supportedFolders', { 'test': true, 'foo': 'anything', 'bar': false });
```

Then, in the `package.json` you could add a menu contribution for the `explorer/context` menu:

```json
// Note, this assumes you have already defined a command called ext.doSpecial
"menus": {
  "explorer/context": [
    {
      "command": "ext.doSpecial",
      "when": "explorerResourceIsFolder && resourceFilename in ext:supportedFolders"
    }
  ]
}
```

In that example, we are taking the value of `resourceFilename` (which is the name of the folder in this case) and checking for its existence in the value of `ext:supportedFolders`. If it exists, the menu will be shown. This powerful operator should allow for richer conditional and dynamic contributions that support `when` clauses, for example menus, views, etc.

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

Note that you cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Source Control Resource State context value

Instances of `SourceControlResourceState` now support an optional `contextValue: string` field which allows defining a context value to match again `when` clauses in the `scm/resourceState/context` menu. The context key to use is `scmResourceState`. More details on the original [feature request](https://github.com/microsoft/vscode/issues/86180) and respective [pull request](https://github.com/microsoft/vscode/pull/90952).

### Contributable context menu for Variables view

We've added command contribution support for the context menu in the Variables view. This makes it possible to implement frequently requested features like variable formatting (for example **View as Hex**) or viewing variables in custom data views:

![Java variables context menu](images/1_49/java-variables.png)

When a registered context menu command is executed, both the underlying variable and its container are passed as Debug Adapter Protocol (DAP) objects. Please note that VS Code's extension API uses opaque stand-in types instead of the real DAP types. In order to access their properties, they can be easily coerced into the corresponding DAP types.

Menu contributions for the Variables view are identified by a `"debug/variables/context"` key. General information about menu contributions can be found in the [contributions points documentation](https://code.visualstudio.com/api/references/contribution-points#contributes.menus).

To help with structuring menu commands into meaningful groups, we have added some predefined groups:

* `navigation`: Commands related to navigation across VS Code. This group always comes first and is currently empty by default.
* `1_view`: Commands related to displaying variables in different view formats. Currently empty by default.
* `3_modifications`:  Commands related to modifications of variables. Currently home of the **Set Value** command.
* `5_cutcopypaste`: Commands related to cutting, copying and pasting of variables. Currently home of the **Copy Value** and **Copy as Expression** commands.
* `z_commands`: Other commands that do not belong to the above categories. This group comes last and currently contains the **Add to Watch** and **Break on Value changes** commands.

Typically, menu contributions in the Variables view should only apply to a specific debug type. This can be easily achieved by adding a `when` clause to the contribution. Here is one for Java:

```js
"when": "debugConfigurationType == 'java'"
```

Since some menu contributions should only be available for variables of a specific kind, we have introduced a new optional context key `debugProtocolVariableMenuContext`, which receives its value from a property `__vscodeVariableMenuContext` of the underlying DAP type `Variable` (please note that the `__vscodeVariableMenuContext` property is not part of the DAP specification, because it is specific to VS Code and its menu contribution support).

This makes it possible to show an array related menu command only on variables where a property `__vscodeVariableMenuContext` got a value `'array'` from the debug adapter:

```js
  "contributes": {
    "menus": {
      "debug/variables/context": [
        {
          "command": "variables-view.showArrayAsCustomDataView",
          "when": "debugConfigurationType == 'java' &&
                     debugProtocolVariableMenuContext == 'array'"
        }
      ]
    }
  }
```

### Webview Views

The newly proposed [Webview View API](https://github.com/microsoft/vscode/issues/46585) allows extensions to contribute webview based views to the sidebar of panel. These views can offer specialized presentations of data, more advanced user interfaces, and much more.

![The 'calico colors' webview view sample extension in the side bar](images/1_49/webview-view.png)

Check out the [webview view extension sample](https://github.com/microsoft/vscode-extension-samples/tree/main/webview-view-sample) to get started, and let us know if you have any feedback about the new API.

## Engineering

### Electron 9.0 Update

In this milestone, we finished the exploration to bundle Electron 9 into VS Code. This is a major Electron release and comes with Chromium 83.0.4103.122 and Node.js 12.14.1.

### Disabling crash reporter

Due to architectural changes for the crash reporter from Electron, the preferred way to disable the crash reporter in VS Code has been changed and is now configured via the `enable-crash-reporter` runtime argument. The existing value for the user setting `telemetry.enableCrashReporter` will be copied over to this runtime argument to ease the transition.

* Open the Command Palette (`kb(workbench.action.showCommands)`).
* Run the **Preferences: Configure Runtime Arguments** command.
* This command will open a `argv.json` file to configure runtime arguments.
* Edit `"enable-crash-reporter": false`.
* Restart VS Code.

### Web Playground moved to a separate repository

The extension that drives our [online web playground](https://vscode-web-test-playground.azurewebsites.net/), as well as `yarn web` when running out of sources, has moved into its own [repository](https://github.com/microsoft/vscode-web-playground).

### Web Playground support for different versions

Our [online web playground](https://vscode-web-test-playground.azurewebsites.net/) is now capable of running not only the latest Insiders version of VS Code, but also the Stable version or any released version by commit.

New query parameters allow you to drive this:

* `quality=<stable|insider>`
* `commit=<commit-sha>`

**Note:** If you specify a `commit`, you must also specify it's associated `quality`.

### Minimal sandboxed workbench

We continue to explore enabling Electron's `sandbox` option in VS Code. During this milestone, we worked on being able to bring up a minimal VS Code instance that is fully sandboxed by shimming various internals that today still require Node.js access. This will be a multi-milestone journey and you can track progress in [issue #92164](https://github.com/microsoft/vscode/issues/92164).

### Making VS Code Trusted Types compliant

We have started an effort to make VS Code "Trusted Types" compliant. The goal is to prevent DOM-based cross site scripting vulnerabilities. You can learn more about trusted types at the web.dev [Trusted Types site](https://web.dev/trusted-types) and follow our progress in [issue #103699](https://github.com/microsoft/vscode/issues/103699).

### New version of Marked library

We picked up a new version of the [Marked](https://marked.js.org) Markdown processing library. This library is used for VS Code's internal Markdown rendering, such as in hovers and IntelliSense. The update includes important bug fixes and improved support for more complicated Markdown patterns.

## Notable fixes

* [38137](https://github.com/microsoft/vscode/issues/38137): Can't call sendText API or paste into terminal with long text
* [82604](https://github.com/microsoft/vscode/issues/82604): Web: editor does not provide a "Paste" action
* [101110](https://github.com/microsoft/vscode/issues/101110): Allow to configure workbench.editor.restoreViewState per language
* [102199](https://github.com/microsoft/vscode/issues/102199): JavaScript debugging: run to cursor can skip to next statement if cursor is after semicolon
* [103052](https://github.com/microsoft/vscode/issues/103052): Order of files in "Search files by name" results is incorrect
* [103105](https://github.com/microsoft/vscode/issues/103105): Debug: timeout after 1000 ms
* [103414](https://github.com/microsoft/vscode/issues/103414): Debug: better support when views are moved out of debug viewlet
* [105481](https://github.com/microsoft/vscode/issues/105481): macOS: detect high contrast theme

## Thank you

Last but certainly not least, a big *__Thank You__* to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

* [Aditya Thakral (@9at8)](https://github.com/9at8): Silently ignore the screen size is bogus error in the Process Explorer [PR #104277](https://github.com/microsoft/vscode/pull/104277)
* [Alexey Schebelev (@AlexxNB)](https://github.com/AlexxNB): Add *.xht file extension [PR #103560](https://github.com/microsoft/vscode/pull/103560)
* [@amagupta-collab](https://github.com/amagupta-collab): empty-line-wrap-fix [PR #103677](https://github.com/microsoft/vscode/pull/103677)
* [Andrew Maust (@andrew-maust)](https://github.com/andrew-maust): fixes #104059 [PR #104193](https://github.com/microsoft/vscode/pull/104193)
* [Anna Kramarska (@annkamsk)](https://github.com/annkamsk)
  * issueReporter: Use DOM API instead of string concatenation [PR #104644](https://github.com/microsoft/vscode/pull/104644)
  * Replace `innerHTML` with `innerText` when assigning non-html string [PR #103841](https://github.com/microsoft/vscode/pull/103841)
  * Create HTML with dom.ts#$ function calls instead of string concatenation [PR #105510](https://github.com/microsoft/vscode/pull/105510)
  * Add script for running tsec [PR #105748](https://github.com/microsoft/vscode/pull/105748)
  * Make renderCodicons function return HTMLElement instead of string [PR #105589](https://github.com/microsoft/vscode/pull/105589)
  * Set HTMLElement's content by providing it as a child, not by .innerHTML [PR #105506](https://github.com/microsoft/vscode/pull/105506)
  * Create HTML with dom.ts#$ function calls instead of string concatenation [PR #105502](https://github.com/microsoft/vscode/pull/105502)
* [Arthur Kushka (@arhelmus)](https://github.com/arhelmus): Implemented filter for debug console output [PR #102704](https://github.com/microsoft/vscode/pull/102704)
* [Brian Malehorn (@bmalehorn)](https://github.com/bmalehorn): Running Extensions: sort slowest ⟶ fastest [PR #103336](https://github.com/microsoft/vscode/pull/103336)
* [Ciprian Florescu (@cipacda)](https://github.com/cipacda): Make mtk classes more specific to avoid CSS conflicts [PR #103485](https://github.com/microsoft/vscode/pull/103485)
* [Chuang Yu (@cyyyu)](https://github.com/cyyyu): fix #102718, add formatting indicator [PR #103951](https://github.com/microsoft/vscode/pull/103951)
* [陈大大哦了 (@d1y)](https://github.com/d1y): add Custom color and size [PR #105103](https://github.com/microsoft/vscode/pull/105103)
* [@danecreekphotography](https://github.com/danecreekphotography): 105160: Support configuring keyboard overlay timeout + mouse indicator color/size [PR #105219](https://github.com/microsoft/vscode/pull/105219)
* [Daybrush (Younkue Choi) (@daybrush)](https://github.com/daybrush): Fix cut & copy for iPad [PR #101702](https://github.com/microsoft/vscode/pull/101702)
* [Dhananjay Tanpure (@DTan13)](https://github.com/DTan13): Activity bar can be at left or right of workbench [PR #103359](https://github.com/microsoft/vscode/pull/103359)
* [Ikko Ashimine (@eltociear)](https://github.com/eltociear): Fixed typo 'occured' to 'occurred' [PR #104181](https://github.com/microsoft/vscode/pull/104181)
* [Eric Piacentini (@epiacentini)](https://github.com/epiacentini): Removed lines requiring Typescript version <= 3.0 [PR #104211](https://github.com/microsoft/vscode/pull/104211)
* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray): fix #104698 Make query element of uri survive conflict resolution massaging [PR #104699](https://github.com/microsoft/vscode/pull/104699)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH): Address feedback in #101467 [PR #105880](https://github.com/microsoft/vscode/pull/105880)
* [Ken Brownfield (@irridia)](https://github.com/irridia): Support \U\u\L\l replace modifiers in global search/replace (see PR #96128) [PR #105101](https://github.com/microsoft/vscode/pull/105101)
* [Jack Works (@Jack-Works)](https://github.com/Jack-Works): Improve typescript.code-snippets for async [PR #99544](https://github.com/microsoft/vscode/pull/99544)
* [Jean Pierre (@jeanp413)](https://github.com/jeanp413)
  * Fixes TypeScript select version inserts full path to tsserver.js [PR #105664](https://github.com/microsoft/vscode/pull/105664)
  * Fixes collapse folders action doesn't work when workspace root folder has just one file child item [PR #104744](https://github.com/microsoft/vscode/pull/104744)
  * Fixes middle-click on a link doesn't open the link properly [PR #104885](https://github.com/microsoft/vscode/pull/104885)
* [Jonatan Ivanov (@jonatan-ivanov)](https://github.com/jonatan-ivanov): Support for .jenkinsfile extension and Jenkinsfile.* filename pattern [PR #105326](https://github.com/microsoft/vscode/pull/105326)
* [Leila Pearson (@leilapearson)](https://github.com/leilapearson): Compare full filenames [PR #104528](https://github.com/microsoft/vscode/pull/104528)
* [Max Reynolds (@MarcelloTheArcane)](https://github.com/MarcelloTheArcane): Update error messages [PR #105388](https://github.com/microsoft/vscode/pull/105388)
* [Marvin A. Ruder (@marvinruder)](https://github.com/marvinruder): Changed description of "Commit Signing" configuration label to include X.509. [PR #105228](https://github.com/microsoft/vscode/pull/105228)
* [Matt Crouch (@mjcrouch)](https://github.com/mjcrouch)
  * Support `scmResourceState` in `when` clauses [PR #90952](https://github.com/microsoft/vscode/pull/90952)
  * SCM: Clear list of created resource groups once they are added [PR #105587](https://github.com/microsoft/vscode/pull/105587)
* [@mtaran-google](https://github.com/mtaran-google)
  * Add to WorkingCopyCapabilities enum to reflect usage. [PR #105020](https://github.com/microsoft/vscode/pull/105020)
  * Ban unreachable code. [PR #104521](https://github.com/microsoft/vscode/pull/104521)
* [n-gist (@n-gist)](https://github.com/n-gist): Making TM_SELECTED_TEXT snippet variable working with overtyped text v2 [PR #105440](https://github.com/microsoft/vscode/pull/105440)
* [Noelle Caldwell (@noellelc)](https://github.com/noellelc): updated rich nav task version [PR #104213](https://github.com/microsoft/vscode/pull/104213)
* [@nrayburn-tech](https://github.com/nrayburn-tech): [themeing] Use Object.create(null) instead of {} for TokenRegistry [PR #103349](https://github.com/microsoft/vscode/pull/103349)
* [Sam Lee (@orionlee)](https://github.com/orionlee): Dimmed Monokai theme - improve markdown support [PR #104468](https://github.com/microsoft/vscode/pull/104468)
* [Pascal Fong Kye (@pfongkye)](https://github.com/pfongkye): Use default flex values and use margins [PR #104052](https://github.com/microsoft/vscode/pull/104052)
* [Pranav Senthilnathan (@PranavSenthilnathan)](https://github.com/PranavSenthilnathan): prune the refactor not applicable reasons if there are too many [PR #103203](https://github.com/microsoft/vscode/pull/103203)
* [Rob OLeary (@robole)](https://github.com/robole): Add snippets for markdown [PR #105174](https://github.com/microsoft/vscode/pull/105174)
* [Rotem Bar (@rotem-bar)](https://github.com/rotem-bar): fix(extpath): windows reserved names being allowed with file extension [PR #104980](https://github.com/microsoft/vscode/pull/104980)
* [Sam Poder (@sampoder)](https://github.com/sampoder): Fix spelling mistake in main.js [PR #104987](https://github.com/microsoft/vscode/pull/104987)
* [Stepan Repin (@stnrepin)](https://github.com/stnrepin): Add 'trailing' option to 'editor.renderWhitespace' [PR #104310](https://github.com/microsoft/vscode/pull/104310)
* [Tyler James Leonhardt (@TylerLeonhardt)](https://github.com/TylerLeonhardt): Add DebugAdapterNamedPipeServer [PR #103210](https://github.com/microsoft/vscode/pull/103210)
* [@ultraGentle](https://github.com/ultraGentle): Adds setting to disable cursor movement while typing into Find Widget [PR #94825](https://github.com/microsoft/vscode/pull/94825)
* [Duc Nghiem Xuan (@xuanduc987)](https://github.com/xuanduc987): Don't include this function types in completeFunctionCalls [PR #104479](https://github.com/microsoft/vscode/pull/104479)
* [ZainChen (@ZainChen)](https://github.com/ZainChen): Update markdown themes [PR #105304](https://github.com/microsoft/vscode/pull/105304)

Contributions to `vscode-json-languageservice`:

* [@KapitanOczywisty](https://github.com/KapitanOczywisty)
  * Resolve schemas from getMatchingSchemas argument [PR #70](https://github.com/microsoft/vscode-json-languageservice/pull/70)
  * Resolve refs inside additionalItems [PR #69](https://github.com/microsoft/vscode-json-languageservice/pull/69)
  * Fix multipleOf with float [PR #67](https://github.com/microsoft/vscode-json-languageservice/pull/67)
  * Use propertyNames for completion proposals [PR #65](https://github.com/microsoft/vscode-json-languageservice/pull/65)
  * Add support for defaultSnippets in patternProperties and additionalProperties [PR #62](https://github.com/microsoft/vscode-json-languageservice/pull/62)

Contributions to `vscode-eslint`:

* [Haojian Wu (@hokein)](https://github.com/hokein): Expose restartTimes to client. [PR #658](https://github.com/microsoft/vscode-languageserver-node/pull/658)

Contributions to `language-server-protocol`:

* [Daniel Mangum (@hasheddan)](https://github.com/hasheddan): Fix typo in v3.16 semantic tokens section [PR #1064](https://github.com/microsoft/language-server-protocol/pull/1064)
* [Danny Tuppeny (@DanTup)](https://github.com/DanTup): Add capabilities fields for semantic tokens [PR #1068](https://github.com/microsoft/language-server-protocol/pull/1068)
* [Titouan Vervack (@tivervac)](https://github.com/tivervac)
  * Fixed typos in ConfigurationItem's documentation [PR #1081](https://github.com/microsoft/language-server-protocol/pull/1081)
  * Fixed typo in workspace/configuration documentation [PR #1090](https://github.com/microsoft/language-server-protocol/pull/1090)
* [Zuckjet (@Zuckjet)](https://github.com/Zuckjet): Fix typo [PR #1086](https://github.com/microsoft/language-server-protocol/pull/1086)


Contributions to `vscode-languageserver-node`:
* [Petar Dambovaliev (@petar-dambovaliev)](https://github.com/petar-dambovaliev): fix spelling in types/src/main.ts [PR #669](https://github.com/microsoft/vscode-languageserver-node/pull/669)

Contributions to `vscode-recipes`:

* [Steven Gum (@stevengum)](https://github.com/stevengum): add skipFiles with node_internals to debugging-mocha-tests [PR #257](https://github.com/microsoft/vscode-recipes/pull/257)

Contributions to `vscode-mock-debug`:

* [Tyler James Leonhardt (@TylerLeonhardt)](https://github.com/TylerLeonhardt): Add DebugAdapterNamedPipeServer factory [PR #46](https://github.com/microsoft/vscode-mock-debug/pull/46)

Contributions to `vscode-js-debug`:

* [Hongcai Deng (@denghongcai)](https://github.com/denghongcai): feat: support node remote debug through ws [PR #667](https://github.com/microsoft/vscode-mock-debug/pull/667)

Contributions to `vscode-textmate`:

* [Anthony Sottile (@asottile)](https://github.com/asottile): fix typo in package.json [PR #132](https://github.com/microsoft/vscode-textmate/pull/132)

Contributions to `vscode-vsce`:

* [Jeffrey (@JeffreyCA)](https://github.com/JeffreyCA): Add githubBranch flag to control the branch for GitHub relative links [PR #474](https://github.com/microsoft/vscode-vsce/pull/474)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
