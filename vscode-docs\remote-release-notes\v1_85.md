# November 2023 (version 1.85)

## Core

### Load certificates in WSL and local Dev Containers

For WSL and local Dev Containers sessions, the local machine's certificates are now also loaded for extensions running in the remote environment. This enables extensions to use HTTPS in scenarios where, for example, a self-signed certificate for development or a corporate proxy with a custom certificate authority is used.

### Automatically change to `hybrid` port source

The `remote.autoForwardPortsSource` is `process` and more than 20 ports have already been auto-forwarded, the port source will automatically switch to `hybrid`. This should result in fewer cases where unneeded ports are unnecessarily forwarded.

## Dev Containers

### Opt out of extensions

If a base image or feature configures an extension that you do not want installed in your Dev Container, you can now opt out by listing the extension with a minus sign in the `extensions` section of your Dev Container configuration file.

For example to disable installing the ESLint extension:

```json
{
    "image": "mcr.microsoft.com/devcontainers/typescript-node:1-20-bookworm",
    "customizations": {
        "vscode": {
            "extensions": [
                "-dbaeumer.vscode-eslint"
            ]
        }
    }
}
```

### Automatically install the GitHub Copilot and Pull Requests extensions

If you have one of the following general-purpose extensions installed locally, it will now automatically be installed in your Dev Container:

- [GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot)
- [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat)
- [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github)

You can control the default extensions by adding an extension ID to the **Dev > Containers: Default Extensions** (dev.containers.defaultExtensions) and **Dev > Containers: Default Extensions If Installed Locally** (dev.containers.defaultExtensionsIfInstalledLocally) user settings:

![Default Extensions Settings](images/1_85/default-extensions-settings.png)

### Archiving the microsoft/vscode-dev-containers repository

[containers.dev](https://containers.dev) has been the new and expanded home for Dev Containers for some time now. We have now archived its former home, the [microsoft/vscode-dev-containers](https://github.com/microsoft/vscode-dev-containers) repository.

Most of the repo contents has moved to the repositories under the [devcontainers](https://github.com/devcontainers) organization on GitHub. Two container features didn't make the move: `homebrew` and `fish`. If you are using either of these, check the list of [available community features](https://containers.dev/features) on containers.dev for replacements.
