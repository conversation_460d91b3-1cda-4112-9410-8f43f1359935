---
Order: 1
Area: students
TOCTitle: NASA Lessons
ContentId: 6a081d45-5324-458b-b60f-7c7ba01ac636
PageTitle: Get Started tutorial for the NASA project in Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: NASA project tutorials using the Python extension in Visual Studio Code.
---
# NASA Inspired Lessons

This learning path introduces you to the world of Python. Explore modules and learning paths inspired by National Aeronautics and Space Administration (NASA) scientists to prepare you for a career in space exploration. Visit [Microsoft Learn training](https://learn.microsoft.com/training/topics/nasa) for the full list of modules.

Through these modules, you will:

* Understand and install the tools needed to learn programming
* Learn core programming concepts and apply them to real-life NASA problems
* Gain knowledge about cutting-edge technologies like machine learning and artificial intelligence
* See actual NASA employees talk about their job and give advice

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/introduction-python-space-exploration-nasa/?WT.mc_id=python-0000-cxa"><h2 class="title faux-h3">Discover the role of Python in space exploration</h2></a>
    </div>
    <p class="description">Learn how Python and data science play a role in the innovative solutions that NASA creates.</p>
    <a href="https://learn.microsoft.com/training/paths/introduction-python-space-exploration-nasa/?WT.mc_id=python-0000-cxa" title="Python NASA module">
        <img src="/assets/learn/students/nasa-python/nasa-python1.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/classify-space-rocks-artificial-intelligence-nasa/?WT.mc_id=python-0000-cxa"><h2 class="title faux-h3">Use Artificial Intelligence to classify space rocks</h2></a>
    </div>
    <p class="description">Create an AI model that can classify types of space rock present in a photo.</p>
    <a href="https://learn.microsoft.com/training/paths/classify-space-rocks-artificial-intelligence-nasa/?WT.mc_id=python-0000-cxa" title="Classifying Python module">
        <img src="/assets/learn/students/nasa-python/nasa-python2.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/machine-learning-predict-launch-delay-nasa/?WT.mc_id=python-0000-cxa"><h2 class="title faux-h3">Use Machine Learning to predict rocket launches</h2></a>
    </div>
    <p class="description">This will introduce you to the world of machine learning and help you build a simple ML model. </p>
    <a href="https://learn.microsoft.com/training/paths/machine-learning-predict-launch-delay-nasa/?WT.mc_id=python-0000-cxa" title="ML Python module">
        <img src="/assets/learn/students/nasa-python/nasa-python3.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>
