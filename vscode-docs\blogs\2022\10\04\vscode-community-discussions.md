---
Order: 77
TOCTitle: VS Code Community Discussions
PageTitle: VS Code Community Discussions for Extension Authors
MetaDescription: Announcing the official launch of VS Code Community Discussions, a place for extension authors to connect.
Date: 2022-10-04
Author: <PERSON>
---

# VS Code Community Discussions for Extension Authors

October 4, 2022 by <PERSON>, [@<PERSON>Guzzardo](https://twitter.com/OliviaGuzzardo)

The true power of VS Code comes from its vast extension ecosystem, which only exists because of our incredible community of extension authors. Whether creating a new language extension to make it possible to program in virtually any language, developing a new theme to help with productivity, or extending the workbench to help with a unique developer workflow, extension authors help millions of people by sharing their creation.

Our goal is, and has always been, to make extension development accessible to everyone. And while we have plenty of [documentation](https://code.visualstudio.com/api) for getting started with extension development, sometimes there are questions that documentation just can’t answer. Sometimes, all you need is a friendly hand from someone who has been there, had that question, and figured it out.

Today we're announcing the official launch of [VS Code Community Discussions](https://github.com/microsoft/vscode-discussions/discussions): a place for extension authors to ask questions, connect with the community, and showcase their work.

## Navigating the Forum

![A screenshot showing the VS Code Community Discussions landing page](github-discussions.png)

The new GitHub Discussions forum is a place for extension authors to connect with each other and stay up to date on announcements from the VS Code team. We want to be as close to developers as possible, and having a centralized, official community helps to achieve that.

When you visit the Discussions repo, you'll notice that there are currently 3 categories...

### Announcements

This is where you can keep up with all of the latest information for extension authors from the VS Code team. This includes things like recently released features such as "Pre-release extensions" as well as some giveaways we have from time-to-time just for our extension author communities - like [free Azure Credits](https://github.com/microsoft/vscode-discussions/discussions/135)!

### Extension Development QnA

Ask your questions related to extension development and help your fellow developers by answering theirs. Questions regarding how to approach a specific problem, looking for more details on certain APIs, and seeking best practices are all great to post here. However, if you find yourself saying "I've gone through the documentation, and I am still unable to interact with the VS Code API to achieve the desired functionality...", then it might be better suited as an [issue](https://github.com/microsoft/vscode/issues) on the official VS Code repo.

### Extension Show and Tell

You've put time and effort into creating your extension; here's your chance to show it off! Share a link to your extension, give the community details about what it does, and get help with promotion. This is also a great place to discover new extensions and connect with developers whose work you appreciate.

## Why GitHub Discussions?

While platforms like Slack and Discord are terrific for real time interaction, there is a sacrifice in discoverability. Often, the same questions have already been asked in these places, but they are not easily found with a simple internet search. GitHub Discussions are easily indexed in search results, so the answer to the question that's been frustrating you all day could be just a quick search away.

We created a new repo to house our GitHub Discussions community, rather than use the existing VS Code repo. People currently go to the VS Code repo to file issues via GitHub Issues, and we saw that the lines would be blurred between GitHub Issues and GitHub Discussions if hosted in the same place. We felt that separating the main repo and the new community was the best way to establish the community as a distinct gathering place.

## Join Today!

It is our hope that the VS Code Community Discussions turns into a thriving community, where extension authors can connect with others and show off their hard work.

VS Code is what it is today because of our amazing contributors, and we want to continue to connect with and foster that community. As such, we have several channels where you can engage with us:

- [VS Code Community Discussions](https://github.com/microsoft/vscode-discussions/discussions) for extension author Q&A and announcements, officially maintained by the VS Code team
- The [Extension Authoring](https://code.visualstudio.com/updates#_extension-authoring) section of our monthly VS Code Release Notes where extension authors can learn about newly released APIs
- [VS Code Dev Slack](https://vscode-dev-community.slack.com) for extension author Q&A, maintained by the community
- [GitHub Issues](https://github.com/Microsoft/vscode/issues) to file an issue in our VS Code repo
- [Stack Overflow](https://stackoverflow.com/questions/tagged/visual-studio-code) to ask and answer questions tagged with visual-studio-code
- [Twitter](https://twitter.com/code) for announcements and updates from members of the development team
- [YouTube](https://www.youtube.com/code) and [TikTok](https://www.tiktok.com/@vscode) for video content on all things VS Code

Happy Coding!

Olivia Guzzardo, [@OliviaGuzzardo](https://twitter.com/OliviaGuzzardo)