---
ContentId: a18e245e-af72-4d0f-b322-fa1030af5284
DateApproved: 05/08/2025
MetaDescription: Set up Copilot in VS Code
MetaSocialImage: images/shared/github-copilot-social.png
---
# Set up Visual Studio Code with Copilot

Welcome to AI-powered development with Visual Studio Code! Follow the steps in this guide to get started in minutes.

## Set up Copilot in VS Code

To use Copilot in VS Code, you need access to a GitHub Copilot subscription.

To set up Copilot in VS Code:

1. [Download and install Visual Studio Code](https://code.visualstudio.com/Download) for your platform

1. Start VS Code

1. Hover over the Copilot icon in the Status Bar and select **Set up Copilot**.

    ![Hover over the Copilot icon in the Status Bar and select Set up Copilot.](images/setup/setup-copilot-status-bar.png)

1. Select **Sign in** to sign in to your GitHub account or **Use Copilot** if you're already signed in.

    If you don't have a Copilot subscription yet, you'll be signed up for the [Copilot Free plan](https://docs.github.com/en/copilot/managing-copilot/managing-copilot-as-an-individual-subscriber/managing-copilot-free/about-github-copilot-free).

    > [!IMPORTANT]
    > Telemetry in your free version of GitHub Copilot is currently enabled. By default, code suggestions that match public code, including code references in the VS Code and <github.com> experience, are allowed. You can opt out of telemetry data collection by disabling telemetry in VS Code by setting `setting(telemetry.telemetryLevel)` to `off`, or you can adjust both telemetry and code suggestion settings in [Copilot Settings](https://github.com/settings/copilot).

1. You can now start using Copilot in VS Code. Learn the basics with the [Copilot Quickstart](/docs/copilot/getting-started.md).

## Next steps

- Discover AI-powered development in VS Code with our [Copilot Quickstart](/docs/copilot/getting-started.md)
- Get an [overview of Copilot in VS Code](/docs/copilot/overview.md)
- Get more info about the [Copilot Free plan details and conditions](https://docs.github.com/en/copilot/about-github-copilot/subscription-plans-for-github-copilot)
