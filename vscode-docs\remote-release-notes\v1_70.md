# July 2022 (version 1.70)

## Development Containers

We continue work across the Dev Containers extension and the open [dev container specification](https://containers.dev/), which seeks to allow anyone in any tool (including ones other than VS Code) to use containers for their inner and outer loop activities. Its reference implementation is the [dev container CLI](https://github.com/devcontainers/cli).

![Container lifecycle diagram](images/1_70/dev-container-stages.png)

### Dev container Features

Dev container **Features** are a preview option of dev containers consisting of self-contained units of installation code and configuration. We've been [iterating](https://github.com/devcontainers/cli/pull/73) on our initial Features implementation, and [the latest has been merged into the spec repo](https://github.com/devcontainers/spec/blob/main/proposals/devcontainer-features.md) and the dev container CLI.

We've also merged an [updated proposal for distributing features and templates](https://github.com/devcontainers/spec/blob/main/proposals/devcontainer-features-distribution.md), now with the ability to upload them as [OCI Artifacts in registries](https://github.com/devcontainers/spec/blob/main/proposals/devcontainer-features-distribution.md#oci-registry). We are working toward getting this into the CLI, and we welcome any feedback on the proposal via GitHub [Issues](https://github.com/devcontainers/spec/issues) or [Discussions](https://github.com/devcontainers/spec/discussions).

### Dev container CLI

The CLI [now supports](https://github.com/devcontainers/cli/pull/61) multiple `--image-name` parameters, such as:

```sh
devcontainer build --workspace-folder ./ --image-name alpine3 --image-name alpine3.0
```

This allows you to tag the same image more than once, which is especially helpful for tagging images with multiple registries.

We've added the ability to [test features](https://github.com/devcontainers/cli/pull/81) from the CLI with `devcontainer features test`. Below is an example of this command:

![Terminal output of devcontainer features test](images/1_70/devcontainer-feature-test.png)

There was also a [bug fix](https://github.com/microsoft/vscode-remote-release/issues/6913) to allow falling back to `id -u -n` in the case when the container can't find a username.

### Dev Containers

We [made a fix](https://github.com/devcontainers/cli/issues/83) so that copying a file with executable permissions (such as the dev container CLI executable) preserves the executable bit on the file permissions.

The latest in dev container features and the CLI will also be landing in Dev Containers in the coming week.
