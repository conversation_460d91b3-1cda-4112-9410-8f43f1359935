---
Order:
TOCTitle: April 2016 Release
PageTitle: Visual Studio April 2016 Release
MetaDescription: Announcing the Visual Studio Code April 2016 Release
Date: 2016-05-09
ShortDescription: Announcing the April 2016 Release of VS Code
Author: <PERSON>
---

# April 2016 Release

May 9, 2016 by The VS Code Team, [@code](https://twitter.com/code)

Today we are releasing the April 2016 build of Visual Studio Code. This is our first monthly release after our 1.0 announcement last month and we really appreciate your support and feedback.

With this release, we're bringing many improvements to your development experience:

## Developer Workflow

* Quickly resize panes by double-clicking editor borders
* Reopen the last closed file using `kb(workbench.action.reopenClosedEditor)`
* Launch your favorite shell when opening a new Terminal from the Explorer or Command Palette

## Debugging

* Improved stepping performance when inspecting very large strings or arrays
* Support for deep call stacks
* Node.js improvements such as experimental "smart" code stepping, ES6 type support in Watch, Locals, etc.

## Extension Authoring

* Language Server protocol 2.0 is now consistent with core VS Code API
* Automated test support for authoring Debug Adapters
* New APIs for working with folders and JSON files

Please see our [Release Notes](https://go.microsoft.com/fwlink/?LinkID=533483) for the full list of features and fixes.

If you have automatic updates turned on (OS X and Windows) then you'll get prompted soon. Otherwise, [download VS Code today](https://code.visualstudio.com)!

Happy Coding!!

The VS Code Team
