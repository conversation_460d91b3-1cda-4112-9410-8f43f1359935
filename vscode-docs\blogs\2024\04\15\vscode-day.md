---
Order: 85
TOCTitle: VS Code Day 2024
PageTitle: Visual Studio Code Day 2024
MetaDescription: Learn about the latest Visual Studio Code features and extensions during VS Code Day 2024 on April 24, 2024
MetaSocialImage: vscodeday-promo.png
Date: 2024-04-15
Author: <PERSON><PERSON><PERSON>
---

# Your Ultimate Guide to VS Code Day 2024

April 15, 2024 by <PERSON><PERSON><PERSON>, [@ReynaldAdolphe](https://twitter.com/ReynaldAdolphe)

> **Watch [all content from VS Code Day 2024](https://youtube.com/live/iCDfAC4f25w) now.**

VS Code Day is our annual event where you'll learn how to elevate your development workflow with the latest and greatest features of Visual Studio Code. This year, we'll have a big focus on AI but not limited to just that. You’ll hear from the VS Code team and other industry experts on topics like AI-powered programming with GitHub Copilot, building and deploying generative AI apps to the cloud, enhancing the C# development experience, and more!

<video src="VS Code Day - Promo.mp4" title="VS Code Day 2024 Promotion video" autoplay muted loop controls></video>

Expect to gain insights into the tools and practices shaping the future of coding, connect via Q&A’s with pioneers and peers, and find inspiration to tackle your next project.

Hosted by Senior Developer Advocates [<PERSON><PERSON><PERSON>olphe](https://aka.ms/Reynald-YT) and [Gwyneth Peña-Siguenza](https://twitter.com/madebygps), this event is for developers seeking to get the most out of VS Code to increase productivity and learn about its new features including those related to AI.

![Picture of hosts Reynald and Gwyn](Reynald-Gwyn.jpg)

## A Two-Day Event!

VS Code Day will be a 2-day event full of fun and learning. On the first day, join a preshow where Sonny Sangha will show you how to build a LinkedIn clone with Azure in 3–4 hours. On the second day, attend the main event of 5 hours, where you will learn from various speakers about VS Code and its features.

## Day 1 - VS Code Day Preshow: Building a LinkedIn Clone with Azure

- **Date**: Tuesday, April 23
- **Time**: 8am EST
- **Where to watch**: [Sonny Sangha on YouTube](https://www.youtube.com/@SonnySangha)
- **Spotlight Session**: Let’s build LinkedIn 2.0 with Azure & NEXT.JS 14!

    | | Details |
    |-|-|
    | ![Sonny Sangha](Sonny_Sangha.jpg) | We'll guide you through building a clone of LinkedIn.com. This session isn't just about building an app; it's a deep dive into using Microsoft Azure, GitHub Copilot, Cosmos DB, and TypeScript to craft robust, scalable web applications.<br/><br/>Sonny Sangha (@SonnySangha), also known as PAPA React, has built one of the largest developer communities on YouTube with over 250,000+ Subscribers. With this he's grown his flagship course and community: Zero to Full Stack Hero to over 1000+ members. He is on a journey to bring more developers into coding while keeping things fun & engaging! |

## Day 2 - VS Code Day: Main Event

- **Date**: Wednesday, April 24
- **Time**: 1pm EST
- **Where to watch**: [VS Code on YouTube](https://www.youtube.com/@code)

| Session       | Speaker   |
|-------------- | --------- |
| [**Keynote**: View Source: What gets into VS Code and why](https://aka.ms/vscd-holland)    | [Burke Holland](https://twitter.com/burkeholland) |
| [Aw, CRUD: Building a Django app with persistent storage](https://aka.ms/vscd-wages) | [Dawn Wages](http://@BajoranEngineer) |
| [Generating Synthetic Datasets with GitHub Copilot](https://aka.ms/vscd-deza)   | [Alfredo Deza](https://www.linkedin.com/in/alfredodeza/) |
| [Real World Development with VS Code and C#](https://aka.ms/Vscd-hr)   | [Leslie Richardson](https://twitter.com/lyrichardson01) & [Scott Hanselman](https://twitter.com/shanselman) |
| [Building a RAG-powered AI chat app with Python and VS Code](https://aka.ms/Vscd-fox)   | [Pamela Fox](https://twitter.com/pamelafox) |
| [Beyond the Editor: Tips to get the Most out of GitHub Copilot](https://aka.ms/vscd-kerr)   | [Kedasha Kerr](https://twitter.com/itsthatladydev)  |
| [LangChain Examples with Azure OpenAI Service + VS Code](https://aka.ms/vscd-kumar)   | [Rishab Kumar](https://twitter.com/rishabincloud) |
| [AI Made Clear: Practical AI Coding Sessions in VS Code](https://aka.ms/vscd-capuano) | [Bruno Capuano](https://twitter.com/elbruno) |
| [Asking Copilot about your workspace](https://aka.ms/vscd-bierner) | [Matt Bierner](https://twitter.com/mattbierner) |
| [Build games with GitHub Copilot](https://aka.ms/vscd-noring) | [Chris Noring](https://twitter.com/chris_noring) |
| [What's New with C++ in VS Code](https://aka.ms/vscd-ka)  | [Alexandra Kemper](https://twitter.com/AlexandraKemper) & [Sinem Akinci](https://twitter.com/sinem__akinci) |

## About the hosts

| | Host |
|-|-|
| ![Reynald Adolphe](Reynald.jpg) | Reynald Adolphe (@ReynaldAdolphe) comes from a background specializing in education, consulting and full stack development. He's currently an advocate at Microsoft for the VS Code team. He’s also a speaker, personal technology coach helping programmers build their brand/career to the next level. You can follow him on his [YouTube channel](https://aka.ms/Reynald-YT). |
| ![Gwyneth Peña-Siguenza](Gwyn.jpg) | Gwyn (@madebygps) is a Senior Cloud Advocate at Microsoft focused on Azure developer tools, founder of LearnToCloud and YouTuber, blending her dedication to technology with a passion and commitment for community. Beyond tech, Gwyn enjoys video games, time with loved ones, and mint chocolate chip ice cream. |

## Make the most of your event

- **Bookmark the Channels**: Subscribe to [VS Code](https://aka.ms/vscodeday-b-code) & [Sonny’s](https://aka.ms/vscodeday-b-sonny) channel hosting the event to get real-time updates.

- **Schedule Smart**: With a packed agenda, plan ahead to catch live sessions or set aside time for replays.

- **Engage and Interact**: Use this opportunity to engage with the community. Share your thoughts, ask questions, and make connections.

- **[Register for the VS Code Day Skills Challenge](https://aka.ms/VSCodeDayCSC)**! Kickstart or pivot your career with our program focused on VS Code and GitHub Copilot, covering areas like Data Science and Artificial Intelligence. Enjoy simple lessons, practical exercises, and live workshops to explore the latest in VS Code. Register at [https://aka.ms/VSCodeDayChallenge](https://aka.ms/VSCodeDayChallenge)!

So, get ready for two days filled with learning and discovery. VS Code Day is your portal to the latest in development, packed with sessions to inspire and challenge you. We can’t wait to see you there!

Happy Coding!

Reynald Adolphe ([@ReynaldAdolphe](https://twitter.com/ReynaldAdolphe))
