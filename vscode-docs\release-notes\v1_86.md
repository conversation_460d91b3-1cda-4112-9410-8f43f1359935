---
Order: 95
TOCTitle: January 2024
PageTitle: Visual Studio Code January 2024
MetaDescription: Learn what is new in the Visual Studio Code January 2024 Release (1.86)
MetaSocialImage: 1_86/release-highlights.png
Date: 2024-2-1
DownloadVersion: 1.86.2
---
# January 2024 (version 1.86)

**Update 1.86.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22December+%2F+January+2024+Recovery+2%22+).

**Update 1.86.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A%22December+%2F+January+2024+Recovery+1%22).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

Welcome to the January 2024 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[Per-window zoom levels](#configure-zoom-levels-per-window)** - Adjust the zoom level for each window independently.
* **[Hey Code voice command](#accessibility)** - Start a chat session with a voice command.
* **[Multi-file diff editor](#review-multiple-files-in-diff-editor)** - Quickly review diffs across multiple files in the diff editor.
* **[Triggered breakpoints](#triggered-breakpoints)** - Efficient debugging with breakpoint dependencies.
* **[Expanded Sticky Scroll support](#sticky-scroll-in-tree-views)** - Sticky Scroll in tree views and notebooks.
* **[Markdown paste options](#languages)** - Rich paste support for links, video, and audio elements.
* **[Flexible Auto Save options](#more-powerful-and-flexible-auto-save-options)** - Skip Auto Save on errors or save only for specific file types.
* **[Source Control input](#source-control)** - Customize commit input and per-language editor settings.
* **[Extension notifications](#allow-disabling-notifications-per-extension)** - Fine-grained control for disabling notifications per extension.
* **[GitHub Copilot updates](#github-copilot)** - Improved default context, add file as context, AI fixes.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Alerts

Screen reader and braille users now have more configurable alerts, such as when debug breakpoints are hit, if there are errors on the current line, and more. These can be configured via settings starting with `accessibility.alert`, or explored and configured with the **Help: List Alerts** command.

### Use "Hey Code" voice command

With the new `accessibility.voice.keywordActivation` setting, you can enable VS Code to listen for the "Hey Code" voice command to start a voice session with Copilot Chat. The voice recognition is computed locally on your machine and is never sent to any server.

Available options are:

* `chatInView`: start voice chat from the Chat view
* `quickChat`: start quick voice chat from the Quick Chat control
* `inlineChat`: start voice chat from inline chat in the editor
* `chatInContext`: start voice from inline chat if the focus is in the editor, otherwise voice chat from the Chat view

The following example shows "Hey Code" with the `inlineChat` option configured:

<video src="images/1_86/hey-code.mp4" autoplay loop controls muted title="Use the 'Hey Code' voice command to activate voice chat."></video>

A small microphone icon in the Status Bar signals when VS Code is using the microphone for recognizing "Hey Code".

Once the voice chat session is completed, VS Code will again listen to "Hey Code" to start it again.

To use this new capability, the following extensions are required:

* [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat)
* [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech)

> **Note:** the voice recognition runs locally on your machine and is never sent to any server.

## Workbench

### Restore auxiliary windows

With this release, all opened floating window associated with a main window will restore when you restart the application. This includes opened editors, and size and location of the auxiliary window.

<video src="images/1_86/aux-restore.mp4" autoplay loop controls muted title="Auxiliary windows restore after reloading or restarting VS Code."></video>

### Sticky Scroll in tree views

Building on the success of Sticky Scroll in the editor, we've extended this feature to all tree views, enabling users to more easily navigate project trees. Sticky Scroll for tree views can be enabled or disabled with the `workbench.tree.enableStickyScroll` setting.

To ensure Sticky Scroll does not take too much space, it is limited to maximum 40% of the view height. Additionally, users can customize the maximum number of sticky elements by configuring `workbench.tree.stickyScrollMaxItemCount`, which is set to 7 by default. If there are more sticky elements than can be displayed, Sticky Scroll will consolidate the last sticky elements together, if the tree view supports this feature.

<video src="images/1_86/sticky-scroll-file-explorer.mp4" autoplay loop controls muted title="Sticky Scroll in the File Explorer."></video>

For an improved tree navigation experience, you can select a sticky element to jump directly to the element within the tree. Alternately, press the chevron of a parent element to hide all its child elements. Additionally, accessing checkboxes and action items is easier when Sticky Scroll is enabled.

<video src="images/1_86/sticky-scroll-extension-trees.mp4" autoplay loop controls muted title="Sticky Scroll in the GitHub Pull Requests and Issues extension Pull Request tree view."></video>

### Configure zoom levels per window

The new setting `window.zoomPerWindow`, which is enabled by default, lets you configure the zoom commands (**Zoom In**, **Zoom Out**, **Zoom Reset**) to apply only to the active window, and not to all opened windows. Previously using these commands would not only apply it to all opened windows, but would also update the `window.zoomLevel` setting.

We believe that using these commands is more of an ad-hoc gesture, for example when giving a presentation, and thus should only apply to the window they are invoked from.

As you can see from this example, only the active window's zoom level changes, but not for the other window:

<video src="images/1_86/zoom.mp4" autoplay loop controls muted title="Configure per-window zoom levels and show zoom indicator in Status Bar."></video>

A zoom level indicator is shown in the Status Bar when the custom zoom level of a window does not match the `window.zoomLevel` setting value. Select the Status Bar indicator to find controls to change zoom levels, reset the zoom level, or quickly go to the related settings.

![Zoom indicator and controls in the Status Bar.](images/1_86/zoom.png)

A window with custom zoom level retains that zoom level across restarts and across workspace changes.

> **Note:** configure `window.zoomPerWindow` to `false` to restore the previous zoom level behavior.

### More powerful and flexible Auto Save options

VS Code offered Auto Save options for a long time. In this release, we make this capability a lot more powerful.

#### Configure Auto Save per resource or language

All Auto Save settings can now be configured per folder or language, which lets you selectively enable Auto Save only for specific languages or folders.

In the example below, settings are configured as:

```json
{
  "[markdown]": {
    "files.autoSave": "afterDelay"
  }
}
```

As you can see, the edited markdown file saves immediately, while the TypeScript file remains dirty.

<video src="images/1_86/autosave.mp4" autoplay loop controls muted title="Configure auto save per language."></video>

#### Disable Auto Save when errors

A new `files.autoSaveWhenNoErrors` setting lets you disable Auto Save if there are error markers in the file when Auto Save would normally save the editor.

This can be useful when you have external tools watching for file changes, where you want to avoid that these tools act on a changed file that contains errors.

#### Auto Save for workspace files only

Finally, a new setting `files.autoSaveWorkspaceFilesOnly` that limits Auto Save only to files that are inside the workspace.

### Allow disabling notifications per extension

We now offer more fine-grained control for disabling notifications coming from extensions. From a notification toast, you can disable notifications for the extension:

![Turn off notifications for an extension.](images/1_86/turn-off-notifications-1.png)

In the Notification Center, there is a central place to manage notification enablement for all extensions that have triggered a notification:

![Manage notifications for extensions.](images/1_86/turn-off-notifications-2.png)

This new feature complements our existing Do Not Disturb Mode, which globally disables all notifications. As with the global switch, error notifications will always appear and cannot be disabled.

### Allow swapping left and right diff editors

When you compare two files (for example from the File Explorer), a new action appears to swap the left and right hand side of the diff editor.

<video src="images/1_86/swap-diff.mp4" autoplay loop controls muted title="Swap left and right sides of the Diff Editor."></video>

> **Note:** comparing two editable files allows you to make changes on either side.

### Do no enforce `--wait` when reading from stdin from the command line

When using a terminal, you can pass the output of a process directly into VS Code to open as editor, for example:

* `ps aux | grep code | code -` on Linux or macOS
* `echo Hello World | code -` on Windows

Until now, this also implied the `--wait` flag, which means that you would not get back to the terminal prompt until you had either closed the editor window or VS Code entirely. In this milestone, we no longer enforce `--wait` when reading from stdin, so you immediately get back to the terminal prompt.

> **Note:** to get the previous behavior back, simply append `--wait` to the command line invocation.

### Support custom title bar with native title bar

We're introducing the `window.customTitleBarVisibility` setting, which allows showing the custom title bar even when the native title bar is being used. This is especially beneficial for macOS users that prefer native tabs. With this new setting, native title bar users can now access custom title bar functionalities, such as:

* **Command Center:** Easily access the command center directly from the custom title bar.
* **Layout Controls:** Customize your workspace layout with greater flexibility.
* **Activity Bar Customization:** Move the Activity Bar actions to the top.
* **Editor Actions Customization:** Relocate editor actions to the custom title bar.

Moreover, for users who prefer an uncluttered view in full-screen mode, the `window.customTitleBarVisibility: "windowed"` option allows you to hide the custom title bar when in full-screen mode. This feature ensures a more focused and distraction-free coding environment, while still providing the option to use custom title bar features when you're not in full-screen mode.

![The 'Window: Custom Title Bar Visibility' setting in the Settings editor.](images/1_86/native-tabs-custom-title-bar.png)

### New setting to enforce system color theme

A new setting `window.systemColorTheme` lets you explicitly override the system color theme that applies to native elements in VS Code, such as the menu or dialogs on macOS. The setting supports the following values:

* `default`: the color theme matches that of the operating system (default option).
* `auto`: pick `light` or `dark`, depending on the VS Code theme
* `light`: pick `light` system theme
* `dark`: pick `dark` system theme

Below is an example of how this applies to the macOS context menus, showing `light` in the top part, and `dark` in the bottom part:

![Showing both 'light' and 'dark' system theme applied to a context menu.](images/1_86/system-theme.png)

### Allow closing untitled workspaces without confirmation

A new setting `window.confirmSaveUntitledWorkspace` lets you disable showing a confirmation dialog when closing a window with an untitled workspace. There's now also a checkbox on the dialog to disable showing it. The `window.confirmSaveUntitledWorkspace` setting is enabled by default to keep the current behavior.

![Untitled workspace confirmation dialog showing checkbox to always discard changes.](images/1_86/untitled-workspace.png)

> **Note:** learn more about untitled workspaces [in our documentation](https://code.visualstudio.com/docs/editor/workspaces).

### Toggle Word Wrap in Output panel

The Output panel now supports toggling word wrap by using the **View: Toggle Word Wrap** command. This is useful for viewing long lines of text in the Output panel.

### Open Output panel in a new window

The Output panel now has an **Open Output in New Window** action in the panel title menu to view the Output panel in a separate window.

### Update Extensions from CLI

You can now update extensions from the command line by using the `--update-extensions` argument. This will update all installed extensions to their latest version.

### Hovers in the Quick Pick now use custom hovers

We've moved several hovers in the Quick Pick over to using custom hovers instead of native rendering. This allows for richer rendering & more consistency across the product.

![Quick Pick hover rendered with custom hover instead of native.](images/1_86/quick-pick-hovers.png)

## Review multiple files in diff editor

With this release, the multi diff editor is now enabled for all users. The multi diff editor lets you view changes across multiple files in one scrollable view:

<video src="images/1_85/multiFileDiffEditor.mp4" autoplay loop controls muted title="Use the multi-file diff view to compare files changes across multiple files."></video>

Currently the multi diff editor can be used to review local changes, staged changes, incoming/outgoing changes, stashes, and changes from pull requests.
It can be opened by selecting the various new **View Changes** actions, which can be recognized by the multi file diff icon.

When the diff editor is opened from the Source Control view, it dynamically updates the view as files become changed or staged.

The multi diff editor is still under development, so expect to see more improvements and bug-fixes in the coming releases.

### Multi-file diffs in the GitHub Pull Requests extension

When the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension is installed, you can set `"githubPullRequests.focusedMode": "multiDiff"` to automatically open the multi-file diff editor when opening a pull request.

When you're set up for [github.dev](https://github.dev), you can press `.` on a pull request on GitHub and [github.dev](https://github.dev) opens with the multi-file diff editor to review the changes.

## Editor

### Paste text/html content

The **Paste As...** command now lets you paste `text/html` content in the clipboard:

![Using 'Paste As' to paste HTML content.](images/1_86/paste-html.png)

When you copy content from a web page for example, you can use this to paste the content's HTML instead of pasting it as plain text:

<video src="images/1_86/paste-html.mp4" title="Pasting content copied from a webpage into a html file. The Live Preview extension is being used to show a preview of the HTML." autoplay loop controls muted></video>

## Source Control

### Commit input customizations

This milestone we have added a new setting, `scm.inputMinLineCount`, that can be used to control the initial height of the commit input field. The setting that controls the maximum size of the commit input field has been renamed from `scm.inputMaxLines` to `scm.inputMaxLineCount` so that it matches the naming convention of similar settings.

Users can now use language-specific editor settings to further customize the commit input field. Language-specific editor settings can be used to define editor rules and disable word wrapping:

```json
"[scminput]": {
    "editor.rulers": [
      50,
      72
    ],
    "editor.wordWrap": "off"
  }
```

### Source Control Repositories view improvements

To help manage workspaces with large number of repositories better, we have added a new command to the repository context menu: **Close Other Repositories**. We have also added multi-selection support for both the **Close Repository** and **Close Other Repositories** commands, so users can close multiple repositories at once, or close all repositories except for a few.

The Source Control Repositories view can get crowded because of actions that are contributed by extensions. To help with this, we have added the ability to hide contributed actions by right clicking on the action and choosing **Hide**. The hidden actions are moved into the **...** menu. All actions, except the **Checkout** and **Sync** can be hidden.

### Incoming/Outgoing changes improvements

We continue to polish the incoming/outgoing changes section in the Source Control view. This milestone, we made changes so that the **Incoming changes** node is only shown if the current branch has a tracking remote branch.

We have added **Fetch** and **Pull** actions to the **Incoming changes** node, and **Push** action to the **Outgoing changes** node.

We have added actions to view each individual commit, and all the incoming/outgoing changes that leverage the multi-file diff editor.

Users can now use a new setting, `scm.showChangesSummary`, to hide the **All Changes** entry.

### Ability to merge tags

We have addressed a long-standing feature request by adding the capability to merge tags. A new command is added to the command palette, **Git: Merge...** that replaces the **Git: Merge Branch...** command. Invoking the **Git: Merge...** command will display a quick pick control that lists both branches and tags.

### View Stash command

We have added a new command, **Git: View Stash...**, that enables users to view any stash from the repository in the multi-file diff editor. When viewing a git stash, the editor title menu will contain actions to apply/pop, or drop the stash.

### Commit signing using SSH keys

This milestone we have added the capability to sign commits using an SSH key with a passphrase. When signing a commit with the SSH key, VS Code will display a quick pick control at the top of the screen to prompt for the passphrase. VS Code does not cache the passphrase and will prompt for it each time a commit is signed.

## Notebooks

### Floating window support

Notebook editors can now be opened in a floating window. You can drag the notebook tab out of the main window to open it in a new window, or use the **View: Move Editor into New Window** command.

<video src="images/1_86/notebook-floating-window.mp4" autoplay loop controls muted title="Drag notebook tab out of the main window to open it in a new,floating window."></video>

### Built-in variable view

We have added an experimental variable view to the Run and Debug view that can be used to view variables from a notebook kernel, if the extension provides them. This functionality can be enabled with this setting:

```json
"notebook.experimental.variablesView": true
```

<video src="images/1_86/notebook-variables-view.mp4" title="Notebook variable tree view in the Run and Debug view." autoplay loop controls muted></video>

### Notebook Sticky Scroll

The notebook editor's Sticky Scroll feature has received significant polishing to align its style with the rest of the workbench, and to add folding controls to each markdown element.

<video src="images/1_86/notebook-sticky-scroll-polish.mp4" title="Using sticky scroll with folding in a notebook." autoplay loop controls muted></video>

## Terminal

### Background shown under selection

When GPU acceleration is enabled, cells with non-default background colors will now be rendered "underneath" the regular selection color.

![Cell background colors in the terminal are now retained, even when selected.](./images/1_86/terminal-selection-bg.png)

### Zoom terminal via mouse wheel

The terminal can now be zoomed in and out with the mouse wheel while holding `kbstyle(Ctrl)`, when enabled with this setting:

```json
"terminal.integrated.mouseWheelZoom": true
```

### Multi-line paste warning improvements

In previous versions, `terminal.integrated.enableMultiLinePasteWarning` accepted either `true` (default) or `false`. When `true` was active, it would always show the warning, except when either bracketed paste mode is enabled or the line ends in a `\n` character. This setting now accepts a string with the following options:

* `auto`: Previous true behavior
* `always`: Always show a warning
* `never`: Never show a warning

Additionally, there is a new button in the dialog to paste the string as a single line, which removes all `\r` and `\n` characters.

### Link improvements

File protocol URIs (`file://`) now support line and column number extensions at the end, like most other links. The new `#<line>` format is also supported.

### Terminal voice commands

The new **Terminal: Start Terminal Voice** and **Terminal: Stop Terminal Voice** commands enable speech-to-text sessions in the terminal.

To use this new capability, install the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension.

## Tasks

### Shorthand for path separator variable

The new `${/}` variable can be used as a shorthand for the existing `${pathSeparator}` variable.

## Debug

### Triggered breakpoints

You can now set breakpoints that are automatically enabled once another breakpoint is hit. For example, this can be useful when diagnosing failure cases in code that happen only after a certain precondition. Triggered breakpoints work for all languages, and conditional breakpoints might also be used as the trigger.

Triggered breakpoints can be set by right-clicking on the glyph margin, selecting **Add Triggered Breakpoint...**, and then choosing which other breakpoint enables this breakpoint.

<video src="images/1_86/debug-triggered-breakpoint.mp4" title="Add a triggered breakpoint that depends on another breakpoint to be hit." autoplay loop controls muted></video>

_Theme: [Codesong](https://marketplace.visualstudio.com/items?itemName=connor4312.codesong) (preview on [vscode.dev](https://vscode.dev/editor/theme/connor4312.codesong))_

### Close readonly files when a session ends

When attached to a debugger, there can be internal or virtual source files that don't exist on-disk, which are opened as readonly. There is a new setting, `debug.closeReadonlyTabsOnEnd`, that causes VS Code to automatically close any opened readonly files from the debug session when the session ends.

## Testing

### More ways to run tests

Holding `kbstyle(Alt)` while clicking on a test decoration, now runs that test in debug mode, when available.

There are now context menu actions in the Explorer view, which can be used to run all tests declared in a file or folder.

### Finalized `TestRunProfile.isDefault/onDidChangeDefault` APIs for extension authors

Previously, `TestRunProfile.isDefault` only controlled the initial default state of a test run profile, and it was never changed or read after that point.

In this release, users changing their selected profiles will be reflected in the `TestRunProfile.isDefault` property and fire a corresponding `TestRunProfile.onDidChangeDefault` event. Likewise, when extensions change `isDefault`, this will cause the selected profiles in the UI to update.

## Languages

### Paste a URL to automatically create a Markdown link

Want to turn that link you copied into a Markdown link? When you paste a URL into a Markdown file with text selected, VS Code now automatically inserts a Markdown link:

<video src="images/1_86/md-paste-url.mp4" title="Pasting a URL to insert a Markdown link." autoplay loop controls muted></video>

This feature is controlled by the `markdown.editor.pasteUrlAsFormattedLink.enabled` setting. You can change it to customize this behavior:

* `smartWithSelection` — The default. This enables the feature when you have selected text and are not inside a special element, such as a code block.
* `smart` — Same as `smartWithSelection` but does not require a selection.
* `always` — Always paste urls as a Markdown links
* `never` — Disable pasting as Markdown links.

After you've pasted a value, you can always switch to paste the content in a different way by using the Paste control.

### Configure audio/video snippets for Markdown

The new `markdown.editor.filePaste.audioSnippet` and `markdown.editor.filePaste.videoSnippet` settings let you customize how audio and video files are inserted into Markdown on drop or paste. The snippet can use the following variables:

* `${src}` — The resolved path of the audio/video file.
* `${title}` — The title used for the audio/video. A snippet placeholder will automatically be created for this variable.

### New Less grammar

The previous Less grammar has been archived and VS Code now uses the grammar from [Better-Less](https://github.com/radium-v/Better-Less) for Less syntax highlighting.

### New Go grammar

The Go syntax highlighting grammar has been changed to use [Go Syntax](https://github.com/worlpaker/go-syntax), which is more actively maintained than the previous grammar.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* Create a `devcontainer.json` in your user data folder.
* Specify build options in `devcontainer.json` when using a Dockerfile.

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_86.md).

## Contributions to extensions

### GitHub Copilot

It is now possible to change the font family of the inline suggestions by using the setting `editor.inlineSuggest.fontFamily`.

#### Confirm inline chat before saving

We emphasize responsible usage of AI, especially when it comes to source code. Therefore, we have added a new setting that asks users for confirmation before saving code that was generated by Copilot. This setting, `inlineChat.acceptedOrDiscardBeforeSave`, is enabled by default.

![Inline chat save consent message.](images/1_86/inline-chat-save-consent.png)
_Theme: [GitHub Light Default](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Light%20Default))_

When the setting is enabled, a file save will wait for the user to Accept or Discard any pending inline chat session. This also applies when Auto Save is enabled, which will be temporarily disabled until inline chat has ended.

#### Hold to Speak mode

A little while back, we added speech support to VS Code. Install the [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension, which adds speech-to-text support for Chat input fields.

For inline chat we have now added "hold to speak", which works as follows:

* Press `kbstyle(Cmd+I)` or `kbstyle(Ctrl+I)` to trigger inline chat.
* Hold the keys pressed and notice how voice recording automatically starts.
* Release the keys to stop recording and send your request to Copilot.

<video src="images/1_86/inline-chat-hold-speak.mp4" title="Hold Cmd+I to enter speak mode, use speech, and then release to send the chat request." autoplay loop controls muted></video>
_Theme: [GitHub Light Default](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme) (preview on [vscode.dev](https://vscode.dev/editor/theme/GitHub.github-vscode-theme/GitHub%20Light%20Default))_

This new mode also comes with a setting, which is enabled by default, but can be disabled with `inlineChat.holdToSpeech`.

#### Preview: Inline Chat Quick Voice

In addition to "hold to speak", we are experimenting with a lighter alternative UI for this functionality. Activate Quick Voice by pressing `kbstyle(Cmd+K)` or `kbstyle(Ctrl+K)` and then holding `I`. While holding `I`, a more lightweight recording UI is shown. Upon release, the request is sent.

![Quick Voice showing only a microphone indicator and the text of the recorded voice message.](images/1_86/inline-chat-quick-speak.png)

#### Inline Chat Live Mode

The past release had a new experimental inline chat mode, called `live3`. It provides a smoother streaming experience and easier to digest diff view. This has now graduated and replaced the former `live`-mode. The default is still `livePreview`, but we encourage users to try out `live`. We are also running an experiment to learn which mode works best.

#### Light bulb for AI fixes (sparkle)

To invoke Copilot, you can also use the light bulb indicator in the editor. Make a selection or move the cursor to a new line, select the light bulb, and then select **Modify with Copilot** or **Generate with Copilot**.

If there are no other code actions, the light bulb shows as a sparkle and will directly open inline chat.

![Inline chat opened from the sparkle.](images/1_86/inline-chat-opened-from-sparkle.gif)

#### `#file` context variable

There are a few context variables that you can already use by typing `#` in the chat input, and we've added `#file` to let you include a specified file in your workspace as context with your chat prompt. Pick `#file` from the suggestion control in the input, and then select a file from the Quick Pick that appears.

If possible, the full contents of the file will be included. If that is too large to fit into the context window, an outline of the file will be included that includes functions and their descriptions without implementations. If the outline is also too large, then the file won't be part of the prompt.

![File context example in the Copilot Chat View.](images/1_86/file-context.png)

#### Default context in chat

Previously, we would include two types of context from the active code editor with your chat requests by default:

1. If there was a text selection, then the selection would be included
2. And if there was no selection, then the range of code in the visible viewport of the active editor would be included

We've found that the second type can lead to confusion when you want to ask a generic question, but the LLM interprets it as a question about the code in your editor that was included as context. We are experimenting with not including code in the viewport by default, but only including code that is selected.

Of course, you will often want to ask about the code you can see in your editor, so we've added a new context variable, `#editor`.

![Editor context example in the Copilot Chat View.](images/1_86/editor-context.png)

#### Added "Clear All Workspace Chats" command

Each time you use the `+` button to start a new chat, your previous chat is stored in your workspace chat history, which you can access by using the clock icon in the header of the Chat view. We've added the **Clear All Workspace Chats** command to clear all of your past chat history for convenience.

#### Commit message language

The code to generate git commit messages now uses the `github.copilot.chat.localeOverride` setting to generate commit messages in a specific language.

### Ask for Additional permissions for private repositories

To enable additional workspace search features for private repositories, we require additional permissions. If we detect that we don't have these permissions already, we will ask for them at startup. Once granted, we'll securely store the session for the future.

![Modal window asking for additional authentication for a private repository.](images/1_86/authentication.png)

### Python

#### Python Debugger extension installed by default

The [Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) extension is now installed by default alongside the Python extension.

The Python Debugger extension aims to separate the debugging functionality from the main Python extension to prevent compatibility issues. This ensures that even as the Python extension drops support for older Python versions (for example, Python 3.7), you can continue debugging projects with those versions without downgrading your Python extension. It also delivers platform-specific builds, ensuring you only receive the components relevant to your specific operating system, reducing download times and unnecessary overhead.

This new extension replicates all the functionality available in the main Python extension, and more.

To ensure you are using the new Python Debugger extension, replace  `"type": "python"` with `"type": "debugpy"` from your `launch.json` configuration file. In the future, the Python extension will no longer offer debugging support, and we will transition all debugging support to the Python Debugger extension for all debugging functionality.

#### Create Environment option in the Python interpreter quick pick

You can now more conveniently create a Python environment from the Python interpreter Quick Pick. Run the **Python: Select Interpreter** command, and then select the **Create Environment** option to create a new virtual environment for your project.

![Create Environment option in the Python interpreter quick pick.](images/1_86/python-create-env-from-quick-pick.png)
_Theme: [Catppuccin Mocha](https://marketplace.visualstudio.com/items?itemName=Catppuccin.catppuccin-vsc) (preview on [vscode.dev](https://vscode.dev/editor/theme/Catppuccin.catppuccin-vsc/Catppuccin%20Mocha))_

#### Improved display for workspace conda environments

The Python interpreter Quick Pick now displays the name of conda environments located in the workspace, to make it easier to identify them.

![Workspace conda environment names displayed in the Python interpreter Quick Pick.](images/1_86/conda-env-names.png)

#### Support for multi-level pytest classes

When using multi-level pytest classes, the top-level class is now displayed in the Test Explorer. Previously, only the lower level classes were displayed.

![Multi-level pytest classes displayed in the Test Explorer.](images/1_86/pytest-nested-classes.png)
_Theme: [Catppuccin Mocha](https://marketplace.visualstudio.com/items?itemName=Catppuccin.catppuccin-vsc) (preview on [vscode.dev](https://vscode.dev/editor/theme/Catppuccin.catppuccin-vsc/Catppuccin%20Mocha))_

### Jupyter

#### Finalized Jupyter Kernel Execution API for extension authors

The Jupyter extension's API for executing code against Jupyter kernels has been finalized. Extensions can use the API to execute code against kernels.

Examples of API usage can be found in the [Jupyter Kernel Execution Sample](https://github.com/microsoft/vscode-extension-samples/tree/main/jupyter-kernel-execution-sample).

The npm package [`@vscode/jupyter-extension`](https://www.npmjs.com/package/@vscode/jupyter-extension) contains all the TypeScript type definitions.

When an extension attempts to access a Jupyter kernel, the user is prompted to grant or revoke access to the kernels.
Access to Jupyter kernels is granted by the user per extension. This means that the user can grant access to extension `A`, and revoke access to extension `B`.

Users can manage (grant/revoke) access to the Jupyter kernels per extension via the command **Jupyter: Manage Access To Jupyter Kernels**.

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues. New features include:

* Resolving conflicts for the currently checked out PR.
* A `groupBy` property for issue queries.
* Clicking permalinks in a checked out PR will open the file in the editor.
* Hovering on comment reactions shows who reacted.
* Setting `"githubPullRequests.focusedMode": "multiDiff"` will open the multi-diff editor with all the files in the PR upon checkout.

Review the [changelog for the 0.80.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0800) release of the extension to learn about the other highlights.

## Preview Features

### TypeScript 5.4 beta support

We now support the beta release of TypeScript 5.4. Check out the [TypeScript 5.4 beta blog post](https://devblogs.microsoft.com/typescript/announcing-typescript-5-4-beta/) and [iteration plan](https://github.com/microsoft/TypeScript/issues/56948) for details on this release. Highlights include:

* Initial work on AI-powered refactorings.
* A new [`NoInfer` intrinsic](https://github.com/microsoft/TypeScript/pull/56794) that can help library and types authors stop incorrect type inference.
* Improvements to [type refinements](https://github.com/microsoft/TypeScript/issues/56948) and type checking.

To start using the TypeScript 5.4 beta, just install the [TypeScript Nightly extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next). Please share feedback and let us know if you run into any bugs with TypeScript 5.4.

### Quick Search Improvements

In version 1.82, we introduced [Quick Access Text Search ("Quick Search")](https://code.visualstudio.com/updates/v1_82#_quick-access-text-search), which allows users to search from a picker. 🔎

Quick Search now has the following features:

* Editor preview on active pick. See the result in the context of the editor as you browse.
* Easier navigation from Quick Search to the Search view. Select the button next to the input or a result to transfer the results to the Search view.

<video src="images/1_86/quick-search-improvements.mp4" title="Editor previews Quick Search results and direct navigation from Quick Search to Search view." autoplay loop controls muted></video>
_Theme: [Night Owl](https://marketplace.visualstudio.com/items?itemName=sdras.night-owl)  (preview on [vscode.dev](https://vscode.dev/editor/theme/sdras.night-owl))_

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [@vscode/dts](https://github.com/microsoft/vscode-dts) and run `npx @vscode/dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Test Coverage API

This milestone, the test coverage's API and in-editor experience have reached feature-complete state. We encourage extension authors to try them out and provide feedback prior to their forecasted finalization in the VS Code 1.87 release.

While the API is too lengthy to include here, we believe it to be fairly straightforward, and would welcome your input on the proposal in [issue #123713](https://github.com/microsoft/vscode/issues/123713).

### Chat Agent API

We've been working on an API to enable extension authors to contribute their own chat agents to the Chat view in VS Code. We've made a few changes to the API, including:

* The history context passed to the chat agent now properly represents which chat agent and command was invoked by a given message, the values of variables in history messages, and what the result was.
* `ChatAgentTask` has been removed, and it's now possible to use `ChatProgressMessage` in the middle of the response stream to cover the same scenario.
* All "slashCommand"-related terms have been renamed to "subCommand".

If you'd like to try out the API, you can start with our [sample chat agent extension](https://github.com/microsoft/vscode-extension-samples/tree/main/chat-agent-sample), and subscribe to [issue #199908](https://github.com/microsoft/vscode/issues/199908) for updates to the API.

### Code Action Highlights API

For refactorings such as Extract method or Move to file, it's not always clear what code the action will apply to. This is especially true for languages like TypeScript that try to intelligently expand the user's current selection.

This API allows code actions to provide one or potentially many ranges (`Range[]`) that they will apply to. The range will be highlighted as the user hovers or arrows through the Code Action list.

* For refactorings, this range would be the code in the current file that will be affected by the refactoring.
* For Quick Fixes, you could highlight the related diagnostics. This is already done in Quick Fixes that are invoked from `editor.codeActionWidget.includeNearbyQuickFixes`.

### Issue Reporter API

Small additions to the proposed API, which allow both `IssueUriRequestHandler` and `IssueDataProvider` to be used together.

Also improvements for the `vscode.commands.openIssueReporter` command, which can now contribute additional extension data and modify the URL of where the GitHub issue is directed to.

Subscribe to [issue #197863](https://github.com/microsoft/vscode/issues/196863) for updates or changes to the API and `openIssueReporter` command.

### Comment reaction reactor API

The `CommentReaction` interface has a new `reactors` property, so that extensions can indicate who reacted to a comment. These `reactors` are currently displayed in the hover of a comment reaction.

See [issue #201131](https://github.com/microsoft/vscode/issues/201131) to track progress on this API.

## Finalized APIs

### New `workspace.save` and `workspace.saveAs` APIs

The new finalized APIs `workspace.save` and `workspace.saveAs` allow extensions to trigger the flow of saving an editor, either to its resource, or by asking the user to provide a resource.

All methods for saving return the resulting `Uri`, or `undefined` if the operation was canceled. Untitled files will always ask the user for a destination, unless a path is already associated.

### Readonly message for `FileSystemProvider` API

When registering a `FileSystemProvider` with `registerFileSystemProvider`, the `options` can be set to mark the entire file system as readonly. Now, if the `isReadonly` option is set to a `MarkdownString`, then that message will be shown in the editor when the user tries to edit the file in the filesystem.

![Example of readonly message showing in the editor.](images/1_86/readonly-message.png)

## Engineering

### Housekeeping

In early December we went through our annual housekeeping. We achieved a net-reduction of 1891 issues across our repositories. The following chart nicely illustrates the need for and the impact of our annual house keeping iteration.

![Chart of issue counts over the last years](images/1_86/housekeeping.png)

### Markdown Language Service 0.4 release

The [Markdown Language Service package](https://www.npmjs.com/package/vscode-markdown-languageservice) powers VS Code's built-in Markdown support. The new 0.4 release bundles a number of improvements we've made over the past half year, which other tooling and editors can benefit from. These are some of the highlights:

* Enable document links, references, and rename for HTML fragments in Markdown.
* Fix potential catastrophic back-tracking in a regular expression.
* Avoid adding extra encoding on completions.
* Use fuzzy matching for workspace symbol search.
* Fix a number of cases around link detection/validation.

### New `localize2` function to make crafting `ILocalizedString`s more easily

In VS Code's core codebase, we use a type called `ILocalizedString` to render both a localized and an English string next to each other. You might have seen this if you use a language pack and you open the Command Palette.

Previously, we had to write these expressions like:

```ts
const str = { value: localize('id', "Hello World"), original: "Hello World" };
```

This can now be expressed as follows:

```ts
const str = localize2('id', "Hello World");
```

Our localization pipeline will then pick up these `localize2` occurrences.

### Electron 27 update

In this milestone, we are promoting the Electron 27 update to users on our stable release. This update comes with Chromium 118.0.5993.159 and Node.js 18.17.1. We want to thank everyone who self-hosted on Insiders builds and provided early feedback.

### Linux minimum requirements update

In this milestone, we have updated the toolchains to build our desktop client. From this release onwards, VS Code desktop is only compatible with Linux distributions based on glibc 2.28 or later, and glibcxx 3.4.25 or later, such as Debian 10, RHEL 8, or Ubuntu 20.04.

If you are unable to upgrade your Linux distribution, the recommended alternative is to use our [web client](https://code.visualstudio.com/docs/setup/vscode-web). If you would like to use the desktop version, then you can [download the VS Code release 1.85](https://code.visualstudio.com/updates/v1_85). Depending on your platform, make sure to disable updates to stay on that version. A good recommendation is to set up the installation with [Portable Mode](https://code.visualstudio.com/docs/editor/portable).

## Notable fixes

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@starball5 (starball)](https://github.com/starball5)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
* [@okineadev (Okinea Dev)](https://github.com/okineadev)

### Pull requests

Contributions to `vscode`:

* [@a-stewart (Anthony Stewart)](https://github.com/a-stewart)
  * Fire an input event on `editor.action.clipboardPasteAction` [PR #198822](https://github.com/microsoft/vscode/pull/198822)
  * Log `extensionLocation` not `location` in error message of `getCustomBuiltinExtensionsFromLocations` [PR #200748](https://github.com/microsoft/vscode/pull/200748)
* [@Ajaykumbhare (Ajay Kumbhare)](https://github.com/Ajaykumbhare): fix: terminal renaming not functioning as expected in editor area [PR #202270](https://github.com/microsoft/vscode/pull/202270)
* [@andrewbranch (Andrew Branch)](https://github.com/andrewbranch): Fix preferTypeOnlyAutoImports preference getter [PR #201376](https://github.com/microsoft/vscode/pull/201376)
* [@aramikuto (Aleksandr Kondrashov)](https://github.com/aramikuto): Update IExplorerView interface [PR #201992](https://github.com/microsoft/vscode/pull/201992)
* [@audreygao (Audrey Ya Gao)](https://github.com/audreygao): Fix #193468 - Fix bug with error notification when pressing "l" on non-expandable reference item. [PR #199996](https://github.com/microsoft/vscode/pull/199996)
* [@bricker (Bryan Ricker)](https://github.com/bricker): doc typo fix [PR #202429](https://github.com/microsoft/vscode/pull/202429)
* [@callumok2004 (Callum OKane)](https://github.com/callumok2004): Add `bun.lockb` to default file nesting under package.json [PR #201065](https://github.com/microsoft/vscode/pull/201065)
* [@CGNonofr (Loïc Mangeonjean)](https://github.com/CGNonofr): Take into account models created beforehand [PR #199652](https://github.com/microsoft/vscode/pull/199652)
* [@chartrandf (Francis Chartrand)](https://github.com/chartrandf): timeline: use follow option so timeline follow file beyond renames [PR #187174](https://github.com/microsoft/vscode/pull/187174)
* [@conwnet (netcon)](https://github.com/conwnet): fix: `workbench.editorAssociations` not work in workbench configurationDefaults [PR #194087](https://github.com/microsoft/vscode/pull/194087)
* [@cpendery (Chapman Pendery)](https://github.com/cpendery)
  * fix: terminal suggestion positioning [PR #199420](https://github.com/microsoft/vscode/pull/199420)
  * fix: accepting terminal completions cursor positions  [PR #199706](https://github.com/microsoft/vscode/pull/199706)
  * build: enable suggestions when 'shellIntegration.suggestEnabled' is true [PR #199821](https://github.com/microsoft/vscode/pull/199821)
  * refactor: xterm suggestion addon into terminalContrib [PR #200107](https://github.com/microsoft/vscode/pull/200107)
* [@DiscreteTom (DiscreteTom)](https://github.com/DiscreteTom): fix #193746 [PR #197523](https://github.com/microsoft/vscode/pull/197523)
* [@effectivecui](https://github.com/effectivecui): avoid to enter the infinite loop when item.children is empty. [PR #201701](https://github.com/microsoft/vscode/pull/201701)
* [@Flanker32 (Hanxiao Liu)](https://github.com/Flanker32): Update java workspace tags [PR #197729](https://github.com/microsoft/vscode/pull/197729)
* [@gayanper (Gayan Perera)](https://github.com/gayanper): Add support for breakpoint dependencies [PR #166202](https://github.com/microsoft/vscode/pull/166202)
* [@GenericTSDeveloper](https://github.com/GenericTSDeveloper): Fixed issue Misalignment of suggestion details widget (https://github.com/microsoft/monaco-editor/issues/3373) [PR #198730](https://github.com/microsoft/vscode/pull/198730)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Implement keyboard navigation between list find/filter matches [PR #180078](https://github.com/microsoft/vscode/pull/180078)
  * Preserve sort order when filtering Git branch / tag quickpicks (fix #199471) [PR #199473](https://github.com/microsoft/vscode/pull/199473)
  * `Git: Merge Branch...` picker duplicates branch name in description (fix #199562) [PR #199742](https://github.com/microsoft/vscode/pull/199742)
  * Fix `Customize Layout` bugs related to Activity Bar (fix #200571) [PR #200572](https://github.com/microsoft/vscode/pull/200572)
* [@gregvanl (Greg Van Liew)](https://github.com/gregvanl): Add missing commas [PR #201137](https://github.com/microsoft/vscode/pull/201137)
* [@harbin1053020115 (ermin.zem)](https://github.com/harbin1053020115): fix: select theme according to current color theme in dev mode [PR #190035](https://github.com/microsoft/vscode/pull/190035)
* [@hsfzxjy (Xie Jingyi)](https://github.com/hsfzxjy): Defer onDidStyleChange to Repl being visible [PR #200473](https://github.com/microsoft/vscode/pull/200473)
* [@iisaduan (Isabel Duan)](https://github.com/iisaduan): fix typescript/54492: check if file rename changes extension [PR #200220](https://github.com/microsoft/vscode/pull/200220)
* [@impressivewebs (Louis Lazaris)](https://github.com/impressivewebs)
  * Fixed some CSS terminology [PR #202125](https://github.com/microsoft/vscode/pull/202125)
  * Corrected CSS Hover Documentation text [PR #202142](https://github.com/microsoft/vscode/pull/202142)
* [@irgendwr (Jonas Bögle)](https://github.com/irgendwr): Add: "Close Other Repositories" in git scm menu [PR #130931](https://github.com/microsoft/vscode/pull/130931)
* [@jacekkopecky (Jacek Kopecký)](https://github.com/jacekkopecky): Fixes #198566: ellipsize left-cut search preview [PR #198567](https://github.com/microsoft/vscode/pull/198567)
* [@jaraco (Jason R. Coombs)](https://github.com/jaraco): Add support for extracting environment from xonsh. [PR #201036](https://github.com/microsoft/vscode/pull/201036)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fix vscode.tasks.executeTask error Unexpected: Task does not exist after task.terminate() [PR #200872](https://github.com/microsoft/vscode/pull/200872)
  * Fix single tab context menu actions sometimes don't work [PR #201021](https://github.com/microsoft/vscode/pull/201021)
  * Fix task progress indicator does not appear after the first time [PR #201064](https://github.com/microsoft/vscode/pull/201064)
  * Fix single terminal tab rename action does not work [PR #201128](https://github.com/microsoft/vscode/pull/201128)
* [@johnsoncodehk (Johnson Chu)](https://github.com/johnsoncodehk): Fix optionalReplacementSpan not being applied to completion entries [PR #200945](https://github.com/microsoft/vscode/pull/200945)
* [@JoyceGu (Joyce Gu)](https://github.com/JoyceGu): add JS Vector DB libraries [PR #199980](https://github.com/microsoft/vscode/pull/199980)
* [@jtbandes (Jacob Bandes-Storch)](https://github.com/jtbandes): Update Swift.tmLanguage [PR #200698](https://github.com/microsoft/vscode/pull/200698)
* [@keerthivasansa (Keerthi Vasan S A)](https://github.com/keerthivasansa): Workbench actions for Tree Fuzzy Search Toggle [PR #175137](https://github.com/microsoft/vscode/pull/175137)
* [@khreenberg (Kim Reenberg)](https://github.com/khreenberg): fix: fallback to strings for non-executable libc.so.6 [PR #202581](https://github.com/microsoft/vscode/pull/202581)
* [@kkocdko (kkocdko)](https://github.com/kkocdko): Fix platform detection after Node.js 21 [PR #200935](https://github.com/microsoft/vscode/pull/200935)
* [@ksg97031 (KSG)](https://github.com/ksg97031): docs: Fix typos in description of multiple argument for Go To commands [PR #201122](https://github.com/microsoft/vscode/pull/201122)
* [@Leask (Sixia "Leask" Huang)](https://github.com/Leask): fix: tweak check-requirements for calling ldconfig [PR #202645](https://github.com/microsoft/vscode/pull/202645)
* [@loganrosen (Logan Rosen)](https://github.com/loganrosen): Update Stack Overflow tag in CONTRIBUTING.md [PR #126199](https://github.com/microsoft/vscode/pull/126199)
* [@mahmoudsalah1993 (Mahmoud Salah)](https://github.com/mahmoudsalah1993): For open diff editors, resolve the underlying original editor to set … [PR #201597](https://github.com/microsoft/vscode/pull/201597)
* [@Malix-off (Malix)](https://github.com/Malix-off): Remove unused import in `extensions/git/src/api/git.d.ts` [PR #200797](https://github.com/microsoft/vscode/pull/200797)
* [@marrej (Marcus Revaj)](https://github.com/marrej): # Fix dropping of partialAccepts [PR #199663](https://github.com/microsoft/vscode/pull/199663)
* [@MaxBR97](https://github.com/MaxBR97): Added scm.inputMinLines configuration [PR #200551](https://github.com/microsoft/vscode/pull/200551)
* [@mohammadamin16 (Mohammad Amin)](https://github.com/mohammadamin16): debug: close read-only tabs on end debug session [PR #199898](https://github.com/microsoft/vscode/pull/199898)
* [@mrienstra (Michael Rienstra)](https://github.com/mrienstra): docs: document new `configuration.markdown.copyFiles.destination` options [PR #203391](https://github.com/microsoft/vscode/pull/203391)
* [@MrJithil (Jithil P Ponnan)](https://github.com/MrJithil): chore: replace deprecated octal escape sequences with hex [PR #197518](https://github.com/microsoft/vscode/pull/197518)
* [@ngarside (Nathan Garside)](https://github.com/ngarside): Add syntax highlighting for `*.repo` files [PR #199859](https://github.com/microsoft/vscode/pull/199859)
* [@nrayburn-tech (Nicholas Rayburn)](https://github.com/nrayburn-tech): npm extension - check for root package.json before findFiles [PR #124803](https://github.com/microsoft/vscode/pull/124803)
* [@poeck (Paul Köck)](https://github.com/poeck): chore: update copyright year [PR #202344](https://github.com/microsoft/vscode/pull/202344)
* [@r3m0t (Tomer Chachamu)](https://github.com/r3m0t): Wait for paste to finish in paste command (fixes #196161) [PR #196516](https://github.com/microsoft/vscode/pull/196516)
* [@RedCMD (RedCMD)](https://github.com/RedCMD)
  * Change lowercase `l` to uppercase `L`. `asp-vb-net.tmLanguage.json` [PR #201981](https://github.com/microsoft/vscode/pull/201981)
  * `onDidChangeEmmiter` fires constantly when it shouldn't [PR #202198](https://github.com/microsoft/vscode/pull/202198)
* [@remcohaszing (Remco Haszing)](https://github.com/remcohaszing): Remove bigNumbersDelta [PR #194712](https://github.com/microsoft/vscode/pull/194712)
* [@RobbyCBennett (Robby Bennett)](https://github.com/RobbyCBennett): make gotoErrorWidget styling of relatedInformation more consistent with that of markerHoverParticipant [PR #195409](https://github.com/microsoft/vscode/pull/195409)
* [@robertohuertasm (Roberto Huertas)](https://github.com/robertohuertasm): feat(stash): return the stash result [PR #177732](https://github.com/microsoft/vscode/pull/177732)
* [@ronakj (Ronak Jain)](https://github.com/ronakj): Fix tsserver crashing when using custom node path [PR #201966](https://github.com/microsoft/vscode/pull/201966)
* [@russelldavis (Russell Davis)](https://github.com/russelldavis): Fix race condition with restoration of problems when closing a file [PR #183271](https://github.com/microsoft/vscode/pull/183271)
* [@samhanic](https://github.com/samhanic): [CLI] extension update command [PR #199893](https://github.com/microsoft/vscode/pull/199893)
* [@shubhisroking (Shubh)](https://github.com/shubhisroking): Replace the deprecated canceled with Cancellation Error. [PR #197605](https://github.com/microsoft/vscode/pull/197605)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke)
  * fix: memory leak in menubar [PR #198052](https://github.com/microsoft/vscode/pull/198052)
  * fix: memory leak in defaultWorkerFactory [PR #198710](https://github.com/microsoft/vscode/pull/198710)
* [@sparxooo (sparxooo)](https://github.com/sparxooo): Fix for #200257 plus existing trailing non-numerics regex fix [PR #200919](https://github.com/microsoft/vscode/pull/200919)
* [@Splizard (Quentin Quaadgras)](https://github.com/Splizard): Mobile/Android: support text selection with a pen/stylus. [PR #198578](https://github.com/microsoft/vscode/pull/198578)
* [@susiwen8 (susiwen8)](https://github.com/susiwen8)
  * fix: #200046 [PR #200485](https://github.com/microsoft/vscode/pull/200485)
  * fix: `install` in `npm script` shouldn't be opened [PR #201082](https://github.com/microsoft/vscode/pull/201082)
* [@VDisawal](https://github.com/VDisawal): #198975: saveValue  => historyNavigator.add [PR #199142](https://github.com/microsoft/vscode/pull/199142)
* [@wenfangdu (Wenfang Du)](https://github.com/wenfangdu): feat(git): added `autoClosingPairs` in language configuration files [PR #131403](https://github.com/microsoft/vscode/pull/131403)
* [@Yesterday17 (Yesterday17)](https://github.com/Yesterday17): fix ExtHostLabelService typo [PR #198855](https://github.com/microsoft/vscode/pull/198855)
* [@yiliang114 (易良)](https://github.com/yiliang114): fix: console format of fetchUrl [PR #198909](https://github.com/microsoft/vscode/pull/198909)
* [@zWingz (zWing)](https://github.com/zWingz): fix registerProfileContentHandler typos [PR #183197](https://github.com/microsoft/vscode/pull/183197)

Contributions to `vscode-flake8`:

* [@maxg203 (Max Goodridge)](https://github.com/maxg203): Fix link in README.md [PR #269](https://github.com/microsoft/vscode-flake8/pull/269)

Contributions to `vscode-hexeditor`:

* [@IngilizAdam (Hasan Tuna)](https://github.com/IngilizAdam): Select between offsets [PR #470](https://github.com/microsoft/vscode-hexeditor/pull/470)

Contributions to `vscode-languageserver-node`:

* [@debonte (Erik De Bonte)](https://github.com/debonte): Support `WorkspaceEditMetadata` in `workspace/applyEdit` [PR #1390](https://github.com/microsoft/vscode-languageserver-node/pull/1390)
* [@MariaSolOs (Maria José Solano)](https://github.com/MariaSolOs)
  * Snippet edit feature [PR #1343](https://github.com/microsoft/vscode-languageserver-node/pull/1343)
  * Allow undefined snippet edit annotation ID [PR #1408](https://github.com/microsoft/vscode-languageserver-node/pull/1408)
* [@rroessler (Reuben Roessler)](https://github.com/rroessler): Changed Successful Exit Notification for Node Client [PR #1404](https://github.com/microsoft/vscode-languageserver-node/pull/1404)
* [@sh-cho (Seonghyeon Cho)](https://github.com/sh-cho): Update build status badge with Azure pipeline [PR #1392](https://github.com/microsoft/vscode-languageserver-node/pull/1392)
* [@werat (Andy Hippo)](https://github.com/werat): Always delete feature providers in `unregister()` [PR #1380](https://github.com/microsoft/vscode-languageserver-node/pull/1380)

Contributions to `vscode-pull-request-github`:

* [@Balastrong (Leonardo Montini)](https://github.com/Balastrong): Create issue from markdown template [PR #5503](https://github.com/microsoft/vscode-pull-request-github/pull/5503)
* [@joshuaobrien](https://github.com/joshuaobrien)
  * Batch mark/unmark files as viewed [PR #4700](https://github.com/microsoft/vscode-pull-request-github/pull/4700)
  * Remove a few unused variables 🌱 [PR #5510](https://github.com/microsoft/vscode-pull-request-github/pull/5510)
* [@pouyakary (Pouya Kary ✨)](https://github.com/pouyakary): Fixes #5620 [PR #5621](https://github.com/microsoft/vscode-pull-request-github/pull/5621)

Contributions to `vscode-vsce`:

* [@sebas2day (Sebastiaan Brouwer)](https://github.com/sebas2day): feat: Support for yarn workspaces [PR #493](https://github.com/microsoft/vscode-vsce/pull/493)
* [@whosafe](https://github.com/whosafe): Filter data from other platforms [PR #912](https://github.com/microsoft/vscode-vsce/pull/912)

Contributions to `language-server-protocol`:

* [@alcarney (Alex Carney)](https://github.com/alcarney)
  * Add lsp-devtools to utilities [PR #1859](https://github.com/microsoft/language-server-protocol/pull/1859)
  * Add esbonio language server [PR #1860](https://github.com/microsoft/language-server-protocol/pull/1860)
* [@asukaminato0721 (Asuka Minato)](https://github.com/asukaminato0721): add-vscoq [PR #1869](https://github.com/microsoft/language-server-protocol/pull/1869)
* [@BeardedFish (Darian Benam)](https://github.com/BeardedFish): Add Apache Dispatcher Config to the Language Server Implementations list [PR #1875](https://github.com/microsoft/language-server-protocol/pull/1875)
* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Clarify snippet escaping rules [PR #1868](https://github.com/microsoft/language-server-protocol/pull/1868)
* [@Fomys (Louis Chauvet)](https://github.com/Fomys): Clarify CompletionItem.textEdit notes [PR #1722](https://github.com/microsoft/language-server-protocol/pull/1722)
* [@mhanberg (Mitchell Hanberg)](https://github.com/mhanberg): fix typo in workspace/configuration [PR #1823](https://github.com/microsoft/language-server-protocol/pull/1823)

Contributions to `monaco-editor`:

* [@kokovtsev (Dmitry Kokovtsev)](https://github.com/kokovtsev): fix: enable markdown in JSON completion details [PR #4210](https://github.com/microsoft/monaco-editor/pull/4210)
* [@mevisioam](https://github.com/mevisioam): Expose JSON worker [PR #4299](https://github.com/microsoft/monaco-editor/pull/4299)
* [@nora-soderlund (Nora Söderlund)](https://github.com/nora-soderlund): chore(docs): fix monaco.d.ts link [PR #4243](https://github.com/microsoft/monaco-editor/pull/4243)
* [@OfekShilon (Ofek)](https://github.com/OfekShilon)
  * Small wording fix in the site [PR #4254](https://github.com/microsoft/monaco-editor/pull/4254)
  * Remove 2 java-only operators from c++ tokenizer [PR #4255](https://github.com/microsoft/monaco-editor/pull/4255)
  * Fix suggestion for #4253 [PR #4256](https://github.com/microsoft/monaco-editor/pull/4256)
* [@soof-golan (Soof Golan)](https://github.com/soof-golan): feat: Python 3.12 keywords support [PR #4212](https://github.com/microsoft/monaco-editor/pull/4212)
* [@tamayika](https://github.com/tamayika): Change JSON symbol information to document symbol [PR #3894](https://github.com/microsoft/monaco-editor/pull/3894)

Contributions to `node-jsonc-parser`:

* [@H4ad (Vinicius Lourenço)](https://github.com/H4ad): perf(format): cache breaklines and spaces as much as possible [PR #81](https://github.com/microsoft/node-jsonc-parser/pull/81)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
