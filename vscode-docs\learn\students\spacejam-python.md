---
Order: 4
Area: students
TOCTitle: Space Jam Lessons
ContentId: bd1bf5a7-5ea5-4654-a0c5-7727e8f9e649
PageTitle: Get Started Tutorial for Space Jam project in Visual Studio Code
DateApproved: 10/22/2020
MetaDescription: A Space Jam project tutorial using the Python extension in Visual Studio Code.
---

# Learn Python with Space Jam

Inspired by the new film "Space Jam: A New Legacy," this learning path shows basketball fans how an understanding of data science and coding can support their passions, create opportunities, and even open doors to possible careers. Develop skills in Visual Studio Code, Azure, GitHub, JavaScript, and Python, to gain insights into how individual moments throughout a player's history can lead to a critical game decision in the finals.

<iframe src="https://www.youtube-nocookie.com/embed/pXkIIzihEYM" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="Learn to code with Space Jam"></iframe>

Get a glimpse into the Python programming language with this introductory learning path that requires no prior background.

<div class="module">
    <div class="info">
        <a href="https://learn.microsoft.com/training/paths/optimize-basketball-games-with-machine-learning/?WT.mc_id=LearnDrG-c9-niner&WT.mc_id=SpaceJam_Learn_-all-cxa"><h2 class="title faux-h3">Use basketball stats to optimize game play with Visual Studio Code</h2></a>
    </div>
    <p class="description">Analyze basketball stats and create an app in this learning path inspired by the film "Space Jam: A New Legacy."</p>
    <a href="https://learn.microsoft.com/training/paths/optimize-basketball-games-with-machine-learning/?WT.mc_id=LearnDrG-c9-niner&WT.mc_id=SpaceJam_Learn_-all-cxa" title="Space Jam module">
        <img src="/assets/learn/students/spacejam-python/spacejam-python.png" aria-hidden="true" class="thumb"/>
    </a>
</div><br/>
