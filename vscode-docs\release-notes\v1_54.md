---
Order:
TOCTitle: February 2021
PageTitle: Visual Studio Code February 2021
MetaDescription: Learn what is new in the Visual Studio Code February 2021 Release (1.54)
MetaSocialImage: 1_54/release-highlights.png
Date: 2021-3-4
DownloadVersion: 1.54.3
---
# February 2021 (version 1.54)

**Update 1.54.1**: The update addresses an [issue](https://github.com/microsoft/vscode/issues/118124) with an extension dependency.

**Update 1.54.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22February+2021+Recovery+2%22+is%3Aclosed).

**Update 1.54.3**: The update addresses this [issue](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22February+2021+Recovery+3%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the February 2021 release of Visual Studio Code. There are a number of updates in this version that we hope you will like, some of the key highlights include:

* **[Apple Silicon builds](#apple-silicon)** - VS Code is now available in stable for Apple Silicon.
* **[Accessibility improvements](#accessibility)** - Better word navigation on Windows and roles for views and buttons.
* **[Persistent terminal processes](#retain-terminal-processes-on-window-reload)** - Local terminal processes are restored on window reload.
* **[Product Icon Themes](#product-icon-themes)** - Personalize your VS Code icon imagery with Product Icon Themes.
* **[Timeline view improvements](#timeline-view)** - Compare changes across Git history timeline entries.
* **[Auto reload Notebooks](#automatically-reload-notebooks)** - Notebooks automatically reload when their file changes on disk.
* **[Remote ports table view](#table-widget)** - Remote ports now shown in a table widget.
* **[Brackets extensions](#brackets)** - Use Brackets keyboard shortcuts in VS Code.
* **[Troubleshooting extensions blog post](#troubleshooting-extensions-blog-post)** - Learn to troubleshoot extensions using extension bisect.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Join us live** at the [VS Code team's livestream](https://code.visualstudio.com/livestream) on Thursday, March 11 at 8am Pacific (4pm London) to see a demo of what's new in this release, and ask us questions live.

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Apple Silicon

We are happy to announce our first release of stable Apple Silicon builds this iteration. Users on Macs with M1 chips can now use VS Code without emulation with Rosetta, and will notice better performance and longer battery life when running VS Code. Thanks to the community for self-hosting with the Insiders build and reporting issues early in the iteration.

The default download of VS Code for macOS is now a Universal build that runs natively on all Macs. On the [Downloads](https://code.visualstudio.com/download) page, you can find more links to architecture-specific builds for Intel or Apple Silicon, which are smaller downloads compared to the Universal package.

![Website showing the new downloads view for Apple Silicon builds](images/1_54/apple-silicon-download.png)

![Website showing the default download for macOS](images/1_54/macOS-universal-download.png)

## Accessibility

### Toolbars and tabs occupy one tab stop

All toolbars in the workbench now follow the standard navigation behavior, so the toolbar occupies only a single position in the tab order, and is navigable with the arrow keys. Once the focus is on the toolbar, arrow keys should be used to move focus to a specific item in the toolbar. As a result of this change, the workbench has far fewer tab stops, making it easier to navigate across all the parts.

For consistency, the same change was applied to the tabs area. Thus the whole tab area is only one tab stop, and keyboard navigation between tabs should be done with the left and right arrow keys.

### Word navigation on Windows

We have improved how word navigation is done on Windows to align with what Screen Readers on Windows expect from an Electron-based application. Now, when using word navigation, each word will be properly read out by the Screen Reader.

Due to this change, we are deprecating the following commands. They will still work, but we might remove them in the future.

* `cursorWordAccessibilityLeft`
* `cursorWordAccessibilityLeftSelect`
* `cursorWordAccessibilityRight`
* `cursorWordAccessibilityRightSelect`

Non-Windows platforms already worked as expected, so we made no changes there.

### Improved roles for views and buttons

Based on user feedback, we have improved the roles of multiple views and buttons across the workbench. **Activity Bar**, **Side Bar**, and **Panel** now have `role:none`, which will make the screen reader less chatty. **Manage** and **Accounts** buttons inside the Activity Bar now properly announce they have a popup.

## Integrated Terminal

### Retain terminal processes on window reload

Local terminal processes are now restored on window reload, such as when an extension install requires a reload. The terminal will be reconnected and the UI state of the terminals will be restored, including the active tab and split terminal relative dimensions.

![In a local window, two split terminals are created and resized. On reload, the layout appears unchanged.](images/1_54/local-terminal-reconnection.gif)

This feature can be disabled by setting `terminal.integrated.enablePersistentSessions` to false.

### Windows performance improvements

Thanks to the [new terminal process layout](#new-terminal-process-layout), frequent pty data events from terminal processes should now process more quickly. This is expected to affect Windows more than other platforms due to the smaller events provided by conpty on Windows.

### Automatically relaunch terminal requiring environment changes

Recently a new feature was rolled out that enabled extensions to influence the terminal's environment. This is used by various language extensions and the built-in git extension to enable VS Code as a git auth provided to negate the need to sign in. Previously the UX for this was to present a subtle ⚠️ icon in the top-right corner to indicate that a terminal's environment was stale. The indicator was subtle, since environment changes may or not be important.

We've tweaked this to automatically relaunch terminals that have stale environments when they have not yet been interacted with. This may result in some flickering, but extension terminal integration is much more seamless now. For example, the first terminal in a WSL window should no longer show a Git auth error, provided the relaunch happened before typing in the terminal.

## Workbench

### Emmet

The **Wrap with Abbreviation** and **Wrap Individual Lines with Abbreviation** commands have been merged into a single **Wrap with Abbreviation** command to reduce any confusion over which wrap command to use. Additionally, the **Wrap with Abbreviation** command now shows the preview whitespace properly.

The `emmet.extensionsPath` setting can now also take in an array of paths, rather than just a string. Therefore, when combined with Settings Sync, one can point to different snippet files depending on the machine they are on, without having to update the setting per machine.

### Product Icon Themes

The first Product Icon Themes have become available in the Extension Marketplace. Thanks to the theme authors for the great work!

To select or find a theme, use the  **Preference: Product Icon Theme** command from the command prompt or the Settings gear.

![Product icon themes in the Extension Marketplace](./images/1_54/product-icon-themes.png)

### IntelliSense for context keys

VS Code uses [when](https://code.visualstudio.com/api/references/when-clause-contexts)-clauses to enable and disable keybindings. Extensions also use when-clauses to control [menu](https://code.visualstudio.com/api/references/contribution-points#contributes.menus)- and [view](https://code.visualstudio.com/api/references/contribution-points#contributes.views)-contributions. Authoring these when-clauses is now much simpler because the editor offers completions for them.

![IntelliSense for context key in keybindings.json](./images/1_54/suggest-when-clause.png)
*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme), Font: FiraCode*

### Open Editors New Untitled File action

There is now a **New Untitled File** action in the **Open Editors** view title area.

![New untitled file in Open Editors view](./images/1_54/new-untitled-file.png)

### Updated application menu settings

The `window.menuBarVisibility` setting for the application menu visibility has been updated to better reflect the options. Two primary changes have been made.

First, the `default` option for the setting has been renamed to `classic`.

Second, the **Show Menu Bar** entry in the application menu bar now toggles between the `classic` and `compact` options. To hide it completely, you can update the setting, or use the context menu of the Activity Bar when in `compact` mode.

![Show Menu Bar entry in application menu](./images/1_54/show-menu-bar.png)
*Theme: [GitHub Light Theme](https://marketplace.visualstudio.com/items?itemName=GitHub.github-vscode-theme)*

### Webview caching improvements

We've improved caching for a local file inside a webview. If a webview reloads a file from disk, then we now use [ETags](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag) to avoid reading the file again if it has not changed. This can eliminate data transfer, and improves response time, especially for remote workspaces.

This caching currently only applies when an existing webview reloads a resource. It does not apply to newly created webviews, even if that webview belongs to an extension that previously created a similar webview.

### Changes to workspace search actions

We have modified the existing `workbench.view.search` to no longer place the selected text into the search input, but instead open the input. To create a new search using the selected text and respecting the `search.mode` setting, `workbench.action.findInFiles` should be used.

### Modifier only keybindings

It is now possible to define keybindings for duplicate modifiers, for example, `shift shift`, `alt alt`, `ctrl ctrl` or `cmd cmd` / `meta meta` / `win win`. These keybindings can be entered manually in `keybindings.json`, for example:

```
{ "key": "shift shift", "command": "workbench.action.quickOpen" }
```

### Updated list/tree UI

We have updated the list/tree styles to match native focus and selection indicators: focused elements will now be rendered with an outline color.

![List/tree UI showing focused element](./images/1_54/tree-ux.png)

**Breaking change**

Theme publishers are advised to adopt this style as well simply by swapping the customization of the `list.focusBackground` color for the `quickInput.list.focusBackground` color. To see an example, check out this [commit](https://github.com/microsoft/vscode/commit/4a941b1853a7b9e0173fd3f9c6eeeb6af13503f0#diff-f264602aa6d3cb74d6e344ffe88192b001c1611314325bad2b4f2cfa58b62031).

### Table widget

We introduced a new table widget, built on top of the existing list widget, so we can render tabular data with the same performance as we can render list elements. For now, it is used in the Ports view for our Remote features.

![Table widget view of remote Ports](./images/1_54/table.png)

### Getting Started

We are experimenting with a new editor to show on startup, called the Getting Started editor. This is intended to make some of VS Code's features more approachable to new users. Some Insiders users may have already seen it, and it will be slowly rolled out to more Stable users over the course of this iteration. You can always opt in or out of the new behavior by modifying the `workbench.startupEditor` setting to or away from `gettingStarted`.

![Getting Started experience walkthrough](./images/1_54/getting-started.gif)

### Timeline view

You can now compare changes across Git history timeline entries, using the **Select for Compare** and **Compare with Selected** commands—similar to comparing files in the Explorer view. This feature lets you quickly see all the changes between any two commits in your Git file history.

![Select for Compare command in context menu](./images/1_54/select-for-compare.png)

### Default sash hover border color

In the [1.52 release](https://code.visualstudio.com/updates/v1_52#_sash-hover-border-color), we introduced the hover border color token for sashes (`sash.hoverBorder`), but did not set a default. We now show this color by default on all themes.

![Sash hover border color](./images/1_54/sash-hover-color.gif)

## Debugging

### Breakpoints view improvements

**New inline action for removing breakpoints**

We have added an inline action **X** to remove breakpoints in the **Breakpoints view**. This action should make it easier to remove breakpoints.

![Breakpoints view showing X next to selected breakpoint](./images/1_54/breakpoints-x.png)

**Help text support for exception filters**

Recently we have added support for editing conditions on exception filters in the **Breakpoints view**.

In order to make these conditions more discoverable and their syntax self-explanatory, VS Code now shows a hover on the exception filter and a placeholder text in the condition text box.

Since the information for both is provided by the underlying debug extension, it can be much more specific (and thus helpful) than some generic message.

Since this new feature is based on additions to the Debug Adapter Protocol (see below), it requires an opt-in of debug extensions. In this release, only VS Code's built-in JavaScript debugger supports the new feature.

![Breakpoint placeholder](./images/1_54/breakpoint-placeholder.png)

### New settings and other improvements

**Collapse identical lines in the Debug Console**

There is a new setting, `debug.console.collapseIdenticalLines`, that controls if the Debug Console should collapse identical lines and show many occurrences with a badge. The default is to collapse.

**Control which editors to save before debugging**

There is a new setting, `debug.saveBeforeStart`, that controls what editors to save before debugging starts. This setting has the following values:

* `allEditorsInActiveGroup`: Save all editors in the active group. The default value.
* `nonUntitledEditorsInActiveGroup`: Save all editors in the active group except untitled ones.
* `none`: Don't save any editors.

This setting is useful for PowerShell debugging since the PowerShell debugger supports debugging unsaved files.

**Improved Enablement for Restart Frame action**

With a new addition to the Debug Adapter Protocol debug extensions are now able to communicate to the VS Code UI whether the "Restart Frame" action is available on a selected stack frame. VS Code now disables the restart frame inline action in the **Call Stack view** for stack frames that cannot be restarted.

For example JS-Debug extension has adopted this and now async stack traces disable the restart frame action.

## Languages

### TypeScript 4.2

VS Code now ships with TypeScript 4.2.2. This major update includes many TypeScript language improvements, along with many improvements and bug fixes for JavaScript and TypeScript tooling.

You can read more about TypeScript 4.2 [on the TypeScript blog](https://devblogs.microsoft.com/typescript/announcing-typescript-4-2/).

### Quick Fix to declare missing functions

The new **Add missing function declaration** Quick Fix stubs out missing functions. For example, if we have a call to a missing function `add`:

```ts
const x: number = add(1, 2);
```

Using **Add missing function declaration** on `add` results in:

```ts
const x: number = add(1, 2);
function add(arg0: number, arg1: number): number {
    throw new Error("Function not implemented.");
}
```

You can use this Quick Fix while writing code to quickly stub out a function based on a call signature.

To use this Quick Fix in JavaScript, you must [enable type checking](https://code.visualstudio.com/Docs/languages/javascript#_type-checking).

### JS Doc templates now generate @returns

JS Doc templates now automatically include `@returns` if the function being annotated includes a `return` statement. For example, for the function `getViz`:

```js
function getViz(el){
    return el?.viz;
}
```

The generated JSDoc template is:

```js
/**
 *
 * @param {*} el
 * @returns
 */
function getViz(el){
    return el?.viz;
}
```

You can disable `@returns` in JSDoc comments with `javascript.suggest.jsdoc.generateReturns` and `typescript.suggest.jsdoc.generateReturns`.

### Deprecated DOM APIs are now marked

Deprecated DOM methods are now marked as deprecated in suggestions and in code:

![document.clear being marked as deprecated in code](images/1_54/ts-deprecated-dom.png)

You can disable showing deprecated properties and functions in the editor by setting `editor.showDeprecated: false`.

## Notebooks

### Automatically reload notebooks

Notebooks now automatically reload when their file changes on disk and when they don't have unsaved changes. This should make it much simpler to work with notebooks that are under version control.

### Improved focus for large cells

When a large notebook cell is focused, you can now better tell which cell is focused when the top and bottom of the cells are out of view. We now show a border on the left and right side of the focused cell.

![Notebook focus for a large cell](./images/1_54/notebook-focus.gif)

### Hovers in CSS/LESS/SCSS and HTML

You can now fine-tune the content of hovers in HTML and CSS to:

* Not show the documentation from MDN
* Not show the link to the MDN page

Use the settings `css.hover.documentation` and `css.hover.references` to change the hovers for CSS. Replace `css` with `html`, `css`, `scss` or `less` to change the settings for those languages.

## Preview features

### Extensible Markdown renderers for notebooks

This iteration, we explored making the Markdown renderer for notebook cells extensible. In the screenshot below, for example, we've extended VS Code's Markdown renderer to support showing math formula in notebook cells:

![A math formula being rendered in a notebook cell](images/1_54/notebook-math.png)

In order to make our Markdown renderer extensible, we explored using [Markdown It](https://github.com/markdown-it/markdown-it) to render all notebook Markdown. We also explored moving the rendered Markdown into the backlayer webview of the notebook.

While we aren't yet ready to open up an official API for this, we plan on continuing this exploration in March. You can also try out the new extensible Markdown renderers today with the undocumented `notebook.experimental.useMarkdownRenderer: true` setting.

## Contributions to extensions

### Brackets

Developers coming from the Brackets source code editor can keep using their same keyboard shortcuts in VS Code with the [Brackets Keymap](https://marketplace.visualstudio.com/items?itemName=ms-vscode.brackets-keybindings) extension. There is also a [Brackets Extension Pack](https://marketplace.visualstudio.com/items?itemName=ms-vscode.brackets-pack), which bundles extensions for working with CSS and creating live previews.

![Brackets Extension Pack in the Marketplace](images/1_54/brackets-keymap-extension.png)

### GitHub Pull Requests and Issues

Work continues on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

To learn about all the new features and updates, you can see the full [changelog for the 0.24.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0240) release of the extension.

### Remote Development

Work continues on the [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), which allow you to use a container, remote machine, or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Feature highlights in 1.54 include:

* Ports view table layout
* Remote - SSH: X11 Forwarding
* WSL: Improved WSL 2 support

You can learn about new extension features and bug fixes in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_54.md).

## Extension authoring

### Limits for editor title menu and "run" submenu

The space in the editor toolbar (`editor/title`) is limited. Therefore, we have

* implemented an overflow behavior. When the primary group, that is, the group of actions that get rendered with icons, exceeds nine commands then all additional commands overflow into the `...` menu.
* added the `editor/title/run` submenu. We ask extension authors to contribute run- and debug-commands into this menu, not into the `editor/title`-menu anymore as we had recommended in a [previous release](https://code.visualstudio.com/updates/v1_47#_guidelines-for-improving-the-single-file-debug-experience). In addition, it is no longer necessary to use the magic group values `1_run@10` and `1_run@20` in order to make the commands show up in the correct position.

![Run dropdown menu](./images/1_54/run-menu.png)

Also know that the run submenu is dynamic, will replace itself with its first child when that's the only child.

### Authentication Provider API

You can now register an authentication provider, which can be used by other extensions to perform authentication against a service.

### New feedback channel for the Extension Marketplace

There's a new [public repository](https://github.com/microsoft/vsmarketplace) for issues related to the Extension Marketplace. If you have technical issues, want to give feedback, or have feature requests, please [open an issue](https://github.com/microsoft/vsmarketplace/issues).

### Updated codicons

We've added the following new icons to our [codicon library](https://code.visualstudio.com/api/references/icons-in-labels):

* `debug-rerun`
* `gather`
* `git-pull-request-create`
* `notebook-template`
* `run-above`
* `run-below`
* `type-hierarchy`
* `type-hierarchy-sub`
* `type-hierarchy-super`
* `variable-group`

![Codicons for tasks](./images/1_54/codicons.png)

We've also updated the following icons to be consistent:

* `debug-alt-small`
* `debug-alt`
* `debug-rerun`
* `play-circle`
* `play`
* `run-above`
* `run-all`
* `run-below`

![Consistent codicons](./images/1_54/codicons-updated.png)

## Proposed extension APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. This is what you have to do to try out a proposed API:

* You must use Insiders because proposed APIs change frequently.
* You must have this line in the `package.json` file of your extension: `"enableProposedApi": true`.
* Copy the latest version of the [vscode.proposed.d.ts](https://github.com/microsoft/vscode/blob/main/src/vs/vscode.proposed.d.ts) file into your project's source location.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Icon contribution point

The icon contribution point allows extensions to define a new icon by ID, along with a default icon.
That icon ID can then be used by the extension (or any other extensions that depend on the extension) at the places where ThemeIcon can be used:
In [Markdown strings](https://github.com/microsoft/vscode/blob/e88863d191fed9c81a0087ff225ef7daf79c40b8/src/vs/vscode.d.ts#L2516) (for example, `"$(distro-ubuntu)" Ubuntu 20.04"`) and at all places that take a [ThemeIcon](https://github.com/microsoft/vscode/blob/e88863d191fed9c81a0087ff225ef7daf79c40b8/src/vs/vscode.d.ts#L800) (for example, `new ThemeIcon("distro-ubuntu")`).

Product icon themes can redefine the icon (if they know about the icon ID).

For more information, see [issue 114942](https://github.com/microsoft/vscode/issues/114942) for details and to provide feedback.

### Inline value provider API

Today the **Show Inline Values** feature of VS Code's debugger is based on a generic implementation in VS Code core, and doesn't provide customizability through settings or extensibility via extensions. As a consequence, it is not a perfect fit for all languages and sometimes shows incorrect values because it doesn't understand the underlying source language. For this reason, we are working on an extension API that allows to replace the built-in implementation completely or to replace parts of the implementation with custom code.

In this milestone, we've made a first implementation of the [proposed API](https://github.com/microsoft/vscode/blob/57c918802b88902adc2094d70c8fd3aa29a13102/src/vs/vscode.proposed.d.ts#L667-L813) available.

Here is the inline values provider API in a nutshell:

* an `InlineValuesProvider` must be registered for a language.
* for a given document the `InlineValuesProvider` must locate all language elements for which inline values should be shown.
* for every element an `InlineValue` descriptor object must be returned which instructs VS Code what information to show. Currently three types of `InlineValue` objects are supported:
  * `InlineValueText`: the complete text to be shown,
  * `InlineValueVariableLookup`: the name of a variable that VS Code will try to look up in the VARIABLES view to find its value. The name and the value are then shown in the form "name = value",
  * `InlineValueEvaluatableExpression`: an expression that VS Code will send to the debugger for evaluation. The expression and the result are then shown in the form "expression = result".

The following snippet (from [Mock Debug](https://github.com/microsoft/vscode-mock-debug/blob/c11887591467730d561ad43e286749a1a96cd874/src/activateMockDebug.ts#L106-L131)) shows a minimal inline values provider for "markdown" files that uses a regular expression to locate "variables" and then instructs VS Code to find their values via the **Variables view**:

```ts
vscode.languages.registerInlineValuesProvider('markdown', {
  provideInlineValues(document: TextDocument, viewport: Range, context: InlineValueContext) : ProviderResult<InlineValue[]> {
    const allValues: InlineValue[] = [];
    for (let l = 0; l <= context.stoppedLocation.end.line; l++) {
      const line = document.lineAt(l);
      var regExp = /local_[ifso]/ig;	// match variables of the form local_i, local_f, Local_i, LOCAL_S...
      do {
        var match = regExp.exec(line.text);
        if (match) {
          const varName = match[0];
          const varRange = new Range(l, match.index, l, match.index + varName.length);
          allValues.push(new InlineValueVariableLookup(varRange, varName));
        }
      } while (match);
    }
    return allValues;
  }
});
```

And now the inline values provider in action:

![Debugger shows inline values](images/1_54/inline-values.gif)

If you want to provide feedback for this proposed API, please add comments to [this issue](https://github.com/microsoft/vscode/issues/105690).

### Testing

We continued to make progress on [testing in VS Code](https://github.com/microsoft/vscode/issues/107467). This month we focused on refining the base experience in VS Code and the extension APIs. We have also published some [provisional documentation](/api/extension-guides/testing) on testing, which will be fleshed out as the APIs continue to stabilize.

## Language Server Protocol

A first proposal of a [diagnostic pull model](https://github.com/microsoft/language-server-protocol/issues/737) got implemented for the upcoming 3.17 release. The proposal is available in the next versions of the [VS Code LSP libraries](https://github.com/Microsoft/vscode-languageserver-node).

## Debug Adapter Protocol

### More help text properties for ExceptionBreakpointsFilter

Two optional UI attributes `description` and `conditionDescription` have been added to the `ExceptionBreakpointsFilter`.

With these attributes, clients can display additional information about exception breakpoints. The `description` property could be displayed in a hover
and the `conditionDescription` could be used as a placeholder text for a text box where the condition can be edited.

### Improved description for setExceptionBreakpoints request

We have improved the description of the `setExceptionBreakpoints` request by adding guidance for what an adapter should do if the user inputs an invalid exception breakpoint condition.

### New canRestart property for stack frames

A new optional boolean property `canRestart` has been added to the stack frame. It indicated whether the stack frame can be restarted with the `restart` request.
Based on this information a client can enable or disable a **Restart Frame** context menu action.

## Engineering

### Progress on Electron sandbox

This milestone we continued to make the VS Code window ready for enabling Electron's [sandbox](https://www.electronjs.org/docs/tutorial/sandbox) and [context isolation](https://www.electronjs.org/docs/tutorial/context-isolation) features.

Specifically:

* We were able to move some of the native module requirements we have in the renderer to other processes.
* The `MessagePort` communication channel was implemented to work when sandbox is enabled.

### New terminal process layout

Terminal processes are now spawned under a new "pty host" process, instead of the window process. This change is not only part of the Electron sandbox but should also improve stability and performance of the terminal.

### Windows installers published to Windows Package Manager

Windows user and system installers for `x86` and `x64` are now being published to the [Windows Package Manager](https://learn.microsoft.com/windows/package-manager/). You can use the [winget](https://learn.microsoft.com/windows/package-manager/winget/) tool to search for all Visual Studio Code installers - `winget search vscode`, and [install](https://learn.microsoft.com/windows/package-manager/winget/install) any of the published installers.

### Building VS Code using TypeScript 4.3

VS Code now is built using a nightly build of TypeScript 4.3. This lets us validate the TypeScript team's latest work and also provide feedback on new changes.

The latest TypeScript version already helped us catch a few cases where we were [missing awaits in conditionals](https://github.com/microsoft/TypeScript/issues/25330).

### Prevent yarn.lock changes

We now have CI that prevents PRs against `yarn.lock` files, in order to improve supply chain security.

### Default branch: main

We have started renaming the default branch of all our repositories to `main`, including `microsoft/vscode`. We are about 60% finished with this effort.

## Documentation

### VS Code and Python in the classroom

Read the [case study](https://code.visualstudio.com/learn/educators/python) of a Computer Science professor at Harvey Mudd College, who uses VS Code in his introductory CS classes with Python. Learn about the settings he recommends for his students, as well as helpful extensions and free self-paced Python and Data Science lessons.

### Troubleshooting extensions blog post

Everybody loves installing and using VS Code extensions. However, when you have lots of extensions installed, sometimes the editor can start to act "funny" and it would be great to know if the behavior is caused by a specific extension. If you missed the earlier announcement of the [extension bisect](https://code.visualstudio.com/updates/v1_52#_troubleshooting-extension-bisect) utility, you can read this ["Resolving extension issues with bisect"](https://code.visualstudio.com/blogs/2021/02/16/extension-bisect) blog post, which describes how you can quickly troubleshoot "bad" extensions and even help the extension author(s) with feedback.

## Notable fixes

* [100151](https://github.com/microsoft/vscode/issues/100151): menu key applied to the wrong item in explorer
* [112055](https://github.com/microsoft/vscode/issues/112055): Debug opens a new integrated terminal for each Python session
* [114881](https://github.com/microsoft/vscode/issues/114881): Preserve symlinks when copying folders or files
* [114933](https://github.com/microsoft/vscode/issues/114933): "Task cannot be tracked" frustration
* [115922](https://github.com/microsoft/vscode/issues/115922): Cursor keys not working for filtering in File Explorer
* [116037](https://github.com/microsoft/vscode/issues/116037): No Link hovers shown for output in the Debug Console
* [116101](https://github.com/microsoft/vscode/issues/116101): I can't find target command in command search because the English description has disappeared
* [116932](https://github.com/microsoft/vscode/issues/116932): Throwing custom exceptions from contributed command execution shows cryptic error popup

## Thank you

Last but certainly not least, a big _**Thank You**_ to the following people who contributed this month to VS Code:

Contributions to our issue tracking:

* [John Murray (@gjsjohnmurray)](https://github.com/gjsjohnmurray)
* [Andrii Dieiev (@IllusionMH)](https://github.com/IllusionMH)
* [Simon Chan (@yume-chan)](https://github.com/yume-chan)
* [ArturoDent (@ArturoDent)](https://github.com/ArturoDent)
* [Alexander (@usernamehw)](https://github.com/usernamehw)

Contributions to `vscode`:

* [@antonvolokha (Anton Volokha)](https://github.com/antonvolokha): Fix issue #116591 [PR #116885](https://github.com/microsoft/vscode/pull/116885)
* [@CameronIrvine (Cameron)](https://github.com/CameronIrvine): Fix #114432: Multiple save dialogs appearing on Windows if Ctrl+S is pressed multiple times [PR #114450](https://github.com/microsoft/vscode/pull/114450)
* [@davidbailey00 (David Bailey)](https://github.com/davidbailey00): RPM: Don't generate build_id links [PR #116105](https://github.com/microsoft/vscode/pull/116105)
* [@DonJayamanne (Don Jayamanne)](https://github.com/DonJayamanne): Ensure Notebook Cell runState is updated correctly [PR #116901](https://github.com/microsoft/vscode/pull/116901)
* [@engelsdamien (Damien Engels)](https://github.com/engelsdamien): Explicitly list out tsec exemption [PR #116460](https://github.com/microsoft/vscode/pull/116460)
* [@ferenczy (Dawid Ferenczy Rogožan)](https://github.com/ferenczy): Corrected typo in the localization string wordBasedSuggestionsMode [PR #115809](https://github.com/microsoft/vscode/pull/115809)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray): Improve guidance commentary in GH Bug Report template [PR #116183](https://github.com/microsoft/vscode/pull/116183)
* [@guiherzog (Guilherme Herzog)](https://github.com/guiherzog): Improving performance of tree traversal on getViewState function [PR #115387](https://github.com/microsoft/vscode/pull/115387)
* [@habibkarim (Habib Karim)](https://github.com/habibkarim): Handle normalized absolute file paths on markdown preview link click  [PR #116649](https://github.com/microsoft/vscode/pull/116649)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes #115922 [PR #116005](https://github.com/microsoft/vscode/pull/116005)
  * Emmet: add output.reverseAttributes option [PR #116088](https://github.com/microsoft/vscode/pull/116088)
  * Fix vscode becomes unresponsive after pasting filename with many dots in explorer [PR #116268](https://github.com/microsoft/vscode/pull/116268)
  * Consolidate `wrapWithAbbreviation` and `wrapIndividualLinesWithAbbreviation` in one command [PR #116603](https://github.com/microsoft/vscode/pull/116603)
* [@jogo-](https://github.com/jogo-): Fix spelling in papercuts.github-issues [PR #115452](https://github.com/microsoft/vscode/pull/115452)
* [@Klagopsalmer (Alexandre Schnegg)](https://github.com/Klagopsalmer): Fixes #95040 [PR #116027](https://github.com/microsoft/vscode/pull/116027)
* [@lancewl (Hsuan-An Weng Lin)](https://github.com/lancewl)
  * Emmet: add css.color.short setting [PR #116912](https://github.com/microsoft/vscode/pull/116912)
  * Emmet: add support of multiple extensions path [PR #117105](https://github.com/microsoft/vscode/pull/117105)
* [@leereilly (Lee Reilly)](https://github.com/leereilly): Add period to list item for consistency [PR #115375](https://github.com/microsoft/vscode/pull/115375)
* [@manuth (Manuel Thalmann)](https://github.com/manuth): Allow Markdown-Rendering Without Providing an Env-Parameter [PR #117041](https://github.com/microsoft/vscode/pull/117041)
* [@mdesrosiers (Martin Desrosiers)](https://github.com/mdesrosiers): Update marked to 2.0.0 [PR #117714](https://github.com/microsoft/vscode/pull/117714)
(https://github.com/microsoft/vscode/pull/115192)
* [@MarcoZehe (Marco Zehe)](https://github.com/MarcoZehe): Upgrade Playwright to v1.8.0.  [PR #115241](https://github.com/microsoft/vscode/pull/115241)
* [@nrayburn-tech (Nicholas Rayburn)](https://github.com/nrayburn-tech): Rename function in git extension [PR #113673](https://github.com/microsoft/vscode/pull/113673)
* [@obsgolem (Josiah Bills)](https://github.com/obsgolem): Added a new "blankLine" option for move units [PR #115578](https://github.com/microsoft/vscode/pull/115578)
* [@pfitzseb (Sebastian Pfitzner)](https://github.com/pfitzseb): Add Julia grammar [PR #115309](https://github.com/microsoft/vscode/pull/115309)
* [@PathToLife (PathToLife)](https://github.com/PathToLife): Feature: Shift Shift, Ctrl Ctrl, Alt Alt Keybinds (Double press modifer keybind support) [PR #115190](https://github.com/microsoft/vscode/pull/115190)
* [@rluvaton (Raz Luvaton)](https://github.com/rluvaton): fix(workbench): add forgotten escaping in regex [PR #115336](https://github.com/microsoft/vscode/pull/115336)
* [@samrg123 (Samuel Gonzalez)](https://github.com/samrg123): Fixed issue #108301 by escaping URI before creating a MarkdownString. [PR #112330](https://github.com/microsoft/vscode/pull/112330)
* [@sargreal (Felix Haase)](https://github.com/sargreal): remove colon from git host in askpass arguments [PR #117230](https://github.com/microsoft/vscode/pull/117230)
* [@shskwmt (Shunsuke Iwamoto)](https://github.com/shskwmt)
  * Fixes 114236: Add a multi-line comment regex to unIndentedLinePattern [PR #114478](https://github.com/microsoft/vscode/pull/114478)
  * Fixes 115154: Add "overflow: hidden" style to split-view-view [PR #115345](https://github.com/microsoft/vscode/pull/115345)
* [@susiwen8 (susiwen8)](https://github.com/susiwen8)
  * Fix: add alwaysConsumeMouseWheel for list [PR #114182](https://github.com/microsoft/vscode/pull/114182)
  * Fix: wrong event for `onDidSaveNotebookDocument` [PR #115855](https://github.com/microsoft/vscode/pull/115855)
* [@SchoofsKelvin (Kelvin Schoofs)](https://github.com/SchoofsKelvin): Fix issue #114473 [PR #114474](https://github.com/microsoft/vscode/pull/114474)
* [@tspence (Ted Spence)](https://github.com/tspence)
  * Allow issue service to display a bug report window when remote extension host is crashed [PR #116896](https://github.com/microsoft/vscode/pull/116896)
  * Fix webview focus issues that prevent cut/copy/paste when opening files with a quick pick [PR #116899](https://github.com/microsoft/vscode/pull/116899)
* [@TacticalDan](https://github.com/TacticalDan): Reduce arbitrary event limiter from 16ms down to 4.16666 (#107016) [PR #114957](https://github.com/microsoft/vscode/pull/114957)
* [@ugultopu (Utku Gultopu)](https://github.com/ugultopu): Fix misspelling of "likelihood" [PR #116728](https://github.com/microsoft/vscode/pull/116728)
* [@vagusX (vagusX)](https://github.com/vagusX): fix: prevent scroll when list focused in zone-widget [PR #114912](https://github.com/microsoft/vscode/pull/114912)
* [@Wscats (Eno Yao)](https://github.com/Wscats): Supplementary function parameter types [PR #115751](https://github.com/microsoft/vscode/pull/115751)

Contributions to `language-server-protocol`:

* [@DanTup (Danny Tuppeny)](https://github.com/DanTup): Fix reference to insertReplaceSupport capability [PR #1207](https://github.com/microsoft/language-server-protocol/pull/1207)
* [@reznikmm (Maxim Reznik)](https://github.com/reznikmm): Minor fix in MD formatting [PR #1208](https://github.com/microsoft/language-server-protocol/pull/1208)

Contributions to `vscode-languageserver-node`:

* [@xAlien95 (Stefano De Carolis)](https://github.com/xAlien95): Fix broken doc markdown link [PR #741](https://github.com/microsoft/vscode-languageserver-node/pull/741)

Contributions to `vscode-emmet-helper`:

* [@aliasliao (Liao Jinyuan)](https://github.com/aliasliao): provide completions for pascal-case tags when typing jsx [PR #53](https://github.com/microsoft/vscode-emmet-helper/pull/53)
* [@AnuBhar (Anushka Bhargava)](https://github.com/AnuBhar): Fix for 114923 - HTML figcaption tag should be suggested when using the full word [PR #44](https://github.com/microsoft/vscode-emmet-helper/pull/44)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413)
  * Fixes vscode #65464 [PR #46](https://github.com/microsoft/vscode-emmet-helper/pull/46)
  * Enables output.reverseAttributes option [PR #47](https://github.com/microsoft/vscode-emmet-helper/pull/47)
* [@johnsoncodehk (Johnson Chu)](https://github.com/johnsoncodehk): fix: doComplete result may be undefined [PR #51](https://github.com/microsoft/vscode-emmet-helper/pull/51)
* [@lancewl (Hsuan-An Weng Lin)](https://github.com/lancewl)
  * Enable stylesheet.shortHex option [PR #50](https://github.com/microsoft/vscode-emmet-helper/pull/50)
  * Enable multiple extensions path to emmet.extensionPath setting [PR #52](https://github.com/microsoft/vscode-emmet-helper/pull/52)
* [@shskwmt (Shunsuke Iwamoto)](https://github.com/shskwmt): Fixes 115946(vscode): Add '{' to htmlAbbreviationStartRegex [PR #45](https://github.com/microsoft/vscode-emmet-helper/pull/45)

Contributions to `vscode-html-languageservice`:

* [@sapphi-red (翠 / green)](https://github.com/sapphi-red): Remove existing attribute from autocomplete suggestions [PR #99](https://github.com/microsoft/vscode-html-languageservice/pull/99)

Contributions to `vscode-js-debug`:

* [@Cu2y (Q)](https://github.com/Cu2y): Dependabot/npm and yarn/ini 1.3.8 [PR #921](https://github.com/microsoft/vscode-js-debug/pull/921)

Contributions to `vscode-textmate`:

* [@msftrncs (Carl Morris)](https://github.com/msftrncs): Improve endless loop detection [PR #146](https://github.com/microsoft/vscode-textmate/pull/146)

Contributions to `vscode-vsce`:

* [@felipecrs (Felipe Santos)](https://github.com/felipecrs): Add verify-pat command [PR #532](https://github.com/microsoft/vscode-vsce/pull/532)

<!-- In-product release notes styles.  Do not modify without also modifying regex in gulpfile.common.js -->
<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
