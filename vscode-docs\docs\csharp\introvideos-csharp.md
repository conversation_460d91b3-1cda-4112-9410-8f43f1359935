---
ContentId: 14819e24-7ddf-4202-8e2a-d6fed961bff7
DateApproved: 12/12/2023
MetaDescription: Get started with C# in Visual Studio Code by watching these introductory videos
---
# Introductory Videos for C# in VS Code

Get started with C# in Visual Studio Code by watching these introductory videos! These videos are designed to help you set up [C# Dev Kit](https://marketplace.visualstudio.com/items?itemName=ms-dotnettools.csdevkit) and build and debug C# projects in VS Code. After watching these quick tutorials, you'll be able to enjoy VS Code's rich C# feature set.

## What is C# Dev Kit?

This video explains how VS Code together with the C# Dev Kit extension provide a productive and powerful C# development environment.

<iframe src="https://www.youtube-nocookie.com/embed/tFCZw-wZVtg" width="960" height="540" allowFullScreen frameBorder="0" title="What is VS Code and C# Dev Kit?"></iframe>

## Installing C# Dev Kit

Learn how to install Visual Studio Code and C# Dev Kit and create your first C# project.

<iframe src="https://www.youtube-nocookie.com/embed/S4Rks1L03LI" width="960" height="540" allowFullScreen frameBorder="0" title="Installing VS Code and C# Dev Kit"></iframe>

## C# project management

This video shows you how to navigate your C# project using the Solution Explorer in C# Dev Kit.

<iframe src="https://www.youtube-nocookie.com/embed/HnP6pkE8Tjs" width="960" height="540" allowFullScreen frameBorder="0" title="C# Project Management in VS Code"></iframe>

## VS Code C# productivity

This video shows you how to write C# code in VS Code and discover cool productivity tools you can use along the way!

<iframe src="https://www.youtube-nocookie.com/embed/8M-wwFTqsCs" width="960" height="540" allowFullScreen frameBorder="0" title="VS Code C# Productivity"></iframe>

## Debugging C# apps

This video shows you how to debug your C# application using C# Dev Kits debugger tools.

<iframe src="https://www.youtube-nocookie.com/embed/VuIOk3DqKgc" width="960" height="540" allowFullScreen frameBorder="0" title="Debugging C# Apps in VS Code"></iframe>

## Testing C# apps

This video shows you how to test your C# application using C# Dev Kit.

<iframe src="https://www.youtube-nocookie.com/embed/TGM386ZzeOc" width="960" height="540" allowFullScreen frameBorder="0" title="Testing C# Apps in VS Code"></iframe>

## How to contribute to C# in VS Code

This video explains about how you can contribute to a better C# experience in VS Code.

<iframe src="https://www.youtube-nocookie.com/embed/jF9ltwBK2Wk" width="960" height="540" allowFullScreen frameBorder="0" title="How to Contribute to C# in VS Code"></iframe>

## Next steps

- Learn about all the [C# Editing Experience](/docs/languages/csharp.md) available in VS Code.
- [Get Started with C#](/docs/csharp/get-started.md) in VS Code.
