---
Order: 93
TOCTitle: October 2023
PageTitle: Visual Studio Code October 2023
MetaDescription: Learn what is new in the Visual Studio Code October 2023 Release (1.84)
MetaSocialImage: 1_84/release-highlights.png
Date: 2023-11-1
DownloadVersion: 1.84.2
---
# October 2023 (version 1.84)

**Update 1.84.1**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2023+Recovery+1%22+is%3Aclosed).

**Update 1.84.2**: The update addresses these [issues](https://github.com/microsoft/vscode/issues?q=is%3Aissue+milestone%3A%22October+2023+Recovery+2%22+is%3Aclosed).

<!-- DOWNLOAD_LINKS_PLACEHOLDER -->

---

Welcome to the October 2023 release of Visual Studio Code. There are many updates in this version that we hope you'll like, some of the key highlights include:

* **[More audio cues](#accessibility)** - New audio cues to indicate clear, save, and format activity.
* **[Activity bar position](#workbench)** - Move Activity bar to the top for compact display.
* **[Hide editor tabs](#hide-editor-tabs)** - Show multiple, single, or no editor tabs.
* **[Maximize Editor Groups](#maximize-editor-group)** - Quickly expand the active Editor Group.
* **[Python improvements](#python)** - Better run code in terminal, easier virtual environment creation.
* **[FastAPI tutorial](#fastapi-tutorial)** - Learn about developing Python FastAPI apps with VS Code.
* **[Gradle for Java](#gradle-for-java)** - Improved support for Java Gradle projects.
* **[Preview: GitHub Copilot](#github-copilot)** - Chat "agents", generate commit messages, terminal support.

>If you'd like to read these release notes online, go to [Updates](https://code.visualstudio.com/updates) on [code.visualstudio.com](https://code.visualstudio.com).

**Insiders:** Want to try new features as soon as possible? You can download the nightly [Insiders](https://code.visualstudio.com/insiders) build and try the latest updates as soon as they are available.

## Accessibility

### Clear, format, and save opt-in audio cues

When `audioCues.clear` is enabled, a sound indicates that the terminal, a notification, or the chat responses have been cleared.

In files and notebooks, `audioCues.save` and `audioCues.format` can be set to play on user gesture or always for each event. While disabled, an ARIA alert is used instead and can be customized with `accessibility.alert.format` and `accessibility.alert.save`.

### Windows magnifier synced

The Windows magnifier now follows the cursor in VS Code properly.

### Accessible View improvements

By default, a user's cursor is positioned at the bottom of the terminal Accessible View; to preserve the position instead, you can set `terminal.integrated.accessibleViewPreserveCursorPosition` to `true`.

The Accessible View can be hidden with `accessibility.hideAccessibleView`, useful if sharing one's screen with an audience of sighted users.

The Accessible View now closes when a user starts typing and focuses the prior element for a smoother work flow.

### Text editor in window title focused view

Last iteration, we added a `${focusedView}` variable to `window.title`. We now also indicate when a `Text Editor` is focused.

## Workbench

### Customize Activity bar position

You can now move the Activity bar to the top of the Side Bar as shown in the following video.

![Move Activity bar position](images/1_84/activity_bar_position.gif)

When the Activity bar is placed on the top, the Accounts and Manage buttons are moved to the far right of the title bar.

**Note:** This is supported only when the custom title bar is enabled (`"window.titleBarStyle": "custom"`).

### Hide Editor Tabs

Users are now able to hide editor tabs by setting `workbench.editor.showTabs` to `none`. Other `showTabs` options are `multiple` (default) and `single` to show a single editor tab for the active editor.

![Hide editor tab bar](images/1_84/hide-tab-bar.gif)

### Maximize Editor Group

There is a new command **View: Toggle Maximize Editor Group** (`kb(workbench.action.toggleMaximizeEditorGroup)`) to maximize an editor group. This will hide all other groups and adds a button to the tab bar, allowing the user to restore the previous layout. If the setting `workbench.editor.doubleClickTabToToggleEditorGroupSizes` is set to `maximize`, users can double-click on an editor tab to maximize and unmaximize the editor group.

![Maximize editor group showing button to restore to original layout](images/1_84/maximize-editor-group.gif)

### Similar settings search in the Settings editor

Like the Command Palette, the Settings editor now runs a similar settings search to gather more relevant results for a given query.

The implementation is currently in an early stage, and you can expect improvements over the next few iterations.

![The query "automatically save files" in the Settings editor showing some auto save settings](images/1_84/se-natlang-search-2.png)

### Confirmation for opening protocol links

When a protocol link for a file or workspace opens in VS Code, a dialog will now ask for confirmation:

![Confirm protocol links dialog display for a local file](images/1_84/confirm-protocol-link.png)

Protocol links can either point to a local file (for example `vscode://file/path/to/file`) or to a remote file (for example `vscode://vscode-remote/ssh-remote+[USER@]HOST[:PORT]/path/to/file`). For each case, there are new settings to disable this behavior:

* `security.promptForLocalFileProtocolHandling` - For local protocol links
* `security.promptForRemoteFileProtocolHandling`- For remote protocol links

## Editor

### Nearest Quick Fix keyboard shortcut

There is a new setting to activate the nearest Quick Fix in a line from `kb(editor.action.quickFix)` (command ID `editor.action.quickFix`), no matter where your cursor is in that line. Previously a preview feature, **Code Action Widget: Include Nearby Quick Fixes** (`editor.codeActionWidget.includeNearbyQuickFixes`) is now enabled by default.

The command highlights the source code that will be refactored or fixed with Quick Fixes. Normal Code Actions and non-fix refactorings can still be activated at the cursor location.

![Example of new Quick Fix behavior, showing activation on nearest errors, and how all Code Actions are still available at the cursor location](images/1_83/nearest-quick-fix.gif)

### Multi-document highlighting

Initial support for code highlighting across multiple documents was added via the setting **Editor: Multi Document Occurrences** (`editor.multiDocumentOccurrencesHighlight`). This initial implementation features only textual occurrences, with support for semantic highlighting coming in the future.

<video src="images/1_84/multi-doc-occurrences.mp4" autoplay loop controls muted title="Multi document highlighting in VS Code"></video>

## Source Control

### Force push using --force-if-includes

This milestone there is now support for the `--force-if-includes` option, which is an auxiliary option to `--force-with-lease` added in Git 2.30. The new option ensures that commits that are being force-pushed were created after examining the commit at the tip of the remote reference, and reduces the chance of losing commits when auto fetch is enabled. You can disable the use of `--force-if-includes` by disabling the `git.useForcePushIfIncludes` setting.

## Notebooks

### Scroll on Execute improvements

How the next cells are revealed when executing through notebooks with `Shift+Enter` was improved to help focus on the output. This also reduces the amount of cell movement when re-executing cells that already have output.

<video src="images/1_84/notebook-scrolling-output.mp4" autoplay loop controls muted title="Focus output after notebook cell execution"></video>

### IPython stack trace rendering

Exception stack traces from IPython now render clickable links to help navigate to the error. This will only apply if the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension does not alter the stack trace first: `"jupyter.formatStackTraces": false`.

<video src="images/1_84/stack-trace-links.mp4" autoplay loop controls muted title="Clickable stack trace frames from IPython"></video>

## Debug

### JavaScript Debugger

#### Improved Event Listener Breakpoints view

The Event Listener Breakpoints view is friendlier and is now presented as a tree with checkboxes:

![Event Listener Breakpoints view shown as a tree with the Canvas WebGL Error Fired event checked](images/1_84/js-debug-event-listener-bps.png)

#### Better handling of sourcemap renames

When code is compiled with a bundler, variables can be renamed. This is especially common with imports in all bundlers, and certain local identifiers in [esbuild](https://esbuild.github.io). The debugger is now aware of scopes each rename applies to, which fixes many snags users historically hit.

This requires the debugger to parse the syntax tree of compiled modules. This is done in a background thread and only when renames are detected, but the behavior can be disabled by setting `"sourceMapRenames": false` in your `launch.json` to avoid any performance impact.

## Remote Development

The [Remote Development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack), allow you to use a [Dev Container](https://code.visualstudio.com/docs/devcontainers/containers), remote machine via SSH or [Remote Tunnels](https://code.visualstudio.com/docs/remote/tunnels), or the [Windows Subsystem for Linux](https://learn.microsoft.com/windows/wsl) (WSL) as a full-featured development environment.

Highlights include:

* You can now log into Tunnels using your Microsoft account.
* Connect to Dev Containers over SSH and Tunnels now supported on Windows.

You can learn more about these features in the [Remote Development release notes](https://github.com/microsoft/vscode-docs/blob/main/remote-release-notes/v1_84.md).

## Contributions to extensions

### GitHub Copilot

#### Streaming inline chat

The [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension's inline chat can now make progressive text edits and "types" at the rate at which a response is being received. This is a more natural experience than the previous behavior of waiting for the entire chat response to be received before applying it to the editor.

<video src="images/1_83/chat-inline-stream.mp4" autoplay loop controls muted title="Inline chat making progressive edits"></video>

Not all edits are insertions, and for replacements Copilot sometimes has a hard time figuring out where to start. In those cases, streaming might not yet work as expected. Stay tuned for improvements in this area.

#### Chat agents

This iteration, we built a new way to interact with Copilot Chat: **agents**. Agents are like experts who have a specialty that they can help you with, and you can talk to them in the chat by mentioning them with the `@` symbol. Currently, there are two agents:

* `@workspace` has context about the code in your workspace and can help you navigate it, finding relevant files or classes.
* `@vscode` knows about commands and features in the VS Code editor itself, and can help you use them.

Each agent also supports a few slash commands, and the slash commands that you may have used before should now be used with an agent. For example, `/explain` is now `@workspace /explain`. But as a shortcut, you can also just type `/` for a list of completions that will automatically expand to the full agent and command.

#### @workspace

The `@workspace` agent uses a meta prompt to determine what information to collect from the workspace to help answer your question. One approach used by the meta prompt is to look back at your conversation history to resolve ambiguous words/phrases in a question. For example, if you ask `@workspace What does it do?`, the meta prompt will now consider the history to figure out what `it` actually is and what information to collect to answer the question. The meta prompt also uses a wide set of terms, including more synonyms, to generate a list of potentially relevant terms.

File paths and symbols in `@workspace` responses are clickable links. This makes it easy to navigate to the code that Copilot is referring to.

The `@workspace` agent respects the `.gitignore` and `.copilotignore` when deciding which files from the workspace to index.

#### Agents replace slash commands

The new agents replace the functionality of slash commands such as `/createWorkspace` and `/createNotebook` with added slash modifiers:

* `/createWorkspace` --> `@workspace /new`
* `/createNotebook` --> `@workspace /newNotebook`
* `/explain` --> `@workspace /explain`
* `/fix` --> `@workspace /fix`
* `/test` --> `@workspace /test`
* `/vscode` --> `@vscode /api`

![Slash command menu showing agents and slash modifiers](images/1_84/slash-command-menu.png)

Try out the new agents, and type `/help` for more tips!

#### Commit message generation

Copilot Chat can now generate commit messages based on the pending changes using the new "sparkle" action in the Source Control input box.

<video src="images/1_84/copilot-git-commit.mp4" autoplay loop controls muted title="Copilot Chat generating commit messages in VS Code"></video>

#### Import grouping

Generated imports are now always put to the top of the file or below existing import blocks. This is supported for most common programming languages.

#### Improved /explain context

You can ask Copilot Chat to explain a code selection in your active editor either through the `@workspace /explain` command or through the **Explain with Copilot** action in the context menu. Copilot Chat now includes the implementations of referenced symbols such as functions and classes, leading to more accurate and useful explanations. This works best across files when you have an extension contributing language services installed for one of the following languages: TypeScript/JavaScript, Python, Java, C#, C++, Go, or Ruby.

#### Persistent chat view state

Previously, the Copilot Chat view was initially hidden and then later shown. The Copilot Chat view now remains active between window reloads so you don't have to manually reopen it. Additionally, the Chat view now guides you through the process of signing into GitHub and activating your free trial of GitHub Copilot.

#### Chat using configured display language

By default, Copilot Chat now initially responds using your configured [display language](https://code.visualstudio.com/docs/getstarted/locales) in VS Code. You can override this automatic behavior by configuring `github.copilot.chat.localeOverride`.

#### Reduce welcome message verbosity

You can now control whether Copilot Chat greets you with a verbose welcome message when you first start a conversation by configuring `github.copilot.chat.welcomeMessage`. The options are `first` (default), `always`, and `never`.

#### Terminal Quick Fixes

When a failed command is run in the terminal, Copilot will now offer a Quick Fix to explain what happened.

![A sparkle icon shows up that offers to explain a command](images/1_84/copilot-terminal-quick-fix.png)

This can be triggered via the sparkle icon (`kb(editor.action.quickFix)`) next to the current terminal prompt.

#### Terminal command suggestions

Copilot can now offer CLI command suggestions via the `kb(inlineChat.start)` keybinding when the terminal is focused. This brings up Quick Chat with `@workspace /terminal` pre-filled:

![Ctrl/Cmd+I now opens up the Quick Chat](images/1_84/copilot-terminal-command-suggest.png)

This `/terminal` slash command is optimized for suggesting shell commands using the current shell. The quality of suggestions and UX will see more improvements in the next release.

#### Improved surfacing of Run in Terminal action

When a code block has a shell language type, the **Run in Terminal** action is now surfaced on hover of the code block:

![The code block now shows run in terminal in the top level actions, not hidden in the overflow menu](images/1_84/copilot-terminal-run-action.png)

#### Inline chat can reply with terminal commands

The inline chat can now reply with commands to be run in the terminal:

![Run in terminal from inline chat](images/1_84/inline-chat-run-in-terminal.png)

### Python

#### Improvements to run line in the terminal

The [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python) extension has improved the behavior of sending lines to the Python REPL (`Shift+Enter`) when no code has been selected to run. Previously, when you placed the cursor on a line of Python code and pressed `Shift+Enter`, the Python extension would send the exact line content to the REPL, even if it would fail, for example, due to being part of a multi-line command.

With the new experimental **Smart Send** feature, the Python extension sends the smallest block of runnable code surrounding the cursor position to the REPL for execution. This ensures that only complete and executable sections of code are sent to the REPL. The cursor will also be automatically moved to the next executable line, to provide a smooth experience when executing multiple chunks iteratively.

To try it out, you can add the following **User** setting: `"python.experiments.optInto": ["pythonREPLSmartSend"]`. While this feature is currently behind an experiment, we expect it to be the default behavior in the future. If you have feedback or suggestions on how we can further improve this feature, please let us know!

<video src="images/1_84/smart-send-python-repl.mp4" autoplay loop controls muted title="A series of Python code selections being iteratively executed on Shift + Enter"></video>

_Theme: [Catppuccin Macchiato](https://marketplace.visualstudio.com/items?itemName=Catppuccin.catppuccin-vsc) (preview on [vscode.dev](https://vscode.dev/editor/theme/Catppuccin.catppuccin-vsc/Catppuccin%20Macchiato))_

#### Improvements to Python linting extensions

We have made several improvements to our supported linting extensions to allow for a more configurable and flexible experience with your favorite Python tools.

The [Pylint](https://marketplace.visualstudio.com/items?itemName=ms-python.pylint), [Mypy](https://marketplace.visualstudio.com/items?itemName=ms-python.mypy-type-checker) and [Flake8](https://marketplace.visualstudio.com/items?itemName=ms-python.flake8) extensions now offer settings that allow you to specify glob patterns for files that you wish to exclude from linting. This can be useful if you are working with a large codebase with many subprojects, and want to exclude certain folders from being linted. These settings are `"pylint.ignorePatterns"`, `"mypy-type-checker.ignorePatterns"` and `"flake8.ignorePatterns"`.

These extensions also support `cwd` settings, which allows you to specify the working directory for the linter. This setting has been updated to support the variable `${fileDirname}`, so the working directory can be dynamically set to the parent folder of the file you have open in the editor. This is useful if you are working with mono repos, and want the linter working directory to be dynamically updated as you open files from different subprojects. These settings are `"pylint.cwd"`, `"mypy-type-checker.cwd"` and `"flake8.cwd"`.

The default value of the `"mypy-type-checker.preferDaemon"`setting was changed (only applicable to the Mypy extension). Previously, it was set to `true`, which meant that the Mypy daemon would be used by default. After receiving feedback, we changed the default value to `false`. If you are wondering which value would be best for you, our recommendation is to use the Mypy daemon if you enabled the Mypy reporting scope to be the entire workspace (`"mypy-type-checker.reportingScope": "workspace"`) for performance reasons. Otherwise, if the reporting scope is set to the current file, we recommend you use the Mypy executable that shipped with the extension.

#### Deprecated built-in linting and formatting features

With all the work and improvements made to the linting and formatting extensions in VS Code, we have [deprecated the built-in linting and formatting features](https://github.com/microsoft/vscode-python/wiki/Migration-to-Python-Tools-Extensions) that are shipped in the Python extension. This includes all the linting and formatting commands as well as settings (`python.linting.*` and `python.formatting.*`). We recommend that you remove these deprecated settings if you are still using them, and use the supported linting and formatting extensions instead.

If you are using a linter without a supported extension, check out the community-contributed [Ruff extension](https://marketplace.visualstudio.com/items?itemName=charliermarsh.ruff). [Ruff](https://docs.astral.sh/ruff/) is a Python linter written in Rust and [supports various linters](https://docs.astral.sh/ruff/rules/) such as pyflakes, pycodestyle, pydocstyle, and more. Recently support was added for using Ruff as a formatter in VS Code (`"[python]": { "editor.defaultFormatter": "charliermarsh.ruff" }`).

You can also create your own linter or formatter VS Code extension for your favorite Python tool. Check out our [Python Tools Extension Template](https://github.com/microsoft/vscode-python-tools-extension-template) for a quick start.

#### Create environment notification

[Virtual environments](https://docs.python.org/3/library/venv.html) are a recommended way to work with Python projects with dependencies that need to be installed. They offer isolation and reproducibility and are very popular in Python projects.

For this reason, the Python extension now displays a notification when you attempt to run or debug a Python file or project with listed dependencies when you don't have a virtual environment selected on your workspace. This notification provides a quick way to create a new virtual environment through the **Python: Create Environment** command.

If you already have a virtual environment on your workspace, you have the option to select it, or delete and recreate it.

This notification can be disabled by setting `python.python.createEnvironment.trigger` to `off`.

![Notification stating that a virtual environment is not currently selected with an option to Create the environment or disable the notification](images/1_84/create-env-prompt.png)

#### Virtual environment deactivation helper

A couple of months ago we announced a new experimental feature for [terminal activation using environment variables](https://code.visualstudio.com/updates/v1_82#_terminal-activation-using-environment-variables), to enhance your development workflow by automatically activating the selected environment in the terminal without the need for explicit activation commands. However, since there are no explicit activation scripts working, the `deactivate` command was no longer working when this experiment was enabled.

The Python extension will now detect when you attempt to run the `deactivate` command and show a helper notification to guide you on how to add scripts for your shell so the command will work again when the environment is activated through environment variables. It also offers a button to open your shell profile file for you to add the necessary scripts.

![Notification stating that the deactivate command is not working and offering a button to open the shell profile file](images/1_84/python-deactivate-notification.png)

You can find the full documentation on how to add the necessary scripts for your shell on the [vscode-python wiki](https://aka.ms/AAmx2ft).

If you are not in the experiment and would like to try out this feature, you can add the following **User** setting: `"python.experiments.optInto": ["pythonTerminalEnvVarActivation"]`.

#### Improvements to test output

We've made significant improvements to how you can view and interact with the test output in the Python extension when the `pythonTestAdapter` experiment is enabled, [announced a few months ago](https://code.visualstudio.com/updates/v1_79#_test-discovery-and-run-rewrite). Previously, output from test discovery and execution was inconsistently scattered across the **Python Test Log** output channel and the **Test Results** panel, with some information being duplicated in both. To consolidate the experience, output related to test execution is displayed in the **Test Results** panel, and test discovery in the **Python** output channel. To learn more, read our related [vscode-python wiki](https://github.com/microsoft/vscode-python/wiki/New-Method-for-Output-Handling-in-Python-Testing).

This new approach also supports colorization in the output if you are using Pytest and set `"python.testing.pytestArgs": ["--color=yes"]` in your `settings.json`. Colorization only works for test execution in the **Test Results** panel and will not work for discovery or for output in the **Python Test Log** panel.

![Test execution output displayed in Test Results panel with colors, while test discovery output is displayed in the Python output channel](images/1_84/python-test-output.png)

There is also a new button **Show output** to easily open the Test Logs from the Test Explorer view when errors on test discovery occur.

!["Show output" button to open the Test Logs in the Test Explorer view](images/1_84/python-test-output-button.png)

#### Platform-specific versions of the Python Debugger extension

The [Python Debugger](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) extension now ships platform-specific versions, so that only the necessary platform-specific files are installed on every update. This reduces the size of the extension and helps improve startup time.

#### Tensorboard extension

The [Tensorboard](https://www.tensorflow.org/tensorboard) functionality has moved out of the Python extension into a standalone [Tensorboard](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.tensorboard) extension.

If you have any issues with this new extension or wish to provide feedback, you can file an issue in the [Tensorboard extension GitHub repo](https://github.com/microsoft/vscode-tensorboard/issues).

### Jupyter

#### Execute with Precedent/Dependent Cells

With the [Jupyter](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) extension, you can now run all precedent or dependent cells of a target cell from the dropdown menu next to the **Cell Run** button. This is still a preview feature and can be enabled with the `jupyter.executionAnalysis.enabled` setting and the `notebook.consolidatedRunButton` setting.

This feature is currently powered by the [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance) extension so you will need to install the latest Prerelease version of Pylance to use this feature.

<video src="images/1_84/run-dependent-cell.mp4" autoplay loop controls muted title="Run Precedent/Dependent Cells"></video>

### VS Code Speech

We are introducing a new extension to bring voice support to VS Code! The new [VS Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension integrates into [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) to enable voice-to-text transcription services for Chat input.

Once installed, a microphone icon appears and when selected, begins filling Chat input with text based on the transcribed results of your voice. The transcription is computed locally on your machine and does not require a connection to the internet.

![VS Code Speech activated by the microphone icon](images/1_84/vscode-voice.gif)

### GitHub Pull Requests and Issues

There has been more progress on the [GitHub Pull Requests and Issues](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) extension, which allows you to work on, create, and manage pull requests and issues.

* Projects are displayed and can be added from the Pull Request description webview.
* Integrated with GitHub Copilot to generate the PR title and description from the PR **Create** view.
* PRs checked out with the GitHub CLI (`gh pr checkout`) are recognized by the extension.

Review the [changelog for the 0.76.0](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#0760) release of the extension to learn about the other highlights.

## Preview Features

### Floating editor windows

We continued exploring how to pull editors out of the workbench window into their own windows and now want to invite the VS Code Insiders community to play with this exciting new feature and provide feedback.

Make sure to install [VS Code Insiders](https://code.visualstudio.com/insiders/) and run the new **View: Move Active Editor into a New Window** command on editors to open them in a floating window.

<video src="images/1_84/floating-windows.mp4" autoplay loop controls muted title="Floating windows in VS Code"></video>

We still have to figure out many [issues](https://github.com/microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Aworkbench-auxwindow+-label%3Afeature-request+) and provide missing [features](https://github.com/microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Aworkbench-auxwindow+label%3Afeature-request), but we are optimistic that we can enable this feature in Stable in the near future.

Thanks for testing!

### WASM-WASI support for Language Servers

Support for language servers in WASM/WASI was added to the experimental [wasm-wasi-core](https://marketplace.visualstudio.com/items?itemName=ms-vscode.wasm-wasi-core) extension.

There is also an extension showcasing a simple language server written in Rust and compiled to WASM in the [vscode-wasm repo](https://github.com/microsoft/vscode-wasm/blob/d4fe7c2f4bec260bb97f4f83cec299e146faa985/testbeds/lsp-rust). The extension depends on the [language server crate](https://docs.rs/lsp-server/latest/lsp_server/) maintained by the [Rust analyzer team](https://github.com/rust-lang/rust-analyzer).

## Extension authoring

### Improved test runner

There is now a command-line runner and extension for VS Code to make running tests for extensions easier. Extensions using the new approach can run in VS Code's testing UI. While some migration is required, this usually only takes a few minutes. Read the VS Code [Testing Extensions](https://code.visualstudio.com/api/working-with-extensions/testing-extension) documentation for more information.

### Finalized TestMessage.contextValue API

You can provide a `contextValue` on `TestMessage`s to be shown when users take actions on those messages. Additionally, two new menu contributions points are available, `testing/message/context` and `testing/message/content`. The former is displayed on the message in the Test Results tree view, and the latter is displayed over the message in the editor. For example, this might be used to provide an action to update a fixture in snapshot testing:

![Failed test result with Update Snapshot action displayed](images/1_84/testmessage-contextvalue.png)

Read more about `contextValue` in [issue #190277](https://github.com/microsoft/vscode/issues/190277).

### Updated codicons

The following new icons were added to our [codicon](https://code.visualstudio.com/api/references/icons-in-labels) library:

![New codicons](images/1_84/codicons-oct-2023-release.png)

* `copilot`
* `git-fetch`
* `mic`
* `mic-filled`
* `thumbsup-filled`
* `thumbsdown-filled`
* `coffee`
* `game`
* `snake`
* `vr`
* `chip`
* `music`
* `piano`

### New theme colors

* `textPreformat.background`: Background color for preformatted text segments

### Root folder icons per name

File icon themes authors can now define name specific icons for root folders using the new properties `rootFolderNames` and `rootFolderNamesExpanded`. You can review the [File Icon Theme guide](https://code.visualstudio.com/api/extension-guides/file-icon-theme) for more information.

## Proposed APIs

Every milestone comes with new proposed APIs and extension authors can try them out. As always, we want your feedback. Here are the steps to try out a proposed API:

1. [Find a proposal that you want to try](https://github.com/microsoft/vscode/tree/main/src/vscode-dts) and add its name to `package.json#enabledApiProposals`.
1. Use the latest [@vscode/dts](https://github.com/microsoft/vscode-dts) and run `npx @vscode/dts dev`. It will download the corresponding `d.ts` files into your workspace.
1. You can now program against the proposal.

You cannot publish an extension that uses a proposed API. There may be breaking changes in the next release and we never want to break existing extensions.

### Support configuring data sent to extensions via Issue Reporter

A new proposed API lets extension authors send additional data via the Issue Reporter.

``` ts
        export interface IssueUriRequestHandler {
    // Handle the request by the issue reporter for the Uri you want to direct the user to.
    handleIssueUrlRequest(): ProviderResult<Uri>;
  }

        export interface IssueDataProvider {
    // Provide the data to be used in the issue reporter.
    provideIssueData(token: CancellationToken): ProviderResult<string>;

    // Provide the template to be used in the description of issue reporter.
    provideIssueTemplate(token: CancellationToken): ProviderResult<string>;
  }

        export namespace env {
    export function registerIssueUriRequestHandler(handler: IssueUriRequestHandler): Disposable;
    export function registerIssueDataProvider(provider: IssueDataProvider): Disposable;
  }
```

You can provide a `URI` via `handleIssueUrlRequest` to have the extension issue filed externally on GitHub, or provide a template `string` and extension data `string` for `provideIssueData` and `provideIssueTemplate` in order to send additional extension data to GitHub via the Issue Reporter.

Read more about this proposal in [issue #196863](https://github.com/microsoft/vscode/issues/196863).

### File watchers with custom exclude rules

This milestone we added a proposed API for creating file system watchers with full control over exclude rules:

```ts
export interface FileSystemWatcherOptions {
  /**
   * An optional set of glob patterns to exclude from watching.
   * Glob patterns are always matched relative to the watched folder.
   */
  readonly excludes?: string[];
}

export function createFileSystemWatcher(pattern: RelativePattern, options?: FileSystemWatcherOptions): FileSystemWatcher;
```

This new API gives your extension full control over the file watcher, irrespective if it's a recursive or non-recursive watcher or whether it wants to watch inside or outside the workspace. User or default configured exclude rules for file watching will not apply, so you can be sure to receive only the events you subscribed to.

## Engineering

### Windows 32-bit support ends

There is no longer support for Windows 32-bit VS Code. If you're still on the 32-bit build of VS Code, you should [update](https://code.visualstudio.com/download) to the 64-bit version.

## Extensions and documentation

### Gradle for Java

Java development in VS Code just got easier with the improved [Gradle for Java](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-gradle) extension. The pre-release version has better support for building Gradle projects thanks to adopting the [Build Server Protocol](https://build-server-protocol.github.io) (BSP). Similar to other protocols used in VS Code, for example the [Language Server Protocol](https://microsoft.github.io/language-server-protocol) (LSP), the BSP provides an abstraction layer between development environments and build tools such as Gradle.

To try out the new Gradle support, install both the [Extension Pack for Java](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-pack) and pre-release version of the [Gradle for Java](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-gradle) extension. You can learn more about Gradle and the BSP in this recent [blog post from the Java extension team](https://devblogs.microsoft.com/java/new-build-server-for-gradle).

![Gradle for Java pre-release extension](images/1_84/gradle-extension.png)

### FastAPI tutorial

[FastAPI](https://fastapi.tiangolo.com/) is a modern and fast web framework for building Python APIs, and has become more and more popular thanks to its simplicity and performance.

You can now learn how you can get the best out of VS Code and the Python extension to create and debug FastAPI applications through our new [FastAPI Tutorial](https://code.visualstudio.com/docs/python/tutorial-fastapi)!

### Custom Layout user guide

There is a new [Custom Layout](https://code.visualstudio.com/docs/configure/custom-layout) article describing layout customization for the workbench and editors. There you'll learn how to modify VS Code's main UI elements such as views, panels, and editors to fit your preferred workflow.

![Customize Layout dropdown](images/1_84/customize-layout-dropdown.png)

Topics include:

* Primary and Secondary Side Bars
* Panel position and alignment
* Pinned editor tabs
* Editor group layout
* and more

## Notable fixes

* [194812](https://github.com/microsoft/vscode/issues/194812) ToC shows up while doing search while `workbench.settings.settingsSearchTocBehavior` set to `hide`
* [195722](https://github.com/microsoft/vscode/issues/195722) Blank settings editor when having network issues

## Thank you

Last but certainly not least, a big _**Thank You**_ to the contributors of VS Code.

### Issue tracking

Contributions to our issue tracking:

* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
* [@IllusionMH (Andrii Dieiev)](https://github.com/IllusionMH)
* [@starball5 (starball)](https://github.com/starball5)
* [@ArturoDent (ArturoDent)](https://github.com/ArturoDent)

### Pull requests

Contributions to `vscode`:

* [@amaust (Andrew Maust)](https://github.com/amaust): Fixes Aria Label Showing [Object object] [PR #195929](https://github.com/microsoft/vscode/pull/195929)
* [@Cazka](https://github.com/Cazka): Fix typo for incrementalNaming setting [PR #194900](https://github.com/microsoft/vscode/pull/194900)
* [@Charles-Gagnon (Charles Gagnon)](https://github.com/Charles-Gagnon): Fix arrow nav on dropdown action buttons focusing hidden dropdown [PR #167662](https://github.com/microsoft/vscode/pull/167662)
* [@christian-bromann (Christian Bromann)](https://github.com/christian-bromann): Only render notebook container if visible [PR #188226](https://github.com/microsoft/vscode/pull/188226)
* [@Connormiha (Mikhail)](https://github.com/Connormiha): fix: simplified map+flat [PR #193949](https://github.com/microsoft/vscode/pull/193949)
* [@futurist (James Yang)](https://github.com/futurist): fix: dom.test.ts typo [PR #195249](https://github.com/microsoft/vscode/pull/195249)
* [@gjsjohnmurray (John Murray)](https://github.com/gjsjohnmurray)
  * Correct the CLI helptext for `--profile` [PR #193766](https://github.com/microsoft/vscode/pull/193766)
  * Allow hiding of launcher on Run and Debug title bar when debugging [PR #193812](https://github.com/microsoft/vscode/pull/193812)
  * Preselect correct row in Extension Log Level quickpick (fix #194515) [PR #194517](https://github.com/microsoft/vscode/pull/194517)
  * Show Reload Required buttons simultaneously after updateAllExtensions (#163627) [PR #195421](https://github.com/microsoft/vscode/pull/195421)
  * Show trust editor tab icon correctly for `"window.density.editorTabHeight": "compact"` (fix #196209) [PR #196212](https://github.com/microsoft/vscode/pull/196212)
  * Centre numbers vertically in top activity bar badges (fix #196691) [PR #196696](https://github.com/microsoft/vscode/pull/196696)
  * Correct the #196696 fix which caused oval badges [PR #196715](https://github.com/microsoft/vscode/pull/196715)
  * Upsize progress badge on top activity bar to match #196696 change [PR #196724](https://github.com/microsoft/vscode/pull/196724)
* [@harbin1053020115 (ermin.zem)](https://github.com/harbin1053020115)
  * fix: fix editor progress position when enabled pinnedTabsOnSeparateRow [PR #195314](https://github.com/microsoft/vscode/pull/195314)
  * feat: support iconThemes definitions for root folders [PR #195319](https://github.com/microsoft/vscode/pull/195319)
* [@hsfzxjy (hsfzxjy)](https://github.com/hsfzxjy): Fix faster __vsc_escape_value [PR #194459](https://github.com/microsoft/vscode/pull/194459)
* [@jeanp413 (Jean Pierre)](https://github.com/jeanp413): Fixes websocket doesn't handle ping frames correctly [PR #194436](https://github.com/microsoft/vscode/pull/194436)
* [@jiawei-hong (Wei)](https://github.com/jiawei-hong): styles: only needs to be set border-right-width is none if it is not … [PR #195078](https://github.com/microsoft/vscode/pull/195078)
* [@jruales (Joaquín Ruales)](https://github.com/jruales): Skip regex.replace() cost when there's nothing to replace [PR #194854](https://github.com/microsoft/vscode/pull/194854)
* [@Jvr2022 (Jvr)](https://github.com/Jvr2022)
  * cleanup unused files in .github [PR #189066](https://github.com/microsoft/vscode/pull/189066)
  * Fix grammar [PR #194970](https://github.com/microsoft/vscode/pull/194970)
* [@k-yle (Kyℓe Hensel)](https://github.com/k-yle): fix syntax highlighting for .git-blame-ignore-revs [PR #194584](https://github.com/microsoft/vscode/pull/194584)
* [@MichaelChirico (Michael Chirico)](https://github.com/MichaelChirico): Update org in reference repo [PR #194415](https://github.com/microsoft/vscode/pull/194415)
* [@MrYuto (Yuto Liyosa)](https://github.com/MrYuto)
  * Make `OpenDisassemblyViewAction` an `Action2` [PR #195623](https://github.com/microsoft/vscode/pull/195623)
  * Resolve absolute file target links in tsconfig (#195514) [PR #195759](https://github.com/microsoft/vscode/pull/195759)
* [@sandeep-sen (Sandeep Sen)](https://github.com/sandeep-sen): Adding mgmt libraries for Go + changing matcher logic for Go [PR #191036](https://github.com/microsoft/vscode/pull/191036)
* [@SimonSiefke (Simon Siefke)](https://github.com/SimonSiefke): fix: memory leak in menu [PR #196302](https://github.com/microsoft/vscode/pull/196302)
* [@tats-u (Tatsunori Uchino)](https://github.com/tats-u): Add support for `--force-if-includes` to force push more safely [PR #187932](https://github.com/microsoft/vscode/pull/187932)
* [@tisilent (xiejialong)](https://github.com/tisilent)
  * Disable transform optimization SettingsTree [PR #179095](https://github.com/microsoft/vscode/pull/179095)
  * Terminal: fix rename and injection [PR #194621](https://github.com/microsoft/vscode/pull/194621)
  * fix #191201 [PR #194965](https://github.com/microsoft/vscode/pull/194965)
* [@vuittont60](https://github.com/vuittont60): fix typos [PR #195562](https://github.com/microsoft/vscode/pull/195562)
* [@whscullin (Will Scullin)](https://github.com/whscullin): Make sure link fragment is preserved [PR #193743](https://github.com/microsoft/vscode/pull/193743)
* [@yiliang114 (易良)](https://github.com/yiliang114)
  * feat: Add the logo of the built-in extension [PR #192999](https://github.com/microsoft/vscode/pull/192999)
  * fix: to #157015 fix view label command localized [PR #193544](https://github.com/microsoft/vscode/pull/193544)
  * fix: Close #195980, Fix contribute points default display for extensions [PR #195984](https://github.com/microsoft/vscode/pull/195984)

Contributions to `vscode-js-debug`:

* [@OnesAndZer0s (OnesAndZer0s)](https://github.com/OnesAndZer0s): feat: Tidying Up of Instrumentation Breakpoints [PR #1853](https://github.com/microsoft/vscode-js-debug/pull/1853)

Contributions to `language-server-protocol`:

* [@manandre (Emmanuel André)](https://github.com/manandre): Fix NotebookDocumentSync example [PR #1831](https://github.com/microsoft/language-server-protocol/pull/1831)

<a id="scroll-to-top" role="button" title="Scroll to top" aria-label="scroll to top" href="#"><span class="icon"></span></a>
<link rel="stylesheet" type="text/css" href="css/inproduct_releasenotes.css"/>
