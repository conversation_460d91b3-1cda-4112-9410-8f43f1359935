# February 2024 (version 1.87)

## Dev Containers

### GitHub Copilot Chat suggested templates and features

When adding Dev Container configuration files to a workspace folder (`F1` > `Dev Containers: Add Dev Container Configuration Files...`) GitHub Copilot Chat now suggests templates and features indicated by the sparkle icon:

![GitHub Copilot Chat template suggestion](images/1_87/devcontainer-copilot-template.png)

![GitHub Copilot Chat feature suggestion](images/1_87/devcontainer-copilot-features.png)

You need the [Dev Containers](https://code.visualstudio.com/docs/devcontainers/containers) and the [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension installed to enable this functionality.
