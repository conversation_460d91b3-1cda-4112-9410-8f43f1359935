# August 2020 (version 1.49)

## Visual Studio Code Remote Core

### Automatically forward ports

Ports that are detected in terminal output are now automatically forwarded.

## SSH

### SSH Welcome view

When you have no SSH hosts configured, the Remote Explorer shows information with a more noticeable link to prompt you to set up SSH hosts.

![Welcome SSH](images/1_49/welcome-ssh.png)

## Containers (version 0.140.x)

### Containers Welcome view

When you don't have Docker running or don't have any containers, the Remote Explorer shows better guidance with buttons and links for how to get started with containers.

Below is the Welcome view if Dock<PERSON> isn't running with a link to display the Help section.

![Welcome Containers no Docker](images/1_49/welcome-containers-no-docker.gif)

If VS Code can not find any containers, the Welcome view will prompt you to open a folder or repository in a container to get you started.

![Welcome Containers no containers](images/1_49/welcome-containers-docker.png)

### Support for Docker Compose when cloning repository in container volume

Cloning a repository in a container volume with the **Dev Containers: Clone Repository in Container Volume** command now also supports DevContainers using Docker Compose.

### Select a GitHub repository when cloning a repository in a container volume

When cloning a repository in a container volume, you can now select a GitHub repository.

![Clone a GitHub repository](images/1_49/clone-github-repo.png)

### DevContainer definition configuration options

DevContainer definitions now come with install and configuration options that are shown after picking a definition with the **Dev Containers: Add Dev Container Configuration Files** command.

In the example below, when configuring a Java DevContainer, there is the option to select the Java version:

![Java version picker](images/1_49/java-version-picker.png)

and then choose one or more optional frameworks:

![Java option picker](images/1_49/java-option-picker.png)

## WSL

### WSL Welcome view

When you don't have Windows Subsystem for Linux enabled or you don't have any Linux distributions installed, the Remote Explorer shows better guidance with buttons and links to help get you started with WSL.

![Welcome WSL](images/1_49/welcome-wsl.png)
