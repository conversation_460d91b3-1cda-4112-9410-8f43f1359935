---
ContentId: 3e36b1fa-cefc-4a07-9773-e672da5881a2
DateApproved: 10/11/2021
MetaDescription: Learn how to customize your Visual Studio Code with settings and keyboard shortcuts.
MetaSocialImage: images/opengraph/introvideos-social.png
---
# Customize Visual Studio Code

In this Visual Studio Code tutorial, we show you how to customize Visual Studio Code with settings and keyboard shortcuts.

<iframe src="https://www.youtube-nocookie.com/embed/nORT3-kONgA?si=yrBv0XmZATIA7gVr" width="640" height="320" allowFullScreen="true" frameBorder="0" title="Customize Visual Studio Code"></iframe>

Pick another video from the list: [Introductory Videos](/docs/getstarted/introvideos.md)

## Video outline

* Anatomy of the VS Code UI
* Implementing themes
* Activity Bar
* Main editor
* Status Bar
* Configure the font size
* Using profiles

## Next video

* [Introductory Videos](/docs/getstarted/introvideos.md) - Review the entire list of videos.

## Related resources

* [Key Bindings for Visual Studio Code](/docs/configure/keybindings.md) - See a list of defined keyboard shortcuts and learn how to update them.
* [User and Workspace Settings](/docs/configure/settings.md) - Learn how to customize user and workspace settings.
